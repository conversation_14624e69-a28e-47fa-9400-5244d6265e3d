package br.com.alice.common.core.extensions

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.serialization.gson
import io.ktor.utils.io.charsets.Charset
import java.net.URLDecoder
import java.net.URLEncoder
import java.security.MessageDigest
import java.text.Normalizer
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale
import java.util.UUID
import java.util.regex.Pattern

fun String.toSnakeCase(): String {
    val regex = "([a-zA-Z])(?=[A-Z])".toRegex()
    val replacement = "$1_"

    return this.replace(regex, replacement).lowercase()
}

fun String.toKebabCase(): String {
    val regex = "([a-z])([A-Z]+)".toRegex()
    val replacement = "$1-$2"

    return this.replace(regex, replacement).lowercase()
}

fun String.snakeToCamelCase(): String {
    val regex = "_[a-zA-Z]".toRegex()

    return this.replace(regex) {
        it.value.replace("_", "")
            .uppercase()
    }
}

enum class SymbolType(val value: Regex) {
    PERSON(Regex("""^\d{3}\.?\d{2,3}(\.?\d{0,3}(-?\d{2})?)?""")),
    COMPANY(Regex("""^\d{2}\.?\d{3}(\.?\d{3}(/?\d{4})(-?\d{2})?)?"""))
}

fun String.cleanSymbols(type: SymbolType = SymbolType.PERSON) =
    if (type.value.matches(this))
        this.onlyNumbers()
    else this

val regexUnaccent = "\\p{InCombiningDiacriticalMarks}+".toRegex()

fun CharSequence.unaccent(): String {
    val temp = Normalizer.normalize(this, Normalizer.Form.NFD)
    return regexUnaccent.replace(temp, "")
}

fun String.onlyDigits(): String {
    val regex = Regex("[^A-Za-z0-9 ]")
    return this.replace(regex, "")
}

fun String.onlyNumbers(): String {
    val regex = Regex("[^0-9]")
    return this.replace(regex, "")
}

fun String.clearWhitespaces(): String = this.replace("\\s".toRegex(), "")

fun String.replaceTabs(): String = this.replace("\t", " ")

fun String.onlyAlphanumeric() = this.replace(Regex("[^a-zA-Z0-9]"), "")

fun String.onlyAlphanumericWithSpaces() = this.replace(Regex("[^a-zA-Z0-9 ]"), "")

fun String.isNumeric() = "^[0-9]*\$".toRegex().matches(this)

fun CharSequence.isNotBlank() = this.trim().isNotEmpty()
fun String?.isNotNullOrBlank(): Boolean = this?.trim().isNullOrBlank().not()

fun String.isValidBrazilianNationalId(): Boolean {
    val numbers = mutableListOf<Int>()

    this.filter { it.isDigit() }.forEach {
        numbers.add(it.toString().toInt())
    }

    if (numbers.size != 11) return false

    (0..9).forEach { n ->
        val digits = arrayListOf<Int>()
        (0..10).forEach { _ -> digits.add(n) }
        if (numbers == digits) return false
    }

    val dv1 = ((0..8).sumOf { (it + 1) * numbers[it] }).rem(11).let {
        if (it >= 10) 0 else it
    }

    val dv2 = ((0..8).sumOf { it * numbers[it] }.let { (it + (dv1 * 9)).rem(11) }).let {
        if (it >= 10) 0 else it
    }

    return numbers[9] == dv1 && numbers[10] == dv2
}

fun String.toUUID(): UUID =
    UUID.fromString(this)

fun String.toSafeUUID(): UUID =
    try {
        this.trim().toUUID()
    } catch (e: Exception) {
        UUID.nameUUIDFromBytes(this.trim().toByteArray())
    }

val regexUUID =
    "[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}".toRegex(RegexOption.IGNORE_CASE)

fun String.isUUID(): Boolean = regexUUID.matches(this)

val regexEmail: Pattern =
    Pattern.compile("[a-zA-Z0-9+._%\\-]{1,256}@[a-zA-Z0-9][a-zA-Z0-9\\-]{0,64}(\\.[a-zA-Z0-9][a-zA-Z0-9\\-]{0,25})+")
val validTlds = setOf(
    "com", "org", "net", "edu", "gov", "br", "io", "dev", "tech", "app", "co", "live",
    "ai", "design", "vc", "me", "energy", "cc", "marketing", "de", "it", "fr",
    "consulting", "us", "bio", "capital", "club", "id", "ag", "art", "agency", "online",
    "pt", "in", "uk", "la", "works", "finance", "studio", "tv", "be", "ie", "do", "py",
    "audio", "biz", "mobi", "gg", "ro", "group", "life", "sg", "one", "world", "ong",
    "systems", "software", "media", "solutions", "shop", "space", "cars", "health",
    "family", "partners", "run", "earth", "tt", "as", "video", "fun"
)


fun String.isEmail(): Boolean {
    if (!regexEmail.matcher(this).matches()) return false
    val tld = this.substringAfterLast(".", "")
    return tld in validTlds
}

fun String.toUrlEncoded(): String = URLEncoder.encode(this, Charset.forName("UTF-8").name())

fun String.toUrlDecoded(): String = URLDecoder.decode(this, Charset.forName("UTF-8").name())

fun String.toCPFMask() =
    if (this.length == 11)
        "${this.substring(0, 3)}.${this.substring(3, 6)}.${this.substring(6, 9)}-${this.substring(9, 11)}"
    else this

fun String.withoutCPFMask() =
    this.replace(".", "").replace("-", "")

fun String.toCNPJMask() =
    if (this.length == 14)
        "${this.substring(0, 2)}.${this.substring(2, 5)}.${this.substring(5, 8)}/${
            this.substring(
                8,
                12
            )
        }-${this.substring(12, 14)}"
    else this

fun String.toAnonymizedCNPJMask(): String {
    val cnpj = this.replace(Regex("\\D"), "")
    return when (cnpj.length) {
        14 -> "${cnpj.substring(0, 2)}.***.***/${cnpj.substring(8, 12)}-${cnpj.substring(12, 14)}"
        else -> this
    }
}

fun String.toAnonymizedCpfMask(): String {
    val cpf = this.replace(Regex("\\D"), "")
    return when (cpf.length) {
        11 -> "${cpf.substring(0, 3)}.***.***-${cpf.substring(9, 11)}"
        else -> this
    }
}

fun String.toPhoneNumberMask() =
    when (this.length) {
        11 -> "(${this.substring(0, 2)}) ${this.substring(2, 7)}-${this.substring(7, 11)}"
        10 -> "(${this.substring(0, 2)}) ${this.substring(2, 6)}-${this.substring(6, 10)}"
        else -> this
    }

fun String.toPhoneNumberMaskWithDDD() =
    when (this.length) {
        12 -> "(${this.substring(0, 3)}) ${this.substring(3, 8)}-${this.substring(8, 12)}"
        11 -> "(${this.substring(0, 3)}) ${this.substring(3, 7)}-${this.substring(7, 11)}"
        else -> this
    }

// https://gist.github.com/lovubuntu/164b6b9021f5ba54cefc67f60f7a1a25
fun String.toSha256(): String {
    val bytes = this.toByteArray()
    val md = MessageDigest.getInstance("SHA-256")
    val digest = md.digest(bytes)
    return digest.fold("") { str, it -> str + "%02x".format(it) }
}

fun String.toSha256(salt: String): String {
    val bytes = this.toByteArray()
    val md = MessageDigest.getInstance("SHA-256")
    md.update(salt.toByteArray())
    val digest = md.digest(bytes)
    return digest.fold("") { str, it -> str + "%02x".format(it) }
}

fun String.capitalizeEachWord() = split(" ").joinToString(" ") { it.lowercase().capitalize() }

fun String?.nullIfBlank() = this?.ifBlank { null }

fun String.mask(digits: Int = 0): String {
    val charArray = this.toCharArray()
    val digitsToTake = if (digits <= 0) this.length else digits

    charArray
        .take(digitsToTake)
        .forEachIndexed { index, _ -> charArray[index] = '*' }

    return String(charArray)
}

fun String.toLocalDateTime(): LocalDateTime = LocalDateTime.parse(this)
fun String.toLocalDateTimeFromBrazilianFormat(): LocalDateTime {
    val formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")
    return LocalDateTime.parse(this, formatter)
}

fun String.toZonedDateTime(): ZonedDateTime = ZonedDateTime.parse(this)

fun String.toLocalDate(formatter: DateTimeFormatter = DateTimeFormatter.ISO_LOCAL_DATE): LocalDate =
    LocalDate.parse(this, formatter)

fun String.toLocalTime(): LocalTime = LocalTime.parse(this)

fun String.toLocalDateFromBrazilianFormat(): LocalDate {
    val formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")
    return LocalDate.parse(this, formatter)
}

fun String.toDotDouble() = this.replace(".", "").replace(",".toRegex(), ".").toDouble()

fun String.fromRfc3339ToLocalDateTime(): LocalDateTime {
    val zonedStartTime = ZonedDateTime.parse(this)
    return zonedStartTime.withZoneSameInstant(ZoneId.of("UTC")).toLocalDateTime()
}

fun CharSequence.splitIgnoreEmpty(delimiter: String): List<String> =
    this.split(delimiter).filter {
        it.isNotEmpty()
    }

fun String.capitalize() =
    this.replaceFirstChar { if (it.isLowerCase()) it.titlecase(Locale.getDefault()) else it.toString() }

fun String.isCnpj(): Boolean = this.matches("^\\d{2}\\.\\d{3}\\.\\d{3}/\\d{4}-\\d{2}|\\d{14}\$".toRegex())

fun String.toPersonId() = PersonId.fromString(this)

fun String.toRangeSafeUUID(): UUID =
    try {
        RangeUUID.generateFromUUID(UUID.fromString(this))
    } catch (e: Exception) {
        RangeUUID.generateFromUUID(UUID.nameUUIDFromBytes(this.toByteArray()))
    }

inline fun <reified T> String.deserializeFromJson(): T = gson.fromJson<T>(this)

fun String.orNull() = this.ifBlank { null }

fun String.isValidCnpj(): Boolean {
    // Remove todos os caracteres não numéricos
    val cnpjNumeros = this.replace("\\D".toRegex(), "")

    // Verifica se a string resultante possui 14 caracteres
    if (cnpjNumeros.length != 14) {
        return false
    }

    // Verifica se todos os dígitos são iguais
    if (cnpjNumeros.all { it == cnpjNumeros[0] }) {
        return false
    }

    // Calcula o primeiro dígito verificador
    var soma = 0
    var peso = 2
    for (i in 11 downTo 0) {
        soma += Integer.parseInt(cnpjNumeros[i].toString()) * peso
        peso = if (peso < 9) peso + 1 else 2
    }
    val resto = soma % 11
    val digito1 = if (resto < 2) 0 else 11 - resto

    // Calcula o segundo dígito verificador
    soma = 0
    peso = 2
    for (i in 12 downTo 0) {
        soma += Integer.parseInt(cnpjNumeros[i].toString()) * peso
        peso = if (peso < 9) peso + 1 else 2
    }
    val resto2 = soma % 11
    val digito2 = if (resto2 < 2) 0 else 11 - resto2

    // Retorna verdadeiro se os dígitos verificadores calculados são iguais aos dígitos informados
    return cnpjNumeros.substring(12) == "$digito1$digito2"
}

fun String.isValidCpf(): Boolean {
    // Remove todos os caracteres não numéricos
    val cpfNumeros = this.replace("\\D".toRegex(), "")

    // Verifica se a string resultante possui 11 caracteres
    if (cpfNumeros.length != 11) {
        return false
    }

    // Verifica se todos os dígitos são iguais
    if (cpfNumeros.all { it == cpfNumeros[0] }) {
        return false
    }

    // Calcula o primeiro dígito verificador
    var soma = 0
    for (i in 0 until 9) {
        soma += Integer.parseInt(cpfNumeros[i].toString()) * (10 - i)
    }
    var digito1 = 11 - soma % 11
    if (digito1 > 9) {
        digito1 = 0
    }

    // Calcula o segundo dígito verificador
    soma = 0
    for (i in 0 until 10) {
        soma += Integer.parseInt(cpfNumeros[i].toString()) * (11 - i)
    }
    var digito2 = 11 - soma % 11
    if (digito2 > 9) {
        digito2 = 0
    }

    // Retorna verdadeiro se os dígitos verificadores calculados são iguais aos dígitos informados
    return cpfNumeros.substring(9) == "$digito1$digito2"
}

fun String.toSlugify(): String = Normalizer
    .normalize(this, Normalizer.Form.NFD)
    .replace("[^\\w\\s-]".toRegex(), "")
    .replace('-', ' ')
    .trim()
    .replace("\\s+".toRegex(), "-")
    .lowercase()

fun String.valueToBoolean(): Boolean {
    val allowedTrueSet = listOf("1", "sim", "s", "true", "t", "verdadeiro", "v")
    return allowedTrueSet.contains(this.lowercase())
}

fun String.normalizeCnpjWithoutMask() = this.replace("\\D".toRegex(), "")
fun String.normalizeCnpjWithMask() = this.replace("\\D".toRegex(), "").toCNPJMask()

fun String.nameNormalizer(): String = Normalizer
    .normalize(this, Normalizer.Form.NFD)
    .replace("[^\\w\\s]".toRegex(), "")
    .capitalizeEachWord()

fun String.isValidZipCode(): Boolean {
    val zipCodeCleaned = this.replace(".", "").replace("-", "")


    if (zipCodeCleaned.length != 8)
        return false

    if (!zipCodeCleaned.all { it.isDigit() })
        return false

    return true
}

fun String.getFirstAndLastName(): Pair<String, String> {
    val fullName = this.trim()
    val firstSpace = fullName.indexOf(" ")

    if (firstSpace == -1) {
        return Pair(fullName, "")
    }

    val firstName = fullName.substring(0, firstSpace).trim()
    val lastName = fullName.substring(firstSpace).trim()

    return Pair(firstName, lastName)
}

fun String.unescapeJson(): String {
    var output = this.replace("\\\"", "\"") // Handle double quotes
    output = output.replace("\\\\", "\\")   // Handle backslashes
    output = unescapeUnicode(output)        // Handle unicode escape sequences
    output = output.replace("\\/", "/")     // Handle slashes
    output = output.replace("\\b", "\b")    // Handle backspace
    output = output.replace("\\f", "\u000C")// Handle formfeed
    output = output.replace("\\n", "\n")    // Handle newline
    output = output.replace("\\r", "\r")    // Handle carriage return
    output = output.replace("\\t", "\t")    // Handle tab
    output = output.replace(": \"{", ": {")
    output = output.replace("}\"", "}")
    return output
}

fun unescapeUnicode(input: String): String {
    val regex = "\\\\u([0-9a-fA-F]{4})".toRegex()
    return regex.replace(input) { matchResult ->
        val hexCode = matchResult.groupValues[1]
        val unicodeChar = hexCode.toInt(16).toChar()
        unicodeChar.toString()
    }
}

fun String.removeOpenAiCitations(): String {
    val regex = Regex("【.*?】")
    return this.replace(regex, "")
}

fun String.removeDoubleQuotes(): String {
    val regex = Regex("\"")
    return this.replace(regex, "")
}

fun String.normalizeWhiteSpaces(): String = this.replace("\\s+".toRegex(), " ")

fun CharSequence.notContains(other: CharSequence) =
    this.toString().trim().lowercase().contains(other.toString().lowercase()).not()
