package br.com.alice.communication.email.sender

import br.com.alice.communication.email.model.EmailAddress
import br.com.alice.communication.email.model.EmailAttachment
import br.com.alice.communication.email.model.EmailRequest
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import software.amazon.awssdk.services.pinpoint.PinpointClient
import software.amazon.awssdk.services.pinpoint.model.MessageResponse
import software.amazon.awssdk.services.pinpoint.model.SendMessagesRequest
import software.amazon.awssdk.services.pinpoint.model.SendMessagesResponse
import java.io.ByteArrayInputStream
import java.nio.charset.Charset
import java.util.Base64
import java.util.Properties
import javax.mail.Message
import javax.mail.Session
import javax.mail.internet.InternetAddress
import javax.mail.internet.MimeMessage
import javax.mail.internet.MimeMultipart
import kotlin.test.BeforeTest
import kotlin.test.Test

internal class PinPointEmailClientTest {

    private lateinit var client: PinpointClient
    private lateinit var sutClient: PinPointEmailClient

    @BeforeTest
    fun setup() {
        this.client = mockk()
        this.sutClient = PinPointEmailClient(client)
    }

    @Test
    fun `#send - should call send with the right parameters`() = runBlocking {
        val fromName = "Alice"
        val expectedFromName = Base64.getEncoder().encodeToString(fromName.toByteArray(Charset.forName("UTF-8")))

        val request = EmailRequest(
            from = EmailAddress(fromName, "<EMAIL>"),
            to = listOf(EmailAddress("David Lynch", "<EMAIL>"),
                EmailAddress("Christopher Nolan", "<EMAIL>")),
            subject = "As fotos da festa ficaram ótimas!",
            campaignId = "campaignId",
            html = "<table><tr><td>Olá! Tão aqui as suas fotos da festa Fyre Festival do dia 2017-04-28!</td></tr></table>"
        )

        val messageResponse = MessageResponse.builder().requestId("RequestId").build()
        val sendMessageResponse = SendMessagesResponse.builder().messageResponse(messageResponse).build()

        coEvery { client.sendMessages(match<SendMessagesRequest>{true}) } returns sendMessageResponse

        val emailReceipt = sutClient.send(request)
        Assertions.assertThat(emailReceipt.id).isEqualTo("RequestId")

        coVerify(exactly = 1) { client.sendMessages(match<SendMessagesRequest> {
            val email = it.messageRequest().messageConfiguration().emailMessage()

            val rawBytes = email.rawEmail().data().asByteArray()
            val session = Session.getDefaultInstance(Properties())
            val inputStream = ByteArrayInputStream(rawBytes)
            val mimeMessage = MimeMessage(session, inputStream)
            val bccRecipients = mimeMessage.getRecipients(Message.RecipientType.BCC)
            val pinpointAddresses = it.messageRequest().addresses()

            (mimeMessage.subject == "As fotos da festa ficaram ótimas!" &&
                    email.fromAddress() == "=?UTF-8?B?${expectedFromName}?= <<EMAIL>>" &&
                    pinpointAddresses.size == 2 &&
                    pinpointAddresses.containsKey("=?UTF-8?B?RGF2aWQgTHluY2g=?= <<EMAIL>>") &&
                    pinpointAddresses.containsKey("=?UTF-8?B?Q2hyaXN0b3BoZXIgTm9sYW4=?= <<EMAIL>>") &&
                    bccRecipients == null &&
                    it.applicationId() == "campaignId")
        }) }
    }

    @Test
    fun `#send - should call send with the right parameters and attachments`() = runBlocking {
        val fromName = "Alice"

        val request = EmailRequest(
            from = EmailAddress(fromName, "<EMAIL>"),
            to = listOf(EmailAddress("David Lynch", "<EMAIL>"),
                EmailAddress("Christopher Nolan", "<EMAIL>")),
            bcc = listOf(EmailAddress("Stanley Kubrick", "<EMAIL>")),
            subject = "As fotos da festa ficaram ótimas!",
            campaignId = "campaignId",
            html = "<table><tr><td>Olá! Tão aqui as suas fotos da festa Fyre Festival do dia 2017-04-28!</td></tr></table>",
            attachments = listOf(
                EmailAttachment(
                    fileName = "carta_referencia",
                    content = ByteArray(0),
                    type = "application/pdf"
                )
            )
        )

        val messageResponse = MessageResponse.builder().requestId("RequestId").build()
        val sendMessageResponse = SendMessagesResponse.builder().messageResponse(messageResponse).build()

        coEvery { client.sendMessages(match<SendMessagesRequest>{true}) } returns sendMessageResponse

        val emailReceipt = sutClient.send(request)
        Assertions.assertThat(emailReceipt.id).isEqualTo("RequestId")

        coVerify(exactly = 1) { client.sendMessages(match<SendMessagesRequest> {
            val email = it.messageRequest().messageConfiguration().emailMessage()

            val rawBytes = email.rawEmail().data().asByteArray()
            val session = Session.getDefaultInstance(Properties())
            val inputStream = ByteArrayInputStream(rawBytes)
            val mimeMessage = MimeMessage(session, inputStream)
            
            val from = mimeMessage.from.first() as InternetAddress
            val to1 = mimeMessage.getRecipients(Message.RecipientType.TO)[0] as InternetAddress
            val to2 = mimeMessage.getRecipients(Message.RecipientType.TO)[1] as InternetAddress
            val bcc = mimeMessage.getRecipients(Message.RecipientType.BCC)[0] as InternetAddress
            val mimeText = ((mimeMessage.content as MimeMultipart).getBodyPart(0).content as MimeMultipart).getBodyPart(0)
            val attachment = (mimeMessage.content as MimeMultipart).getBodyPart(1)
            from.address == "<EMAIL>" &&
                    from.personal == "Alice" &&
                    to1.address == "<EMAIL>" &&
                    to2.address == "<EMAIL>" &&
                    bcc.address == "<EMAIL>" &&
                    mimeMessage.subject == "As fotos da festa ficaram ótimas!" &&
                    mimeText.content == "<table><tr><td>Olá! Tão aqui as suas fotos da festa Fyre Festival do dia 2017-04-28!</td></tr></table>" &&
                    mimeText.contentType == "text/html; charset=UTF-8" &&
                    attachment.fileName == "carta_referencia" &&
                    attachment.dataHandler.dataSource.contentType == "application/pdf; name=carta_referencia" &&
                    it.applicationId() == "campaignId"
        }) }
    }

    @Test
    fun `#send - should use RawEmail and handle BCC correctly without attachments`() = runBlocking {
        val fromName = "Alice"

        val request = EmailRequest(
            from = EmailAddress(fromName, "<EMAIL>"),
            to = listOf(EmailAddress("David Lynch", "<EMAIL>")),
            bcc = listOf(EmailAddress("Stanley Kubrick", "<EMAIL>")),
            subject = "Email com BCC sem anexos",
            campaignId = "campaignId",
            html = "<p>Este email tem BCC mas não tem anexos</p>"
        )

        val messageResponse = MessageResponse.builder().requestId("RequestId").build()
        val sendMessageResponse = SendMessagesResponse.builder().messageResponse(messageResponse).build()

        coEvery { client.sendMessages(match<SendMessagesRequest>{true}) } returns sendMessageResponse

        val emailReceipt = sutClient.send(request)
        Assertions.assertThat(emailReceipt.id).isEqualTo("RequestId")

        coVerify(exactly = 1) { client.sendMessages(match<SendMessagesRequest> {
            val email = it.messageRequest().messageConfiguration().emailMessage()

            // Verify it uses RawEmail even without attachments when BCC is present
            val rawBytes = email.rawEmail().data().asByteArray()
            val session = Session.getDefaultInstance(Properties())
            val inputStream = ByteArrayInputStream(rawBytes)
            val mimeMessage = MimeMessage(session, inputStream)

            val from = mimeMessage.from.first() as InternetAddress
            val to = mimeMessage.getRecipients(Message.RecipientType.TO)[0] as InternetAddress
            val bcc = mimeMessage.getRecipients(Message.RecipientType.BCC)[0] as InternetAddress

            // Verify BCC is in MIME headers but not in Pinpoint addresses
            val pinpointAddresses = it.messageRequest().addresses()

            (from.address == "<EMAIL>" &&
                    from.personal == "Alice" &&
                    to.address == "<EMAIL>" &&
                    bcc.address == "<EMAIL>" &&
                    mimeMessage.subject == "Email com BCC sem anexos" &&
                    // BCC should NOT be in Pinpoint addresses (only TO addresses)
                    pinpointAddresses.containsKey("=?UTF-8?B?RGF2aWQgTHluY2g=?= <<EMAIL>>") &&
                    !pinpointAddresses.containsKey("=?UTF-8?B?U3RhbmxleSBLdWJyaWNr?= <<EMAIL>>") &&
                    pinpointAddresses.size == 1 && // Only TO addresses in Pinpoint
                    it.applicationId() == "campaignId")
        }) }
    }

    @Test
    fun `#send - should handle multiple BCC recipients correctly`() = runBlocking {
        val fromName = "Alice"

        val request = EmailRequest(
            from = EmailAddress(fromName, "<EMAIL>"),
            to = listOf(EmailAddress("David Lynch", "<EMAIL>")),
            bcc = listOf(
                EmailAddress("Stanley Kubrick", "<EMAIL>"),
                EmailAddress("Martin Scorsese", "<EMAIL>"),
                EmailAddress("Quentin Tarantino", "<EMAIL>")
            ),
            subject = "Email com múltiplos BCC",
            campaignId = "campaignId",
            html = "<p>Este email tem múltiplos destinatários BCC</p>"
        )

        val messageResponse = MessageResponse.builder().requestId("RequestId").build()
        val sendMessageResponse = SendMessagesResponse.builder().messageResponse(messageResponse).build()

        coEvery { client.sendMessages(match<SendMessagesRequest>{true}) } returns sendMessageResponse

        val emailReceipt = sutClient.send(request)
        Assertions.assertThat(emailReceipt.id).isEqualTo("RequestId")

        coVerify(exactly = 1) { client.sendMessages(match<SendMessagesRequest> {
            val email = it.messageRequest().messageConfiguration().emailMessage()

            val rawBytes = email.rawEmail().data().asByteArray()
            val session = Session.getDefaultInstance(Properties())
            val inputStream = ByteArrayInputStream(rawBytes)
            val mimeMessage = MimeMessage(session, inputStream)

            val bccRecipients = mimeMessage.getRecipients(Message.RecipientType.BCC)
            val pinpointAddresses = it.messageRequest().addresses()

            (bccRecipients.size == 3 &&
                    (bccRecipients[0] as InternetAddress).address == "<EMAIL>" &&
                    (bccRecipients[1] as InternetAddress).address == "<EMAIL>" &&
                    (bccRecipients[2] as InternetAddress).address == "<EMAIL>" &&
                    // Only TO addresses should be in Pinpoint addresses
                    pinpointAddresses.size == 1 &&
                    pinpointAddresses.containsKey("=?UTF-8?B?RGF2aWQgTHluY2g=?= <<EMAIL>>") &&
                    it.applicationId() == "campaignId")
        }) }
    }

}
