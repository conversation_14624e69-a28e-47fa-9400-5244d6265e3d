package br.com.alice.communication.email.sender

import br.com.alice.communication.email.model.CalendarEventEmailRequest
import br.com.alice.communication.email.model.EmailAttachment
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.communication.email.model.EmailRequest
import java.io.ByteArrayOutputStream
import java.util.Properties
import javax.activation.DataHandler
import javax.mail.Message.RecipientType
import javax.mail.Session
import javax.mail.internet.InternetAddress
import javax.mail.internet.MimeBodyPart
import javax.mail.internet.MimeMessage
import javax.mail.internet.MimeMultipart
import javax.mail.util.ByteArrayDataSource

abstract class EmailSenderClient {
    abstract suspend fun send(request: EmailRequest): EmailReceipt
    abstract suspend fun sendCalendarEventEmail(request: CalendarEventEmailRequest): EmailReceipt

    protected fun buildMimeMessage(request: EmailRequest): MimeMessage {
        val session = Session.getDefaultInstance(Properties())
        val mimeMessage = MimeMessage(session)
        mimeMessage.setSubject(request.subject, "UTF-8")
        mimeMessage.setFrom(InternetAddress(request.from.email, request.from.name))
        val emailsTo = request.to.map { it.email }
        mimeMessage.setRecipients(
            RecipientType.TO,
            InternetAddress.parse(emailsTo.joinToString())
        )

        val emailsBcc = request.bcc.map { it.email }
        if (emailsBcc.isNotEmpty()) {
            mimeMessage.setRecipients(
                RecipientType.BCC,
                InternetAddress.parse(emailsBcc.joinToString())
            )
        }

        var mimeMixedContainer = MimeMultipart("mixed")
        val mimeWrap = getBodyContainer(html = request.html)
        mimeMessage.setContent(mimeMixedContainer)
        mimeMixedContainer.addBodyPart(mimeWrap)

        request.attachments?.let {
            mimeMixedContainer = setAttachments(it, mimeMixedContainer)
        }

        return mimeMessage
    }

    protected fun getMessageByteArray(mimeMessage: MimeMessage): ByteArray {
        val outputStream = ByteArrayOutputStream()
        mimeMessage.writeTo(outputStream)
        return outputStream.toByteArray()
    }

    protected fun getBodyContainer(html: String): MimeBodyPart {
        val mimeAlternativeContainer = MimeMultipart("alternative")
        val mimeWrap = MimeBodyPart()
        val mimeText = MimeBodyPart()
        mimeText.setContent(html, "text/html; charset=UTF-8")
        mimeAlternativeContainer.addBodyPart(mimeText)
        mimeWrap.setContent(mimeAlternativeContainer)

        return mimeWrap
    }

    private fun setAttachments(attachments: List<EmailAttachment>, mimeMixedContainer: MimeMultipart): MimeMultipart {
        attachments.forEachIndexed { index, attachment ->
            val fileDataSource = ByteArrayDataSource(attachment.content, attachment.type)
            val attachmentHandler = MimeBodyPart()
            attachmentHandler.dataHandler = DataHandler(fileDataSource)

            val appendAttachmentNumber = if (index > 0) "$index" else ""
            attachmentHandler.fileName = "${attachment.fileName}$appendAttachmentNumber"

            mimeMixedContainer.addBodyPart(attachmentHandler)
        }

        return mimeMixedContainer
    }

}
