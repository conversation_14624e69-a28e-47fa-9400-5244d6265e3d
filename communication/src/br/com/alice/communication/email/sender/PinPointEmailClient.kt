package br.com.alice.communication.email.sender

import br.com.alice.common.logging.logger
import br.com.alice.communication.email.model.CalendarEventEmailRequest
import br.com.alice.communication.email.model.EmailAddress
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.communication.email.model.EmailRequest
import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.services.pinpoint.PinpointClient
import software.amazon.awssdk.services.pinpoint.model.AddressConfiguration
import software.amazon.awssdk.services.pinpoint.model.ChannelType
import software.amazon.awssdk.services.pinpoint.model.DeliveryStatus
import software.amazon.awssdk.services.pinpoint.model.DirectMessageConfiguration
import software.amazon.awssdk.services.pinpoint.model.EmailMessage
import software.amazon.awssdk.services.pinpoint.model.MessageRequest
import software.amazon.awssdk.services.pinpoint.model.MessageResult
import software.amazon.awssdk.services.pinpoint.model.RawEmail
import software.amazon.awssdk.services.pinpoint.model.SendMessagesRequest
import software.amazon.awssdk.services.pinpoint.model.SendMessagesResponse
import java.nio.charset.Charset
import java.util.Base64.getEncoder

class PinPointEmailClient(private val client: PinpointClient) :
    EmailSenderClient() {

    private fun formatEmailAddress(emailAddress: EmailAddress): String {
        val encodedFromName = getEncoder().encodeToString(
            emailAddress.name.toByteArray(Charset.forName("UTF-8"))
        )

        val fromName = "=?UTF-8?B?${encodedFromName}?="
        return "$fromName <${emailAddress.email}>"
    }

    override suspend fun send(request: EmailRequest): EmailReceipt {
        logger.info("PinPointEmailClient.send start")

        val mimeMessage = buildMimeMessage(request)
        val messageByteArray = getMessageByteArray(mimeMessage)

        val rawEmail = RawEmail
            .builder()
            .data(SdkBytes.fromByteArray(messageByteArray))
            .build()

        val emailMessage = EmailMessage.builder()
            .rawEmail(rawEmail)
            .fromAddress(formatEmailAddress(request.from))
            .build()

        val messageConfig = DirectMessageConfiguration.builder()
            .emailMessage(emailMessage)
            .build()

        val addresses = request.to.associate {
            formatEmailAddress(it) to AddressConfiguration.builder()
                .channelType(ChannelType.EMAIL)
                .build()
        }

        val message = MessageRequest.builder()
            .messageConfiguration(messageConfig)
            .addresses(addresses)
            .build()

        val messageRequest = SendMessagesRequest.builder()
            .applicationId(request.campaignId)
            .messageRequest(message)
            .build()

        val response = client.sendMessages(messageRequest)

        logEmailSend(request, response)

        return EmailReceipt(response.messageResponse().requestId())
    }

    private fun logEmailSend(request: EmailRequest, response: SendMessagesResponse) {
        val splitEmailFrom = request.from.email.split("@")
        val emailFromUsername = splitEmailFrom.first()
        val emailFromDomain = splitEmailFrom.last()

        val emailToMap = mutableMapOf<String, String>()
        request.to.forEachIndexed { i, emailTo ->
            val splitEmailTo = emailTo.email.split("@")
            val emailToUsername = splitEmailTo.first()
            val emailToDomain = splitEmailTo.last()
            emailToMap["email_to_username_$i"] = emailToUsername
            emailToMap["email_to_domain_$i"] = emailToDomain
        }

        val emailBccMap = mutableMapOf<String, String>()
        request.bcc.forEachIndexed { i, emailBcc ->
            val splitEmailBcc = emailBcc.email.split("@")
            val emailBccUsername = splitEmailBcc.first()
            val emailBccDomain = splitEmailBcc.last()
            emailBccMap["email_bcc_username_$i"] = emailBccUsername
            emailBccMap["email_bcc_domain_$i"] = emailBccDomain
        }

        val messageResult: MessageResult? = response.messageResponse().result().values.firstOrNull()
        val logArguments = mapOf(
            "email_from_username" to emailFromUsername,
            "email_from_domain" to emailFromDomain,
            "email_to" to emailToMap,
            "email_bcc" to emailBccMap,
            "pinpoint_project_id" to request.campaignId,
            "pinpoint_response_has_result" to response.messageResponse().hasResult(),
            "pinpoint_response_result_size" to response.messageResponse().result().size,
            "pinpoint_response_result_message_id" to messageResult?.messageId(),
            "pinpoint_response_result_status_code" to messageResult?.statusCode(),
            "pinpoint_response_result_status_message" to messageResult?.statusMessage(),
            "pinpoint_response_result_delivery_status" to messageResult?.deliveryStatusAsString(),
        )

        if (messageResult?.deliveryStatus() == DeliveryStatus.SUCCESSFUL) {
            logger.info("PinPointEmailClient.send successfully",
                *logArguments.map { it.key to it.value }.toTypedArray()
            )
        } else if (messageResult == null) {
            logger.error("PinPointEmailClient.send without MessageResult",
                *logArguments.map { it.key to it.value }.toTypedArray()
            )
        } else {
            logger.error("PinPointEmailClient.send with error",
                *logArguments.map { it.key to it.value }.toTypedArray()
            )
        }
    }

    override suspend fun sendCalendarEventEmail(request: CalendarEventEmailRequest): EmailReceipt {
        TODO("Not yet implemented")
    }
}
