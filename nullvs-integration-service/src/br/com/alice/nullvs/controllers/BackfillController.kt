package br.com.alice.nullvs.controllers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.isNotNullOrBlank
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.common.toResponse
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.NULLVS_INTEGRATION_ENVIRONMENT_BACKFILL
import br.com.alice.data.layer.NULLVS_INTEGRATION_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.CompanyContract
import br.com.alice.data.layer.models.CompanyContractStatus
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.CompanySubContractStatus
import br.com.alice.data.layer.models.ExternalModelType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.data.layer.models.NullvsIntegrationRecord
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.InvoiceItemService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.event.MemberInvoiceGroupPaidEvent
import br.com.alice.nullvs.common.NullvsActionType
import br.com.alice.nullvs.common.fromTotvsFormatBoolean
import br.com.alice.nullvs.common.fromTotvsFormatDateDMY
import br.com.alice.nullvs.common.fromTotvsFormatPaymentModel
import br.com.alice.nullvs.converters.NullvsInvoiceConverter.toNullvsInvoiceRequestEvent
import br.com.alice.nullvs.events.NullvsCreateIntegrationRecordEvent
import br.com.alice.nullvs.events.NullvsInvoiceRequestEvent
import br.com.alice.nullvs.events.NullvsSyncClientRequestEvent
import br.com.alice.nullvs.events.NullvsSyncCompanyContractRequestEvent
import br.com.alice.nullvs.events.NullvsSyncCompanySubcontractRequestEvent
import br.com.alice.nullvs.events.NullvsSyncInvoiceRequestEvent
import br.com.alice.nullvs.events.NullvsSyncMemberRequestEvent
import br.com.alice.nullvs.models.BindBeneficiaryToSubcontractRequest
import br.com.alice.nullvs.models.CancelWaitingLogsRequest
import br.com.alice.nullvs.models.ClientsSyncRequest
import br.com.alice.nullvs.models.ContractsSyncRequest
import br.com.alice.nullvs.models.CreateContractSubcontractForCompanyRequest
import br.com.alice.nullvs.models.DeleteNullvsIntegrationRecordRequest
import br.com.alice.nullvs.models.FailedOrPendingInvoiceItemsRequest
import br.com.alice.nullvs.models.InvoicesSyncRequest
import br.com.alice.nullvs.models.MembersSyncRequest
import br.com.alice.nullvs.models.NullvsIntegrationRecordRequest
import br.com.alice.nullvs.models.RecurrentPaymentSyncRequest
import br.com.alice.nullvs.models.SubcontractsSyncRequest
import br.com.alice.nullvs.models.SyncInvoiceItemsFromStartMonthRequest
import br.com.alice.nullvs.models.SyncResponse
import br.com.alice.nullvs.models.SyncStatus
import br.com.alice.nullvs.models.UpdatePriceListingFromTotvsRequest
import br.com.alice.nullvs.models.payment.NullvsInvoiceBatchRequest
import br.com.alice.nullvs.services.internals.NullvsIntegrationRecordService
import br.com.alice.nullvs.services.internals.NullsIntegrationLogReprocessService
import br.com.alice.nullvs.services.internals.TotvsPriceListingService
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.lift
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class BackfillController(
    private val memberService: MemberService,
    private val billingAccountablePartyService: BillingAccountablePartyService,
    private val kafkaProducerService: KafkaProducerService,
    private val nullvsIntegrationRecordService: NullvsIntegrationRecordService,
    private val invoicesService: InvoicesService,
    private val companyService: CompanyService,
    private val companyContractService: CompanyContractService,
    private val companySubContractService: CompanySubContractService,
    private val beneficiaryService: BeneficiaryService,
    private val nullsIntegrationLogReprocessService: NullsIntegrationLogReprocessService,
    private val memberInvoiceGroupService: MemberInvoiceGroupService,
    private val invoicePaymentService: InvoicePaymentService,
    private val totvsPriceListingService: TotvsPriceListingService,
    private val invoiceItemService: InvoiceItemService
) : Controller() {

    private suspend fun withBackfillEnvironment(func: suspend () -> Response) =
        withRootServicePolicy(NULLVS_INTEGRATION_ROOT_SERVICE_NAME) {
            withUnauthenticatedTokenWithKey(NULLVS_INTEGRATION_ENVIRONMENT_BACKFILL) {
                func.invoke()
            }
        }

    suspend fun syncFirstPaymentTowardsTotvs(request: InvoicesSyncRequest) = withBackfillEnvironment {
        logger.info(
            "Start backfill to sync first payment invoice towards Totvs integration",
            "invoice_ids" to request.ids,
        )

        invoicesService.findInvoicesByIds(request.ids).pmapEach { memberInvoice ->
            logger.info("MemberInvoice synced with Totvs successfully", "member_id" to memberInvoice.id)
            if (memberInvoice.alreadyPaid) {
                kafkaProducerService.produce(NullvsSyncInvoiceRequestEvent(memberInvoice))
            }

            SyncStatus(memberInvoice.id, memberInvoice.alreadyPaid)
        }.foldResponse()
    }

    suspend fun emitNullvsInvoiceRequestEvent(idsRequest: InvoicesSyncRequest) = withBackfillEnvironment {
        invoicePaymentService.listInvoicePaymentsById(idsRequest.ids)
            .pmapEach {
                val mig = memberInvoiceGroupService.get(it.invoiceGroupId!!).get()
                val record = nullvsIntegrationRecordService.findByInternalIdAndModel(
                    it.billingAccountablePartyId!!,
                    InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                ).get()
                val payload = mig.toNullvsInvoiceRequestEvent(
                    NotificationEvent(
                        name = "nullvs-invoice-request",
                        producer = "nullvs-integration-service",
                        payload = ""
                    ),
                    it,
                    clientCode = record.externalId,
                    actionType = NullvsActionType.UPDATE_PAYMENT,
                )
                kafkaProducerService.produce(NullvsInvoiceRequestEvent(payload))
            }.foldResponse()
    }

    suspend fun syncRecurrentPaymentTowardsTotvs(request: RecurrentPaymentSyncRequest) = withBackfillEnvironment {
        coroutineScope {
            logger.info(
                "Start backfill to sync recurrent payments towards Totvs integration",
                "member_invoice_group_ids" to request.memberInvoiceGroupIds,
            )

            val memberInvoiceGroupsDeferred =
                async { memberInvoiceGroupService.getByIds(request.memberInvoiceGroupIds) }
            val invoicePaymentsDeferred =
                async { invoicePaymentService.getByInvoiceGroupIds(request.memberInvoiceGroupIds) }

            val memberInvoiceGroups = memberInvoiceGroupsDeferred.await()
            val invoicePayments = invoicePaymentsDeferred.await().get().associateBy { it.invoiceGroupId!! }

            if (invoicePayments.isEmpty()) {
                logger.warn("None of given MemberInvoiceGroup has payment associated")
                return@coroutineScope false.success().foldResponse()
            }

            val results = memberInvoiceGroups.pmapEach { memberInvoiceGroup ->
                kafkaProducerService.produce(
                    MemberInvoiceGroupPaidEvent(
                        memberInvoiceGroup,
                        invoicePayments[memberInvoiceGroup.id]!!
                    )
                ).success()
                    .then { logger.info("MemberInvoiceGroupPaidEvent produced for ${memberInvoiceGroup.id}") }
                SyncStatus(memberInvoiceGroup.id, true)
            }.thenError {
                logger.error(
                    "MemberInvoiceGroup payment is not synced with Totvs due to an error",
                    "exception" to it
                )
            }.get()

            SyncResponse(results).toResponse()
        }
    }

    suspend fun syncMembersTowardsTotvs(
        request: MembersSyncRequest
    ) = withBackfillEnvironment {
        logger.info(
            "Start backfill to sync members towards Totvs integration",
            "member_ids" to request.ids,
            "action" to request.action
        )

        val results = memberService.findByIds(request.ids).pmapEach { member ->
            logger.info("Member synced with Totvs successfully", "member_id" to member.id)
            kafkaProducerService.produce(NullvsSyncMemberRequestEvent(member, request.action, true))

            SyncStatus(member.id, true)
        }.thenError {
            logger.error(
                "Member is not synced with Totvs due to an error",
                "exception" to it
            )
        }.get()

        SyncResponse(results).toResponse()
    }

    suspend fun syncContractTowardsTotvs(
        request: ContractsSyncRequest
    ) = withBackfillEnvironment {
        logger.info(
            "Start backfill to sync contracts towards Totvs integration",
            "contract_ids" to request.ids,
            "action" to request.action
        )

        val results = companyContractService.findByIds(request.ids).pmapEach { contract ->
            logger.info("Contract synced with Totvs successfully", "contract_id" to contract.id)
            kafkaProducerService.produce(NullvsSyncCompanyContractRequestEvent(contract, request.action))

            SyncStatus(contract.id, true)
        }.thenError {
            logger.error(
                "Contract is not synced with Totvs due to an error",
                "exception" to it
            )
        }.get()

        SyncResponse(results).toResponse()
    }

    suspend fun syncSubcontractTowardsTotvs(
        request: SubcontractsSyncRequest
    ) = withBackfillEnvironment {
        logger.info(
            "Start backfill to sync subcontracts towards Totvs integration",
            "contract_ids" to request.ids,
            "action" to request.action
        )

        val results = companySubContractService.findByIds(request.ids).pmapEach { subcontract ->
            logger.info("Subcontract synced with Totvs successfully", "subcontract_id" to subcontract.id)
            kafkaProducerService.produce(NullvsSyncCompanySubcontractRequestEvent(subcontract, request.action))

            SyncStatus(subcontract.id, true)
        }.thenError {
            logger.error(
                "Subcontract is not synced with Totvs due to an error",
                "exception" to it
            )
        }.get()

        SyncResponse(results).toResponse()
    }

    suspend fun syncClientsTowardsTotvs(
        request: ClientsSyncRequest
    ) = withBackfillEnvironment {
        request.billingAccountablePartyIds.let { ids ->
            logger.info(
                "Start backfill to sync clients towards Totvs integration",
                "billing_accountable_party_ids" to ids
            )

            billingAccountablePartyService.findById(ids).pmapEach {
                kafkaProducerService.produce(NullvsSyncClientRequestEvent(it))
                SyncStatus(it.id, true)
            }.thenError {
                logger.error(
                    "Client is not synced with Totvs due to an error",
                    "exception" to it
                )
            }.foldResponse()
        }
    }

    suspend fun createNullvsIntegrationRecord(
        request: NullvsIntegrationRecordRequest
    ) = withBackfillEnvironment {
        val now = LocalDateTime.now()
        request.nullvsIntegrationRecords.let { nullvsIntegrationRecords ->
            logger.info(
                "Start backfill to create NullvsIntegrationRecords",
                "nullvs_integration_records" to nullvsIntegrationRecords
            )
            nullvsIntegrationRecords
        }.map {
            kafkaProducerService.produce(
                NullvsCreateIntegrationRecordEvent(
                    NullvsIntegrationRecord(
                        internalId = it.internalId,
                        externalId = it.externalId,
                        internalModelName = it.internalModelName,
                        externalModelName = it.externalModelName,
                        integratedAt = now
                    )
                )
            )
        }.toResponse()
    }

    suspend fun deleteNullvsRecordById(
        request: DeleteNullvsIntegrationRecordRequest
    ) = withBackfillEnvironment {
        request.nullvsIntegrationRecordId.let { nullvsId ->
            logger.info(
                "Start backfill to delete NullvsIntegrationRecord",
                "nullv_integration_record_id" to nullvsId
            )
            nullvsIntegrationRecordService.deleteById(nullvsId)
        }.foldResponse()
    }

    suspend fun bindBeneficiaryToSubcontract(request: BindBeneficiaryToSubcontractRequest) =
        withBackfillEnvironment {
            coroutineScope {
                logger.info(
                    "Start backFill to bind beneficiary to subcontract", "request" to request,
                )
                val memberIds = request.list.map { it.memberId }
                val beneficiariesDeferred = async {
                    beneficiaryService.findByMemberIds(memberIds)
                        .map { beneficiaries -> beneficiaries.associateBy { it.memberId } }
                        .get()
                }

                val contractExtenalIds = request.list.map { it.contractNumber to it.groupCompany }.distinct()
                val contractsDeferred = async {
                    contractExtenalIds.pmap { (externalId, groupCompany) ->
                        companyContractService.getByExternalIdAndGroupCompany(
                            externalId,
                            groupCompany
                        )
                    }
                        .lift()
                        .map { contracts -> contracts.associateBy { it.externalId + it.groupCompany } }
                        .get()
                }

                val beneficiaries = beneficiariesDeferred.await()
                val contracts = contractsDeferred.await()

                val contractIds = contracts.values.map { it.id }

                val subcontracts = companySubContractService.findByContractIds(contractIds)
                    .map { subcontracts -> subcontracts.associateBy { it.externalId + it.contractId } }
                    .get()

                request.list.pmap { beneficiaryReference ->
                    val contractKey = beneficiaryReference.contractNumber + beneficiaryReference.groupCompany
                    val contract = contracts[contractKey]!!

                    val subcontractKey = beneficiaryReference.subcontractNumber + contract.id
                    val subcontract = subcontracts[subcontractKey]!!

                    val beneficiary = beneficiaries[beneficiaryReference.memberId]!!

                    beneficiaryService.update(
                        beneficiary.copy(
                            companySubContractId = subcontract.id,
                        )
                    )
                }
                    .toResponse()
            }
        }

    suspend fun createContractSubcontract(request: CreateContractSubcontractForCompanyRequest): Response =
        withBackfillEnvironment {
            coroutineScope {
                logger.info(
                    "Start backFill to create contract and subcontract", "request" to request,
                )

                val codeClients =
                    (request.list.mapNotNull { it.contractCodeClient } + request.list.mapNotNull { it.subcontractCodeClient }).filterNot { it.isEmpty() }
                        .distinct()

                logger.info("Code clients found", "ids" to codeClients)

                val billingsDeferred =
                    async {
                        nullvsIntegrationRecordService.findByExternalIdsAndModel(codeClients, ExternalModelType.CLIENT)
                            .map { it.associate { record -> record.internalId to record.externalId } }
                            .flatMap { fromTo ->
                                val ids = fromTo.keys.toList()

                                billingAccountablePartyService.findById(ids)
                                    .map { billings ->
                                        billings.associateBy { fromTo[it.id]!! }
                                    }
                            }.get()
                    }

                val cnpjs = request.list.map { it.subcontractCnpj }

                logger.info("CNPJS found", "cnpjs" to cnpjs)

                val companiesDeferred =
                    async {
                        companyService.findByCnpjs(cnpjs).map { companies -> companies.associateBy { it.cnpj } }.get()
                    }

                val billings = billingsDeferred.await()
                val companies = companiesDeferred.await()

                request.list.pmap { contractSubcontract ->
                    val company = companies[contractSubcontract.subcontractCnpj]!!

                    val contractData = CompanyContract(
                        title = "${contractSubcontract.contractName?.trim()} (${contractSubcontract.contractNumber})",
                        billingAccountablePartyId = contractSubcontract.contractCodeClient.let { if (it.isNotNullOrBlank()) billings[it]?.id else null },
                        startedAt = contractSubcontract.contractStartedAt?.fromTotvsFormatDateDMY(),
                        accountableEmail = company.email,
                        contractFileIds = emptyList(),
                        isProRata = true,
                        defaultProductId = company.defaultProductId,
                        availableProducts = company.availableProducts,
                        dueDate = contractSubcontract.contractDueDate ?: 10,
                        isBillingLevel = contractSubcontract.contractIsBillingLevel.fromTotvsFormatBoolean(),
                        paymentType = contractSubcontract.contractMode.fromTotvsFormatPaymentModel(),
                        externalId = contractSubcontract.contractNumber,
                        status = CompanyContractStatus.ACTIVE,
                        groupCompany = contractSubcontract.contractGroupCompany,
                    )

                    companyContractService.add(contractData, false)
                        .coFoldDuplicated {
                            companyContractService.getByExternalIdAndGroupCompany(
                                contractSubcontract.contractNumber,
                                contractSubcontract.contractGroupCompany,
                            ).flatMap { companyContract ->
                                companyContractService.update(
                                    contractData.copy(id = companyContract.id, version = companyContract.version),
                                    false
                                )
                            }
                        }.andThen {
                            nullvsIntegrationRecordService.add(
                                NullvsIntegrationRecord(
                                    internalId = it.id,
                                    internalModelName = InternalModelType.CONTRACT,
                                    externalId = it.externalId!!,
                                    externalModelName = ExternalModelType.CONTRACT,
                                    integratedAt = LocalDateTime.now(),
                                )
                            ).coFoldDuplicated { true.success() }
                        }.flatMapPair { contract ->
                            companyService.update(company.copy(contractIds = listOf(contract.id)))

                            val subcontractData = CompanySubContract(
                                externalId = contractSubcontract.subcontractNumber,
                                status = CompanySubContractStatus.ACTIVE,
                                title = "${contractSubcontract.subcontractShortDescription.trim()} (${contractSubcontract.subcontractNumber})",
                                billingAccountablePartyId = contractSubcontract.subcontractCodeClient.let { if (it.isNotNullOrBlank()) billings[it]?.id else null },
                                companyId = company.id,
                                contractId = contract.id,
                                isProRata = contractSubcontract.subcontractHasProRata.fromTotvsFormatBoolean(),
                                flexBenefit = company.flexBenefit,
                                hasEmployeesAbroad = company.hasEmployeesAbroad ?: true,
                                defaultFlowType = company.defaultFlowType,
                                dueDate = contractSubcontract.subcontractDueDate,
                                isBillingLevel = contractSubcontract.subcontractIsBillingLevel.fromTotvsFormatBoolean(),
                                paymentType = contractSubcontract.contractMode.fromTotvsFormatPaymentModel(),
                            )

                            companySubContractService.add(subcontractData, false)
                                .coFoldDuplicated {
                                    companySubContractService.findByContractIdAndExternalId(
                                        contract.id,
                                        contractSubcontract.subcontractNumber,
                                    ).flatMap { companySubContract ->
                                        companySubContractService.update(
                                            subcontractData.copy(
                                                id = companySubContract.id,
                                                version = companySubContract.version
                                            ), false
                                        )
                                    }
                                }.andThen {
                                    nullvsIntegrationRecordService.add(
                                        NullvsIntegrationRecord(
                                            internalId = it.id,
                                            internalModelName = InternalModelType.SUBCONTRACT,
                                            externalId = it.externalId!!,
                                            externalModelName = ExternalModelType.SUBCONTRACT,
                                            integratedAt = LocalDateTime.now(),
                                        )
                                    ).coFoldDuplicated { true.success() }
                                }

                        }
                }.toResponse()
            }
        }

    suspend fun cancelWaitingLogs(request: CancelWaitingLogsRequest): Response =
        withBackfillEnvironment {

            logger.info(
                "Start backfill to cancel waiting logs",
                "internal_ids" to request.ids,
            )

            nullsIntegrationLogReprocessService.cancelWaitingLogs(request)

            logger.info(
                "end backfill to cancel waiting logs",
                "internal_ids" to request.ids,
            )

            request.ids.toResponse()

        }

    suspend fun cancelAllWaitingLogs(): Response =
        withBackfillEnvironment {

            logger.info(
                "Start backfill to cancel all waiting logs",
            )

            nullsIntegrationLogReprocessService.cancelAllWaitingLogs()

            logger.info(
                "end backfill to cancel waiting logs",
            )

            true.toResponse()

        }


    suspend fun backfillPriceRange(request: UpdatePriceListingFromTotvsRequest): Response =
        withBackfillEnvironment {
            when {
                request.subContractId != null -> backfillSubContractPriceRange(request.subContractId)
                request.memberId != null -> backfillBeneficiaryPriceRange(request.memberId)
                else -> true.success()
            }.toResponse()
        }

    suspend fun syncInvoiceItemByTypeFromStartMonth(request: SyncInvoiceItemsFromStartMonthRequest): Response =
        withBackfillEnvironment {
            if (request.ids?.isNotEmpty() == true) {
                invoiceItemService.syncInvoiceItemsByIds(request.ids)
            } else {
                if (request.type == null || request.startMonth == null) return@withBackfillEnvironment false.success()
                    .toResponse()
                val referenceMonth = LocalDate.parse(request.startMonth)
                when (request.type.lowercase()) {
                    "member" -> invoiceItemService.syncMemberInvoiceItemsFromStartMonth(referenceMonth)
                    "subcontract" -> invoiceItemService.syncSubcontractInvoiceItemsFromStartMonth(referenceMonth)
                    else -> true.success()
                }
            }.toResponse()
        }

    private suspend fun backfillSubContractPriceRange(subContractId: UUID) =
        companySubContractService.get(subContractId)
            .map { totvsPriceListingService.processSubContractPriceListingBySubContractIdFromTotvs(it) }

    private suspend fun backfillBeneficiaryPriceRange(memberId: UUID) =
        totvsPriceListingService.processMemberPriceListingByMemberIdFromTotvs(memberId)

    suspend fun reprocessFailedOrPendingInvoiceItems(request: FailedOrPendingInvoiceItemsRequest): Response =
        withBackfillEnvironment {
            if (request.ids?.isNotEmpty() == true) {
                invoiceItemService.reprocessFailedOrPendingInvoiceItemsByIds(request.ids, request.isFirstPayment)
            } else {
                if (request.type == null || request.startMonth == null) {
                    return@withBackfillEnvironment false.success().toResponse()
                }
                val referenceMonth = request.startMonth.let { LocalDate.parse(it) }
                when (request.type.lowercase()) {
                    "member" -> invoiceItemService.reprocessFailedOrPendingMemberInvoiceItemsFromStartMonth(
                        referenceMonth
                    )

                    "subcontract" -> invoiceItemService.reprocessFailedOrPendingSubcontractInvoiceItemsFromStartMonth(
                        referenceMonth
                    )

                    else -> true.success()
                }
            }.toResponse()
        }

}
