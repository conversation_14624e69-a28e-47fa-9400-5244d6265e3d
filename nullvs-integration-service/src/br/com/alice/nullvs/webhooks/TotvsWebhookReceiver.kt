package br.com.alice.nullvs.webhooks

import TotvsMemberWebhookResponse
import br.com.alice.authentication.Authenticator
import br.com.alice.common.Response
import br.com.alice.common.core.extensions.nullIfBlank
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.BatchType
import br.com.alice.data.layer.models.InternalModelType
import br.com.alice.nullvs.common.fromTotvsToTotvsGroupCompany
import br.com.alice.nullvs.events.NullvsClientWebhookReceivedEvent
import br.com.alice.nullvs.events.NullvsCompanyContractWebhookReceivedEvent
import br.com.alice.nullvs.events.NullvsCompanySubcontractWebhookReceivedEvent
import br.com.alice.nullvs.events.NullvsInvoiceItemCanceledWebhookReceivedEvent
import br.com.alice.nullvs.events.NullvsInvoiceItemCreatedWebhookReceivedEvent
import br.com.alice.nullvs.events.NullvsInvoiceWebhookEvent
import br.com.alice.nullvs.events.NullvsMemberWebhookEvent
import br.com.alice.nullvs.events.NullvsPaymentWebhookReceivedEvent
import br.com.alice.nullvs.events.NullvsPriceListingWebhookReceived
import br.com.alice.nullvs.events.NullvsPriceListingWebhookReceivedEvent
import br.com.alice.nullvs.models.NullvsClientWebhookReceived
import br.com.alice.nullvs.models.NullvsMemberWebhookReceived
import br.com.alice.nullvs.models.TotvsClientWebhookResponse
import br.com.alice.nullvs.models.TotvsInvoiceItemCanceledWebhookResponse
import br.com.alice.nullvs.models.TotvsInvoiceItemCreatedWebhookResponse
import br.com.alice.nullvs.models.TotvsInvoiceWebhookResponse
import br.com.alice.nullvs.models.TotvsPaymentWebhookResponse
import br.com.alice.nullvs.models.TotvsPriceListingWebhookResponse
import br.com.alice.nullvs.models.TotvsStatus
import br.com.alice.nullvs.models.company.NullvsCompanyContractWebhookReceived
import br.com.alice.nullvs.models.company.NullvsCompanySubcontractWebhookReceived
import br.com.alice.nullvs.models.company.TotvsCompanyContractWebhookResponse
import br.com.alice.nullvs.models.company.TotvsCompanySubcontractWebhookResponse
import br.com.alice.nullvs.models.payment.NullvsInvoiceWebhookReceived
import br.com.alice.nullvs.models.payment.NullvsPaymentWebhookReceived
import io.ktor.http.HttpStatusCode
import java.util.UUID

class TotvsWebhookReceiver(
    private val kafkaService: KafkaProducerService,
) {

    private suspend fun doAndLog(
        funcName: String, idSoc: String, batchId: String,
        block: suspend () -> Any
    ): Response {
        logger.info("TotvsWebhookReceiver, received on $funcName", "id_soc" to idSoc, "batch_id" to batchId)
        val result = block()
        logger.info("Produced events: ", "producer_results" to result)

        return Response(HttpStatusCode.OK, "[accepted]")
    }

    suspend fun totvsMemberCreationReceiver(totvsMemberWebhook: TotvsMemberWebhookResponse) =
        generateEvent("totvsMemberCreationReceiver", totvsMemberWebhook)

    suspend fun totvsMemberCancelReceiver(totvsMemberWebhook: TotvsMemberWebhookResponse) =
        generateEvent("totvsMemberCancelReceiver", totvsMemberWebhook)

    suspend fun totvsMemberUpdateReceiver(totvsMemberWebhook: TotvsMemberWebhookResponse) =
        generateEvent("totvsMemberUpdateReceiver", totvsMemberWebhook)

    private suspend fun generateEvent(name: String, totvsMemberWebhook: TotvsMemberWebhookResponse) = doAndLog(
        name,
        idSoc = totvsMemberWebhook.idSoc, batchId = totvsMemberWebhook.batch
    ) {
        totvsMemberWebhook.payload.map { payload ->
            val memberReceived = NullvsMemberWebhookReceived(
                batchId = totvsMemberWebhook.batch,
                idSoc = totvsMemberWebhook.idSoc,
                payloadId = payload.payloadId,
                status = TotvsStatus.convertTotvsStatus(payload.status),
                description = payload.description
            )

            if (!payload.beneficiaries.isNullOrEmpty()) {
                payload.beneficiaries.map {
                    memberReceived.copy(
                        externalId = it.totvsId.trim(),
                        internalId = it.aliceId,
                    )
                }
            } else {
                listOf(memberReceived)
            }
        }.flatten().map { event ->
            kafkaService.produce(NullvsMemberWebhookEvent(event))
        }
    }

    suspend fun totvsSubContractCreationReceiver(subcontractWebhook: TotvsCompanySubcontractWebhookResponse) =
        subcontractWebhook.convertAndProduceNullvsCompanySubcontractWebhookReceivedEvent(
            "totvsSubContractCreationReceiver",
            BatchType.CREATE
        )

    suspend fun totvsSubContractUpdateReceiver(subcontractWebhook: TotvsCompanySubcontractWebhookResponse) =
        subcontractWebhook.convertAndProduceNullvsCompanySubcontractWebhookReceivedEvent(
            "totvsSubContractUpdateReceiver",
            BatchType.UPDATE
        )

    suspend fun totvsContractCreationReceiver(contractWebhook: TotvsCompanyContractWebhookResponse) =
        contractWebhook.convertAndProduceNullvsCompanyContractWebhookReceivedEvent(
            "totvsContractCreationReceiver",
            BatchType.CREATE
        )

    suspend fun totvsContractUpdateReceiver(contractWebhook: TotvsCompanyContractWebhookResponse) =
        contractWebhook.convertAndProduceNullvsCompanyContractWebhookReceivedEvent(
            "totvsContractUpdateReceiver",
            BatchType.UPDATE
        )

    suspend fun totvsClientCreationReceiver(totvsClientWebhook: TotvsClientWebhookResponse) =
        convertAndProduceNullvsClientWebhookReceivedEvent(
            "totvsClientCreationReceiver",
            totvsClientWebhook,
            BatchType.CREATE
        )

    suspend fun totvsClientUpdateReceiver(totvsClientWebhook: TotvsClientWebhookResponse) =
        convertAndProduceNullvsClientWebhookReceivedEvent(
            "totvsClientUpdateReceiver",
            totvsClientWebhook,
            BatchType.UPDATE
        )

    suspend fun totvsPriceListingCreationReceiver(totvsPriceListingWebhook: TotvsPriceListingWebhookResponse) =
        convertAndProduceNullvsPriceListingWebhookReceivedEvent(
            "totvsPriceListingCreationReceiver",
            totvsPriceListingWebhook,
            BatchType.CREATE
        )

    suspend fun totvsPriceListingUpdateReceiver(totvsPriceListingWebhook: TotvsPriceListingWebhookResponse) =
        convertAndProduceNullvsPriceListingWebhookReceivedEvent(
            "totvsPriceListingUpdateReceiver",
            totvsPriceListingWebhook,
            BatchType.UPDATE
        )

    private suspend fun convertAndProduceNullvsPriceListingWebhookReceivedEvent(
        funcName: String, totvsPriceListingWebhook: TotvsPriceListingWebhookResponse, type: BatchType
    ) = doAndLog(funcName, idSoc = "", batchId = "") {
        kafkaService.produce(
            NullvsPriceListingWebhookReceivedEvent(
                NullvsPriceListingWebhookReceived(total = totvsPriceListingWebhook.total,
                    type = totvsPriceListingWebhook.type,
                    entity = totvsPriceListingWebhook.entity,
                    dateStamp = totvsPriceListingWebhook.dateStamp,
                    user = totvsPriceListingWebhook.user,
                    action = type,
                    idSoc = "",
                    payload = totvsPriceListingWebhook.payload.map { payload ->
                        NullvsPriceListingWebhookReceived.Payload(
                            idTotvs = payload.idTotvs,
                            ansProductCode = payload.ansProductCode,
                            groupCompany = payload.btnCode.nullIfBlank()?.substring(4),
                            subContractExternalId = payload.subContractExternalId,
                            contractExternalId = payload.contractExternalId,
                            memberRegistration = payload.memberRegistration,
                            readjustBaseDate = payload.readjustBaseDate,
                            startDate = payload.startDate,
                            previousStartDate = payload.previousStartDate,
                            prices = payload.ranges.map { range ->
                                NullvsPriceListingWebhookReceived.PriceRangePayload(
                                    appliedReadjust = range.appliedReadjust,
                                    minAge = range.minAge,
                                    maxAge = range.maxAge,
                                    value = range.value,
                                    oldValue = range.oldValue,
                                )
                            }
                        )
                    })
            )
        )
    }

    private suspend fun convertAndProduceNullvsClientWebhookReceivedEvent(
        funcName: String,
        totvsClientWebhook: TotvsClientWebhookResponse,
        type: BatchType
    ) = doAndLog(funcName, idSoc = totvsClientWebhook.idSoc, batchId = totvsClientWebhook.batchId) {
        totvsClientWebhook.payload.map { payload ->
            val event = NullvsClientWebhookReceived(
                idSoc = totvsClientWebhook.idSoc,
                batchId = totvsClientWebhook.batchId,
                status = if (payload.errorCode == null) TotvsStatus.SUCCESS else TotvsStatus.FAILURE,
                posBatchPayload = totvsClientWebhook.posBatchPayload,
                type = type,
                dateStamp = totvsClientWebhook.dateStamp,
                payload = NullvsClientWebhookReceived.Payload(
                    internalModelType = InternalModelType.BILLING_ACCOUNTABLE_PARTY,
                    internalId = payload.internalId?.let { UUID.fromString(it) },
                    clientCode = payload.clientCode,
                    store = payload.store,
                    cgc = payload.cgc,
                    clientName = payload.clientName,
                    errorCode = payload.errorCode,
                    errorMessage = payload.errorMessage,
                )
            )

            kafkaService.produce(NullvsClientWebhookReceivedEvent(event))
        }
    }


    private suspend fun TotvsCompanySubcontractWebhookResponse.convertAndProduceNullvsCompanySubcontractWebhookReceivedEvent(
        funcName: String,
        type: BatchType
    ) = doAndLog(funcName, this.idSoc, this.batchId) {
        this.payload.map { payload ->
            val event = NullvsCompanySubcontractWebhookReceived(
                batchId = this.batchId,
                idSoc = this.idSoc,
                status = if (payload.errorCode == null) TotvsStatus.SUCCESS else TotvsStatus.FAILURE,
                contractNumber = payload.contractNumber,
                internalId = payload.internalId?.trim()?.toUUID(),
                externalId = payload.number,
                groupCompany = payload.groupCompany?.fromTotvsToTotvsGroupCompany(),
                errorCode = payload.errorCode,
                errorMessage = payload.errorMessage,
                type = type
            )
            val inheritedAuthToken = Authenticator.generateCustomToken(
                userId = "${this.idSoc}-${this.batchId}",
                className = "TotvsWebhook"
            )
            kafkaService.produce(
                NullvsCompanySubcontractWebhookReceivedEvent(event, inheritedAuthToken)
            )
        }
    }

    private suspend fun TotvsCompanyContractWebhookResponse.convertAndProduceNullvsCompanyContractWebhookReceivedEvent(
        funcName: String,
        type: BatchType
    ) = doAndLog(funcName, this.idSoc, this.batchId) {
        this.payload.map { payload ->
            val event = NullvsCompanyContractWebhookReceived(
                batchId = this.batchId,
                idSoc = this.idSoc,
                status = if (payload.errorCode == null) TotvsStatus.SUCCESS else TotvsStatus.FAILURE,
                externalId = payload.number,
                internalId = payload.internalId,
                groupCompany = payload.groupCompany?.fromTotvsToTotvsGroupCompany(),
                errorCode = payload.errorCode,
                errorMessage = payload.errorMessage,
                type = type
            )
            val inheritedAuthToken = Authenticator.generateCustomToken(
                userId = "${this.idSoc}-${this.batchId}",
                className = "TotvsWebhook"
            )
            kafkaService.produce(
                NullvsCompanyContractWebhookReceivedEvent(event, inheritedAuthToken)
            )
        }
    }

    suspend fun totvsInvoiceCreationReceiver(totvsInvoiceWebhook: TotvsInvoiceWebhookResponse) =
        doAndLog(
            "totvsInvoiceCreationReceiver",
            idSoc = totvsInvoiceWebhook.idSoc,
            batchId = totvsInvoiceWebhook.batchId
        ) {
            totvsInvoiceWebhook.payload.map { payload ->
                val event = NullvsInvoiceWebhookReceived(
                    idSoc = totvsInvoiceWebhook.idSoc,
                    batchId = totvsInvoiceWebhook.batchId,
                    posBatchPayload = totvsInvoiceWebhook.posBatchPayload,
                    type = BatchType.CREATE,
                    idBatch = totvsInvoiceWebhook.batchId,
                    batchType = BatchType.CREATE,
                    entity = totvsInvoiceWebhook.entity,
                    date = totvsInvoiceWebhook.date,
                    payload = NullvsInvoiceWebhookReceived.Payload(
                        prefix = payload.prefix,
                        number = payload.number,
                        installment = payload.installment,
                        issuedDate = payload.issuedDate,
                        dueDate = payload.dueDate,
                        value = payload.value,
                        financialHistory = payload.financialHistory,
                        type = payload.type,
                        client = payload.client,
                        store = payload.store,
                        clientName = payload.clientName,
                        situation = payload.situation,
                        discount = payload.discount,
                        fine = payload.fine,
                        interest = payload.interest,
                        monetaryCorrection = payload.monetaryCorrection,
                        origin = payload.origin,
                        memberInvoiceGroupId = payload.memberInvoiceGroupId?.trim(),
                    )
                )
                kafkaService.produce(NullvsInvoiceWebhookEvent(event))
            }
        }

    suspend fun totvsPaymentUpdateReceiver(totvsPaymentWebhook: TotvsPaymentWebhookResponse) =
        doAndLog("totvsPaymentReceiver", idSoc = totvsPaymentWebhook.idSoc, batchId = totvsPaymentWebhook.batchId) {
            totvsPaymentWebhook.payload.map {
                NullvsPaymentWebhookReceived(
                    batchId = totvsPaymentWebhook.batchId,
                    idSoc = totvsPaymentWebhook.idSoc,
                    posBatchPayload = totvsPaymentWebhook.posBatchPayload,
                    date = totvsPaymentWebhook.date,
                    status = TotvsStatus.convertTotvsStatus(it.errorCode?.toString()),
                    errorMessage = it.errorMessage,
                    payload = NullvsPaymentWebhookReceived.Payload(
                        prefix = it.prefix,
                        numberOfTitle = it.numberOfTitle,
                        installment = it.installment,
                        type = it.type,
                        client = it.client,
                        store = it.store,
                        paidAt = it.paidAt,
                        value = it.value,
                        aliceBank = it.aliceBank,
                        aliceBankAccount = it.aliceBankAccount,
                        aliceBankAgency = it.aliceBankAgency,
                        description = it.description,
                        reason = it.reason,
                        interest = it.interest,
                        fine = it.fine,
                        discount = it.discount,
                        errorCode = it.errorCode,
                        errorMessage = it.errorMessage
                    ),
                )
            }.pmap { kafkaService.produce(NullvsPaymentWebhookReceivedEvent(it)) }
        }

    suspend fun totvsInvoiceItemCreatedReceiver(totvsInvoiceItemWebhook: TotvsInvoiceItemCreatedWebhookResponse) =
        doAndLog("totvsInvoiceItemCreatedReceiver", idSoc = "", batchId = "") {
            val event = NullvsInvoiceItemCreatedWebhookReceivedEvent(
                NullvsInvoiceItemCreatedWebhookReceivedEvent.NullvsInvoiceItemCreatedWebhookReceived(
                    entity = totvsInvoiceItemWebhook.returnAdditionalBilling.entity,
                    payload = totvsInvoiceItemWebhook.returnAdditionalBilling.billings.map {
                        NullvsInvoiceItemCreatedWebhookReceivedEvent.NullvsInvoiceItemCreatedWebhookReceived.Payload(
                            idAddBilling = it.idAddBilling,
                            status = it.status,
                            idTotvs = it.idTotvs,
                            cpf = it.cpf,
                            message = it.message,
                            attribute = it.attribute,
                        )
                    }
                )
            )

            kafkaService.produce(event)
        }

    suspend fun totvsInvoiceItemCanceledReceiver(totvsInvoiceItemWebhook: TotvsInvoiceItemCanceledWebhookResponse) =
        doAndLog("totvsInvoiceItemCanceledReceiver", idSoc = "", batchId = "") {
            val event = NullvsInvoiceItemCanceledWebhookReceivedEvent(
                idAddBilling = totvsInvoiceItemWebhook.returnAdditionalBilling.idAddBilling,
                status = totvsInvoiceItemWebhook.returnAdditionalBilling.status,
                idTotvs = totvsInvoiceItemWebhook.returnAdditionalBilling.idTotvs,
                message = totvsInvoiceItemWebhook.returnAdditionalBilling.message
            )

            kafkaService.produce(event)
        }
}
