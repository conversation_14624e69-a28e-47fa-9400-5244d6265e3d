package br.com.alice.nullvs.routes

import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.nullvs.controllers.BackfillController
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Routing.backFillRoutes() {
    authenticate {
        val backFillController by inject<BackfillController>()

        route("backfill") {
            post("/sync/members") { coHandler(backFillController::syncMembersTowardsTotvs) }
            post("/sync/contracts") { coHandler(backFillController::syncContractTowardsTotvs) }
            post("/sync/subcontracts") { coHandler(backFillController::syncSubcontractTowardsTotvs) }
            post("/sync/clients") { coHandler(backFillController::syncClientsTowardsTotvs) }
            post("/sync/invoices") { coHandler(backFillController::syncFirstPaymentTowardsTotvs) }
            post("/sync/approve-invoice") { coHandler(backFillController::emitNullvsInvoiceRequestEvent) }
            post("/sync/invoice-group-payments") { coHandler(backFillController::syncRecurrentPaymentTowardsTotvs) }
            post("/sync/invoice-items") { coHandler(backFillController::syncInvoiceItemByTypeFromStartMonth) }
            post("/reprocess-failed-invoice-items") { coHandler(backFillController::reprocessFailedOrPendingInvoiceItems) }
            post("/create-nullvs-integration-record") { coHandler(backFillController::createNullvsIntegrationRecord) }
            post("/delete-nullvs-integration-record") { coHandler(backFillController::deleteNullvsRecordById) }
            post("/bind-beneficiary-to-subcontract") { coHandler(backFillController::bindBeneficiaryToSubcontract) }
            post("/create-contract-subcontract") { coHandler(backFillController::createContractSubcontract) }
            post("/cancel-waiting-logs") { coHandler(backFillController::cancelWaitingLogs) }
            post("/totvs-price-list-sync") { coHandler(backFillController::backfillPriceRange) }

            route("/reprocess-queue") {
                post("/cancel-waiting-logs") { coHandler(backFillController::cancelWaitingLogs) }
                post("/cancel-waiting-logs/all") { coHandler(backFillController::cancelAllWaitingLogs) }
            }
        }
        
        
    }
}
