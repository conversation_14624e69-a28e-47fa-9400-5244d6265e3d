package br.com.alice.nullvs.webhooks

import MemberBeneficiary
import MemberPayload
import TotvsMemberWebhookResponse
import br.com.alice.authentication.Authenticator
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.nullvs.events.NullvsClientWebhookReceivedEvent
import br.com.alice.nullvs.events.NullvsCompanyContractWebhookReceivedEvent
import br.com.alice.nullvs.events.NullvsCompanySubcontractWebhookReceivedEvent
import br.com.alice.nullvs.events.NullvsInvoiceWebhookEvent
import br.com.alice.nullvs.events.NullvsMemberWebhookEvent
import br.com.alice.nullvs.events.NullvsPaymentWebhookReceivedEvent
import br.com.alice.nullvs.events.NullvsPriceListingWebhookReceivedEvent
import br.com.alice.nullvs.models.NullvsMemberWebhookReceived
import br.com.alice.nullvs.models.TotvsStatus
import io.ktor.client.statement.bodyAsText
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TotvsWebhookReceiverTest : BaseRoutesTestHelper() {

    private val kafkaService: KafkaProducerService = mockk()
    private val authorizationHeader = mapOf("Authorization" to "Basic YWxpY2U6YWxpY2U=")

    @BeforeTest
    override fun setup() {
        super.setup()

        // Mock Authenticator to avoid Firebase dependency in tests
        mockkObject(Authenticator)
        every { Authenticator.generateCustomToken(any(), any<String>()) } returns "mockToken"

        module.single { TotvsWebhookReceiver(kafkaService) }
    }

    @AfterTest
    fun tearDown() = clearAllMocks()

    @Test
    fun `#totvsMemberCreationReceiver should return 200 OK when payload were received and events are produced`() {
        val memberBeneficiary1 = MemberBeneficiary(
            aliceId = "db375c37-d4d7-432e-a09e-5599f3f83a01".toUUID(),
            totvsId = "00010003000001000"
        )
        val memberBeneficiary2 = MemberBeneficiary(
            aliceId = "c67c066a-740a-4c27-8c68-7c887ea75a01".toUUID(),
            totvsId = "00010003000001010"
        )
        val memberBeneficiary3 = MemberBeneficiary(
            aliceId = "8197b158-96a0-4de0-9e92-23a2fd73ff01".toUUID(),
            totvsId = "00010003000001030"
        )

        val memberPayload = MemberPayload(
            payloadId = 1,
            status = "F",
            description = "CONTRATO ENVIADO NAO LOCALIZADO",
        )
        val memberPayload2 = MemberPayload(
            payloadId = 2,
            status = "S",
            beneficiaries = listOf(
                memberBeneficiary1,
                memberBeneficiary2,
                memberBeneficiary3
            )
        )

        val totvsMemberWebhookResponse = TotvsMemberWebhookResponse(
            idSoc = "7",
            batch = "*********1",
            payload = listOf(
                memberPayload,
                memberPayload2
            ),
        )

        val nullvsMemberWebhookReceivedLast = NullvsMemberWebhookReceived(
            batchId = totvsMemberWebhookResponse.batch,
            idSoc = totvsMemberWebhookResponse.idSoc,
            payloadId = memberPayload2.payloadId,
            status = TotvsStatus.convertTotvsStatus(memberPayload2.status),
        )

        coEvery {
            kafkaService.produce(
                NullvsMemberWebhookEvent(
                    nullvsMemberWebhookReceived = NullvsMemberWebhookReceived(
                        batchId = totvsMemberWebhookResponse.batch,
                        idSoc = totvsMemberWebhookResponse.idSoc,
                        payloadId = memberPayload.payloadId,
                        status = TotvsStatus.convertTotvsStatus(memberPayload.status),
                        description = memberPayload.description
                    )
                )
            )
        } returns mockk()

        coEvery {
            kafkaService.produce(
                NullvsMemberWebhookEvent(
                    nullvsMemberWebhookReceived = nullvsMemberWebhookReceivedLast.copy(
                        internalId = memberBeneficiary1.aliceId,
                        externalId = memberBeneficiary1.totvsId
                    )
                )
            )
        } returns mockk()

        coEvery {
            kafkaService.produce(
                NullvsMemberWebhookEvent(
                    nullvsMemberWebhookReceived = nullvsMemberWebhookReceivedLast.copy(
                        internalId = memberBeneficiary2.aliceId,
                        externalId = memberBeneficiary2.totvsId
                    )
                )
            )
        } returns mockk()

        coEvery {
            kafkaService.produce(
                NullvsMemberWebhookEvent(
                    nullvsMemberWebhookReceived = nullvsMemberWebhookReceivedLast.copy(
                        internalId = memberBeneficiary3.aliceId,
                        externalId = memberBeneficiary3.totvsId
                    )
                )
            )
        } returns mockk()

        post("/webhook/member/insert", body = totvsMemberWebhookResponse, headers = authorizationHeader) {
            ResponseAssert.assertThat(it).isOK()
            Assertions.assertThat(it.bodyAsText()).isEqualTo("[accepted]")
        }

        coVerify(exactly = 4) { kafkaService.produce(any()) }
    }

    @Test
    fun `#totvsMemberCreationReceiver should return 200 OK when payload were received and events are produced to delete`() {
        val payloadTotvsReceived =
            """{
                "idlote": "9614f753-1a36-4f1c-be10-903ce5dc5300",
                "lote": "0000056253",
                "payload": [
                    {
                        "idpayloadseq": 1,
                        "status": "F",
                        "descstatus": "A0042 - CNPJ não coincide com o Grupo empresa enviado"
                    }
                ]
            }"""

        coEvery { kafkaService.produce(any()) } returns mockk()

        post("/webhook/member/delete", body = payloadTotvsReceived, headers = authorizationHeader) {
            ResponseAssert.assertThat(it).isOK()
            Assertions.assertThat(it.bodyAsText()).isEqualTo("[accepted]")
        }

        coVerify(exactly = 1) { kafkaService.produce(any()) }
    }

    @Test
    fun `#totvsMemberCreationReceiver should return 200 OK when payload were received and events are produced to update`() {
        val payloadTotvsReceived =
            """{
                "idlote": "19700944-d96f-426f-bccd-597f23eb2100",
                "lote": "0000056230",
                "payload": [
                    {
                        "idpayloadseq": 1,
                        "status": "S",
                        "beneficiarios": []
                    }
                ]
            }"""

        coEvery { kafkaService.produce(any()) } returns mockk()

        post("/webhook/member/delete", body = payloadTotvsReceived, headers = authorizationHeader) {
            ResponseAssert.assertThat(it).isOK()
            Assertions.assertThat(it.bodyAsText()).isEqualTo("[accepted]")
        }

        coVerify { kafkaService.produce(any()) }
    }

    @Test
    fun `#totvsClientCreationReceiver should return 200 OK when payload were received and events are produced`() {
        val payloadTotvsReceived =
            """{
                "idlote": "1015",   
                "pospayloadlote": 1,
                "tipo": "inclusao",  
                "entidade": "cliente", 
                "lote": "1234",
                "datestamp": "2017-06-29T20:23:47",  
                "payload": [
                    {
                        "A1_XIDRESP": "a1e33ef1-d767-4fee-846c-ccf3e035ac9d",
                        "A1_COD": "000001",
                        "A1_LOJA": "00",
                        "A1_CGC": "69909091000100",
                        "A1_NOME": "PRONTO ANALISES SAO MARCOS LTDA."
                  },
                  {
                        "A1_XIDRESP": "B1e33ef1-d767-4fee-846c-ccf3e035ac9F",
                        "A1_COD": "000002",
                        "A1_LOJA": "00",
                        "A1_CGC": "69909091000101",
                        "A1_NOME": "PRONTO ANALISES SAO MARCOS LTDA."
                  }
                ]
            }"""

        coEvery { kafkaService.produce(any()) } returns mockk()

        post("/webhook/client/insert", body = payloadTotvsReceived, headers = authorizationHeader) {
            ResponseAssert.assertThat(it).isOK()
            Assertions.assertThat(it.bodyAsText()).isEqualTo("[accepted]")
        }

        coVerify(exactly = 2) { kafkaService.produce(any()) }
    }

    @Test
    fun `#totvsClientCreationReceiver should return 200 OK when the payload with an error message were received and events are produced`() {
        val payloadTotvsReceived =
            """{
                "idlote": "7af8a438-af3f-4b31-8a4b-94c63f72a600",
                "tipo": "inclusao",
                "entidade": "cliente",
                "lote": "1000000510",
                "datestamp": "2023-05-20T12:58:15",
                "pospayloadlote": 1,
                "payload": [
                    {
                        "errorCode": 400,
                        "errorMessage": "A propriedade [A1_BAIRRO] não foi informada."
                    }
                ]
            }"""

        coEvery { kafkaService.produce(any()) } returns mockk()

        post("/webhook/client/insert", body = payloadTotvsReceived, headers = authorizationHeader) {
            ResponseAssert.assertThat(it).isOK()
            Assertions.assertThat(it.bodyAsText()).isEqualTo("[accepted]")
        }

        coVerifyOnce { kafkaService.produce(any()) }
    }

    @Test
    fun `#totvsClientUpdateReceiver should return 200 OK when payload were received and events are produced`() {
        val payloadTotvsReceived =
            """{
                "idlote": "1015",  
                "pospayloadlote": 1,
                "tipo": "atualizacao",  
                "entidade": "cliente", 
                "lote": "1234",
                "datestamp": "2017-06-29T20:23:47",  
                "payload": [
                    {
                        "A1_XIDRESP": "a1e33ef1-d767-4fee-846c-ccf3e035ac9d",
                        "A1_COD": "000001",
                        "A1_LOJA": "00",
                        "A1_CGC": "69909091000100",
                        "A1_NOME": "PRONTO ANALISES SAO MARCOS LTDA."
                  },
                  {
                        "A1_XIDRESP": "B1e33ef1-d767-4fee-846c-ccf3e035ac9F",
                        "A1_COD": "000002",
                        "A1_LOJA": "00",
                        "A1_CGC": "69909091000101",
                        "A1_NOME": "PRONTO ANALISES SAO MARCOS LTDA."
                  }
                ]
            }"""

        coEvery { kafkaService.produce(any()) } returns mockk()

        post("/webhook/client/insert", body = payloadTotvsReceived, headers = authorizationHeader) {
            ResponseAssert.assertThat(it).isOK()
            Assertions.assertThat(it.bodyAsText()).isEqualTo("[accepted]")
        }

        coVerify(exactly = 2) { kafkaService.produce(match { it.name == NullvsClientWebhookReceivedEvent.name }) }
    }

    @Test
    fun `#totvsInvoiceCreationReceiver should return 200 OK when payload were received and events are produced`() {
        val payloadTotvsReceived =
            """{
                "idlote": "1015234",
                "lote": "00000087",
                "pospayloadlote": 1,
                "tipo": "inclusao",  
                "entidade": "invoice", 
                "datestamp": "2017-06-29T20:23:47",
                "payload": [
                    {
                        "E1_PREFIXO": "prefixo",
                        "E1_NUM": "000001",
                        "E1_PARCELA": "01",
                        "E1_TIPO": "tipo",
                        "E1_EMISSAO": "emissao",
                        "E1_VENCTO": "2017-06-29T20:23:47",
                        "E1_VALOR": 123.01,
                        "E1_HIST": "cobranca",
                        "E1_CLIENTE": "cliente",
                        "E1_LOJA": "001",
                        "E1_NOMCLI": "USUARIO",
                        "E1_SITUACA": "0",
                        "E1_DESCONT": 0.0,
                        "E1_MULTA": 0.0,
                        "E1_JUROS": 0.0,
                        "E1_CORREC": 0.0,
                        "E1_NATUREZ": "natureza",
                        "E1_XIDTIT": "720d3f1d-a7f0-4d8b-9c85-a10926443e00       "
                  },
                  {
                        "E1_PREFIXO": "prefixo",
                        "E1_NUM": "000002",
                        "E1_PARCELA": "02",
                        "E1_TIPO": "tipo",
                        "E1_EMISSAO": "emissao",
                        "E1_VENCTO": "2017-06-29T20:23:47",
                        "E1_VALOR": 123.01,
                        "E1_HIST": "cobranca",
                        "E1_CLIENTE": "cliente",
                        "E1_LOJA": "001",
                        "E1_NOMCLI": "USUARIO",
                        "E1_SITUACA": "0",
                        "E1_DESCONT": 0.0,
                        "E1_MULTA": 0.0,
                        "E1_JUROS": 0.0,
                        "E1_CORREC": 0.0,
                        "E1_NATUREZ": "natureza",
                        "E1_XIDTIT": "720d3f1d-a7f0-4d8b-9c85-a10926443e00      "
                  }
                ]
            }"""

        coEvery { kafkaService.produce(any()) } returns mockk()

        post("/webhook/invoice/insert", body = payloadTotvsReceived, headers = authorizationHeader) {
            ResponseAssert.assertThat(it).isOK()
            Assertions.assertThat(it.bodyAsText()).isEqualTo("[accepted]")
        }

        coVerify(exactly = 2) { kafkaService.produce(match { it.name == NullvsInvoiceWebhookEvent.name }) }
    }

    @Test
    fun `#totvsContractCreationReceiver should return 200 OK when payload were received and events are produced`() {
        val payloadTotvsReceived =
            """{
                "idlote": "1015234",
                "lote": "00000087",
                "tipo": "inclusao",  
                "entidade": "contract",
                "payload": [
                    {
                        "BT5_XIDALI": "dc79e6cb-3c82-4efb-9d60-54ede05cfc24",
                        "BT5_CODIGO": "123",
                        "BT5_NUMCON": "1234",
                        "BT5_VERSAO": "1"
                  },
                  {
                        "BT5_XIDALI": "d7d49190-b966-44f1-b3e8-557df0dd0db1",
                        "BT5_CODIGO": "123",
                        "BT5_NUMCON": "12345",
                        "BT5_VERSAO": "1"
                  }
                ]
            }"""

        coEvery { kafkaService.produce(any()) } returns mockk()

        post("/webhook/contract/insert", body = payloadTotvsReceived, headers = authorizationHeader) {
            ResponseAssert.assertThat(it).isOK()
            Assertions.assertThat(it.bodyAsText()).isEqualTo("[accepted]")
        }

        coVerify(exactly = 2) { kafkaService.produce(match { it.name == NullvsCompanyContractWebhookReceivedEvent.name }) }
    }

    @Test
    fun `#totvsPaymentUpdateReceiver should return 200 OK when payload were received and events are produced`() {
        val payloadTotvsReceived =
            """{
                "idlote": "1015234",
                "lote": "00000087",
                "pospayloadlote": 1,   
                "tipo": "update",  
                "entidade": "baixa-receber", 
                "datestamp": "2017-06-29T20:23:47",
                "payload": [
                    {
                        "E5_PREFIXO": "prefixo",
                        "E5_NUMERO": "000001",
                        "E5_PARCELA": "01",
                        "E5_TIPO": "tipo",
                        "E5_CLIENTE": "cliente",
                        "E5_LOJA": "001",
                        "E5_DATA": "25042023",
                        "E5_VALOR": 123.01,
                        "E5_BANCO":"104",
                        "E5_AGENCIA":"4355",
                        "E5_CONTA":"0002996-0",
                        "E5_HISTOR": "BAIXA REF FATURA NR 123 MÊS MARCO",
                        "E5_MOTBX": "NOR",
                        "E5_VLDESCO": 0.0,
                        "E5_VLMULTA": 0.0,
                        "E5_VLJUROS": 0.0
                  }
                ]
            }"""

        coEvery { kafkaService.produce(any()) } returns mockk()

        post("/webhook/payment/update", body = payloadTotvsReceived, headers = authorizationHeader) {
            ResponseAssert.assertThat(it).isOK()
            Assertions.assertThat(it.bodyAsText()).isEqualTo("[accepted]")
        }

        coVerifyOnce { kafkaService.produce(match { it.name == NullvsPaymentWebhookReceivedEvent.name }) }
    }

    @Test
    fun `#totvsPaymentUpdateReceiver should return 200 OK when payload were received and events are produced with error`() {
        val payloadTotvsReceived =
            """{
                "idlote": "1015234",
                "lote": "00000087",
                "pospayloadlote": 1,   
                "tipo": "update",  
                "entidade": "baixa-receber", 
                "datestamp": "2017-06-29T20:23:47",
                "payload": [
                    {
                        "errorCode": 400,
                        "errorMessage": "AJUDA:TITBAIXADO\r\nO título selecionado já está baixado ou não existem titulos a baixar neste momento.\r\n\r\n"
                    }
                ]
            }"""

        coEvery { kafkaService.produce(any()) } returns mockk()

        post("/webhook/payment/update", body = payloadTotvsReceived, headers = authorizationHeader) {
            ResponseAssert.assertThat(it).isOK()
            Assertions.assertThat(it.bodyAsText()).isEqualTo("[accepted]")
        }

        coVerifyOnce {
            kafkaService.produce(match {
                it.name == NullvsPaymentWebhookReceivedEvent.name &&
                        (it.payload as NullvsPaymentWebhookReceivedEvent.Payload).webhook.payload.errorCode == 400
            })
        }
    }

    @Test
    fun `#totvsSubcontractCreationReceiver should return 200 OK when payload were received and events are produced`() {
        val payloadTotvsReceived =
            """{"idlote":"abcafa7c-f51e-4add-b892-7ab8a7e7b900","lote":"1000014183","payload":[{"BQC_CODIGO":"00010001","BQC_NUMCON":"300000000435","BQC_VERCON":"001","BQC_CODINT":"0001","BQC_CODEMP":"0001","BQC_SUBCON":"*********","BQC_VERSUB":"001","BQC_DATCON":"11102023","BQC_DESCRI":"Company","BQC_NREDUZ":"Company","BQC_COBNIV":"0","BQC_CNPJ":"60841807000189","BQC_PODREM":"1","BQC_TPVCPP":"1","BQC_VENCCO":1,"BQC_TPVCCO":"1","BQC_ALTVEN":"1","BQC_GRATUI":"0","BQC_COBRET":"1","BQC_COBRAT":"0","BQC_CONCON":"0","BQC_PATROC":"0","BQC_MESREA":"10","BQC_PERREJ":"12","BQC_ENDCOB":"1","BQC_INFANS":"1","BQC_EMICAR":"1","BQC_PERCOM":"1","BQC_ABRQUE":"0","BQC_RPGPAT":"1","BQC_OUTLAN":"0","BQC_REGFIN":"1","BQC_REGGOP":"0","BQC_COBJUR":"0","BQC_GUIPOS":"1","BQC_MOTREA":"REAJUSTE CONFORME CLAUSULA CONTRATUAL","BQC_CARREA":"4","BQC_REALIN":"1","BQC_CODPAT":"000001","BQC_DIASIN":50,"BQC_XIDALI":"720d3f1d-a7f0-4d8b-9c85-a10926443e00","BQC_XCLINC":"1000014183"}]}"""

        coEvery { kafkaService.produce(any()) } returns mockk()

        post("/webhook/subcontract/insert", body = payloadTotvsReceived, headers = authorizationHeader) {
            ResponseAssert.assertThat(it).isOK()
            Assertions.assertThat(it.bodyAsText()).isEqualTo("[accepted]")
        }

        coVerify(exactly = 1) { kafkaService.produce(match { it.name == NullvsCompanySubcontractWebhookReceivedEvent.name }) }
    }

    @ParameterizedTest
    @ValueSource(strings = ["member/insert", "client/insert", "subcontract/insert", "contract/insert", "invoice/insert", "payment/update"])
    fun `#should return 401 Unauthorized when Authorization header is not found`(path: String) {
        val payloadTotvsReceived = ""
        post(
            "/webhook/$path",
            body = payloadTotvsReceived,
            headers = mapOf()
        ) {
            ResponseAssert.assertThat(it).isUnauthorized()
        }
    }

    @ParameterizedTest
    @ValueSource(strings = ["member/insert", "client/insert", "subcontract/insert", "contract/insert", "invoice/insert", "payment/update", "price/insert"])
    fun `#should return 401 Unauthorized when Authorization is invalid`(path: String) {
        val payloadTotvsReceived = ""
        post(
            "/webhook/$path",
            body = payloadTotvsReceived,
            headers = mapOf("Authorization" to "Basic aW52YWxpZG86aW52YWxpZG8=")
        ) {
            ResponseAssert.assertThat(it).isUnauthorized()
        }
    }

    @Test
    fun `#totvsPriceListingCreationReceiver should return 200 OK when subcontract insert`() {
        val payloadTotvsReceived =
            """{
                   "total":1,
                   "tipo":"cadastro",
                   "entidade":"faixas_subcontrato",
                   "data":"17052023",
                   "usuario":"teste",
                   "acao":"insert",
                   "payload":[
                      {
                         "REGANS":"12345",
                         "VIGINI":"20241001",
                         "BTN_PERREJ":"",
                         "BTN_SUBCON" : "",
                         "BTN_NUMCON" : "",
                         "IDTOTVS" : "123",
                         "Faixas": [
                            {
                               "BTN_IDAINI":0,
                               "BTN_IDAFIN":19,
                               "BTN_VALFAI":123.45,
                               "BTN_REJAPL":0,
                               "BTN_VLRANT":0
                            }
                         ]
                      }
                   ]
                }""".trimIndent()

        coEvery { kafkaService.produce(any()) } returns mockk()

        post("/webhook/price/insert", body = payloadTotvsReceived, headers = authorizationHeader) {
            ResponseAssert.assertThat(it).isOK()
            Assertions.assertThat(it.bodyAsText()).isEqualTo("[accepted]")
        }

        coVerifyOnce { kafkaService.produce(match { it.name == NullvsPriceListingWebhookReceivedEvent.name }) }
    }

    @Test
    fun `#totvsPriceListingCreationReceiver should return 200 OK when family update`() {
        val payloadTotvsReceived =
            """{
                   "total":1,
                   "tipo":"cadastro",
                   "entidade":"faixas_subcontrato",
                   "data":"17052023",
                   "usuario":"teste",
                   "acao":"insert",
                   "payload":[
                      {
                         "REGANS":"12345",
                         "VIGINI":"20241001",
                         "BTN_PERREJ":"",
                         "MATRICULAFAMILIA" : "",
                         "IDTOTVS" : "123",
                         "Faixas": [
                            {
                               "BTN_IDAINI":0,
                               "BTN_IDAFIN":19,
                               "BTN_VALFAI":123.45,
                               "BTN_REJAPL":0,
                               "BTN_VLRANT":0
                            }
                         ]
                      }
                   ]
                }""".trimIndent()

        coEvery { kafkaService.produce(any()) } returns mockk()

        post("/webhook/price/update", body = payloadTotvsReceived, headers = authorizationHeader) {
            ResponseAssert.assertThat(it).isOK()
            Assertions.assertThat(it.bodyAsText()).isEqualTo("[accepted]")
        }

        coVerifyOnce { kafkaService.produce(match { it.name == NullvsPriceListingWebhookReceivedEvent.name }) }
    }

    @Test
    fun `#totvsPriceListingCreationReceiver should return 200 OK when beneficiary update`() {
        val payloadTotvsReceived =
            """{
                   "total":1,
                   "tipo":"cadastro",
                   "entidade":"faixas_beneficiarios",
                   "data":"17052023",
                   "usuario":"teste",
                   "acao":"update",
                   "payload":[
                      {
                         "REGANS":"12345",
                         "BTN_PERREJ":"",
                         "MATRICULAFAMILIA" : "123",
                         "VIGINI":"20241001",
                         "IDTOTVS" : "123",
                         "Faixas": [
                            {
                               "BTN_IDAINI":0,
                               "BTN_IDAFIN":19,
                               "BTN_VALFAI":123.45,
                               "BTN_REJAPL":0,
                               "BTN_VLRANT":0
                            }
                         ]
                      }
                   ]
                }""".trimIndent()

        coEvery { kafkaService.produce(any()) } returns mockk()

        post("/webhook/price/update", body = payloadTotvsReceived, headers = authorizationHeader) {
            ResponseAssert.assertThat(it).isOK()
            Assertions.assertThat(it.bodyAsText()).isEqualTo("[accepted]")
        }

        coVerifyOnce { kafkaService.produce(match { it.name == NullvsPriceListingWebhookReceivedEvent.name }) }
    }
}
