package br.com.alice.person.services.internal

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.notification.NotificationEventAction
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.services.MemberModelDataService
import br.com.alice.person.events.MemberChangedEvent
import br.com.alice.person.converters.toModel
import br.com.alice.person.converters.toTransport
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class MemberInternalServiceTest : MockedTestHelper() {

    private val memberDataService: MemberModelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val memberInternalService = MemberInternalService(memberDataService, kafkaProducerService)

    private val memberId = RangeUUID.generate()
    private val personId = PersonId()
    private val companyId = RangeUUID.generate()
    private val memberModel = TestModelFactory.buildMember(
        id = memberId,
        personId = personId,
        status = MemberStatus.ACTIVE
    ).toModel()

    @Test
    fun `#get should return member when found`() = runBlocking {
        coEvery { memberDataService.get(memberId) } returns memberModel.success()

        val result = memberInternalService.get(memberId)

        assertThat(result).isSuccessWithData(memberModel)
        coVerifyOnce { memberDataService.get(memberId) }
    }

    @Test
    fun `#get should return failure when member not found`() = runBlocking {
        val notFoundError = NotFoundException("Member not found")
        coEvery { memberDataService.get(memberId) } returns notFoundError.failure()

        val result = memberInternalService.get(memberId)

        assertThat(result).isFailureOfType(NotFoundException::class)
        coVerifyOnce { memberDataService.get(memberId) }
    }

    @Test
    fun `#findByFiltersWithoutPagination should return members with filter and range`() = runBlocking {
        val filter = MemberModelDataService.Filter(
            personIds = listOf(personId),
            statuses = listOf(MemberStatus.ACTIVE)
        )
        val range = 0..10
        val expectedMembers = listOf(memberModel)

        coEvery { 
            memberDataService.findByFiltersWithoutPagination(filter, range) 
        } returns expectedMembers.success()

        val result = memberInternalService.findByFiltersWithoutPagination(filter, range)

        assertThat(result).isSuccessWithData(expectedMembers)
        coVerifyOnce { memberDataService.findByFiltersWithoutPagination(filter, range) }
    }

    @Test
    fun `#findByFiltersWithoutPagination should return members with filter and no range`() = runBlocking {
        val filter = MemberModelDataService.Filter(
            personIds = listOf(personId),
            statuses = listOf(MemberStatus.ACTIVE)
        )
        val expectedMembers = listOf(memberModel)

        coEvery { 
            memberDataService.findByFiltersWithoutPagination(filter, null) 
        } returns expectedMembers.success()

        val result = memberInternalService.findByFiltersWithoutPagination(filter)

        assertThat(result).isSuccessWithData(expectedMembers)
        coVerifyOnce { memberDataService.findByFiltersWithoutPagination(filter, null) }
    }

    @Test
    fun `#findByFilters should return members with pagination`() = runBlocking {
        val filter = MemberModelDataService.Filter(
            personIds = listOf(personId),
            statuses = listOf(MemberStatus.ACTIVE)
        )
        val range = 0..10
        val expectedMembers = listOf(memberModel)

        coEvery { 
            memberDataService.findByFilters(filter, range) 
        } returns expectedMembers.success()

        val result = memberInternalService.findByFilters(filter, range)

        assertThat(result).isSuccessWithData(expectedMembers)
        coVerifyOnce { memberDataService.findByFilters(filter, range) }
    }

    @Test
    fun `#countByFilters should return count for given filter`() = runBlocking {
        val filter = MemberModelDataService.Filter(
            personIds = listOf(personId),
            statuses = listOf(MemberStatus.ACTIVE)
        )
        val expectedCount = 5

        coEvery { 
            memberDataService.countByFilters(filter) 
        } returns expectedCount.success()

        val result = memberInternalService.countByFilters(filter)

        assertThat(result).isSuccessWithData(expectedCount)
        coVerifyOnce { memberDataService.countByFilters(filter) }
    }

    @Test
    fun `#countByCompanyIdAndStatus should return count with all parameters`() = runBlocking {
        val status = MemberStatus.ACTIVE
        val type = BeneficiaryType.EMPLOYEE
        val withNoPendingCancellation = true
        val expectedCount = 3

        coEvery { 
            memberDataService.countByCompanyIdAndStatus(companyId, status, type, withNoPendingCancellation) 
        } returns expectedCount.success()

        val result = memberInternalService.countByCompanyIdAndStatus(
            companyId, status, type, withNoPendingCancellation
        )

        assertThat(result).isSuccessWithData(expectedCount)
        coVerifyOnce { 
            memberDataService.countByCompanyIdAndStatus(companyId, status, type, withNoPendingCancellation) 
        }
    }

    @Test
    fun `#countByCompanyIdAndStatus should return count with default parameters`() = runBlocking {
        val status = MemberStatus.ACTIVE
        val expectedCount = 7

        coEvery { 
            memberDataService.countByCompanyIdAndStatus(companyId, status, null, false) 
        } returns expectedCount.success()

        val result = memberInternalService.countByCompanyIdAndStatus(companyId, status)

        assertThat(result).isSuccessWithData(expectedCount)
        coVerifyOnce { 
            memberDataService.countByCompanyIdAndStatus(companyId, status, null, false) 
        }
    }

    @Test
    fun `#add should return added member and produce MemberChangedEvent with CREATED action`() = runBlocking {
        val expectedEvent = MemberChangedEvent(memberModel.toTransport(), NotificationEventAction.CREATED)
        val producerResult = ProducerResult(
            producedAt = LocalDateTime.now(),
            topic = "member-changed",
            offset = 1L
        )

        coEvery { memberDataService.add(memberModel) } returns memberModel.success()
        coEvery { kafkaProducerService.produce(expectedEvent, memberModel.id.toString()) } returns producerResult

        val result = memberInternalService.add(memberModel)

        assertThat(result).isSuccessWithData(memberModel)
        coVerifyOnce { memberDataService.add(memberModel) }
        coVerifyOnce { kafkaProducerService.produce(expectedEvent, memberModel.id.toString()) }
    }

    @Test
    fun `#add should return failure when add fails and not produce event`() = runBlocking {
        val addError = RuntimeException("Failed to add member")
        coEvery { memberDataService.add(memberModel) } returns addError.failure()

        val result = memberInternalService.add(memberModel)

        assertThat(result).isFailureOfType(RuntimeException::class)
        coVerifyOnce { memberDataService.add(memberModel) }
        coVerifyNone { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#update should return updated member and produce MemberChangedEvent with UPDATED action`() = runBlocking {
        val expectedEvent = MemberChangedEvent(memberModel.toTransport(), NotificationEventAction.UPDATED)
        val producerResult = ProducerResult(
            producedAt = LocalDateTime.now(),
            topic = "member-changed",
            offset = 1L
        )

        coEvery { memberDataService.update(memberModel) } returns memberModel.success()
        coEvery { kafkaProducerService.produce(expectedEvent, memberModel.id.toString()) } returns producerResult

        val result = memberInternalService.update(memberModel)

        assertThat(result).isSuccessWithData(memberModel)
        coVerifyOnce { memberDataService.update(memberModel) }
        coVerifyOnce { kafkaProducerService.produce(expectedEvent, memberModel.id.toString()) }
    }

    @Test
    fun `#update should return failure when update fails and not produce event`() = runBlocking {
        val updateError = RuntimeException("Failed to update member")
        coEvery { memberDataService.update(memberModel) } returns updateError.failure()

        val result = memberInternalService.update(memberModel)

        assertThat(result).isFailureOfType(RuntimeException::class)
        coVerifyOnce { memberDataService.update(memberModel) }
        coVerifyNone { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#findByFiltersWithoutPagination should return failure when data service fails`() = runBlocking {
        val filter = MemberModelDataService.Filter(personIds = listOf(personId))
        val serviceError = RuntimeException("Database error")

        coEvery {
            memberDataService.findByFiltersWithoutPagination(filter, null)
        } returns serviceError.failure()

        val result = memberInternalService.findByFiltersWithoutPagination(filter)

        assertThat(result).isFailureOfType(RuntimeException::class)
        coVerifyOnce { memberDataService.findByFiltersWithoutPagination(filter, null) }
    }

    @Test
    fun `#findByFilters should return failure when data service fails`() = runBlocking {
        val filter = MemberModelDataService.Filter(personIds = listOf(personId))
        val range = 0..10
        val serviceError = RuntimeException("Database error")

        coEvery {
            memberDataService.findByFilters(filter, range)
        } returns serviceError.failure()

        val result = memberInternalService.findByFilters(filter, range)

        assertThat(result).isFailureOfType(RuntimeException::class)
        coVerifyOnce { memberDataService.findByFilters(filter, range) }
    }

    @Test
    fun `#countByFilters should return failure when data service fails`() = runBlocking {
        val filter = MemberModelDataService.Filter(personIds = listOf(personId))
        val serviceError = RuntimeException("Database error")

        coEvery {
            memberDataService.countByFilters(filter)
        } returns serviceError.failure()

        val result = memberInternalService.countByFilters(filter)

        assertThat(result).isFailureOfType(RuntimeException::class)
        coVerifyOnce { memberDataService.countByFilters(filter) }
    }

    @Test
    fun `#countByCompanyIdAndStatus should return failure when data service fails`() = runBlocking {
        val status = MemberStatus.ACTIVE
        val serviceError = RuntimeException("Database error")

        coEvery {
            memberDataService.countByCompanyIdAndStatus(companyId, status, null, false)
        } returns serviceError.failure()

        val result = memberInternalService.countByCompanyIdAndStatus(companyId, status)

        assertThat(result).isFailureOfType(RuntimeException::class)
        coVerifyOnce {
            memberDataService.countByCompanyIdAndStatus(companyId, status, null, false)
        }
    }
}
