package br.com.alice.person.services

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CassiMemberInfo
import br.com.alice.business.client.CassiMemberService
import br.com.alice.business.client.MemberContractService
import br.com.alice.business.exceptions.CassiMemberNotCreatedException
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDate
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Contract
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberBeneficiary
import br.com.alice.data.layer.models.MemberLifecycleEventType
import br.com.alice.data.layer.models.MemberLifecycleReasonEvents
import br.com.alice.data.layer.models.MemberModel
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.MemberStatus.ACTIVE
import br.com.alice.data.layer.models.MemberStatus.CANCELED
import br.com.alice.data.layer.models.MemberStatus.PENDING
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.data.layer.models.ProductType
import br.com.alice.data.layer.models.UserSignature
import br.com.alice.data.layer.models.withCassiMember
import br.com.alice.data.layer.models.withMemberProductPrice
import br.com.alice.data.layer.services.MemberLightweightModelDataService
import br.com.alice.data.layer.services.MemberModelDataService.Filter
import br.com.alice.data.layer.services.PersonModelDataService
import br.com.alice.membership.client.ContractRegistry
import br.com.alice.membership.client.MemberLifeCycleEventsService
import br.com.alice.membership.client.onboarding.MemberNotFoundException
import br.com.alice.person.br.com.alice.person.exception.MemberActivationDateIsEarlierThanContractStartedDateException
import br.com.alice.person.br.com.alice.person.exception.MemberCanceledActivationException
import br.com.alice.person.br.com.alice.person.exception.SelfDependentException
import br.com.alice.person.br.com.alice.person.model.MemberCurrentAndPrevious
import br.com.alice.person.br.com.alice.person.model.PersonWithReferenceDate
import br.com.alice.person.br.com.alice.person.model.events.MemberReactivatedEvent
import br.com.alice.person.client.ActiveMembershipNotFoundException
import br.com.alice.person.client.MemberProductPriceService
import br.com.alice.person.client.MemberService.FindOptions
import br.com.alice.person.client.MembershipAlreadyActiveMismatchException
import br.com.alice.person.client.MembershipMustBeAptToBeReactivatedException
import br.com.alice.person.client.PendingMembershipMismatchException
import br.com.alice.person.clients.B2CHolderIsNotAllowedException
import br.com.alice.person.converters.toModel
import br.com.alice.person.model.MemberWithProduct
import br.com.alice.person.model.MemberWithProductWithProviders
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.person.model.events.MemberCancelledEvent
import br.com.alice.person.model.events.MemberCreatedEvent
import br.com.alice.person.model.events.MemberUpdatedEvent
import br.com.alice.person.model.events.ProductChangedEvent
import br.com.alice.person.services.internal.MemberInternalService
import br.com.alice.product.client.ProductService
import br.com.alice.product.model.ProductWithProviders
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import io.mockk.slot
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class MemberServiceImplTest : MockedTestHelper() {

    private val memberInternalService: MemberInternalService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val memberProductPriceService: MemberProductPriceService = mockk()
    private val productService: ProductService = mockk()
    private val memberLightweightDataService: MemberLightweightModelDataService = mockk()
    private val cassiMemberService: CassiMemberService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val personDataService: PersonModelDataService = mockk()
    private val memberLifeCycleEventsService: MemberLifeCycleEventsService = mockk()
    private val contractRegistry: ContractRegistry = mockk()
    private val memberContractService: MemberContractService = mockk()

    private val person = TestModelFactory.buildPerson()
    private val personModel = person.toModel()
    private val contract = TestModelFactory.buildOnboardingContract(person.id)

    private val memberService = MemberServiceImpl(
        memberInternalService,
        personDataService,
        productService,
        kafkaProducerService,
        memberProductPriceService,
        memberLightweightDataService,
        cassiMemberService,
        beneficiaryService,
        memberLifeCycleEventsService,
        contractRegistry,
        memberContractService,
    )

    private val member = TestModelFactory.buildMember(
        personId = person.id,
        status = ACTIVE,
        activationDate = LocalDateTime.now()
    )
    private val memberModel = member.toModel()

    private val pendingMembership = TestModelFactory.buildMember(
        personId = person.id,
        status = PENDING,
        activationDate = null
    )
    private val pendingMembershipModel = pendingMembership.toModel()

    private val product = TestModelFactory.buildProduct()
    private val provider = TestModelFactory.buildProvider()

    private val productWithProviders = ProductWithProviders(
        product,
        listOf(provider)
    )

    private val memberWithProductWithBundlesWithProviders = MemberWithProductWithProviders(
        member,
        productWithProviders
    )

    @Test
    fun `#get should return expected Member`() = runBlocking {
        val member = TestModelFactory.buildMember()
        val memberModel = member.toModel()
        coEvery { memberInternalService.get(member.id) } returns memberModel

        val result = memberService.get(member.id)
        assertThat(result).isSuccessWithData(member)
    }

    @Test
    fun `#findByIds should return expected Members`() = runBlocking {
        val member1 = TestModelFactory.buildMember()
        val member2 = TestModelFactory.buildMember()
        val member3 = TestModelFactory.buildMember()
        val memberIds = listOf(member1.id, member2.id, member3.id)
        val members = listOf(member1, member2, member3)
        val memberModels = listOf(member1.toModel(), member2.toModel(), member3.toModel())

        coEvery { memberInternalService.findByFiltersWithoutPagination(Filter(ids = memberIds)) } returns memberModels

        val result = memberService.findByIds(memberIds)
        assertThat(result).isSuccessWithData(members)

        coVerifyOnce { memberInternalService.findByFiltersWithoutPagination(Filter(ids = memberIds)) }
    }

    @Test
    fun `#get should return expected Member when has CassiMember`() = runBlocking {
        val member = TestModelFactory.buildMember()
        val memberModel = member.toModel()
        val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)

        coEvery { memberInternalService.get(member.id) } returns memberModel
        coEvery { cassiMemberService.getByMemberId(member.id) } returns cassiMember

        val result = memberService.get(member.id, findOptions = FindOptions(withCassiMember = true))

        val expectedResult = member.withCassiMember(cassiMember)
        assertThat(result).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#get should return expected Member when has no CassiMember`() = runBlocking {
        val member = TestModelFactory.buildMember()
        val memberModel = member.toModel()

        coEvery { memberInternalService.get(member.id) } returns memberModel
        coEvery { cassiMemberService.getByMemberId(member.id) } returns NotFoundException("CassiMember not found").failure()

        val result = memberService.get(member.id, findOptions = FindOptions(withCassiMember = true))
        assertThat(result).isSuccessWithData(member)
    }

    @Nested
    inner class HasContractSigned {
        @Test
        fun `#should return true when the member is already active`() = runBlocking {
            val personId = person.id

            val membersList = listOf(memberModel)

            coEvery {
                memberInternalService.findByFiltersWithoutPagination(
                    Filter(personIds = listOf(person.id))
                )
            } returns membersList


            val result = memberService.hasContractSigned(personId)
            assertThat(result).isSuccessWithData(true)
        }

        @Nested
        inner class B2C {
            @Test
            fun `#should return true when the registry of the contract is found and signed on the pending membership`() =
                runBlocking {
                    val personId = person.id

                    val membersList =
                        listOf(pendingMembershipModel.copy(selectedProduct = TestModelFactory.buildMemberProduct(type = ProductType.B2C)))
                    val signedContract = TestModelFactory.buildOnboardingContract(person.id).copy(
                        contractSignature = TestModelFactory.buildContractSignature(person),
                        ansOrientationLetterSignature = TestModelFactory.buildContractSignature(person),
                        healthDeclarationSignature = TestModelFactory.buildContractSignature(person)
                    )

                    coEvery {
                        memberInternalService.findByFiltersWithoutPagination(
                            Filter(personIds = listOf(person.id))
                        )
                    } returns membersList

                    coEvery { contractRegistry.findContract(personId) } returns signedContract

                    val result = memberService.hasContractSigned(personId)
                    assertThat(result).isSuccessWithData(true)

                    coVerify {
                        memberInternalService.findByFiltersWithoutPagination(
                            Filter(personIds = listOf(person.id))
                        )
                        contractRegistry.findContract(personId)
                    }

                    coVerifyNone {
                        memberContractService.findByMember(any())
                    }
                }

            @Test
            fun `#should return false when the registry of the contract is found and not signed on the pending membership`() =
                runBlocking {
                    val personId = person.id

                    val membersList =
                        listOf(pendingMembershipModel.copy(selectedProduct = TestModelFactory.buildMemberProduct(type = ProductType.B2C)))
                    val unsignedContract = TestModelFactory.buildOnboardingContract(person.id).copy(
                        contractSignature = null,
                        ansOrientationLetterSignature = null,
                        healthDeclarationSignature = null
                    )

                    coEvery {
                        memberInternalService.findByFiltersWithoutPagination(
                            Filter(personIds = listOf(person.id))
                        )
                    } returns membersList

                    coEvery { contractRegistry.findContract(personId) } returns unsignedContract

                    val result = memberService.hasContractSigned(personId)
                    assertThat(result).isSuccessWithData(false)

                    coVerify {
                        memberInternalService.findByFiltersWithoutPagination(
                            Filter(personIds = listOf(person.id))
                        )
                        contractRegistry.findContract(personId)
                    }

                    coVerifyNone {
                        memberContractService.findByMember(any())
                    }
                }

            @Test
            fun `#should return false when the registry of the contract is not found on the pending membership`() =
                runBlocking {
                    val personId = person.id

                    val membersList =
                        listOf(pendingMembershipModel.copy(selectedProduct = TestModelFactory.buildMemberProduct(type = ProductType.B2C)))

                    coEvery {
                        memberInternalService.findByFiltersWithoutPagination(
                            Filter(personIds = listOf(person.id))
                        )
                    } returns membersList

                    coEvery { contractRegistry.findContract(personId) } returns NotFoundException()

                    val result = memberService.hasContractSigned(personId)
                    assertThat(result).isSuccessWithData(false)

                    coVerify {
                        memberInternalService.findByFiltersWithoutPagination(
                            Filter(personIds = listOf(person.id))
                        )
                        contractRegistry.findContract(personId)
                    }

                    coVerifyNone {
                        memberContractService.findByMember(any())
                    }
                }
        }

        @Nested
        inner class B2B {
            @Test
            fun `#should return true when the registry of the contract is found and signed on the pending membership`() =
                runBlocking {
                    val personId = person.id

                    val signedMemberContract = TestModelFactory.buildMemberContract(
                        memberId = pendingMembership.id,
                        signature = UserSignature(
                            userAgent = "IOS",
                            ipAddress = "***********",
                            idToken = "idToken",
                            signedAt = LocalDateTime.now(),
                        )
                    )

                    val membersList =
                        listOf(pendingMembershipModel.copy(selectedProduct = TestModelFactory.buildMemberProduct(type = ProductType.B2B)))

                    coEvery {
                        memberInternalService.findByFiltersWithoutPagination(
                            Filter(personIds = listOf(person.id))
                        )
                    } returns membersList

                    coEvery { memberContractService.findByMember(pendingMembership.id) } returns signedMemberContract

                    val result = memberService.hasContractSigned(personId)
                    assertThat(result).isSuccessWithData(true)

                    coVerify {
                        memberInternalService.findByFiltersWithoutPagination(
                            Filter(personIds = listOf(person.id))
                        )
                        memberContractService.findByMember(pendingMembership.id)
                    }

                    coVerifyNone { contractRegistry.findContract(any()) }
                }

            @Test
            fun `#should return false when the registry of the contract is found and not signed on the pending membership`() =
                runBlocking {
                    val personId = person.id

                    val unsignedMemberContract = TestModelFactory.buildMemberContract(memberId = member.id)

                    val membersList =
                        listOf(pendingMembershipModel.copy(selectedProduct = TestModelFactory.buildMemberProduct(type = ProductType.B2B)))

                    coEvery {
                        memberInternalService.findByFiltersWithoutPagination(
                            Filter(personIds = listOf(person.id))
                        )
                    } returns membersList

                    coEvery { memberContractService.findByMember(pendingMembership.id) } returns unsignedMemberContract

                    val result = memberService.hasContractSigned(personId)
                    assertThat(result).isSuccessWithData(false)

                    coVerify {
                        memberInternalService.findByFiltersWithoutPagination(
                            Filter(personIds = listOf(person.id))
                        )
                        memberContractService.findByMember(pendingMembership.id)
                    }

                    coVerifyNone { contractRegistry.findContract(any()) }
                }

            @Test
            fun `#should return false when the registry of the contract is not found on the pending membership`() =
                runBlocking {
                    val personId = person.id

                    val membersList =
                        listOf(pendingMembershipModel.copy(selectedProduct = TestModelFactory.buildMemberProduct(type = ProductType.B2B)))

                    coEvery {
                        memberInternalService.findByFiltersWithoutPagination(
                            Filter(personIds = listOf(person.id))
                        )
                    } returns membersList

                    coEvery { memberContractService.findByMember(pendingMembership.id) } returns NotFoundException()

                    val result = memberService.hasContractSigned(personId)
                    assertThat(result).isSuccessWithData(false)

                    coVerify {
                        memberInternalService.findByFiltersWithoutPagination(
                            Filter(personIds = listOf(person.id))
                        )
                        memberContractService.findByMember(pendingMembership.id)
                    }

                    coVerifyNone { contractRegistry.findContract(any()) }
                }
        }
    }

    @Nested
    inner class Create {
        @Test
        fun `#should return expected exception when Person already contains a pending Member but for a different Product`() =
            runBlocking {
                val personId = person.id
                val pendingMember = TestModelFactory.buildMember(personId).toModel()
                val product = TestModelFactory.buildProduct(id = contract.productId)

                coEvery { productService.getProduct(product.id) } returns product
                coEvery {
                    memberInternalService.findByFiltersWithoutPagination(
                        Filter(
                            personIds = listOf(personId),
                            statuses = listOf(ACTIVE, PENDING)
                        )
                    )
                } returns listOf(
                    pendingMember
                )

                val result = memberService.create(person.id, contract)
                assertThat(result).isFailureOfType(PendingMembershipMismatchException::class)
                coVerifyNone { kafkaProducerService.produce(any(), any()) }
            }

        @Test
        fun `#should return expected exception when Person already contains an active Member`() = runBlocking {
            val personId = person.id
            val activeMember = TestModelFactory.buildMember(personId, status = ACTIVE).toModel()
            val product = TestModelFactory.buildProduct(id = contract.productId)

            coEvery { productService.getProduct(product.id) } returns product
            coEvery {
                memberInternalService.findByFiltersWithoutPagination(
                    Filter(
                        personIds = listOf(personId),
                        statuses = listOf(ACTIVE, PENDING)
                    )
                )
            } returns listOf(
                activeMember
            )

            val result = memberService.create(person.id, contract)
            assertThat(result).isFailureOfType(MembershipAlreadyActiveMismatchException::class)
            coVerifyNone { kafkaProducerService.produce(any(), any()) }
        }

        @Test
        fun `#should return success when Person already contains a Member for the same Product`() = runBlocking {
            val personId = person.id
            val pendingMember = TestModelFactory.buildMember(personId, selectedProduct = contract.selectedProduct)
            val pendingMemberModel = pendingMember.toModel()
            val product = TestModelFactory.buildProduct(id = contract.productId)

            coEvery { productService.getProduct(product.id) } returns product
            coEvery {
                memberInternalService.findByFiltersWithoutPagination(
                    Filter(
                        personIds = listOf(personId),
                        statuses = listOf(ACTIVE, PENDING)
                    )
                )
            } returns listOf(
                pendingMemberModel
            )

            val result = memberService.create(person.id, contract)
            assertThat(result).isSuccessWithData(pendingMember)
            coVerify { kafkaProducerService wasNot called }
        }

        @Test
        fun `#should call add successfully when person doesn't have a member, without productPriceListingId`() =
            runBlocking {
                val personId = person.id
                val memberProductPrice = TestModelFactory.buildMemberProductPrice()

                val slot = slot<MemberModel>()
                val member = TestModelFactory.buildMember(personId)
                val memberModel = member.toModel()
                val product = TestModelFactory.buildProduct(id = contract.productId)

                coEvery { productService.getProduct(product.id) } returns product
                coEvery { cassiMemberService.createCassiMember(any()) } returns CassiMemberNotCreatedException(
                    member.id,
                    product.id
                )
                coEvery { memberInternalService.add(capture(slot)) } returns memberModel
                coEvery {
                    memberInternalService.findByFiltersWithoutPagination(
                        Filter(
                            personIds = listOf(personId),
                            statuses = listOf(ACTIVE, PENDING)
                        )
                    )
                } returns emptyList()
                coEvery { memberProductPriceService.setCurrent(any(), any(), any()) } returns memberProductPrice
                coEvery { memberInternalService.get(any()) } returns memberModel

                coEvery {
                    kafkaProducerService.produce(match { it.name == "MEMBER-CREATED" })
                } returns ProducerResult(
                    producedAt = LocalDateTime.now(),
                    topic = MemberCreatedEvent.name,
                    offset = 1L
                )

                val result = memberService.create(person.id, contract)
                val capturedMember = slot.captured

                val expectedMember = Member(
                    personId = personId,
                    selectedProduct = contract.selectedProduct,
                    status = PENDING,
                    contract = Contract(
                        documentUrl = contract.documentUrl
                    )
                )

                assertThat(result).isSuccess()
                assertThat(capturedMember).usingRecursiveComparison()
                    .ignoringFields("id", "createdAt", "migrationDate", "updatedAt", "statusHistory")
                    .isEqualTo(expectedMember)
                assertThat(capturedMember.statusHistory).hasSize(1)

                coVerifyOnce { kafkaProducerService.produce(match { it.name == "MEMBER-CREATED" }) }

                coVerifyNone { memberProductPriceService.setProductPriceListing(any(), any(), any()) }
                coVerifyOnce { memberProductPriceService.setCurrent(member.id, member.productId, any()) }
            }

        @Test
        fun `#should call add with CassiMember successfully when Person doesn't have a Member`() = runBlocking {
            val personId = person.id
            val memberProductPrice = TestModelFactory.buildMemberProductPrice()
            val member = TestModelFactory.buildMember(personId)
            val memberModel = member.toModel()

            val cassiMemberInfo = CassiMemberInfo(
                accountNumber = "**********",
                startDate = "1997-06-02",
                expirationDate = "2022-06-02",
            )
            val cassiMember = cassiMemberInfo.toCassiMember(member.id)
            val product = TestModelFactory.buildProduct(id = contract.productId)

            coEvery { productService.getProduct(product.id) } returns product
            val slot = slot<MemberModel>()
            coEvery { memberInternalService.add(capture(slot)) } returns memberModel
            coEvery {
                memberInternalService.findByFiltersWithoutPagination(
                    Filter(
                        personIds = listOf(personId),
                        statuses = listOf(ACTIVE, PENDING)
                    )
                )
            } returns emptyList()
            coEvery { memberProductPriceService.setCurrent(any(), any(), any()) } returns memberProductPrice
            coEvery { memberInternalService.get(any()) } returns memberModel
            coEvery { cassiMemberService.createCassiMember(any()) } returns cassiMember

            coEvery {
                kafkaProducerService.produce(match { it.name == "MEMBER-CREATED" })
            } returns ProducerResult(
                producedAt = LocalDateTime.now(),
                topic = MemberCreatedEvent.name,
                offset = 1L
            )

            val result = memberService.create(person.id, contract, cassiMemberInfo)
            val capturedMember = slot.captured

            val expectedMember = Member(
                personId = personId,
                selectedProduct = contract.selectedProduct,
                status = PENDING,
                contract = Contract(
                    documentUrl = contract.documentUrl
                ),
                cassiMember = cassiMember,
            )

            assertThat(result).isSuccess()
            assertThat(capturedMember).usingRecursiveComparison().ignoringFields(
                "id",
                "createdAt",
                "updatedAt",
                "migrationDate",
                "statusHistory",
                "cassiMember.id",
                "cassiMember.memberId"
            ).isEqualTo(expectedMember)
            assertThat(capturedMember.statusHistory).hasSize(1)

            coVerifyOnce { kafkaProducerService.produce(match { it.name == "MEMBER-CREATED" }) }

            coVerifyNone { memberProductPriceService.setProductPriceListing(any(), any(), any()) }
            coVerifyOnce { memberProductPriceService.setCurrent(member.id, member.productId, any()) }
        }

        @Test
        fun `#should call add successfully when person doesn't have a member, with productPriceListingId`() =
            runBlocking {
                val personId = person.id
                val memberProductPrice = TestModelFactory.buildMemberProductPrice()
                val contract = contract.copy(productPriceListingId = RangeUUID.generate())

                val slot = slot<MemberModel>()
                val member = TestModelFactory.buildMember(personId)
                val memberModel = member.toModel()
                val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)
                val product = TestModelFactory.buildProduct(id = contract.productId)

                coEvery { productService.getProduct(product.id) } returns product
                coEvery { cassiMemberService.createCassiMember(any()) } returns cassiMember
                coEvery { memberInternalService.add(capture(slot)) } returns memberModel
                coEvery {
                    memberInternalService.findByFiltersWithoutPagination(
                        Filter(
                            personIds = listOf(personId),
                            statuses = listOf(ACTIVE, PENDING)
                        )
                    )
                } returns emptyList()
                coEvery {
                    memberProductPriceService.setProductPriceListing(
                        any(),
                        any(),
                        any()
                    )
                } returns memberProductPrice
                coEvery { memberInternalService.get(any()) } returns memberModel

                coEvery {
                    kafkaProducerService.produce(match { it.name == "MEMBER-CREATED" })
                } returns ProducerResult(
                    producedAt = LocalDateTime.now(),
                    topic = MemberCreatedEvent.name,
                    offset = 1L
                )

                val result = memberService.create(person.id, contract)
                val capturedMember = slot.captured

                val expectedMember = Member(
                    personId = personId,
                    selectedProduct = contract.selectedProduct,
                    status = PENDING,
                    contract = Contract(
                        documentUrl = contract.documentUrl
                    )
                )

                assertThat(result).isSuccess()
                assertThat(capturedMember).usingRecursiveComparison()
                    .ignoringFields("id", "createdAt", "updatedAt", "migrationDate", "statusHistory")
                    .isEqualTo(expectedMember)
                assertThat(capturedMember.statusHistory).hasSize(1)

                coVerifyOnce { kafkaProducerService.produce(match { it.name == "MEMBER-CREATED" }) }

                coVerifyOnce {
                    memberProductPriceService.setProductPriceListing(
                        member.id,
                        contract.productPriceListingId!!,
                        any()
                    )
                }
                coVerifyNone { memberProductPriceService.setCurrent(any(), any(), any()) }
            }

        @Test
        fun `#should call add successfully when person doesn't have a member to B2B product without create a MPPL`() =
            runBlocking {
                val personId = person.id
                val memberProductPrice = TestModelFactory.buildMemberProductPrice()
                val contract = contract.copy(productPriceListingId = RangeUUID.generate())

                val slot = slot<MemberModel>()
                val member = TestModelFactory.buildMember(personId, productType = ProductType.B2B)
                val memberModel = member.toModel()
                val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)
                val product = TestModelFactory.buildProduct(id = contract.productId, type = ProductType.B2B)

                coEvery { productService.getProduct(product.id) } returns product
                coEvery { cassiMemberService.createCassiMember(any()) } returns cassiMember
                coEvery { memberInternalService.add(capture(slot)) } returns memberModel
                coEvery {
                    memberInternalService.findByFiltersWithoutPagination(
                        Filter(
                            personIds = listOf(personId),
                            statuses = listOf(ACTIVE, PENDING)
                        )
                    )
                } returns emptyList()
                coEvery {
                    memberProductPriceService.setProductPriceListing(
                        any(),
                        any(),
                        any()
                    )
                } returns memberProductPrice
                coEvery { memberInternalService.get(any()) } returns memberModel

                coEvery {
                    kafkaProducerService.produce(match { it.name == "MEMBER-CREATED" })
                } returns ProducerResult(
                    producedAt = LocalDateTime.now(),
                    topic = MemberCreatedEvent.name,
                    offset = 1L
                )

                val result = memberService.create(person.id, contract)
                val capturedMember = slot.captured

                val expectedMember = Member(
                    personId = personId,
                    selectedProduct = contract.selectedProduct,
                    status = PENDING,
                    contract = Contract(
                        documentUrl = contract.documentUrl
                    )
                )

                assertThat(result).isSuccess()
                assertThat(capturedMember).usingRecursiveComparison()
                    .ignoringFields("id", "createdAt", "updatedAt", "migrationDate", "statusHistory")
                    .isEqualTo(expectedMember)
                assertThat(capturedMember.statusHistory).hasSize(1)

                coVerifyOnce { kafkaProducerService.produce(match { it.name == "MEMBER-CREATED" }) }

                coVerifyNone { memberProductPriceService.setProductPriceListing(any(), any(), any()) }
                coVerifyNone { memberProductPriceService.setCurrent(any(), any(), any()) }
            }
    }

    @Nested
    inner class Update {

        @Test
        fun `#update should update member and produce event`() = runBlocking {
            val oldMember = TestModelFactory.buildMember(personId = person.id, status = PENDING)
            val newMember = oldMember.copy(status = ACTIVE)
            val newMemberModel = newMember.toModel()

            coEvery { memberInternalService.update(any()) } returns newMemberModel
            coEvery {
                kafkaProducerService.produce(match { it.name == "MEMBER-UPDATED" })
            } returns ProducerResult(
                producedAt = LocalDateTime.now(),
                topic = MemberUpdatedEvent.name,
                offset = 1L
            )

            val result = memberService.update(newMember)
            assertThat(result).isSuccessWithData(newMember)

            coVerifyOnce { kafkaProducerService.produce(match { it.name == "MEMBER-UPDATED" }) }
        }

        @Test
        fun `#should not update the member when trying to udpate the member as its parent`() = runBlocking {
            val oldMember = TestModelFactory.buildMember(personId = person.id, status = PENDING)
            val newMember =
                oldMember.copy(status = ACTIVE, parentMember = oldMember.id, parentPerson = oldMember.personId)


            val result = memberService.update(newMember)
            assertThat(result).isFailureOfType(SelfDependentException::class)

            coVerifyNone {
                memberInternalService.update(any())
                kafkaProducerService.produce(any())
            }
        }

        @Test
        fun `#update should update member and skip the produce event`() = runBlocking {
            val oldMember = TestModelFactory.buildMember(personId = person.id, status = PENDING)
            val newMember = oldMember.copy(status = ACTIVE)
            val newMemberModel = newMember.toModel()

            coEvery { memberInternalService.update(any()) } returns newMemberModel

            val result = memberService.update(newMember, false)
            assertThat(result).isSuccessWithData(newMember)

            coVerifyNone { kafkaProducerService.produce(any()) }
        }

    }


    @Nested
    inner class ActivateMember {
        @Test
        fun `#should not update when a member is already activated`() = runBlocking {
            val activatedMember = TestModelFactory.buildMember(person.id).copy(
                activationDate = LocalDateTime.now(),
                status = ACTIVE
            )

            val activationMemberResult = memberService.activateMember(activatedMember)
            assertThat(activationMemberResult).isSuccess()

            coVerifyNone { memberInternalService.update(any()) }
        }

        @Test
        fun `#should update active and activationDate when it was successful to B2C dependent`() =
            runBlocking {
                val member =
                    TestModelFactory.buildMember(
                        person.id,
                        parentMember = RangeUUID.generate(),
                        parentPerson = PersonId()
                    )
                val memberModel = member.toModel()
                val slot = slot<MemberModel>()

                coEvery { memberInternalService.update(capture(slot)) } returns memberModel
                coEvery { kafkaProducerService.produce(any()) } returns ProducerResult(
                    LocalDateTime.now(),
                    MemberActivatedEvent.name,
                    1L
                )


                val activationMemberResult = memberService.activateMember(member)
                val capturedMember = slot.captured

                assertThat(capturedMember.status).isEqualTo(MemberStatus.ACTIVE)
                assertThat(activationMemberResult).isSuccess()

                coVerifyNone { beneficiaryService.getCompanyContractStarted(any()) }
            }

        @Test
        fun `#should block B2C holder`() = runBlocking {
            val member = TestModelFactory.buildMember(person.id)

            coEvery { personDataService.get(member.personId.toUUID()) } returns personModel
            val activationMemberResult = memberService.activateMember(member)

            assertThat(activationMemberResult).isFailureOfType(B2CHolderIsNotAllowedException::class)

        }

        @Test
        fun `#should block b2c holder unless it is on feature flag`() = runBlocking {
            val member = TestModelFactory.buildMember(person.id)
            val memberModel = member.toModel()
            val slot = slot<MemberModel>()

            coEvery { personDataService.get(member.personId.toUUID()) } returns personModel
            coEvery { memberInternalService.update(capture(slot)) } returns memberModel
            coEvery { kafkaProducerService.produce(any()) } returns ProducerResult(
                LocalDateTime.now(),
                MemberActivatedEvent.name,
                1L
            )

            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "skip_b2c_holder_block", listOf(person.nationalId)) {
                val activationMemberResult = memberService.activateMember(member)
                val capturedMember = slot.captured

                assertThat(capturedMember.status).isEqualTo(MemberStatus.ACTIVE)
                assertThat(activationMemberResult).isSuccess()
            }

            coVerifyNone { beneficiaryService.getCompanyContractStarted(any()) }
        }

        @Test
        fun `#should update active and activationDate when it was successful to B2B`() = runBlocking {
            val member = TestModelFactory.buildMember(person.id, productType = ProductType.B2B)
            val memberModel = member.toModel()
            val contractStartedAt = LocalDate.now().minusDays(10)
            val slot = slot<MemberModel>()

            coEvery { memberInternalService.update(capture(slot)) } returns memberModel
            coEvery { beneficiaryService.getCompanyContractStarted(member.id) } returns contractStartedAt
            coEvery { kafkaProducerService.produce(any()) } returns ProducerResult(
                LocalDateTime.now(),
                MemberActivatedEvent.name,
                1L
            )

            val activationMemberResult = memberService.activateMember(member)
            val capturedMember = slot.captured

            assertThat(capturedMember.status).isEqualTo(MemberStatus.ACTIVE)
            assertThat(activationMemberResult).isSuccess()

            coVerifyOnce { beneficiaryService.getCompanyContractStarted(any()) }
        }

        @Test
        fun `#should update active and activationDate when it was successful to B2B with a specific activationDate`() =
            runBlocking {
                val member = TestModelFactory.buildMember(person.id, productType = ProductType.B2B)
                val memberModel = member.toModel()
                val oldActivationDate = LocalDateTime.of(2024, 7, 23, 0, 0, 0)
                val contractStartedAt = oldActivationDate.minusDays(10).toLocalDate()
                val slot = slot<MemberModel>()

                coEvery { memberInternalService.update(capture(slot)) } returns memberModel
                coEvery { beneficiaryService.getCompanyContractStarted(member.id) } returns contractStartedAt
                coEvery { kafkaProducerService.produce(any()) } returns ProducerResult(
                    LocalDateTime.now(),
                    MemberActivatedEvent.name,
                    1L
                )

                val activationMemberResult = memberService.activateMember(member, oldActivationDate)
                val capturedMember = slot.captured

                assertThat(capturedMember.status).isEqualTo(MemberStatus.ACTIVE)
                assertThat(capturedMember.activationDate).isEqualTo(oldActivationDate)
                assertThat(activationMemberResult).isSuccess()

                coVerifyOnce { beneficiaryService.getCompanyContractStarted(any()) }
            }

        @Test
        fun `#should not update active and activationDate if the activationDate is before contractStartedAt`() =
            runBlocking {
                val member = TestModelFactory.buildMember(person.id, productType = ProductType.B2B)
                val memberModel = member.toModel()
                val contractStartedAt = LocalDate.now().plusDays(10)

                val slot = slot<MemberModel>()

                coEvery { memberInternalService.update(capture(slot)) } returns memberModel
                coEvery { beneficiaryService.getCompanyContractStarted(member.id) } returns contractStartedAt
                coEvery { kafkaProducerService.produce(any()) } returns ProducerResult(
                    LocalDateTime.now(),
                    MemberActivatedEvent.name,
                    1L
                )

                assertThat(memberService.activateMember(member)).isFailureOfType(
                    MemberActivationDateIsEarlierThanContractStartedDateException::class
                )

            }

        @Test
        fun `#should not active a canceled member`() =
            runBlocking {
                val member = TestModelFactory.buildMember(person.id, productType = ProductType.B2B, status = CANCELED)

                assertThat(memberService.activateMember(member)).isFailureOfType(
                    MemberCanceledActivationException::class
                )
            }

        @Test
        fun `#should not update active and activationDate when the specific activationDate is before contractStartedAt`() =
            runBlocking {
                val member = TestModelFactory.buildMember(person.id, productType = ProductType.B2B)
                val memberModel = member.toModel()
                val oldActivationDate = LocalDateTime.of(2024, 7, 23, 0, 0, 0)
                val contractStartedAt = LocalDate.now().plusDays(10)

                val slot = slot<MemberModel>()

                coEvery { memberInternalService.update(capture(slot)) } returns memberModel
                coEvery { beneficiaryService.getCompanyContractStarted(member.id) } returns contractStartedAt
                coEvery { kafkaProducerService.produce(any()) } returns ProducerResult(
                    LocalDateTime.now(),
                    MemberActivatedEvent.name,
                    1L
                )

                assertThat(memberService.activateMember(member, oldActivationDate)).isFailureOfType(
                    MemberActivationDateIsEarlierThanContractStartedDateException::class
                )

            }

        @Test
        fun `#should not update active and activationDate when contractStartedAt is null to B2B`() =
            runBlocking {
                val member = TestModelFactory.buildMember(person.id, productType = ProductType.B2B)

                coEvery { beneficiaryService.getCompanyContractStarted(member.id) } returns IllegalArgumentException()
                coEvery { kafkaProducerService.produce(any()) } returns ProducerResult(
                    LocalDateTime.now(),
                    MemberActivatedEvent.name,
                    1L
                )

                val result = memberService.activateMember(member)

                coVerifyNone { memberInternalService.update(any()) }
                coVerifyNone { kafkaProducerService.produce(any()) }
                assertThat(result).isFailureOfType(IllegalArgumentException::class)
            }
    }

    @Nested
    inner class CancelById {

        @Test
        fun `#cancelById should call update with canceled Member`() = runBlocking {
            val member = TestModelFactory.buildMember()
            val memberModel = member.toModel()
            coEvery { memberInternalService.get(member.id) } returns memberModel

            val updatedMemberSlot = slot<MemberModel>()
            val cancelledMember = member.cancel()
            val cancelledMemberModel = cancelledMember.toModel()
            coEvery { memberInternalService.update(capture(updatedMemberSlot)) } returns cancelledMemberModel
            coEvery { kafkaProducerService.produce(any()) } returns ProducerResult(LocalDateTime.now(), "", 0)

            val result = memberService.cancelById(member.id)
            val updatedMemberCaptured = updatedMemberSlot.captured

            assertThat(result).isSuccessWithData(cancelledMember)

            assertThat(updatedMemberCaptured.status).isEqualTo(MemberStatus.CANCELED)
            assertThat(updatedMemberCaptured.canceledAt).isNotNull

            coVerifyOnce {
                kafkaProducerService.produce(match<MemberCancelledEvent> {
                    it.payload.member == cancelledMember
                })
            }
        }
    }

    @Nested
    inner class Cancel {

        @Test
        fun `#cancel should call update with canceled Member`() = runBlocking {
            val member = TestModelFactory.buildMember()
            val memberModel = member.toModel()
            coEvery { memberInternalService.get(member.id) } returns memberModel

            val updatedMemberSlot = slot<MemberModel>()
            val cancelledMember = member.cancel()
            val cancelledMemberModel = cancelledMember.toModel()
            coEvery { memberInternalService.update(capture(updatedMemberSlot)) } returns cancelledMemberModel
            coEvery { kafkaProducerService.produce(any()) } returns ProducerResult(LocalDateTime.now(), "", 0)

            val result = memberService.cancel(member)
            val updatedMemberCaptured = updatedMemberSlot.captured

            assertThat(result).isSuccessWithData(cancelledMember)

            assertThat(updatedMemberCaptured.status).isEqualTo(MemberStatus.CANCELED)
            assertThat(updatedMemberCaptured.canceledAt).isEqualTo(LocalDateTime.now().atEndOfTheDay())

            coVerifyOnce {
                kafkaProducerService.produce(match<MemberCancelledEvent> {
                    it.payload.member == cancelledMember
                })
            }
        }

        @Test
        fun `#cancel should call update with canceled Member with a specific canceled at date time`() = runBlocking {
            val member = TestModelFactory.buildMember()
            val memberModel = member.toModel()
            val canceledAt = LocalDateTime.now().minusDays(2).atEndOfTheDay()
            coEvery { memberInternalService.get(member.id) } returns memberModel

            val updatedMemberSlot = slot<MemberModel>()
            val cancelledMember = member.cancel(canceledAt)
            val cancelledMemberModel = cancelledMember.toModel()
            coEvery { memberInternalService.update(capture(updatedMemberSlot)) } returns cancelledMemberModel
            coEvery { kafkaProducerService.produce(any()) } returns ProducerResult(LocalDateTime.now(), "", 0)

            val result = memberService.cancel(member, canceledAt)
            val updatedMemberCaptured = updatedMemberSlot.captured

            assertThat(result).isSuccessWithData(cancelledMember)

            assertThat(updatedMemberCaptured.status).isEqualTo(MemberStatus.CANCELED)
            assertThat(updatedMemberCaptured.canceledAt).isEqualTo(canceledAt)

            coVerifyOnce {
                kafkaProducerService.produce(match<MemberCancelledEvent> {
                    it.payload.member == cancelledMember
                })
            }
        }

        @Test
        fun `#cancel should not call update and not produce event when already canceled Member`() = runBlocking {
            val member = TestModelFactory.buildMember(status = CANCELED)
            val memberModel = member.toModel()
            coEvery { memberInternalService.get(member.id) } returns memberModel

            val result = memberService.cancel(member)
            assertThat(result).isSuccessWithData(member)

            coVerifyNone { kafkaProducerService.produce(any()) }
        }
    }

    @Test
    fun `#findPendingMembership should return only pending membership`() = runBlocking {
        val member = TestModelFactory.buildMember(person.id)
        val memberModel = member.toModel()
        val memberProductPrice = TestModelFactory.buildMemberProductPrice(memberId = member.id)

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(personIds = listOf(person.id), statuses = listOf(PENDING))
            )
        } returns listOf(memberModel)
        coEvery { memberProductPriceService.findCurrent(member.id) } returns memberProductPrice

        val pendingMemberResult =
            memberService.findPendingMembership(person.id, findOptions = FindOptions(withPriceListing = true))

        assertThat(pendingMemberResult).isSuccessWithData(member.withMemberProductPrice(memberProductPrice))
    }

    @Test
    fun `#findPendingMembership should return null when a pending member is not found`() = runBlocking {
        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(personIds = listOf(person.id), statuses = listOf(PENDING))
            )
        } returns NotFoundException("Member not found")

        val pendingMemberResult = memberService.findPendingMembership(person.id)
        assertThat(pendingMemberResult).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#findFirstMembership should return only pending membership`() = runBlocking {
        val member = TestModelFactory.buildMember(person.id)
        val memberModel = member.toModel()

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(Filter(personIds = listOf(person.id)))
        } returns listOf(memberModel)

        val pendingMemberResult = memberService.findFirstMembership(person.id)
        assertThat(pendingMemberResult).isSuccessWithData(member)
    }

    @Test
    fun `#findFirstMembership should return null when a pending member is not found`() = runBlocking {
        coEvery {
            memberInternalService.findByFiltersWithoutPagination(Filter(personIds = listOf(person.id)))
        } returns emptyList()

        val pendingMemberResult = memberService.findFirstMembership(person.id)
        assertThat(pendingMemberResult).isFailureOfType(NotFoundException::class)
    }

    @Nested
    inner class ChangeProduct {

        @Test
        fun `#should return not found exception when a person doesn't have an active membership`() =
            runBlocking {
                coEvery {
                    memberInternalService.findByFiltersWithoutPagination(
                        Filter(
                            personIds = listOf(person.id),
                            statuses = listOf(PENDING, ACTIVE)
                        )
                    )
                } returns NotFoundException("Member not found")

                val product = TestModelFactory.buildProduct()

                val updatedMembership = memberService.changeProduct(person.id, product)
                assertThat(updatedMembership).isFailureOfType(ActiveMembershipNotFoundException::class)
            }

        @Test
        fun `#should not create a new active member when a previous cancellation had a failure`() =
            runBlocking {
                val activeMembership = TestModelFactory.buildMember(person.id, status = ACTIVE)
                val activeMembershipModel = activeMembership.toModel()
                val slot = slot<MemberModel>()
                coEvery {
                    memberInternalService.findByFiltersWithoutPagination(
                        Filter(
                            personIds = listOf(person.id),
                            statuses = listOf(PENDING, ACTIVE)
                        )
                    )
                } returns listOf(activeMembershipModel)
                coEvery { memberInternalService.update(capture(slot)) } returns DuplicatedItemException("duplicated_item")

                val product = TestModelFactory.buildProduct()

                val updatedMembership = memberService.changeProduct(person.id, product)
                val capturedMember = slot.captured

                assertThat(updatedMembership).isFailureOfType(DuplicatedItemException::class)
                assertThat(capturedMember.isCanceled).isTrue()

                coVerifyNone { memberInternalService.add(any()) }
            }

        @Test
        fun `#should cancel an active membership and create a new one`() = runBlocking {
            val activeMembership = TestModelFactory.buildMember(person.id, status = ACTIVE)
            val activeMembershipModel = activeMembership.toModel()
            val previousMembership = activeMembership.cancel()
            val previousMembershipModel = previousMembership.toModel()
            val canceledMemberSlot = slot<MemberModel>()
            val addedMemberSlot = slot<MemberModel>()
            val newMember = TestModelFactory.buildMember(personId = person.id, status = ACTIVE)
            val newMemberModel = newMember.toModel()
            val product = TestModelFactory.buildProduct()
            val productChangedEventSlot = slot<ProductChangedEvent>()
            val memberUpdatedEventSlot = slot<MemberUpdatedEvent>()

            coEvery {
                memberInternalService.findByFiltersWithoutPagination(
                    Filter(
                        personIds = listOf(person.id),
                        statuses = listOf(PENDING, ACTIVE)
                    )
                )
            } returns listOf(activeMembershipModel)
            coEvery { memberInternalService.update(capture(canceledMemberSlot)) } returns previousMembershipModel
            coEvery { memberInternalService.add(capture(addedMemberSlot)) } returns newMemberModel
            coEvery {
                memberProductPriceService.setCurrent(
                    newMember.id,
                    product.id,
                    any()
                )
            } returns TestModelFactory.buildMemberProductPrice()
            coEvery {
                cassiMemberService.copyOrCreateCassiMember(previousMembership.id, newMember.id, product)
            } returns CassiMemberNotCreatedException(newMember.id, product.id)
            coEvery { kafkaProducerService.produce(capture(productChangedEventSlot)) } returns mockk()
            coEvery { kafkaProducerService.produce(capture(memberUpdatedEventSlot)) } returns mockk()

            val updatedMembership = memberService.changeProduct(person.id, product)
            val capturedCanceledMember = canceledMemberSlot.captured
            val capturedMember = addedMemberSlot.captured
            val capturedProductChangedEvent = productChangedEventSlot.captured
            val capturedMemberUpdatedEvent = memberUpdatedEventSlot.captured
            val now = LocalDateTime.now()

            assertThat(updatedMembership).isSuccess()
            assertThat(capturedMember.status).isEqualTo(MemberStatus.ACTIVE)
            assertThat(capturedMember.activationDate).isEqualTo(now.atBeginningOfTheDay().plusHours(3))
            assertThat(capturedMember.statusHistory).hasSize(1)
            assertThat(capturedCanceledMember.canceledAt).isEqualTo(
                now.minusDays(1).atEndOfTheDay()
            )

            assertThat(capturedProductChangedEvent.payload.previousMember).isEqualTo(previousMembership)
            assertThat(capturedProductChangedEvent.payload.newMember).isEqualTo(newMember)

            assertThat(capturedMemberUpdatedEvent.payload.member).isEqualTo(previousMembership)

            coVerifyOnce { memberInternalService.add(capturedMember) }
            coVerifyOnce { cassiMemberService.copyOrCreateCassiMember(previousMembership.id, newMember.id, product) }
        }

        @Test
        fun `#should cancel an active membership and create a new pending one`() = runBlocking {
            val activeMembership = TestModelFactory.buildMember(person.id, status = ACTIVE)
            val activeMembershipModel = activeMembership.toModel()
            val previousMembership = activeMembership.cancel()
            val previousMembershipModel = previousMembership.toModel()
            val canceledMemberSlot = slot<MemberModel>()
            val addedMemberSlot = slot<MemberModel>()
            val newMember = TestModelFactory.buildMember(personId = person.id, status = PENDING)
            val newMemberModel = newMember.toModel()
            val product = TestModelFactory.buildProduct()
            val productChangedEventSlot = slot<ProductChangedEvent>()
            val memberUpdatedEventSlot = slot<MemberUpdatedEvent>()

            coEvery {
                memberInternalService.findByFiltersWithoutPagination(
                    Filter(
                        personIds = listOf(person.id),
                        statuses = listOf(PENDING, ACTIVE)
                    )
                )
            } returns listOf(activeMembershipModel)
            coEvery { memberInternalService.update(capture(canceledMemberSlot)) } returns previousMembershipModel
            coEvery { memberInternalService.add(capture(addedMemberSlot)) } returns newMemberModel
            coEvery {
                cassiMemberService.copyOrCreateCassiMember(previousMembership.id, newMember.id, product)
            } returns CassiMemberNotCreatedException(newMember.id, product.id)
            coEvery {
                memberProductPriceService.setCurrent(
                    newMember.id,
                    product.id,
                    any()
                )
            } returns TestModelFactory.buildMemberProductPrice()
            coEvery { kafkaProducerService.produce(capture(productChangedEventSlot)) } returns mockk()
            coEvery { kafkaProducerService.produce(capture(memberUpdatedEventSlot)) } returns mockk()

            val updatedMembership = memberService.changeProduct(person.id, product, false)
            val capturedCanceledMember = canceledMemberSlot.captured
            val capturedMember = addedMemberSlot.captured
            val capturedProductChangedEvent = productChangedEventSlot.captured
            val capturedMemberUpdatedEvent = memberUpdatedEventSlot.captured
            val now = LocalDateTime.now()

            assertThat(updatedMembership).isSuccess()
            assertThat(capturedMember.status).isEqualTo(MemberStatus.PENDING)
            assertThat(capturedMember.activationDate).isNull()
            assertThat(capturedMember.statusHistory).hasSize(1)
            assertThat(capturedCanceledMember.canceledAt).isEqualTo(
                now.minusDays(1).atEndOfTheDay()
            )

            assertThat(capturedProductChangedEvent.payload.previousMember).isEqualTo(previousMembership)
            assertThat(capturedProductChangedEvent.payload.newMember).isEqualTo(newMember)

            assertThat(capturedMemberUpdatedEvent.payload.member).isEqualTo(previousMembership)

            coVerifyOnce { memberInternalService.add(capturedMember) }
            coVerifyOnce { cassiMemberService.copyOrCreateCassiMember(previousMembership.id, newMember.id, product) }
        }

        @Test
        fun `#should cancel an active membership and create a new pending one when the canceled membership is B2B and the contract started at is after the cancellation date`() =
            runBlocking {
                val activeMembership =
                    TestModelFactory.buildMember(person.id, status = ACTIVE, productType = ProductType.B2B)
                val activeMembershipModel = activeMembership.toModel()
                val previousMembership = activeMembership.cancel()
                val previousMembershipModel = previousMembership.toModel()
                val canceledMemberSlot = slot<MemberModel>()
                val addedMemberSlot = slot<MemberModel>()
                val newMember = TestModelFactory.buildMember(personId = person.id, status = PENDING)
                val newMemberModel = newMember.toModel()
                val product = TestModelFactory.buildProduct(type = ProductType.B2B)
                val productChangedEventSlot = slot<ProductChangedEvent>()
                val memberUpdatedEventSlot = slot<MemberUpdatedEvent>()
                val contractStartedAt = LocalDate.now()

                coEvery {
                    memberInternalService.findByFiltersWithoutPagination(
                        Filter(
                            personIds = listOf(person.id),
                            statuses = listOf(PENDING, ACTIVE)
                        )
                    )
                } returns listOf(activeMembershipModel)
                coEvery { memberInternalService.update(capture(canceledMemberSlot)) } returns previousMembershipModel
                coEvery { memberInternalService.add(capture(addedMemberSlot)) } returns newMemberModel
                coEvery {
                    cassiMemberService.copyOrCreateCassiMember(previousMembership.id, newMember.id, product)
                } returns CassiMemberNotCreatedException(newMember.id, product.id)
                coEvery {
                    memberProductPriceService.setCurrent(
                        newMember.id,
                        product.id,
                        any()
                    )
                } returns TestModelFactory.buildMemberProductPrice()
                coEvery { beneficiaryService.getCompanyContractStarted(activeMembership.id) } returns contractStartedAt
                coEvery { kafkaProducerService.produce(capture(productChangedEventSlot)) } returns mockk()
                coEvery { kafkaProducerService.produce(capture(memberUpdatedEventSlot)) } returns mockk()

                val updatedMembership = memberService.changeProduct(person.id, product, false)
                val capturedCanceledMember = canceledMemberSlot.captured
                val capturedMember = addedMemberSlot.captured
                val capturedProductChangedEvent = productChangedEventSlot.captured
                val capturedMemberUpdatedEvent = memberUpdatedEventSlot.captured
                val now = LocalDateTime.now()

                assertThat(updatedMembership).isSuccess()
                assertThat(capturedMember.status).isEqualTo(MemberStatus.PENDING)
                assertThat(capturedMember.activationDate).isNull()
                assertThat(capturedMember.statusHistory).hasSize(1)
                assertThat(capturedCanceledMember.canceledAt).isEqualTo(
                    now.atEndOfTheDay()
                )

                assertThat(capturedProductChangedEvent.payload.previousMember).isEqualTo(previousMembership)
                assertThat(capturedProductChangedEvent.payload.newMember).isEqualTo(newMember)

                assertThat(capturedMemberUpdatedEvent.payload.member).isEqualTo(previousMembership)

                coVerifyOnce { memberInternalService.add(capturedMember) }
                coVerifyOnce {
                    cassiMemberService.copyOrCreateCassiMember(
                        previousMembership.id,
                        newMember.id,
                        product
                    )
                }
            }

        @Test
        fun `#should cancel an active membership, create a new one and create cassi member when product has national coverage`() =
            runBlocking {
                val activeMembership = TestModelFactory.buildMember(person.id, status = ACTIVE)
                val activeMembershipModel = activeMembership.toModel()
                val previousMembership = activeMembership.cancel()
                val previousMembershipModel = previousMembership.toModel()
                val addedMemberSlot = slot<MemberModel>()
                val canceledMemberSlot = slot<MemberModel>()
                val newMember = TestModelFactory.buildMember(personId = person.id, status = ACTIVE)
                val newMemberModel = newMember.toModel()
                val product = TestModelFactory.buildProduct(hasNationalCoverage = true)
                val productChangedEventSlot = slot<ProductChangedEvent>()
                val memberUpdatedEventSlot = slot<MemberUpdatedEvent>()
                val newCassiMember = TestModelFactory.buildCassiMember(
                    memberId = newMember.id,
                    accountNumber = null,
                    startDate = null,
                    expirationDate = null,
                )

                coEvery {
                    memberInternalService.findByFiltersWithoutPagination(
                        Filter(
                            personIds = listOf(person.id),
                            statuses = listOf(PENDING, ACTIVE)
                        )
                    )
                } returns listOf(activeMembershipModel)
                coEvery { memberInternalService.update(capture(canceledMemberSlot)) } returns previousMembershipModel
                coEvery { memberInternalService.add(capture(addedMemberSlot)) } returns newMemberModel
                coEvery {
                    memberProductPriceService.setCurrent(
                        newMember.id,
                        product.id,
                        any()
                    )
                } returns TestModelFactory.buildMemberProductPrice()
                coEvery {
                    cassiMemberService.copyOrCreateCassiMember(previousMembership.id, newMember.id, product)
                } returns newCassiMember
                coEvery { kafkaProducerService.produce(capture(productChangedEventSlot)) } returns mockk()
                coEvery { kafkaProducerService.produce(capture(memberUpdatedEventSlot)) } returns mockk()

                val updatedMembership = memberService.changeProduct(person.id, product)
                val capturedCanceledMember = canceledMemberSlot.captured
                val capturedMember = addedMemberSlot.captured
                val capturedProductChangedEvent = productChangedEventSlot.captured
                val capturedMemberUpdatedEvent = memberUpdatedEventSlot.captured
                val now = LocalDateTime.now()

                assertThat(updatedMembership).isSuccess()
                assertThat(capturedMember.status).isEqualTo(MemberStatus.ACTIVE)
                assertThat(capturedMember.activationDate).isEqualTo(now.atBeginningOfTheDay().plusHours(3))
                assertThat(capturedMember.statusHistory).hasSize(1)
                assertThat(capturedCanceledMember.canceledAt).isEqualTo(
                    now.minusDays(1).atEndOfTheDay()
                )

                assertThat(capturedProductChangedEvent.payload.previousMember).isEqualTo(previousMembership)
                assertThat(capturedProductChangedEvent.payload.newMember).isEqualTo(newMember)

                assertThat(capturedMemberUpdatedEvent.payload.member).isEqualTo(previousMembership)

                coVerifyOnce { memberInternalService.add(capturedMember) }
                coVerifyOnce {
                    cassiMemberService.copyOrCreateCassiMember(
                        previousMembership.id,
                        newMember.id,
                        product
                    )
                }
            }

        @Test
        fun `#should cancel an active membership, create a new one and create cassi member when product has national coverage - previous date`() =
            runBlocking {
                val fixedActivationDate = LocalDateTime.of(2022, 1, 1, 0, 0, 0)
                val activeMembership = TestModelFactory.buildMember(
                    person.id,
                    status = ACTIVE,
                    activationDate = LocalDateTime.of(2021, 1, 1, 0, 0, 0)
                )
                val activeMembershipModel = activeMembership.toModel()
                val previousMembership = activeMembership.cancel()
                val previousMembershipModel = previousMembership.toModel()
                val addedMemberSlot = slot<MemberModel>()
                val canceledMemberSlot = slot<MemberModel>()
                val newMember = TestModelFactory.buildMember(personId = person.id, status = PENDING)
                val newMemberModel = newMember.toModel()
                val product = TestModelFactory.buildProduct(hasNationalCoverage = true)
                val productChangedEventSlot = slot<ProductChangedEvent>()
                val memberUpdatedEventSlot = slot<MemberUpdatedEvent>()
                val newCassiMember = TestModelFactory.buildCassiMember(
                    memberId = newMember.id,
                    accountNumber = null,
                    startDate = null,
                    expirationDate = null,
                )

                coEvery {
                    memberInternalService.findByFiltersWithoutPagination(
                        Filter(
                            personIds = listOf(person.id),
                            statuses = listOf(PENDING, ACTIVE)
                        )
                    )
                } returns listOf(activeMembershipModel)
                coEvery { memberInternalService.update(capture(canceledMemberSlot)) } returns previousMembershipModel
                coEvery { memberInternalService.add(capture(addedMemberSlot)) } returns newMemberModel
                coEvery {
                    memberProductPriceService.setCurrent(
                        newMember.id,
                        product.id,
                        any()
                    )
                } returns TestModelFactory.buildMemberProductPrice()
                coEvery {
                    cassiMemberService.copyOrCreateCassiMember(previousMembership.id, newMember.id, product)
                } returns newCassiMember
                coEvery { kafkaProducerService.produce(capture(productChangedEventSlot)) } returns mockk()
                coEvery { kafkaProducerService.produce(capture(memberUpdatedEventSlot)) } returns mockk()

                val updatedMembership = memberService.changeProduct(person.id, product, false, fixedActivationDate)
                val capturedCanceledMember = canceledMemberSlot.captured
                val capturedMember = addedMemberSlot.captured
                val capturedProductChangedEvent = productChangedEventSlot.captured
                val capturedMemberUpdatedEvent = memberUpdatedEventSlot.captured

                assertThat(updatedMembership).isSuccess()
                assertThat(capturedMember.status).isEqualTo(MemberStatus.PENDING)
                assertThat(capturedMember.activationDate).isNull()
                assertThat(capturedMember.statusHistory).hasSize(1)
                assertThat(capturedCanceledMember.canceledAt).isEqualTo(
                    fixedActivationDate.minusDays(1).atEndOfTheDay()
                )

                assertThat(capturedProductChangedEvent.payload.previousMember).isEqualTo(previousMembership)
                assertThat(capturedProductChangedEvent.payload.newMember).isEqualTo(newMember)

                assertThat(capturedMemberUpdatedEvent.payload.member).isEqualTo(previousMembership)

                coVerifyOnce { memberInternalService.add(capturedMember) }
                coVerifyOnce {
                    cassiMemberService.copyOrCreateCassiMember(
                        previousMembership.id,
                        newMember.id,
                        product
                    )
                }
            }

        @Test
        fun `#should cancel an active membership, create a new one and create cassi member when product has national coverage - future date`() =
            runBlocking {
                val fixedActivationDate = LocalDateTime.of(2040, 1, 1, 0, 0, 0)
                val activeMembership = TestModelFactory.buildMember(person.id, status = ACTIVE)
                val activeMembershipModel = activeMembership.toModel()
                val previousMembership = activeMembership.cancel()
                val previousMembershipModel = previousMembership.toModel()
                val addedMemberSlot = slot<MemberModel>()
                val canceledMemberSlot = slot<MemberModel>()
                val newMember = TestModelFactory.buildMember(personId = person.id, status = PENDING)
                val newMemberModel = newMember.toModel()
                val product = TestModelFactory.buildProduct(hasNationalCoverage = true)
                val productChangedEventSlot = slot<ProductChangedEvent>()
                val memberUpdatedEventSlot = slot<MemberUpdatedEvent>()
                val newCassiMember = TestModelFactory.buildCassiMember(
                    memberId = newMember.id,
                    accountNumber = null,
                    startDate = null,
                    expirationDate = null,
                )

                coEvery {
                    memberInternalService.findByFiltersWithoutPagination(
                        Filter(
                            personIds = listOf(person.id),
                            statuses = listOf(PENDING, ACTIVE)
                        )
                    )
                } returns listOf(activeMembershipModel)
                coEvery { memberInternalService.update(capture(canceledMemberSlot)) } returns previousMembershipModel
                coEvery { memberInternalService.add(capture(addedMemberSlot)) } returns newMemberModel
                coEvery {
                    memberProductPriceService.setCurrent(
                        newMember.id,
                        product.id,
                        any()
                    )
                } returns TestModelFactory.buildMemberProductPrice()
                coEvery {
                    cassiMemberService.copyOrCreateCassiMember(previousMembership.id, newMember.id, product)
                } returns newCassiMember
                coEvery { kafkaProducerService.produce(capture(productChangedEventSlot)) } returns mockk()
                coEvery { kafkaProducerService.produce(capture(memberUpdatedEventSlot)) } returns mockk()

                val updatedMembership = memberService.changeProduct(person.id, product, false, fixedActivationDate)
                val capturedCanceledMember = canceledMemberSlot.captured
                val capturedMember = addedMemberSlot.captured
                val capturedProductChangedEvent = productChangedEventSlot.captured
                val capturedMemberUpdatedEvent = memberUpdatedEventSlot.captured
                val now = LocalDateTime.now()

                assertThat(updatedMembership).isSuccess()
                assertThat(capturedMember.status).isEqualTo(MemberStatus.PENDING)
                assertThat(capturedMember.activationDate).isNull()
                assertThat(capturedMember.statusHistory).hasSize(1)
                assertThat(capturedCanceledMember.canceledAt).isEqualTo(
                    now.atEndOfTheDay()
                )

                assertThat(capturedProductChangedEvent.payload.previousMember).isEqualTo(previousMembership)
                assertThat(capturedProductChangedEvent.payload.newMember).isEqualTo(newMember)

                assertThat(capturedMemberUpdatedEvent.payload.member).isEqualTo(previousMembership)

                coVerifyOnce { memberInternalService.add(capturedMember) }
                coVerifyOnce {
                    cassiMemberService.copyOrCreateCassiMember(
                        previousMembership.id,
                        newMember.id,
                        product
                    )
                }
            }

        @Test
        fun `#should create the new membership with the brand duquesa, since product is duquesa`() =
            runBlocking {
                val activeMembership = TestModelFactory.buildMember(
                    personId = person.id,
                    status = ACTIVE,
                    brand = Brand.ALICE
                )
                val activeMembershipModel = activeMembership.toModel()
                val previousMembership = activeMembership.cancel()
                val previousMembershipModel = previousMembership.toModel()
                val canceledMemberSlot = slot<MemberModel>()
                val addedMemberSlot = slot<MemberModel>()
                val newMember = TestModelFactory.buildMember(
                    personId = person.id,
                    status = ACTIVE,
                    brand = Brand.DUQUESA,
                )
                val newMemberModel = newMember.toModel()
                val product = TestModelFactory.buildProduct(hasNationalCoverage = true, brand = Brand.DUQUESA)
                val productChangedEventSlot = slot<ProductChangedEvent>()
                val memberUpdatedEventSlot = slot<MemberUpdatedEvent>()
                val newCassiMember = TestModelFactory.buildCassiMember(
                    memberId = newMember.id,
                    accountNumber = null,
                    startDate = null,
                    expirationDate = null,
                )

                coEvery {
                    memberInternalService.findByFiltersWithoutPagination(
                        Filter(
                            personIds = listOf(person.id),
                            statuses = listOf(PENDING, ACTIVE)
                        )
                    )
                } returns listOf(activeMembershipModel)
                coEvery { memberInternalService.update(capture(canceledMemberSlot)) } returns previousMembershipModel
                coEvery { memberInternalService.add(capture(addedMemberSlot)) } returns newMemberModel
                coEvery {
                    memberProductPriceService.setCurrent(
                        newMember.id,
                        product.id,
                        any()
                    )
                } returns TestModelFactory.buildMemberProductPrice()
                coEvery {
                    cassiMemberService.copyOrCreateCassiMember(previousMembership.id, newMember.id, product)
                } returns newCassiMember
                coEvery { kafkaProducerService.produce(capture(productChangedEventSlot)) } returns mockk()
                coEvery { kafkaProducerService.produce(capture(memberUpdatedEventSlot)) } returns mockk()

                val updatedMembership = memberService.changeProduct(person.id, product)
                val capturedMember = addedMemberSlot.captured
                val capturedCanceledMember = canceledMemberSlot.captured
                val capturedProductChangedEvent = productChangedEventSlot.captured
                val capturedMemberUpdatedEvent = memberUpdatedEventSlot.captured
                val now = LocalDateTime.now()

                assertThat(updatedMembership).isSuccess()
                assertThat(capturedMember.status).isEqualTo(MemberStatus.ACTIVE)
                assertThat(capturedMember.activationDate).isEqualTo(now.atBeginningOfTheDay().plusHours(3))
                assertThat(capturedMember.statusHistory).hasSize(1)
                assertThat(capturedCanceledMember.canceledAt).isEqualTo(
                    now.minusDays(1).atEndOfTheDay()
                )

                assertThat(capturedProductChangedEvent.payload.previousMember).isEqualTo(previousMembership)
                assertThat(capturedProductChangedEvent.payload.newMember).isEqualTo(newMember)
                assertThat(capturedMemberUpdatedEvent.payload.member).isEqualTo(previousMembership)

                coVerifyOnce { memberInternalService.add(capturedMember) }
                coVerifyOnce {
                    cassiMemberService.copyOrCreateCassiMember(
                        previousMembership.id,
                        newMember.id,
                        product
                    )
                }
            }
    }

    @Nested
    inner class CancelActiveOrPendingMembership {

        @Test
        fun `#cancelActiveOrPendingMembership - return error if member not found`() = runBlocking {
            coEvery {
                memberInternalService.findByFiltersWithoutPagination(
                    Filter(
                        personIds = listOf(person.id),
                        statuses = listOf(PENDING, ACTIVE)
                    )
                )
            } returns emptyList()

            val result = memberService.cancelActiveOrPendingMembership(person.id)

            assertThat(result).isFailureOfType(MemberNotFoundException::class)
            coVerifyNone { kafkaProducerService.produce(any()) }
        }

        @Test
        fun `#cancelActiveOrPendingMembership - cancel and produce kafka event`() = runBlocking {
            val activeMembership = TestModelFactory.buildMember(person.id)
            val activeMembershipModel = activeMembership.toModel()
            val cancelledMembership = activeMembership.cancel()
            val cancelledMembershipModel = cancelledMembership.toModel()

            coEvery {
                memberInternalService.findByFiltersWithoutPagination(
                    Filter(
                        personIds = listOf(person.id),
                        statuses = listOf(PENDING, ACTIVE)
                    )
                )
            } returns listOf(
                activeMembershipModel
            )
            coEvery { memberInternalService.update(any()) } returns cancelledMembershipModel
            coEvery { kafkaProducerService.produce(any(), any()) } returns ProducerResult(
                LocalDateTime.now(),
                "",
                0
            )

            val result = memberService.cancelActiveOrPendingMembership(person.id)

            coVerify { kafkaProducerService.produce(any<MemberCancelledEvent>()) }
            coVerify { kafkaProducerService.produce(any<MemberUpdatedEvent>()) }

            assertThat(result).isSuccessWithData(cancelledMembership)
        }

        @Test
        fun `#cancelActiveOrPendingMembership - cancel B2B member`() = runBlocking {
            val activeMembership = TestModelFactory.buildMember(
                personId = person.id,
                productType = ProductType.B2B
            )
            val activeMembershipModel = activeMembership.toModel()
            val beneficiary = TestModelFactory.buildBeneficiary()

            coEvery {
                memberInternalService.findByFiltersWithoutPagination(
                    Filter(
                        personIds = listOf(person.id),
                        statuses = listOf(PENDING, ACTIVE)
                    )
                )
            } returns listOf(
                activeMembershipModel
            )

            coEvery { beneficiaryService.findByMemberId(activeMembership.id) } returns beneficiary
            coEvery { beneficiaryService.cancelBeneficiaryById(beneficiary.id, any()) } returns beneficiary

            val result = memberService.cancelActiveOrPendingMembership(person.id)

            coVerifyNone { kafkaProducerService.produce(any<MemberCancelledEvent>()) }
            coVerifyNone { kafkaProducerService.produce(any<MemberUpdatedEvent>()) }

            assertThat(result).isSuccessWithData(activeMembership)
        }
    }

    @Test
    fun `#cancelPendingMembership - cancel and produce kafka event`() = runBlocking {
        val activeMembership = TestModelFactory.buildMember(person.id)
        val activeMembershipModel = activeMembership.toModel()
        val cancelledMembership = activeMembership.cancel()
        val cancelledMembershipModel = cancelledMembership.toModel()

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(
                    personIds = listOf(person.id),
                    statuses = listOf(PENDING)
                )
            )
        } returns listOf(activeMembershipModel)
        coEvery { memberInternalService.get(any()) } returns activeMembershipModel
        coEvery { memberInternalService.update(any()) } returns cancelledMembershipModel
        coEvery { kafkaProducerService.produce(any(), any()) } returns ProducerResult(
            LocalDateTime.now(),
            "",
            0
        )

        val result = memberService.cancelPendingMembership(person.id)

        coVerifyOnce {
            kafkaProducerService.produce(match<MemberCancelledEvent> {
                it.payload.member == cancelledMembership
            })
        }

        assertThat(result).isSuccessWithData(cancelledMembership)
    }

    @Test
    fun `#findFirstActivated should find first activated`() = runBlocking {
        val member = TestModelFactory.buildMember(person.id, activationDate = LocalDateTime.now())
        val memberModel = member.toModel()

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(personIds = listOf(person.id))
            )
        } returns listOf(
            memberModel,
            memberModel.copy(
                activationDate = null
            ),
            memberModel.copy(
                activationDate = memberModel.activationDate?.plusDays(1)
            )
        )

        val memberResult = memberService.findFirstActivated(person.id)
        assertThat(memberResult).isSuccessWithData(member)
    }

    @Test
    fun `#findActiveLightweight should call data service with expected query`() = runBlocking {
        val memberLightweight = TestModelFactory.buildMemberLightweight()
        val expectedResult = listOf(memberLightweight)
        val expectedResultModel = listOf(memberLightweight.toModel())

        coEvery {
            memberLightweightDataService.find(queryEq {
                where { this.status.eq(MemberStatus.ACTIVE).and(this.archived.eq(false)) }
            })
        } returns expectedResultModel

        val memberResult = memberService.findActiveLightweight()

        assertThat(memberResult).isSuccessWithData(expectedResult)
    }

    @Test
    fun `#findByPersonIds returns member with price listing found by ids`() = runBlocking {
        val member = TestModelFactory.buildMember()
        val memberModel = member.toModel()
        val memberProductPrice = TestModelFactory.buildMemberProductPrice(memberId = member.id)

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(personIds = listOf(member.personId))
            )
        } returns listOf(memberModel)

        coEvery {
            memberProductPriceService.findCurrents(listOf(member.id))
        } returns listOf(memberProductPrice)

        val result =
            memberService.findByPersonIds(
                listOf(member.personId),
                findOptions = FindOptions(withPriceListing = true)
            )
        assertThat(result).isSuccessWithData(listOf(member.withMemberProductPrice(memberProductPrice)))

        coVerifyOnce { memberInternalService.findByFiltersWithoutPagination(any()) }
        coVerifyOnce { memberProductPriceService.findCurrents(any()) }
        coVerify { productService wasNot called }
        coVerify { kafkaProducerService wasNot called }
        coVerify { memberLightweightDataService wasNot called }
        confirmVerified(memberInternalService, memberProductPriceService)
    }

    @Test
    fun `#findByPersonIds returns member without price listing found by ids, prioritizing ACTIVE member`() =
        runBlocking {
            val personId = PersonId()
            val pendingMember = TestModelFactory.buildMember(personId = personId, status = PENDING)
            val pendingMemberModel = pendingMember.toModel()
            val activeMember = TestModelFactory.buildMember(personId = personId, status = ACTIVE)
            val activeMemberModel = activeMember.toModel()
            val canceledMember = TestModelFactory.buildMember(personId = personId, status = CANCELED)
            val canceledMemberModel = canceledMember.toModel()

            coEvery {
                memberInternalService.findByFiltersWithoutPagination(
                    Filter(personIds = listOf(personId))
                )
            } returns listOf(pendingMemberModel, activeMemberModel, canceledMemberModel)

            val result = memberService.findByPersonIds(listOf(personId))
            assertThat(result).isSuccessWithData(listOf(activeMember))

            coVerifyOnce { memberInternalService.findByFiltersWithoutPagination(any()) }
            coVerify { productService wasNot called }
            coVerify { kafkaProducerService wasNot called }
            coVerify { memberProductPriceService wasNot called }
            coVerify { memberLightweightDataService wasNot called }
            confirmVerified(memberInternalService)
        }

    @Test
    fun `#findByPersonIds returns member without price listing found by ids, prioritizing PENDING member`() =
        runBlocking {
            val personId = PersonId()
            val canceledMember = TestModelFactory.buildMember(personId = personId, status = CANCELED)
            val canceledMemberModel = canceledMember.toModel()
            val pendingMember = TestModelFactory.buildMember(personId = personId, status = PENDING)
            val pendingMemberModel = pendingMember.toModel()

            coEvery {
                memberInternalService.findByFiltersWithoutPagination(
                    Filter(personIds = listOf(personId))
                )
            } returns listOf(canceledMemberModel, pendingMemberModel)

            val result = memberService.findByPersonIds(listOf(personId))
            assertThat(result).isSuccessWithData(listOf(pendingMember))

            coVerifyOnce { memberInternalService.findByFiltersWithoutPagination(any()) }
            coVerify { productService wasNot called }
            coVerify { kafkaProducerService wasNot called }
            coVerify { memberProductPriceService wasNot called }
            coVerify { memberLightweightDataService wasNot called }
            confirmVerified(memberInternalService)
        }

    @Test
    fun `#findByPersonIds returns member without price listing found by ids, returning CANCELED when is the only one`() =
        runBlocking {
            val personId = PersonId()
            val canceledMember = TestModelFactory.buildMember(personId = personId, status = CANCELED)
            val canceledMemberModel = canceledMember.toModel()

            coEvery {
                memberInternalService.findByFiltersWithoutPagination(
                    Filter(personIds = listOf(personId))
                )
            } returns listOf(canceledMemberModel)

            val result = memberService.findByPersonIds(listOf(personId))
            assertThat(result).isSuccessWithData(listOf(canceledMember))

            coVerifyOnce { memberInternalService.findByFiltersWithoutPagination(any()) }
            coVerify { productService wasNot called }
            coVerify { kafkaProducerService wasNot called }
            coVerify { memberProductPriceService wasNot called }
            coVerify { memberLightweightDataService wasNot called }
            confirmVerified(memberInternalService)
        }

    @Test
    fun `#getByPerson returns single member prioritized ACTIVE member`() = runBlocking {
        val personId = PersonId()
        val pendingMember = TestModelFactory.buildMember(personId = personId, status = PENDING)
        val activeMember = TestModelFactory.buildMember(personId = personId, status = ACTIVE)
        val canceledMember = TestModelFactory.buildMember(personId = personId, status = CANCELED)

        val membersList = listOf(pendingMember.toModel(), activeMember.toModel(), canceledMember.toModel())

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(personIds = listOf(personId))
            )
        } returns membersList

        val result = memberService.getCurrent(personId)
        assertThat(result).isSuccessWithData(activeMember)
    }

    @Test
    fun `#findActiveMembershipWithProductAndProviders should return Member with Product and CassiMember`() =
        runBlocking {
            val personId = PersonId()
            val cassiMember = TestModelFactory.buildCassiMember()
            val member =
                TestModelFactory.buildMember(personId = personId, status = ACTIVE, cassiMember = cassiMember)
            val memberModel = member.toModel()
            val product = TestModelFactory.buildProduct()

            coEvery {
                memberInternalService.findByFiltersWithoutPagination(
                    Filter(personIds = listOf(personId), statuses = listOf(ACTIVE))
                )
            } returns listOf(
                memberModel
            )
            coEvery { cassiMemberService.getByMemberId(member.id) } returns cassiMember
            coEvery {
                productService.getProduct(
                    member.productId,
                    ProductService.FindOptions(withPriceListing = false)
                )
            } returns product

            val expected = MemberWithProduct(member, product)

            val result =
                memberService.findActiveMembershipWithProduct(personId, FindOptions(withCassiMember = true))
            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { memberInternalService.findByFiltersWithoutPagination(any()) }
            coVerifyOnce { cassiMemberService.getByMemberId(any()) }
            coVerifyOnce { productService.getProduct(any(), any()) }
        }

    @Test
    fun `#findActiveMembershipWithProduct should return Member with Product`() = runBlocking {
        val personId = PersonId()
        val member = TestModelFactory.buildMember(personId = personId, status = ACTIVE)
        val memberModel = member.toModel()
        val product = TestModelFactory.buildProduct()

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(personIds = listOf(personId), statuses = listOf(ACTIVE))
            )
        } returns listOf(memberModel)
        coEvery {
            productService.getProduct(
                member.productId,
                ProductService.FindOptions(withPriceListing = false)
            )
        } returns product

        val expected = MemberWithProduct(member, product)

        val result = memberService.findActiveMembershipWithProduct(personId)
        assertThat(result).isSuccessWithData(expected)

        coVerifyNone { cassiMemberService.getByMemberId(member.id) }
    }

    @Test
    fun `#findMembershipsWithProductsByReferenceDate should return Members with their respective Product at the reference date`() =
        runBlocking {
            val date = LocalDate.now()

            val person1 = TestModelFactory.buildPerson()
            val person2 = TestModelFactory.buildPerson()

            val product11 = TestModelFactory.buildProduct()
            val product12 = TestModelFactory.buildProduct()
            val product21 = TestModelFactory.buildProduct()
            val product22 = TestModelFactory.buildProduct()

            val member11 = TestModelFactory.buildMember(
                personId = person1.id,
                product = product11,
                status = ACTIVE,
                activationDate = date.atBeginningOfTheDay().plusDays(1)
            )
            val member12 = TestModelFactory.buildMember(
                personId = person1.id,
                product = product12,
                status = CANCELED,
                activationDate = date.atBeginningOfTheDay().minusDays(1),
                canceledAt = date.atBeginningOfTheDay().plusDays(1)
            )

            val member21 = TestModelFactory.buildMember(
                personId = person2.id,
                product = product21,
                status = ACTIVE,
                activationDate = date.atBeginningOfTheDay().plusDays(1)
            )
            val member22 = TestModelFactory.buildMember(
                personId = person2.id,
                product = product22,
                status = CANCELED,
                activationDate = date.atBeginningOfTheDay().minusDays(1),
                canceledAt = date.atBeginningOfTheDay().plusDays(1)
            )

            coEvery {
                memberInternalService.findByFiltersWithoutPagination(
                    Filter(
                        personIds = listOf(person1.id, person2.id),
                        statuses = listOf(ACTIVE, CANCELED)
                    )
                )
            } returns listOf(
                member11.toModel(), member12.toModel(),
                member21.toModel(), member22.toModel()
            )

            coEvery {
                productService.findByIds(
                    listOf(member12.productId, member21.productId),
                    ProductService.FindOptions(withPriceListing = false, withBundles = false)
                )
            } returns listOf(product12, product21)

            val expected = listOf(
                MemberWithProduct(member12, product12),
                MemberWithProduct(member21, product21)
            )

            mockLocalDate(date) {
                val result = memberService.findMembershipWithProductValidOnDate(
                    listOf(
                        PersonWithReferenceDate(person1.id, date),
                        PersonWithReferenceDate(person2.id, date.plusDays(2))
                    )
                )

                assertThat(result).isSuccessWithData(expected)
            }

            coVerifyOnce {
                memberInternalService.findByFiltersWithoutPagination(any())
                productService.findByIds(any(), any())
            }

        }

    @Test
    fun `#findMembershipWithProduct should return pending Member with Product`() = runBlocking {
        val membersList = listOf(memberModel)

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(personIds = listOf(person.id))
            )
        } returns membersList
        coEvery { productService.getProductWithProviders(member.productId) } returns productWithProviders


        val result = memberService.findMembershipWithProduct(person.id)
        assertThat(result).isSuccessWithData(memberWithProductWithBundlesWithProviders)

        coVerifyOnce { memberInternalService.findByFiltersWithoutPagination(any()) }
        coVerifyOnce { productService.getProductWithProviders(any()) }
    }

    @Test
    fun `#findMembershipWithProduct should return active Member with Product`() = runBlocking {
        val membersList = listOf(memberModel)

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(personIds = listOf(person.id))
            )
        } returns membersList
        coEvery { productService.getProductWithProviders(member.productId) } returns productWithProviders

        val result = memberService.findMembershipWithProduct(person.id)
        assertThat(result).isSuccessWithData(memberWithProductWithBundlesWithProviders)

        coVerifyOnce { memberInternalService.findByFiltersWithoutPagination(any()) }
        coVerifyOnce { productService.getProductWithProviders(any()) }
    }

    @Nested
    inner class ReactivateMemberById {
        @Test
        fun `#should not reactivate when a member is already activated`() = runBlocking {
            val activeMember = TestModelFactory.buildMember(person.id).copy(
                activationDate = LocalDateTime.now(),
                status = ACTIVE
            )
            val activeMemberModel = activeMember.toModel()

            coEvery { memberInternalService.get(any()) } returns activeMemberModel
            coEvery { memberInternalService.findByFiltersWithoutPagination(any()) } returns listOf(activeMemberModel)

            val reactivationMemberResult = memberService.reactivateMemberById(activeMember.id)
            assertThat(reactivationMemberResult).isFailureOfType(MembershipMustBeAptToBeReactivatedException::class)

            coVerifyNone { memberInternalService.update(any()) }
            coVerifyNone { memberInternalService.add(any()) }
        }

        @Test
        fun `#should not reactivate when current member is different from member requested`() =
            runBlocking {
                val activeMember = TestModelFactory.buildMember(person.id).copy(
                    activationDate = LocalDateTime.now(),
                    status = ACTIVE
                )
                val activeMemberModel = activeMember.toModel()

                val currentMember = TestModelFactory.buildMember(person.id).copy(
                    activationDate = LocalDateTime.now(),
                    status = ACTIVE
                )
                val currentMemberModel = currentMember.toModel()

                coEvery { memberInternalService.get(any()) } returns activeMemberModel
                coEvery { memberInternalService.findByFiltersWithoutPagination(any()) } returns listOf(currentMemberModel)

                val reactivationMemberResult = memberService.reactivateMemberById(activeMember.id)
                assertThat(reactivationMemberResult).isFailureOfType(MembershipMustBeAptToBeReactivatedException::class)

                coVerifyNone { memberInternalService.update(any()) }
                coVerifyNone { memberInternalService.add(any()) }
            }

        @Test
        fun `#should create a new member B2C dependent based on a canceled member`() = runBlocking {
            val canceledMember = TestModelFactory.buildMember(
                person.id, status = CANCELED, parentMember = RangeUUID.generate()
            )
            val canceledMemberModel = canceledMember.toModel()
            val memberProductPrice = TestModelFactory.buildMemberProductPrice(memberId = canceledMember.id)
            val pendingMember = canceledMember.copy(id = RangeUUID.generate(), status = PENDING)
            val pendingMemberModel = pendingMember.toModel()
            val expectedResult = pendingMember.copy(activationDate = LocalDateTime.now(), status = ACTIVE)
            val expectedResultModel = expectedResult.toModel()
            val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)
            val product = TestModelFactory.buildProduct(id = expectedResult.selectedProduct.id)

            coEvery { productService.getProduct(product.id) } returns product
            coEvery { cassiMemberService.createCassiMember(any()) } returns cassiMember
            coEvery { memberInternalService.get(any()) } returns canceledMemberModel
            coEvery { memberInternalService.findByFiltersWithoutPagination(any()) } returns listOf(canceledMemberModel)
            coEvery { memberProductPriceService.findCurrent(canceledMember.id) } returns memberProductPrice
            coEvery { memberInternalService.add(any()) } returns pendingMemberModel
            coEvery {
                memberProductPriceService.setProductPriceListing(
                    any(),
                    any(),
                    any()
                )
            } returns memberProductPrice
            coEvery {
                kafkaProducerService.produce(match { it.name == "MEMBER-CREATED" })
            } returns ProducerResult(
                producedAt = LocalDateTime.now(),
                topic = MemberCreatedEvent.name,
                offset = 1L
            )
            coEvery { memberInternalService.update(any()) } returns expectedResultModel
            coEvery {
                kafkaProducerService.produce(match { it.name == "MEMBER-UPDATED" })
            } returns ProducerResult(
                producedAt = LocalDateTime.now(),
                topic = MemberUpdatedEvent.name,
                offset = 1L
            )
            coEvery {
                kafkaProducerService.produce(match { it.name == "activated-member" })
            } returns ProducerResult(
                producedAt = LocalDateTime.now(),
                topic = MemberActivatedEvent.name,
                offset = 1L
            )

            val reactivatedMember = memberService.reactivateMemberById(canceledMember.id).get()
            assertFalse(reactivatedMember.isCanceled)

            coVerifyOnce { kafkaProducerService.produce(match { it.name == "MEMBER-CREATED" }) }
            coVerifyOnce { kafkaProducerService.produce(match { it.name == "activated-member" }) }
            coVerifyOnce {
                memberProductPriceService.setProductPriceListing(
                    pendingMember.id,
                    memberProductPrice.productPriceListingId,
                    any()
                )
            }
        }

        @Test
        fun `#should create a new member B2B based on a canceled member`() = runBlocking {
            val canceledMember =
                TestModelFactory.buildMember(person.id, status = CANCELED, productType = ProductType.B2B)
            val canceledMemberModel = canceledMember.toModel()
            val pendingMember = canceledMember.copy(id = RangeUUID.generate(), status = PENDING)
            val pendingMemberModel = pendingMember.toModel()
            val expectedResult = pendingMember.copy(activationDate = LocalDateTime.now(), status = ACTIVE)
            val expectedResultModel = expectedResult.toModel()
            val cassiMember = TestModelFactory.buildCassiMember(memberId = member.id)
            val product = TestModelFactory.buildProduct(id = expectedResult.selectedProduct.id)

            coEvery { productService.getProduct(product.id) } returns product
            coEvery { cassiMemberService.createCassiMember(any()) } returns cassiMember
            coEvery { memberInternalService.get(any()) } returns canceledMemberModel
            coEvery { memberInternalService.findByFiltersWithoutPagination(any()) } returns listOf(canceledMemberModel)
            coEvery { memberInternalService.add(any()) } returns pendingMemberModel
            coEvery {
                kafkaProducerService.produce(match { it.name == "MEMBER-CREATED" })
            } returns ProducerResult(
                producedAt = LocalDateTime.now(),
                topic = MemberCreatedEvent.name,
                offset = 1L
            )
            coEvery { memberInternalService.update(any()) } returns expectedResultModel
            coEvery {
                kafkaProducerService.produce(match { it.name == "MEMBER-UPDATED" })
            } returns ProducerResult(
                producedAt = LocalDateTime.now(),
                topic = MemberUpdatedEvent.name,
                offset = 1L
            )
            coEvery {
                kafkaProducerService.produce(match { it.name == "activated-member" })
            } returns ProducerResult(
                producedAt = LocalDateTime.now(),
                topic = MemberActivatedEvent.name,
                offset = 1L
            )

            val reactivatedMember = memberService.reactivateMemberById(canceledMember.id).get()
            assertFalse(reactivatedMember.isCanceled)

            coVerifyOnce { kafkaProducerService.produce(match { it.name == "MEMBER-CREATED" }) }
            coVerifyOnce { kafkaProducerService.produce(match { it.name == "activated-member" }) }
            coVerifyNone {
                memberProductPriceService.setProductPriceListing(
                    any(),
                    any(),
                    any()
                )
            }
        }

        @Test
        fun `#should reactive current membership, generate mlc event and send MemberReactivatedEvent`() = runBlocking {
            val canceledMember =
                TestModelFactory.buildMember(person.id, status = CANCELED, productType = ProductType.B2B)
            val canceledMemberModel = canceledMember.toModel()
            val expectedResult = canceledMember.copy(status = ACTIVE)
            val expectedResultModel = expectedResult.toModel()
            val cassiMember = TestModelFactory.buildCassiMember(memberId = UUID.randomUUID())

            val mlcEvent = TestModelFactory.buildMemberLifeCycleEvents(
                memberId = canceledMember.id,
                type = MemberLifecycleEventType.REACTIVATION,
                reason = MemberLifecycleReasonEvents.COURT_ORDER,
                actionAt = LocalDate.now()
            )

            coEvery { memberInternalService.get(any()) } returns canceledMemberModel
            coEvery { memberInternalService.findByFiltersWithoutPagination(any()) } returns listOf(canceledMemberModel)
            coEvery { memberInternalService.update(any()) } returns expectedResultModel
            coEvery { memberLifeCycleEventsService.create(any()) } returns mlcEvent
            coEvery { cassiMemberService.resetCassiMember(any(), any()) } returns cassiMember
            coEvery { productService.getProduct(any()) } returns product.copy(hasNationalCoverage = true)

            coEvery {
                kafkaProducerService.produce(match { it.name == "member-reactivated" })
            } returns ProducerResult(
                producedAt = LocalDateTime.now(),
                topic = MemberReactivatedEvent.name,
                offset = 1L
            )

            coEvery {
                kafkaProducerService.produce(match { it.name == "MEMBER-UPDATED" })
            } returns ProducerResult(
                producedAt = LocalDateTime.now(),
                topic = MemberUpdatedEvent.name,
                offset = 1L
            )

            val reactivatedMember = memberService.reactivateMemberById(canceledMember.id, mlcEvent).get()
            assertTrue(reactivatedMember.active)

            coVerifyOnce {
                productService.getProduct(any())
                cassiMemberService.resetCassiMember(any(), any())
                kafkaProducerService.produce(match { it.name == "member-reactivated" })
                kafkaProducerService.produce(match { it.name == "MEMBER-UPDATED" })
            }
            coVerifyNone {
                memberProductPriceService.setProductPriceListing(
                    any(),
                    any(),
                    any()
                )
            }
        }

    }

    @Nested
    inner class CancelAndCreateNewMember {
        @Test
        fun `#should cancel an active membership and create a new one`() = runBlocking {
            val activeMembership = TestModelFactory.buildMember(person.id, status = ACTIVE)
            val activeMembershipModel = activeMembership.toModel()
            val previousMembership = activeMembership.cancel()
            val previousMembershipModel = previousMembership.toModel()
            val newMember = TestModelFactory.buildMember(personId = person.id, status = ACTIVE)
            val newMemberModel = newMember.toModel()
            val product = TestModelFactory.buildProduct()

            val addedMemberSlot = slot<MemberModel>()
            val canceledMemberSlot = slot<MemberModel>()

            coEvery { memberInternalService.get(any()) } returns activeMembershipModel
            coEvery { memberInternalService.update(capture(canceledMemberSlot)) } returns previousMembershipModel
            coEvery { memberInternalService.add(capture(addedMemberSlot)) } returns newMemberModel
            coEvery { productService.getProduct(any(), any()) } returns product
            coEvery {
                memberProductPriceService.setCurrent(any(), any(), any())
            } returns TestModelFactory.buildMemberProductPrice()
            coEvery {
                cassiMemberService.copyOrCreateCassiMember(any(), any(), any())
            } returns TestModelFactory.buildCassiMember()
            coEvery { kafkaProducerService.produce(any()) } returns mockk()

            val newMembership = memberService.cancelAndCreateNewMember(activeMembership.id)

            val capturedCanceledMember = canceledMemberSlot.captured
            val capturedMember = addedMemberSlot.captured
            val now = LocalDateTime.now()

            assertThat(newMembership).isSuccessWithData(newMember)
            assertThat(capturedMember.activationDate).isEqualTo(now.atBeginningOfTheDay().plusHours(3))
            assertThat(capturedMember.statusHistory).hasSize(1)
            assertThat(capturedCanceledMember.canceledAt).isEqualTo(
                now.minusDays(1).atEndOfTheDay()
            )
            assertThat(capturedMember.beneficiaryId).isNull()

            coVerifyOnce { memberInternalService.get(activeMembership.id) }
            coVerifyOnce { memberInternalService.update(match { it.status == CANCELED }) }
            coVerifyOnce { memberInternalService.add(match { it.id != activeMembership.id }) }
            coVerifyOnce { productService.getProduct(newMember.productId, any()) }
            coVerifyOnce { memberProductPriceService.setCurrent(newMember.id, newMember.productId, any()) }
            coVerifyOnce { cassiMemberService.copyOrCreateCassiMember(previousMembership.id, newMember.id, product) }
            coVerifyOnce { kafkaProducerService.produce(match { it.name == MemberUpdatedEvent.name }) }
        }

        @Test
        fun `#should cancel an active membership and create a new one with correct canceledAt`() = runBlocking {
            val activationDate = LocalDateTime.now()
            val expectedCanceledAt = activationDate.minusDays(1).atEndOfTheDay()
            val activeMembership = TestModelFactory.buildMember(
                person.id,
                status = ACTIVE,
                activationDate = LocalDateTime.now().minusDays(100)
            )
            val activeMembershipModel = activeMembership.toModel()
            val previousMembership = activeMembership.cancel(expectedCanceledAt)
            val previousMembershipModel = previousMembership.toModel()
            val newMember = TestModelFactory.buildMember(personId = person.id, status = ACTIVE)
            val newMemberModel = newMember.toModel()
            val product = TestModelFactory.buildProduct()

            val addedMemberSlot = slot<MemberModel>()
            val canceledMemberSlot = slot<MemberModel>()

            coEvery { memberInternalService.get(any()) } returns activeMembershipModel
            coEvery { memberInternalService.update(capture(canceledMemberSlot)) } returns previousMembershipModel
            coEvery { memberInternalService.add(capture(addedMemberSlot)) } returns newMemberModel
            coEvery { productService.getProduct(any(), any()) } returns product
            coEvery {
                memberProductPriceService.setCurrent(any(), any(), any())
            } returns TestModelFactory.buildMemberProductPrice()
            coEvery {
                cassiMemberService.copyOrCreateCassiMember(any(), any(), any())
            } returns TestModelFactory.buildCassiMember()
            coEvery { kafkaProducerService.produce(any()) } returns mockk()

            val newMembership = memberService.cancelAndCreateNewMember(activeMembership.id, activationDate)

            val capturedCanceledMember = canceledMemberSlot.captured
            val capturedMember = addedMemberSlot.captured
            val now = LocalDateTime.now()

            assertThat(newMembership).isSuccessWithData(newMember)
            assertThat(capturedMember.activationDate).isEqualTo(activationDate)
            assertThat(capturedMember.statusHistory).hasSize(1)
            assertThat(capturedCanceledMember.canceledAt).isEqualTo(
                expectedCanceledAt
            )

            coVerifyOnce { memberInternalService.get(activeMembership.id) }
            coVerifyOnce { memberInternalService.update(match { it.status == CANCELED }) }
            coVerifyOnce { memberInternalService.add(match { it.id != activeMembership.id }) }
            coVerifyOnce { productService.getProduct(newMember.productId, any()) }
            coVerifyOnce { memberProductPriceService.setCurrent(newMember.id, newMember.productId, any()) }
            coVerifyOnce { cassiMemberService.copyOrCreateCassiMember(previousMembership.id, newMember.id, product) }
            coVerifyOnce { kafkaProducerService.produce(match { it.name == MemberUpdatedEvent.name }) }
        }

        @Test
        fun `#should cancel a pending membership and create a new one`() = runBlocking {
            val pendingMembership = TestModelFactory.buildMember(person.id, status = PENDING)
            val pendingMembershipModel = pendingMembership.toModel()
            val previousMembership = pendingMembership.cancel()
            val previousMembershipModel = previousMembership.toModel()
            val newMember = TestModelFactory.buildMember(personId = person.id, status = PENDING)
            val newMemberModel = newMember.toModel()
            val product = TestModelFactory.buildProduct()

            val addedMemberSlot = slot<MemberModel>()
            val canceledMemberSlot = slot<MemberModel>()

            coEvery { memberInternalService.get(any()) } returns pendingMembershipModel
            coEvery { memberInternalService.update(capture(canceledMemberSlot)) } returns previousMembershipModel
            coEvery { memberInternalService.add(capture(addedMemberSlot)) } returns newMemberModel
            coEvery { productService.getProduct(any(), any()) } returns product
            coEvery {
                memberProductPriceService.setCurrent(any(), any(), any())
            } returns TestModelFactory.buildMemberProductPrice()
            coEvery {
                cassiMemberService.copyOrCreateCassiMember(any(), any(), any())
            } returns TestModelFactory.buildCassiMember()
            coEvery { kafkaProducerService.produce(any()) } returns mockk()

            val newMembership = memberService.cancelAndCreateNewMember(pendingMembership.id)

            val capturedCanceledMember = canceledMemberSlot.captured
            val capturedMember = addedMemberSlot.captured
            val now = LocalDateTime.now()

            assertThat(newMembership).isSuccessWithData(newMember)
            assertThat(capturedMember.activationDate).isNull()
            assertThat(capturedMember.statusHistory).hasSize(1)
            assertThat(capturedCanceledMember.canceledAt).isEqualTo(
                now.minusDays(1).atEndOfTheDay()
            )

            coVerifyOnce { memberInternalService.get(pendingMembership.id) }
            coVerifyOnce { memberInternalService.update(match { it.status == CANCELED }) }
            coVerifyOnce { memberInternalService.add(match { it.id != pendingMembership.id }) }
            coVerifyOnce { productService.getProduct(newMember.productId, any()) }
            coVerifyOnce { memberProductPriceService.setCurrent(newMember.id, newMember.productId, any()) }
            coVerifyOnce { cassiMemberService.copyOrCreateCassiMember(previousMembership.id, newMember.id, product) }
            coVerifyOnce { kafkaProducerService.produce(match { it.name == MemberUpdatedEvent.name }) }
        }

        @Test
        fun `#should cancel an active membership and create a new one when the canceled membership is B2B and the contract started at is after the cancellation date`() =
            runBlocking {
                val activeMembership =
                    TestModelFactory.buildMember(person.id, status = ACTIVE, productType = ProductType.B2B)
                val activeMembershipModel = activeMembership.toModel()
                val previousMembership = activeMembership.cancel()
                val previousMembershipModel = previousMembership.toModel()
                val newMember = TestModelFactory.buildMember(personId = person.id, status = ACTIVE)
                val newMemberModel = newMember.toModel()
                val product = TestModelFactory.buildProduct()
                val contractStartedAt = LocalDate.now()
                val addedMemberSlot = slot<MemberModel>()
                val canceledMemberSlot = slot<MemberModel>()

                coEvery { memberInternalService.get(any()) } returns activeMembershipModel
                coEvery { memberInternalService.update(capture(canceledMemberSlot)) } returns previousMembershipModel
                coEvery { memberInternalService.add(capture(addedMemberSlot)) } returns newMemberModel
                coEvery { productService.getProduct(any(), any()) } returns product
                coEvery {
                    memberProductPriceService.setCurrent(any(), any(), any())
                } returns TestModelFactory.buildMemberProductPrice()
                coEvery {
                    cassiMemberService.copyOrCreateCassiMember(any(), any(), any())
                } returns TestModelFactory.buildCassiMember()
                coEvery { kafkaProducerService.produce(any()) } returns mockk()
                coEvery { beneficiaryService.getCompanyContractStarted(activeMembership.id) } returns contractStartedAt

                val newMembership = memberService.cancelAndCreateNewMember(activeMembership.id)

                val capturedCanceledMember = canceledMemberSlot.captured
                val capturedMember = addedMemberSlot.captured
                val now = LocalDateTime.now()

                assertThat(newMembership).isSuccessWithData(newMember)
                assertThat(capturedMember.activationDate).isEqualTo(now.atBeginningOfTheDay().plusHours(3))
                assertThat(capturedMember.statusHistory).hasSize(1)
                assertThat(capturedCanceledMember.canceledAt).isEqualTo(now.atEndOfTheDay())

                coVerifyOnce { memberInternalService.get(activeMembership.id) }
                coVerifyOnce { memberInternalService.update(match { it.status == CANCELED }) }
                coVerifyOnce { memberInternalService.add(match { it.id != activeMembership.id }) }
                coVerifyOnce { productService.getProduct(newMember.productId, any()) }
                coVerifyOnce { memberProductPriceService.setCurrent(newMember.id, newMember.productId, any()) }
                coVerifyOnce {
                    cassiMemberService.copyOrCreateCassiMember(
                        previousMembership.id,
                        newMember.id,
                        product
                    )
                }
                coVerifyOnce { kafkaProducerService.produce(match { it.name == MemberUpdatedEvent.name }) }
            }
    }

    @Test
    fun `#findMemberCurrentAndPrevious - should return current and previous from personId`() = runBlocking {
        val member2 = member.copy(id = RangeUUID.generate())
        val memberModel2 = member2.toModel()
        val expected = MemberCurrentAndPrevious(
            current = member,
            previous = member2
        )
        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(personIds = listOf(member.personId))
            )
        } returns listOf(memberModel, memberModel2, memberModel2.copy(id = RangeUUID.generate())).success()

        val result = memberService.findMemberCurrentAndPrevious(member.personId)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { memberInternalService.findByFiltersWithoutPagination(any()) }
    }

    @Test
    fun `#findMemberCurrentAndPrevious - should return only current`() = runBlocking {
        val expected = MemberCurrentAndPrevious(
            current = member,
            previous = null
        )
        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(personIds = listOf(member.personId))
            )
        } returns listOf(memberModel).success()

        val result = memberService.findMemberCurrentAndPrevious(member.personId)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { memberInternalService.findByFiltersWithoutPagination(any()) }
    }

    @Test
    fun `#findDependsByParentMemberIdAndStatus should get dependent members if product is B2C`() = runBlocking {
        val parentMember = TestModelFactory.buildMember(
            status = ACTIVE,
            selectedProduct = TestModelFactory.buildMemberProduct(
                type = ProductType.B2C,
            ),
        )

        val dependent = TestModelFactory.buildMember(
            status = ACTIVE,
            parentMember = parentMember.id
        )
        val dependentModel = dependent.toModel()

        coEvery { memberInternalService.get(parentMember.id) } returns parentMember.toModel()

        coEvery { memberInternalService.findByFiltersWithoutPagination(
            Filter(
                parentMemberId = parentMember.id,
                statuses = listOf(ACTIVE),
            )
        ) } returns listOf(dependentModel)

        val result = memberService.findDependsByParentMemberIdAndStatus(parentMember.id, listOf(ACTIVE))

        assertThat(result).isSuccessWithData(listOf(dependent))

        coVerifyOnce {
            memberInternalService.findByFiltersWithoutPagination(any())
        }
    }

    @Test
    fun `#findDependsByParentMemberIdAndStatus should get dependent beneficiaries if product is not B2C`() = runBlocking {
        val parentMember = TestModelFactory.buildMember(
            status = ACTIVE,
            selectedProduct = TestModelFactory.buildMemberProduct(
                type = ProductType.B2B,
            ),
        )

        val dependent = TestModelFactory.buildMember(
            status = ACTIVE,
            parentMember = parentMember.id
        )
        val dependentModel = dependent.toModel()
        val dependentBeneficiary = TestModelFactory.buildBeneficiary(
            memberId = dependent.id,
            memberStatus = ACTIVE,
        )

        coEvery { memberInternalService.get(parentMember.id) } returns parentMember.toModel()

        coEvery { beneficiaryService.findByParentPerson(parentMember.personId, listOf(ACTIVE)) } returns listOf(dependentBeneficiary)

        coEvery { memberInternalService.findByFiltersWithoutPagination(any()) } returns listOf(dependentModel)

        val result = memberService.findDependsByParentMemberIdAndStatus(parentMember.id, listOf(ACTIVE))

        assertThat(result).isSuccessWithData(listOf(dependent))

    }

    @Test
    fun `#getByBeneficiaryId should return member when found`() = runBlocking {
        // Given
        val beneficiaryId = UUID.randomUUID()
        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(beneficiaryIds = listOf(beneficiaryId))
            )
        } returns listOf(memberModel).success()

        // When
        val result = memberService.getByBeneficiaryId(beneficiaryId)

        // Then
        assertThat(result).isSuccessWithData(member)
        coVerify(exactly = 1) { memberInternalService.findByFiltersWithoutPagination(any()) }
    }

    @Test
    fun `#findByBeneficiaryIds should return list of members when found`() = runBlocking {
        // Given
        val beneficiaryId = UUID.randomUUID()
        val beneficiaryIds = listOf(beneficiaryId)
        val members = listOf(memberModel)

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(beneficiaryIds = beneficiaryIds)
            )
        } returns members.success()

        // When
        val result = memberService.findByBeneficiaryIds(beneficiaryIds)

        // Then
        assertThat(result).isSuccessWithData(listOf(member))
        coVerify(exactly = 1) { memberInternalService.findByFiltersWithoutPagination(any()) }
    }

    @Test
    fun `#findByCompanyId should return list of members when found`() = runBlocking {
        // Given
        val companyId = UUID.randomUUID()
        val range = IntRange(0, 10)
        val members = listOf(memberModel)

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(companyIds = listOf(companyId)), range
            )
        } returns members.success()

        // When
        val result = memberService.findByCompanyId(companyId, range)

        // Then
        assertThat(result).isSuccessWithData(listOf(member))
        coVerify(exactly = 1) { memberInternalService.findByFiltersWithoutPagination(any(), any()) }
    }

    @Test
    fun `#findByCompanyIds should return list of members when found`() = runBlocking {
        // Given
        val companyId = UUID.randomUUID()
        val range = IntRange(0, 10)
        val members = listOf(memberModel)

        coEvery {
            memberInternalService.findByFilters(
                Filter(companyIds = listOf(companyId)), range
            )
        } returns members.success()

        // When
        val result = memberService.findByCompanyIds(listOf(companyId), range)

        // Then
        assertThat(result).isSuccessWithData(listOf(member))
        coVerify(exactly = 1) { memberInternalService.findByFilters(any(), any()) }
    }

    @Test
    fun `#findByCompanySubContractId should return list of members when found`() = runBlocking {
        // Given
        val companySubContractId = UUID.randomUUID()
        val range = IntRange(0, 10)
        val members = listOf(memberModel)

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(companySubContractIds = listOf(companySubContractId)), range
            )
        } returns members.success()

        // When
        val result = memberService.findByCompanySubContractId(companySubContractId, range)

        // Then
        assertThat(result).isSuccessWithData(listOf(member))
        coVerify(exactly = 1) { memberInternalService.findByFiltersWithoutPagination(any(), any()) }
    }

    @Test
    fun `#findByParentPersonId should return list of members when found`() = runBlocking {
        // Given
        val parentPersonId = PersonId()
        val members = listOf(memberModel)
        val statuses = listOf(MemberStatus.ACTIVE)

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(parentPersonId = parentPersonId, statuses = statuses)
            )
        } returns members.success()

        // When
        val result = memberService.findByParentPersonIdAndStatuses(parentPersonId, statuses)

        // Then
        assertThat(result).isSuccessWithData(listOf(member))
        coVerify(exactly = 1) { memberInternalService.findByFiltersWithoutPagination(any()) }
    }

    @Test
    fun `#findByParentBeneficiaryId should return list of members when found with onlyDirectDependents=false`() = runBlocking {
        // Given
        val parentBeneficiaryId = UUID.randomUUID()
        val onlyDirectDependents = false
        val members = listOf(memberModel)

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(parentBeneficiaryId = parentBeneficiaryId)
            )
        } returns members.success()

        // When
        val result = memberService.findByParentBeneficiaryId(parentBeneficiaryId, onlyDirectDependents)

        // Then
        assertThat(result).isSuccessWithData(listOf(member))
        coVerify(exactly = 1) { memberInternalService.findByFiltersWithoutPagination(any()) }
    }

    @Test
    fun `#findByParentBeneficiaryId should filter direct dependents when onlyDirectDependents=true`() = runBlocking {
        // Given
        val parentBeneficiaryId = UUID.randomUUID()
        val onlyDirectDependents = true
        val directDependentMember = TestModelFactory.buildMember(
            beneficiary = MemberBeneficiary(
                type = BeneficiaryType.DEPENDENT,
                activatedAt = LocalDateTime.now(),
                parentBeneficiaryRelationType = ParentBeneficiaryRelationType.SPOUSE,
            )
        )

        val indirectDependentMember = TestModelFactory.buildMember(
            beneficiary = MemberBeneficiary(
                type = BeneficiaryType.DEPENDENT,
                activatedAt = LocalDateTime.now(),
                parentBeneficiaryRelationType = ParentBeneficiaryRelationType.GRANDCHILD,
            )
        )

        val members = listOf(directDependentMember.toModel(), indirectDependentMember.toModel())

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(parentBeneficiaryId = parentBeneficiaryId)
            )
        } returns members.success()

        // When
        val result = memberService.findByParentBeneficiaryId(parentBeneficiaryId, onlyDirectDependents)

        // Then
        assertThat(result).isSuccessWithData(listOf(directDependentMember))
        coVerify(exactly = 1) { memberInternalService.findByFiltersWithoutPagination(any()) }
    }

    @Test
    fun `#findByCompanyIdAndPersonIds should return list of members when found`() = runBlocking {
        // Given
        val personId = PersonId()
        val companyId = UUID.randomUUID()
        val members = listOf(memberModel)

        coEvery {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(companyIds = listOf(companyId), personIds = listOf(personId))
            )
        } returns members.success()

        // When
        val result = memberService.findByCompanyIdAndPersonIds(companyId, listOf(personId))

        // Then
        assertThat(result).isSuccessWithData(listOf(member))
        coVerify(exactly = 1) { memberInternalService.findByFiltersWithoutPagination(any()) }
    }

    @Test
    fun `#countByBeneficiaryIds should return count when found`() = runBlocking {
        // Given
        val beneficiaryId = UUID.randomUUID()
        val beneficiaryIds = listOf(beneficiaryId)
        val count = 5

        coEvery {
            memberInternalService.countByFilters(
                Filter(beneficiaryIds = beneficiaryIds)
            )
        } returns count.success()

        // When
        val result = memberService.countByBeneficiaryIds(beneficiaryIds)

        // Then
        assertThat(result).isSuccessWithData(count)
        coVerify(exactly = 1) { memberInternalService.countByFilters(any()) }
    }

    @Test
    fun `#findByFilter should return list of members when found`() = runBlocking {
        // Given
        val companyId = UUID.randomUUID()
        val parentBeneficiaryId = UUID.randomUUID()
        val memberStatus = ACTIVE
        val range = IntRange(0, 10)
        val members = listOf(memberModel)

        coEvery {
            memberInternalService.findByFilters(
                Filter(
                    companyIds = listOf(companyId),
                    parentBeneficiaryId = parentBeneficiaryId,
                    statuses = listOf(ACTIVE)
                ), range
            )
        } returns members.success()

        // When
        val result = memberService.findByFilter(companyId, parentBeneficiaryId, memberStatus, range)

        // Then
        assertThat(result).isSuccessWithData(listOf(member))
        coVerify(exactly = 1) { memberInternalService.findByFilters(any(), any()) }
    }

    @Test
    fun `#findByCancellationDate should return list of members when found`() = runBlocking {
        // Given
        val startDate = LocalDate.now().minusDays(30)
        val endDate = LocalDate.now()
        val range = IntRange(0, 10)
        val members = listOf(memberModel)

        coEvery {
            memberInternalService.findByFilters(
                Filter(
                    cancellationRange = startDate.atStartOfDay()..endDate.atEndOfTheDay()
                ), range
            )
        } returns members.success()

        // When
        val result = memberService.findByCancellationDate(startDate, endDate, range)

        // Then
        assertThat(result).isSuccessWithData(listOf(member))
        coVerify(exactly = 1) { memberInternalService.findByFilters(any(), any()) }
    }

    @Test
    fun `#countByFilter should return number of members when found`() = runBlocking {
        // Given
        val companyId = UUID.randomUUID()
        val status = MemberStatus.ACTIVE
        val parentBeneficiaryId = UUID.randomUUID()

        coEvery {
            memberInternalService.countByFilters(
                Filter(
                    companyIds = listOf(companyId),
                    parentBeneficiaryId = parentBeneficiaryId,
                    statuses = listOf(status)
                )
            )
        } returns 1.success()

        // When
        val result = memberService.countByFilter(companyId, status, parentBeneficiaryId)

        // Then
        assertThat(result).isSuccessWithData(1)
        coVerify(exactly = 1) { memberInternalService.countByFilters(any()) }
    }

    @Test
    fun `#countByCompanyIdAndStatus should return number of members when found`() = runBlocking {
        // Given
        val companyId = UUID.randomUUID()
        val status = ACTIVE
        val beneficiaryType = BeneficiaryType.EMPLOYEE

        coEvery { memberInternalService.countByCompanyIdAndStatus(companyId, ACTIVE, beneficiaryType, true) } returns 1.success()

        // When
        val result = memberService.countByCompanyIdAndStatus(companyId, status, beneficiaryType, true)

        // Then
        assertThat(result).isSuccessWithData(1)
        coVerify(exactly = 1) { memberInternalService.countByCompanyIdAndStatus(any(), any(), any(), any()) }
    }

    @Test
    fun `#findByIds should return empty list when ids list is empty`() = runBlocking {
        assertThat(memberService.findByIds(emptyList()))
            .isSuccessWithData(emptyList())
    }

    @Test
    fun `#getCurrentsByIds should return empty list when ids list is empty`() = runBlocking {
        assertThat(memberService.getCurrentsByIds(emptyList()))
            .isSuccessWithData(emptyList())
    }

    @Test
    fun `#getCurrentsByPersonIds should return empty list when personIds list is empty`() = runBlocking {
        assertThat(memberService.getCurrentsByPersonIds(emptyList()))
            .isSuccessWithData(emptyList())
    }

    @Test
    fun `#findMembershipWithProductValidOnDate should return empty list when personsWithReferenceDate list is empty`() = runBlocking {
        assertThat(memberService.findMembershipWithProductValidOnDate(emptyList()))
            .isSuccessWithData(emptyList())
    }

    @Test
    fun `#findByPersonIds should return empty list when personIds list is empty`() = runBlocking {
        assertThat(memberService.findByPersonIds(emptyList()))
            .isSuccessWithData(emptyList())
    }

    @Test
    fun `#findActiveMembersByPersonIds should return empty list when personIds list is empty`() = runBlocking {
        assertThat(memberService.findActiveMembersByPersonIds(emptyList()))
            .isSuccessWithData(emptyList())
    }

    @Test
    fun `#findActiveOrPendingMembersByPersonIds should return empty list when personIds list is empty`() = runBlocking {
        assertThat(memberService.findActiveOrPendingMembersByPersonIds(emptyList()))
            .isSuccessWithData(emptyList())
    }

    @Test
    fun `#findByIdsAndStatus should return empty list when both ids and status lists are empty`() = runBlocking {
        assertThat(memberService.findByIdsAndStatus(emptyList(), listOf(ACTIVE)))
            .isSuccessWithData(emptyList())
    }

    @Test
    fun `#findByBeneficiaryIds should return empty list when beneficiaryIds list is empty`() = runBlocking {
        assertThat(memberService.findByBeneficiaryIds(emptyList()))
            .isSuccessWithData(emptyList())
    }

    @Test
    fun `#findByCompanyIds should return empty list when companyIds list is empty`() = runBlocking {
        assertThat(memberService.findByCompanyIds(emptyList(), 0..10))
            .isSuccessWithData(emptyList())
    }

    @Test
    fun `#countByBeneficiaryIds should return zero when beneficiaryIds list is empty`() = runBlocking {
        assertThat(memberService.countByBeneficiaryIds(emptyList()))
            .isSuccessWithData(0)
    }

    @Test
    fun `#countByFilter should return zero when all parameters are null`() = runBlocking {
        assertThat(memberService.countByFilter(null, null, null))
            .isSuccessWithData(0)
    }

}
