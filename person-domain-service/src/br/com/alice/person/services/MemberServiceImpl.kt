package br.com.alice.person.services

import br.com.alice.authentication.currentUserId
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CassiMemberInfo
import br.com.alice.business.client.CassiMemberService
import br.com.alice.business.client.MemberContractService
import br.com.alice.business.exceptions.CassiMemberNotCreatedException
import br.com.alice.business.model.BeneficiaryCancelation
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InternalServiceErrorException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.classSimpleName
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.coFoldError
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.foldNotFound
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.mapFirst
import br.com.alice.common.extensions.mapPair
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.pmapEachNotNull
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.BeneficiaryCancelationReason
import br.com.alice.data.layer.models.CassiMember
import br.com.alice.data.layer.models.ContractModel
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberLifeCycleEvents
import br.com.alice.data.layer.models.MemberLightweight
import br.com.alice.data.layer.models.MemberModel
import br.com.alice.data.layer.models.MemberProduct
import br.com.alice.data.layer.models.MemberProductPrice
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.MemberStatus.ACTIVE
import br.com.alice.data.layer.models.MemberStatus.CANCELED
import br.com.alice.data.layer.models.MemberStatus.PENDING
import br.com.alice.data.layer.models.MemberStatusHistoryEntryModel
import br.com.alice.data.layer.models.OnboardingContract
import br.com.alice.data.layer.models.Product
import br.com.alice.data.layer.models.withCassiMember
import br.com.alice.data.layer.models.withMemberProductPrice
import br.com.alice.data.layer.services.MemberLightweightModelDataService
import br.com.alice.data.layer.services.MemberModelDataService.Filter
import br.com.alice.data.layer.services.PersonModelDataService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.membership.client.ContractRegistry
import br.com.alice.membership.client.MemberLifeCycleEventsService
import br.com.alice.membership.client.onboarding.MemberNotFoundException
import br.com.alice.person.br.com.alice.person.exception.MemberActivationDateIsEarlierThanContractStartedDateException
import br.com.alice.person.br.com.alice.person.exception.MemberCanceledActivationException
import br.com.alice.person.br.com.alice.person.exception.SelfDependentException
import br.com.alice.person.br.com.alice.person.model.MemberCurrentAndPrevious
import br.com.alice.person.br.com.alice.person.model.PersonWithReferenceDate
import br.com.alice.person.br.com.alice.person.model.events.MemberReactivatedEvent
import br.com.alice.person.client.ActiveMembershipNotFoundException
import br.com.alice.person.client.MemberProductPriceService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.MemberService.FindOptions
import br.com.alice.person.client.MembershipAlreadyActiveMismatchException
import br.com.alice.person.client.MembershipMustBeAptToBeReactivatedException
import br.com.alice.person.client.PendingMembershipMismatchException
import br.com.alice.person.clients.B2CHolderIsNotAllowedException
import br.com.alice.person.converters.toModel
import br.com.alice.person.converters.toTransport
import br.com.alice.person.exceptions.NoFilterException
import br.com.alice.person.logics.MemberServiceLogics.filterByPriority
import br.com.alice.person.model.MemberWithProduct
import br.com.alice.person.model.MemberWithProductWithProviders
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.person.model.events.MemberCancelledEvent
import br.com.alice.person.model.events.MemberCreatedEvent
import br.com.alice.person.model.events.MemberUpdatedEvent
import br.com.alice.person.model.events.ProductChangedEvent
import br.com.alice.person.services.internal.MemberInternalService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map
import com.github.kittinunf.result.mapError
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class MemberServiceImpl(
    private val memberInternalService: MemberInternalService,
    private val personDataService: PersonModelDataService,
    private val productService: ProductService,
    private val kafkaProducerService: KafkaProducerService,
    private val memberProductPriceService: MemberProductPriceService,
    private val memberLightweightDataService: MemberLightweightModelDataService,
    private val cassiMemberService: CassiMemberService,
    private val beneficiaryService: BeneficiaryService,
    private val memberLifeCycleEventsService: MemberLifeCycleEventsService,
    private val contractRegistry: ContractRegistry,
    private val memberContractService: MemberContractService,
) : MemberService {

    override suspend fun get(id: UUID, findOptions: FindOptions) =
        memberInternalService
            .get(id)
            .flatMap { withOptions(it, findOptions) }
            .map { it.toTransport() }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findByIds(ids: List<UUID>, findOptions: FindOptions): Result<List<Member>, Throwable> =
        validateFilter(listOf(ids))
            .flatMap { memberInternalService.findByFiltersWithoutPagination(Filter(ids = ids)) }
            .flatMap { withOptions(it, findOptions) }
            .mapEach { it.toTransport() }
            .flatMapError {
                if (it is NoFilterException)
                    emptyList<Member>().success()
                else it.failure()
            }

    override suspend fun getLightweight(id: UUID): Result<MemberLightweight, Throwable> =
        memberLightweightDataService.get(id).map { it.toTransport() }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun getCurrent(personId: PersonId): Result<Member, Throwable> =
        findByPerson(personId)
            .map { filterByPriority(it) }
            .mapFirst()

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun getCurrentsByIds(ids: List<UUID>) =
        validateFilter(listOf(ids))
            .flatMap { findByIds(ids) }
            .map { filterByPriority(it) }
            .flatMapError {
                if (it is NoFilterException)
                    emptyList<Member>().success()
                else it.failure()
            }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun getCurrentsByPersonIds(ids: List<PersonId>): Result<List<Member>, Throwable> =
        validateFilter(listOf(ids))
            .flatMap { memberInternalService.findByFiltersWithoutPagination(Filter(personIds = ids)) }
            .map { members -> filterByPriority(members.map { it.toTransport() }) }
            .flatMapError {
                if (it is NoFilterException)
                    emptyList<Member>().success()
                else it.failure()
            }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findByPerson(personId: PersonId) =
        memberInternalService.findByFiltersWithoutPagination(Filter(personIds = listOf(personId)))
            .mapEach { it.toTransport() }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findByPersonAndStatus(personId: PersonId, status: MemberStatus) =
        memberInternalService.findByFiltersWithoutPagination(
            Filter(
                personIds = listOf(personId), statuses = listOf(status)
            )
        ).mapEach { it.toTransport() }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findActiveMembership(personId: PersonId, findOptions: FindOptions): Result<Member, Throwable> =
        memberInternalService.findByFiltersWithoutPagination(
            Filter(personIds = listOf(personId), statuses = listOf(ACTIVE))
        ).mapFirst().flatMap { withOptions(it, findOptions) }.map { it.toTransport() }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findActiveMembershipWithProduct(
        personId: PersonId,
        findOptions: FindOptions
    ): Result<MemberWithProduct, Throwable> =
        findActiveMembership(personId)
            .flatMap { withOptions(it.toModel(), findOptions) }
            .flatMapPair {
                productService.getProduct(
                    it.productId,
                    ProductService.FindOptions(withPriceListing = findOptions.withPriceListing)
                )
            }
            .map { (product, member) -> MemberWithProduct(member.toTransport(), product) }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findMembershipWithProductValidOnDate(
        personsWithReferenceDate: List<PersonWithReferenceDate>,
    ): Result<List<MemberWithProduct>, Throwable> = coResultOf {
        val members = validateFilter(listOf(personsWithReferenceDate))
            .flatMap { memberInternalService.findByFiltersWithoutPagination(
                Filter(
                    personIds = personsWithReferenceDate.map { it.personId },
                    statuses = listOf(ACTIVE, CANCELED)
                )
            ) }
            .flatMapError {
                if (it is NoFilterException)
                    emptyList<MemberModel>().success()
                else it.failure()
            }.get()

        val membersGrouped = members.groupBy { it.personId }
        val membersAtReferenceDate = personsWithReferenceDate.mapNotNull { (personId, date) ->
            val dateTime = date.atBeginningOfTheDay()

            membersGrouped[personId]
                ?.firstOrNull {
                    (it.activationDate?.let { activation -> dateTime >= activation } ?: false)
                            && (it.canceledAt?.let { canceled -> dateTime < canceled } ?: true)
                }
        }

        if (membersAtReferenceDate.isEmpty()) {
            return@coResultOf emptyList<MemberWithProduct>()
        }

        val productIds = membersAtReferenceDate.map { it.productId }.distinct()
        val products = productService.findByIds(
            productIds,
            ProductService.FindOptions(withPriceListing = false, withBundles = false)
        ).get()

        val productsMap = products.associateBy { it.id }
        membersAtReferenceDate.map { MemberWithProduct(it.toTransport(), productsMap[it.productId]!!) }
    }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findMembershipWithProduct(
        personId: PersonId,
        findOptions: FindOptions
    ): Result<MemberWithProductWithProviders, Throwable> =
        getCurrent(personId)
            .flatMap { withOptions(it.toModel(), findOptions) }
            .flatMapPair {
                logger.info(
                    "MemberServiceImpl::findMembershipWithProduct",
                    "person_id" to personId.toString(),
                    "is_member_active" to it.active,
                    "product_id" to it.productId,
                    "member_status" to it.status,
                    it
                )
                productService.getProductWithProviders(it.productId)
            }
            .map { (product, member) -> MemberWithProductWithProviders(member.toTransport(), product) }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findActiveOrPendingMembership(
        personId: PersonId,
        findOptions: FindOptions
    ): Result<Member, Throwable> =
        memberInternalService.findByFiltersWithoutPagination(
            Filter(personIds = listOf(personId), statuses = listOf(PENDING, ACTIVE))
        ).map { filterByPriority(it.map { it.toTransport() }) }.mapFirst()
            .flatMap { withOptions(it.toModel(), findOptions) }
            .map { it.toTransport() }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findPendingMembership(
        personId: PersonId,
        findOptions: FindOptions
    ): Result<Member, Throwable> =
        memberInternalService.findByFiltersWithoutPagination(
            Filter(personIds = listOf(personId), statuses = listOf(PENDING))
        ).mapFirst().flatMap { withOptions(it, findOptions) }.map { it.toTransport() }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findFirstMembership(personId: PersonId): Result<Member, Throwable> =
        memberInternalService.findByFiltersWithoutPagination(Filter(personIds = listOf(personId)))
            .map { members -> members.sortedBy { it.activationDate }.first() }
            .map { it.toTransport() }
            .mapError {
                NotFoundException("first_member_not_found")
            }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findByPersonIds(personIds: List<PersonId>, findOptions: FindOptions) = useReadDatabase {
        validateFilter(listOf(personIds))
            .flatMap { memberInternalService.findByFiltersWithoutPagination(Filter(personIds = personIds)) }
            .map { filterByPriority(it.map { it.toTransport() }) }
            .flatMap { withOptions(it.map { it.toModel() }, findOptions) }
            .mapEach { it.toTransport() }
            .flatMapError {
                if (it is NoFilterException)
                    emptyList<Member>().success()
                else it.failure()
            }
    }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findActiveMembersByPersonIds(personIds: List<PersonId>) =
        validateFilter(listOf(personIds))
            .flatMap { memberInternalService.findByFiltersWithoutPagination(
                Filter(personIds = personIds, statuses = listOf(ACTIVE))
            ) }.mapEach { it.toTransport() }
            .flatMapError {
                if (it is NoFilterException)
                    emptyList<Member>().success()
                else it.failure()
            }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findActiveOrPendingMembersByPersonIds(personIds: List<PersonId>) =
        validateFilter(listOf(personIds))
            .flatMap { memberInternalService.findByFiltersWithoutPagination(
                Filter(personIds = personIds, statuses = listOf(ACTIVE, PENDING))
            ) }.mapEach { it.toTransport() }
            .flatMapError {
                if (it is NoFilterException)
                    emptyList<Member>().success()
                else it.failure()
            }

    override suspend fun create(
        personId: PersonId,
        contract: OnboardingContract,
        cassiMember: CassiMemberInfo?,
    ): Result<Member, Throwable> {
        val product = productService.getProduct(contract.productId).get()

        val members = memberInternalService.findByFiltersWithoutPagination(
            Filter(
                personIds = listOf(personId),
                statuses = listOf(ACTIVE, PENDING)
            )
        ).getOrNullIfNotFound()

        val activeMember = members?.firstOrNull { it.status == ACTIVE }

        if (activeMember != null)
            return MembershipAlreadyActiveMismatchException(personId).failure()

        val currentPendingMembership = members?.firstOrNull { it.status == PENDING }

        if (currentPendingMembership != null) {
            return if (currentPendingMembership.productId != contract.productId) PendingMembershipMismatchException(
                personId
            ).failure()
            else currentPendingMembership.toTransport().success()
        }

        val newPendingMember = MemberModel(
            personId = personId,
            selectedProduct = contract.selectedProduct,
            contract = ContractModel(
                documentUrl = contract.documentUrl
            ),
            status = PENDING,
            statusHistory = listOf(
                MemberStatusHistoryEntryModel(
                    status = PENDING,
                    createdAt = LocalDateTime.now().toString()
                )
            ),
            brand = product.brand,
        ).let { it.withCassiMember(cassiMember?.toCassiMember(it.id)) }

        return addMember(newPendingMember, contract.productPriceListingId).map { it.toTransport() }
    }

    private suspend fun addMember(
        newMember: MemberModel,
        productPriceListingId: UUID?
    ): Result<MemberModel, Throwable> {
        return memberInternalService.add(newMember)
            .then { member ->
                val event = MemberCreatedEvent(payload = member.toTransport())
                logger.info("Producing event ${event.name} for member ${member.id}", "event" to event)
                kafkaProducerService.produce(event)
            }
            .andThen { member ->
                if (member.isB2C) {
                    if (productPriceListingId != null)
                        memberProductPriceService.setProductPriceListing(
                            member.id,
                            productPriceListingId,
                            LocalDateTime.now()
                        )
                    else
                        memberProductPriceService.setCurrent(
                            member.id,
                            member.productId,
                            LocalDateTime.now()
                        )
                } else {
                    logger.info(
                        "MemberServiceImpl::addMember - skipping the MPPL creation when the member is B2B or ADESAO",
                        "person_id" to member.personId,
                        "member_id" to member.id,
                        "product_id" to member.productId,
                        "type" to member.productType,
                    )
                    true.success()
                }
            }
            .andThen {
                cassiMemberService.createCassiMember(it.toTransport())
                    .coFoldError(
                        CassiMemberNotCreatedException::class to br.com.alice.common.core.suspend() { _: Throwable -> true.success() })
            }
            .then {
                logger.info(
                    "MemberServiceImpl.addMember: Added member", "person_id" to newMember.personId,
                    "member_id" to newMember.id, "current_user_id" to currentUserId()
                )
            }
            .thenError {
                logger.info(
                    "MemberServiceImpl.addMember: Added member error", "person_id" to newMember.personId,
                    "member_id" to newMember.id, "current_user_id" to currentUserId()
                )
            }
            .flatMap { get(it.id) }
            .map { it.toModel() }
    }

    override suspend fun activateMember(member: Member, activationDate: LocalDateTime?) =
        coResultOf<Member, Throwable> {
            logger.info(
                "MemberServiceImpl.activateMember - starting",
                "id" to member.id,
                "person_id" to member.personId,
                "custom_activation_date" to activationDate,
                "member_status" to member.status
            )

            if (member.active) {
                return@coResultOf member
            } else if (!member.isPending) {
                throw MemberCanceledActivationException(member.personId, member.id)
            }

            val contractStartedAt: LocalDate? = if (member.isB2BOrAdesao) {
                getContractStartedAtByMember(member.id)
                    .thenError { logger.error("Member ${member.id} cannot be activated once the company does not have a contract start date") }
                    .get()
            } else {
                null
            }

            member.validateMemberActivation()
                .flatMap { it.activate(contractStartedAt, activationDate) }
                .flatMap { update(it) }
                .then {
                    kafkaProducerService.produce(MemberActivatedEvent(it))
                    logger.info(
                        "MemberServiceImpl.activateMember: Activated member",
                        "person_id" to member.personId,
                        "member_id" to member.id,
                        "current_user_id" to currentUserId()
                    )
                }
                .mapError { error ->
                    if (error is IllegalArgumentException)
                        MemberActivationDateIsEarlierThanContractStartedDateException(
                            error.message
                                ?: "Member activation date cannot be earlier than contractStartedAt - $contractStartedAt"
                        )
                    else error
                }.get()

        }

    private suspend fun Member.validateMemberActivation(): Result<Member, Throwable> {
        if (this.isB2C && this.parentMember == null) {
            val nationalId = personDataService.get(this.personId.toUUID()).get().nationalId
            if (FeatureService.inList(
                    namespace = FeatureNamespace.MEMBERSHIP,
                    key = "skip_b2c_holder_block",
                    testValue = nationalId,
                    defaultReturn = false
                )
            ) {
                return this.success()
            }
            return B2CHolderIsNotAllowedException().failure()
        }
        return this.success()
    }

    override suspend fun reactivateMemberById(id: UUID, reactivationParams: MemberLifeCycleEvents?) =
        get(id)
            .flatMapPair { getCurrent(it.personId) }
            .flatMap { (currentMember, member) -> shouldReactivateMember(member, currentMember) }
            .flatMap { member -> handleReactivationWorkflow(member, reactivationParams) }

    private suspend fun handleReactivationWorkflow(
        member: Member,
        params: MemberLifeCycleEvents?
    ): Result<Member, Throwable> {
        logger.info(
            "MemberServiceImpl.handleReactivationWorkflow",
            "person_id" to member.personId,
            "member_id" to member.id,
            "params" to params
        )
        return when (params) {
            null -> handleDuplicateMembershipReactivation(member.toModel()).map { it.toTransport() }
            else -> handleOriginalMembershipReactivation(member, params)
        }
    }

    private suspend fun handleDuplicateMembershipReactivation(member: MemberModel): Result<MemberModel, Throwable> =
        createFromMember(member)
            .flatMap { it.reactivate(changeActivationDate = true) }
            .flatMap { update(it) }
            .then {
                produceReactivationEvent(it, MemberActivatedEvent(it))
                logger.info("MemberServiceImpl::handleDuplicateMembershipReactivation - Member reactivated with with duplicate membership parameters")
            }.map { it.toModel() }

    private suspend fun handleOriginalMembershipReactivation(
        member: Member,
        params: MemberLifeCycleEvents
    ): Result<Member, Throwable> =
        generateLifeCycleEvent(params)
            .flatMap { member.reactivate(changeActivationDate = false) }
            .flatMap { update(it.copy(canceledAt = null)) }
            .flatMap { resetCassiMember(it) }
            .then {
                produceReactivationEvent(it, MemberReactivatedEvent(it))
                logger.info("MemberServiceImpl::handleOriginalMembershipReactivation - Member reactivated with original membership parameters")
            }

    private suspend fun produceReactivationEvent(member: Member, event: NotificationEvent<*>) {
        logger.info(
            "MemberServiceImpl::produceReactivationEvent - Member reactivation event produced",
            "person_id" to member.personId,
            "member_id" to member.id,
            "current_user_id" to currentUserId(),
            "event_type" to event.classSimpleName()
        )
        kafkaProducerService.produce(event)
    }


    private fun shouldReactivateMember(member: Member, currentMember: Member) =
        if (member.isCanceled && currentMember.id == member.id)
            member.success()
        else
            MembershipMustBeAptToBeReactivatedException(member.personId, member.status.toString()).failure()

    private suspend fun generateLifeCycleEvent(params: MemberLifeCycleEvents) =
        memberLifeCycleEventsService.create(params)

    private suspend fun resetCassiMember(member: Member): Result<Member, Throwable> =
        productService.getProduct(member.productId).map {
            if (it.hasNationalCoverage) {
                cassiMemberService.resetCassiMember(memberId = member.id, productId = member.selectedProduct.id).get()
            }
            member
        }

    override suspend fun cancelById(id: UUID): Result<Member, Throwable> = get(id).flatMap { cancel(it) }

    override suspend fun cancel(member: Member, canceledAt: LocalDateTime): Result<Member, Throwable> =
        if (member.isCanceled) member.success()
        else update(member.cancel(canceledAt))
            .then {
                logger.info("Producing event ${MemberCancelledEvent.name} for member ${it.id}")
                kafkaProducerService.produce(MemberCancelledEvent(it))
                logger.info(
                    "MemberServiceImpl.cancel: Canceled member", "person_id" to member.personId,
                    "member_id" to member.id, "current_user_id" to currentUserId()
                )
            }


    private suspend fun createFromMember(member: MemberModel): Result<Member, Throwable> = coroutineScope {
        val productPriceListingIdDeferred =
            async {
                if (member.isB2C) memberProductPriceService.findCurrent(member.id)
                    .get().productPriceListingId else null
            }
        val brandDeferred =
            async { member.brand?.let { productService.getProduct(member.productId).get().brand } }

        val productPriceListingId = productPriceListingIdDeferred.await()
        val brand = brandDeferred.await()

        val newPendingMember = MemberModel(
            personId = member.personId,
            parentMember = member.parentMember,
            parentPerson = member.parentPerson,
            selectedProduct = member.selectedProduct,
            contract = member.contract,
            status = PENDING,
            statusHistory = listOf(
                MemberStatusHistoryEntryModel(
                    status = PENDING,
                    createdAt = LocalDateTime.now().toString()
                )
            ),
            brand = brand,
        )

        addMember(newPendingMember, productPriceListingId).map { it.toTransport() }
    }

    private suspend fun MemberModel.getRightCanceledAt() =
        LocalDateTime.now().minusDays(1).atEndOfTheDay().let { canceledAt ->
            logger.info(
                "MemberService::Member.getRightCanceledAt",
                "member_id" to this.id,
                "person_id" to this.personId,
                "canceledAt" to canceledAt,
                "member_status" to this.active,
                "member_type" to this.productType,
            )

            if (this.isB2BOrAdesao && this.active) {
                getContractStartedAtByMember(this.id)
                    .thenError {
                        logger.error(
                            "MemberService::changeProduct: It is not possible found the contract started at",
                            "member_id" to this.id,
                            "person_id" to this.personId,
                        )
                    }
                    .map { contractStated ->
                        contractStated.atBeginningOfTheDay().takeIf { it.isAfter(canceledAt) } ?: canceledAt
                    }.getOrElse { canceledAt }
            } else {
                canceledAt
            }
        }

    override suspend fun changeProduct(
        personId: PersonId,
        product: Product,
        keepStatus: Boolean,
        activationDate: LocalDateTime?
    ): Result<Member, Throwable> {
        val activeOrPendingMembership = findActiveOrPendingMembership(personId).getOrNullIfNotFound()
            ?: return ActiveMembershipNotFoundException(personId).failure()
        val now = LocalDate.now()

        val canceledAt = activationDate
            ?.let {
                if (it.toLocalDate().isAfter(now)) now.atEndOfTheDay() else
                    it.minusDays(1).atEndOfTheDay()
            } ?: activeOrPendingMembership.toModel().getRightCanceledAt()

        val canceledMembership = activeOrPendingMembership.cancel(canceledAt)
        val status = when {
            keepStatus && (activeOrPendingMembership.isB2C && product.isB2C || activeOrPendingMembership.isB2B) -> activeOrPendingMembership.status
            else -> PENDING
        }

        return update(canceledMembership)
            .mapPair { previousMember ->
                previousMember.toNewMember(
                    personId,
                    product,
                    status,
                    activationDate
                )
            }
            .map { (newMember, previousMember) -> memberInternalService.add(newMember).get() to previousMember }
            .then { (newMember, previousMember) ->
                cassiMemberService.copyOrCreateCassiMember(previousMember.id, newMember.id, product)
            }
            .then { (newMember, _) ->
                if (newMember.isB2C) {
                    memberProductPriceService.setCurrent(
                        newMember.id,
                        product.id,
                        LocalDateTime.now()
                    )
                } else {
                    logger.info(
                        "MemberServiceImpl::changeProduct - skipping the MPPL creation when the member is B2B or ADESAO",
                        "person_id" to newMember.personId,
                        "member_id" to newMember.id,
                        "product_id" to newMember.productId,
                        "type" to newMember.productType,
                    )

                }
            }
            .then { (newMember, previousMember) ->
                kafkaProducerService.produce(
                    ProductChangedEvent(
                        previousMember,
                        newMember.toTransport()
                    )
                )
                logger.info(
                    "MemberServiceImpl.changeProduct: Changed member product",
                    "person_id" to personId,
                    "previous_product_id" to canceledMembership.productId,
                    "new_product_id" to newMember.productId,
                    "current_user_id" to currentUserId()
                )
            }
            .map { (newMember, _) -> newMember.toTransport() }
    }

    override suspend fun cancelAndCreateNewMember(
        previousMemberId: UUID,
        activationDate: LocalDateTime?
    ): Result<Member, Throwable> {
        val previousMember = get(previousMemberId).get()
        val canceledAt = activationDate?.minusDays(1)?.atEndOfTheDay() ?: previousMember.toModel().getRightCanceledAt()
        return update(previousMember.cancel(canceledAt))
            .map {
                val activationDate = activationDate ?: LocalDateTime.now().atBeginningOfTheDay().plusHours(3)

                val previousMemberStatus = previousMember.status
                it.toNewMember(
                    previousMemberStatus,
                    if (previousMemberStatus == ACTIVE) activationDate else null
                )
            }
            .flatMap { memberInternalService.add(it.toModel()) }
            .then { member ->
                productService.getProduct(member.productId)
                    .flatMap { cassiMemberService.copyOrCreateCassiMember(previousMemberId, member.id, it) }
            }
            .then {
                if (it.isB2C) {
                    memberProductPriceService.setCurrent(
                        it.id,
                        it.productId,
                        LocalDateTime.now()
                    )
                } else {
                    logger.info(
                        "MemberServiceImpl::cancelAndCreateNewMember - skipping the MPPL creation when the member is B2B or ADESAO",
                        "person_id" to it.personId,
                        "member_id" to it.id,
                        "product_id" to it.productId,
                        "type" to it.productType,
                    )

                    true.success()
                }
            }.then {
                logger.info(
                    "MemberServiceImpl.cancelAndCreateNewMember: Canceled and created new member",
                    "person_id" to it.personId, "member_id" to it.id, "current_user_id" to currentUserId()
                )
            }.map { it.toTransport() }
    }

    private fun Member.toNewMember(
        personId: PersonId,
        newProduct: Product,
        status: MemberStatus,
        activationDate: LocalDateTime? = null
    ): MemberModel {

        logger.info(
            "MemberServiceImpl::Member.toNewMember",
            "person_id" to this.personId, "member_id" to this.id, "activation_date" to activationDate,
        )

        return MemberModel(
            personId = personId,
            selectedProduct = MemberProduct(
                id = newProduct.id,
                prices = newProduct.prices,
                priceListing = newProduct.priceListing,
                productPriceListingId = null,
                type = newProduct.type
            ),
            activationDate = if (status == ACTIVE) (activationDate
                ?: LocalDateTime.now()).atBeginningOfTheDay()
                .plusHours(3) else null,
            contract = ContractModel(
                documentUrl = this.contractDocumentUrl
            ),
            status = status,
            statusHistory = listOf(
                MemberStatusHistoryEntryModel(
                    status = status,
                    createdAt = activationDate.toString()
                )
            ),
            brand = newProduct.brand,
        )
    }

    override suspend fun cancelActiveOrPendingMembership(personId: PersonId): Result<Member, Throwable> =
        findActiveOrPendingMembership(personId)
            .coFoldNotFound { MemberNotFoundException(personId).failure() }
            .flatMap {
                if (it.isB2BOrAdesao && it.isAlice) cancelByBeneficiary(it)
                else cancel(it)
            }

    private suspend fun cancelByBeneficiary(member: Member) =
        beneficiaryService.findByMemberId(member.id)
            .flatMap {
                beneficiaryService.cancelBeneficiaryById(
                    it.id,
                    BeneficiaryCancelation(
                        canceledAt = LocalDate.now(),
                        canceledReason = BeneficiaryCancelationReason.ANOTHER,
                        canceledDescription = "Cancelamento por backoffice",
                        hasContributed = false
                    )
                )
            }.map { member }


    override suspend fun cancelPendingMembership(personId: PersonId): Result<Member, Throwable> =
        findPendingMembership(personId)
            .coFoldNotFound { MemberNotFoundException(personId).failure() }
            .flatMap { cancel(it) }

    override suspend fun findActiveLightweight(): Result<List<MemberLightweight>, Throwable> =
        memberLightweightDataService.find {
            where {
                this.status.eq(ACTIVE).and(this.archived.eq(false))
            }
        }.mapEach { it.toTransport() }

    override suspend fun findFirstActivated(personId: PersonId): Result<Member, Throwable> =
        memberInternalService.findByFiltersWithoutPagination(Filter(personIds = listOf(personId)))
            .map { members -> members.filter { it.activationDate != null }.sortedBy { it.activationDate }.first() }
            .map { it.toTransport() }
            .mapError {
                when {
                    it is NoSuchElementException -> MemberNotFoundException(personId)
                    else -> InternalServiceErrorException("Unknown error")
                }
            }

    override suspend fun update(member: Member, sendEvent: Boolean) =
        member
            .validate()
            .flatMap {
                memberInternalService.update(member.toModel())
                    .then { memberLayer ->
                        if (sendEvent) {
                            val event = MemberUpdatedEvent(member = memberLayer.toTransport())
                            logger.info(
                                "Producing event ${event.name} for member ${memberLayer.id}",
                                "event" to event
                            )
                            kafkaProducerService.produce(event)
                        } else {
                            logger.info("Member updated but the event producer was skipped", "member_id" to member.id)
                        }
                    }.map { it.toTransport() }
            }

    private fun Member.validate() =
        if (parentMember == id || parentPerson == personId) {
            SelfDependentException(id).failure()
        } else success()

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findActiveMemberByProduct(productId: UUID): Result<List<Member>, Throwable> =
        memberInternalService.findByFiltersWithoutPagination(
            Filter(
                statuses = listOf(ACTIVE),
                productId = productId
            )
        ).mapEach { it.toTransport() }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findByIdsAndStatus(
        ids: List<UUID>,
        status: List<MemberStatus>
    ): Result<List<Member>, Throwable> =
        validateFilter(listOf(ids))
            .flatMap { memberInternalService.findByFiltersWithoutPagination(
                Filter(
                    ids = ids,
                    statuses = status
                )
            ) }.mapEach { it.toTransport() }
            .flatMapError {
                if (it is NoFilterException)
                    emptyList<Member>().success()
                else it.failure()
            }

    //FIXME: should be merged in a single endpoint and range should be added
    override suspend fun findMemberCurrentAndPrevious(personId: PersonId): Result<MemberCurrentAndPrevious, Throwable> =
        memberInternalService.findByFiltersWithoutPagination(
            Filter(
                personIds = listOf(personId)
            )
        ).map { members -> members.sortedBy { it.createdAt }
            .take(2) }
            .map {
                MemberCurrentAndPrevious(
                    current = it.first().toTransport(),
                    previous = it.takeIf { it.size > 1 }?.last()?.toTransport()
                )
            }

    private suspend fun getContractStartedAtByMember(memberId: UUID) = useReadDatabase {
        beneficiaryService.getCompanyContractStarted(memberId)
    }

    private suspend fun withOptions(
        member: MemberModel,
        findOptions: FindOptions,
    ) = withPriceListing(member, findOptions.withPriceListing)
        .flatMap { withCassiMember(it, findOptions.withCassiMember) }

    private suspend fun withOptions(
        members: List<MemberModel>,
        findOptions: FindOptions,
    ) = withPriceListing(members, findOptions.withPriceListing)
        .flatMap { withCassiMember(it, findOptions.withCassiMember) }

    private suspend fun withPriceListing(
        member: MemberModel,
        withPriceListing: Boolean
    ): Result<MemberModel, Throwable> {
        return if (withPriceListing && member.brand != Brand.DUQUESA)
            memberProductPriceService.findCurrent(member.id)
                .map { member.withMemberProductPrice(it.toModel()) }
        else member.success()
    }

    private suspend fun withPriceListing(
        members: List<MemberModel>,
        withPriceListing: Boolean
    ): Result<List<MemberModel>, Throwable> =
        if (withPriceListing)
            memberProductPriceService.findCurrents(members.map { it.id })
                .map { it.associateBy(MemberProductPrice::memberId) }
                .map { map -> members.map { it.withMemberProductPrice(map[it.id]?.toModel()) } }
        else members.success()

    private suspend fun withCassiMember(
        member: MemberModel,
        withCassiMember: Boolean
    ): Result<MemberModel, Throwable> =
        if (withCassiMember)
            cassiMemberService.getByMemberId(member.id)
                .map { member.withCassiMember(it) }
                .foldNotFound { member.success() }
        else member.success()

    private suspend fun withCassiMember(
        members: List<MemberModel>,
        withCassiMember: Boolean
    ): Result<List<MemberModel>, Throwable> =
        if (withCassiMember)
            cassiMemberService.findByMemberIds(members.map { it.id })
                .map { it.associateBy(CassiMember::memberId) }
                .map { map -> members.map { it.withCassiMember(map[it.id]) } }
        else members.success()

    override suspend fun hasContractSigned(personId: PersonId) = getCurrent(personId)
        .then {
            logger.info(
                "MemberService::hasContractSigned member found",
                "member_id" to it.id,
                "member_status" to it.status,
                "member_product_type" to it.productType,
            )
        }
        .map { member ->
            if (member.active) {
                logger.info(
                    "MemberService::hasContractSigned The member is already activated so the contract",
                )

                true
            } else {
                if (member.isB2C) {
                    contractRegistry.findContract(personId)
                        .then {
                            logger.info(
                                "MemberService::hasContractSigned The B2C contract was found",
                                "is_signed" to it.alreadySigned,
                            )
                        }.getOrNullIfNotFound()?.alreadySigned ?: false
                } else {
                    memberContractService.findByMember(member.id)
                        .then {
                            logger.info(
                                "MemberService::hasContractSigned The B2B contract was found",
                                "is_signed" to it.isSigned
                            )
                        }.getOrNullIfNotFound()?.isSigned ?: false
                }
            }
        }.coFoldNotFound { false.success() }

    override suspend fun findDependsByParentMemberIdAndStatus(
        parentMemberId: UUID,
        status: List<MemberStatus>
    ): Result<List<Member>, Throwable> {
        val parent = get(parentMemberId).get()
        return if (parent.isB2C) {
            memberInternalService.findByFiltersWithoutPagination(
                Filter(
                    parentMemberId = parentMemberId,
                    statuses = status
                )
            ).mapEach { it.toTransport() }
        } else {
            beneficiaryService.findByParentPerson(parent.personId, status)
                .flatMap { dependentBeneficiaries ->
                    findByIdsAndStatus(dependentBeneficiaries.map { it.memberId }, status)
                }
        }
    }

    // Beneficiary
    override suspend fun getByBeneficiaryId(beneficiaryId: UUID) =
        memberInternalService.findByFiltersWithoutPagination(
            Filter(beneficiaryIds = listOf(beneficiaryId))
        ).mapFirst().map { it.toTransport() }

    override suspend fun findByBeneficiaryIds(beneficiaryIds: List<UUID>) =
        validateFilter(listOf(beneficiaryIds))
            .flatMap {
                memberInternalService.findByFiltersWithoutPagination(
                    Filter(beneficiaryIds = beneficiaryIds)
                )
            }.pmapEach { it.toTransport() }
            .flatMapError {
                if (it is NoFilterException)
                    emptyList<Member>().success()
                else it.failure()
            }

    override suspend fun findByCompanyId(companyId: UUID, range: IntRange?) =
        memberInternalService.findByFiltersWithoutPagination(
            Filter(companyIds = listOf(companyId)), range
        ).pmapEach { it.toTransport() }

    override suspend fun findByCompanyIds(companyIds: List<UUID>, range: IntRange) =
        validateFilter(listOf(companyIds))
            .flatMap {
                memberInternalService.findByFilters(
                    Filter(companyIds = companyIds), range
                )
            }.pmapEach { it.toTransport() }
            .flatMapError {
                if (it is NoFilterException)
                    emptyList<Member>().success()
                else it.failure()
            }

    override suspend fun findByCompanySubContractId(companySubContractId: UUID, range: IntRange?) =
        memberInternalService.findByFiltersWithoutPagination(
            Filter(companySubContractIds = listOf(companySubContractId)), range
        ).pmapEach { it.toTransport() }

    override suspend fun findByCompanyIdAndPersonIds(companyId: UUID, personIds: List<PersonId>) =
        memberInternalService.findByFiltersWithoutPagination(
            Filter(companyIds = listOf(companyId), personIds = personIds)
        ).pmapEach { it.toTransport() }

    override suspend fun findByParentPersonIdAndStatuses(parentPersonId: PersonId, statuses: List<MemberStatus>) =
        memberInternalService.findByFiltersWithoutPagination(
            Filter(parentPersonId = parentPersonId, statuses = statuses)
        ).pmapEach { it.toTransport() }

    override suspend fun findByParentBeneficiaryId(parentBeneficiaryId: UUID, onlyDirectDependents: Boolean) =
        memberInternalService.findByFiltersWithoutPagination(
            Filter(parentBeneficiaryId = parentBeneficiaryId)
        ).pmapEachNotNull {
            it.takeIf {
                !onlyDirectDependents ||
                        it.beneficiary?.parentBeneficiaryRelationType?.isDirectDependency() ?: false
            }
        }.pmapEach { it.toTransport() }

    override suspend fun countByBeneficiaryIds(beneficiaryIds: List<UUID>) =
        validateFilter(listOf(beneficiaryIds))
            .flatMap {
                memberInternalService.countByFilters(
                    Filter(beneficiaryIds = beneficiaryIds)
                )
            }
            .flatMapError {
                if (it is NoFilterException)
                    0.success()
                else it.failure()
            }

    override suspend fun findByFilter(
        companyId: UUID?,
        beneficiaryParentId: UUID?,
        status: MemberStatus?,
        range: IntRange
    ) = memberInternalService.findByFilters(
            Filter(
                companyIds = companyId?.let { listOf(companyId) } ?: emptyList(),
                parentBeneficiaryId = beneficiaryParentId,
                statuses = status?.let { listOf(it) } ?: emptyList()
            ), range
        ).pmapEach { it.toTransport() }

    override suspend fun findByCancellationDate(startDate: LocalDate, endDate: LocalDate, range: IntRange) =
        memberInternalService.findByFilters(
            Filter(
                cancellationRange = startDate.atStartOfDay()..endDate.atEndOfTheDay()
            ), range
        ).pmapEach { it.toTransport() }

    override suspend fun countByFilter(companyId: UUID?, status: MemberStatus?, parentBeneficiaryId: UUID?) =
        validateFilter(listOf(companyId, status, parentBeneficiaryId))
            .flatMap {
                memberInternalService.countByFilters(
                    Filter(
                        companyIds = companyId?.let { listOf(companyId) } ?: emptyList(),
                        parentBeneficiaryId = parentBeneficiaryId,
                        statuses = status?.let { listOf(it) } ?: emptyList()
                    )
                )
            }
            .flatMapError {
                if (it is NoFilterException)
                    0.success()
                else it.failure()
            }

    override suspend fun countByCompanyIdAndStatus(
        companyId: UUID,
        status: MemberStatus,
        type: BeneficiaryType?,
        withNoPendingCancellation: Boolean
    ) = memberInternalService.countByCompanyIdAndStatus(companyId, status, type, withNoPendingCancellation)

    private fun validateFilter(list: List<*>) =
        list.firstOrNull { item ->
            when(item) {
                null -> false
                is String -> item.isNotBlank()
                is Collection<*> -> item.filterNotNull().isNotEmpty()
                else -> true
            }
        }?.success() ?: NoFilterException().failure()
}
