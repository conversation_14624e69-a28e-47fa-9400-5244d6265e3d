package br.com.alice.person.services.internal

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.notification.NotificationEventAction
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.MemberModel
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.services.MemberModelDataService
import br.com.alice.person.events.MemberChangedEvent
import br.com.alice.person.converters.toTransport
import com.github.kittinunf.result.map
import java.util.UUID

class MemberInternalService(
    private val memberDataService: MemberModelDataService,
    private val kafkaProducerService: KafkaProducerService
) {
    suspend fun get(id: UUID) =
        memberDataService.get(id)

    @Deprecated("Use findByFilters instead")
    suspend fun findByFiltersWithoutPagination(filter: MemberModelDataService.Filter, range: IntRange? = null) = useReadDatabase {
        memberDataService.findByFiltersWithoutPagination(filter, range)
    }

    suspend fun findByFilters(filter: MemberModelDataService.Filter, range: IntRange) = useReadDatabase {
        memberDataService.findByFilters(filter, range)
    }

    suspend fun countByFilters(filter: MemberModelDataService.Filter) = useReadDatabase {
        memberDataService.countByFilters(filter)
    }

    suspend fun countByCompanyIdAndStatus(
        companyId: UUID,
        status: MemberStatus,
        type: BeneficiaryType? = null,
        withNoPendingCancellation: Boolean = false
    ) = useReadDatabase {
        memberDataService.countByCompanyIdAndStatus(companyId, status, type, withNoPendingCancellation)
    }

    suspend fun add(memberModel: MemberModel) =
        memberDataService.add(memberModel)
            .then { kafkaProducerService.produce(
                MemberChangedEvent(
                    it.toTransport(),
                    NotificationEventAction.CREATED
                ), it.id.toString()
            ) }

    suspend fun update(memberModel: MemberModel) =
        memberDataService.update(memberModel)
            .then { kafkaProducerService.produce(
                MemberChangedEvent(
                    it.toTransport(),
                    NotificationEventAction.UPDATED
                ), it.id.toString()
            ) }

    private suspend fun validateForUpdate(memberModel: MemberModel) =
        get(memberModel.id)
            .map { oldMemberModel ->
                memberModel.takeIf {
                    it.status == MemberStatus.PENDING ||
                            (it.parentMember == oldMemberModel.parentMember && it.parentPerson == oldMemberModel.parentPerson) ||
                            (oldMemberModel.parentMember == null && oldMemberModel.parentPerson == null)
                } ?: throw IllegalArgumentException("Cannot update parent from a non pending member")
            }
}