package br.com.alice.exec.indicator.models

import java.util.UUID

data class GuiaRegulationResult(
    val guiaId: UUID,
    val status: RegulationStatus,
    val procedures: List<ProcedureRegulationResult>
)

data class ProcedureRegulationResult(
    val code: String,
    val status: RegulationStatus,
    val regulationDescriptions: List<RegulationDescription> = emptyList()
)

data class RegulationDescription(
    val glossAuthorizationInfoCode: String,
    val observation: String
)

enum class RegulationStatus {
    AUTHORIZED,
    PENDING,
    UNAUTHORIZED,
    PARTIALLY_AUTHORIZED
}
