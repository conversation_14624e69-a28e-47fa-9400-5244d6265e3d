package br.com.alice.exec.indicator.client.guiaRegulation

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.exec.indicator.models.GuiaRegulationResult
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface GuiaRegulationService : Service {

    override val namespace get() = "exec_indicator"
    override val serviceName get() = "guia_regulation_service"

    suspend fun regulate(guiaId: UUID): Result<GuiaRegulationResult, Throwable>

}
