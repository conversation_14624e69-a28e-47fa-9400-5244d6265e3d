package br.com.alice.exec.indicator.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.data.layer.models.ExecIndicatorAuthorizer
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ExecIndicatorAuthorizerService : Service,
    Adder<ExecIndicatorAuthorizer>,
    Getter<ExecIndicatorAuthorizer>,
    Updater<ExecIndicatorAuthorizer>{

    override val namespace get() = "exec_indicator"
    override val serviceName get() = "exec_indicator_authorizer"

    override suspend fun add(model: ExecIndicatorAuthorizer): Result<ExecIndicatorAuthorizer, Throwable>

    override suspend fun get(id: UUID): Result<ExecIndicatorAuthorizer, Throwable>

    override suspend fun update(model: ExecIndicatorAuthorizer): Result<ExecIndicatorAuthorizer, Throwable>

    suspend fun countAll(): Result<Int, Throwable>

    suspend fun getByRange(range: IntRange): Result<List<ExecIndicatorAuthorizer>, Throwable>

    suspend fun getByDomain(domain: String): Result<List<ExecIndicatorAuthorizer>, Throwable>

    suspend fun getByProviderUnitId(providerUnitId: UUID): Result<ExecIndicatorAuthorizer, Throwable>

    suspend fun getByProviderUnitIds(providerUnitIds: List<UUID>, range: IntRange? = null): Result<List<ExecIndicatorAuthorizer>, Throwable>
    suspend fun countByProviderUnitIds(providerUnitIds: List<UUID>): Result<Int, Throwable>

    suspend fun getByMvCdLocalPrestador(cdPrestador: Int): Result<ExecIndicatorAuthorizer, Throwable>

    suspend fun findByIds(ids: List<UUID>): Result<List<ExecIndicatorAuthorizer>, Throwable>

    suspend fun getByMvCdPrestador(cdPrestador: Int): Result<ExecIndicatorAuthorizer, Throwable>
    suspend fun delete(id: UUID): Result<Boolean, Throwable>
}
