package br.com.alice.data.layer.pipelines

import br.com.alice.authentication.TokenVerifier
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.authorization.AuthorizationService
import br.com.alice.data.layer.models.UpdatedByReference
import br.com.alice.data.layer.pipelines.context.ContextService
import br.com.alice.data.layer.services.PersonTokenService
import br.com.alice.data.layer.services.ReplicationLagService
import br.com.alice.data.layer.tables.DeIdentifiedReference
import br.com.alice.data.layer.tables.Table
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.jdbi.v3.core.Jdbi
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import java.util.UUID

class DatabasePipelineFactoryTest {
    
    private val jdbi: Jdbi = mockk()
    private val jdbiReadDb: Jdbi = mockk()
    private val authorizationService: AuthorizationService = mockk()
    private val tokenVerifier: TokenVerifier = mockk()
    private val personTokenService: PersonTokenService = mockk()
    private val contextService: ContextService = mockk()
    private val replicationLagService: ReplicationLagService = mockk()

    private val factory = DatabasePipelineFactory(
        jdbi = jdbi,
        jdbiReadDb = jdbiReadDb,
        authorizationService = authorizationService,
        tokenVerifier = tokenVerifier,
        personTokenService = personTokenService,
        contextService = contextService,
        replicationLagService = replicationLagService
    )

    @Test
    fun `#get should return AuthorizationPipeline`() {
        val pipeline = factory.get(
            modelClass = SimpleModel::class,
            tableClass = SimpleTable::class
        )

        assertThat(pipeline).isInstanceOf(AuthorizationPipeline::class.java)
    }

    @Test
    fun `#getWithoutAuth should return ConversionPipeline for simple model`() {
        val pipeline = factory.getWithoutAuth(
            modelClass = SimpleModel::class,
            tableClass = SimpleTable::class
        )

        assertThat(pipeline).isInstanceOf(ConversionPipeline::class.java)
    }
    
    @Test
    fun `#getWithoutAuth should add AuditPipeline for auditable models`() {
        val pipeline = factory.getWithoutAuth(
            modelClass = AuditableModel::class,
            tableClass = SimpleTable::class
        )
        
        assertThat(pipeline).isInstanceOf(AuditPipeline::class.java)
    }
    
    @Test
    fun `#getWithoutAuth should add DeIdentificationPipeline for models with PersonId`() {
        val pipeline = factory.getWithoutAuth(
            modelClass = ModelWithPersonId::class,
            tableClass = SimpleTable::class
        )

        assertThat(pipeline).isInstanceOf(ConversionPipeline::class.java)

        pipeline as ConversionPipeline<*, *>

        assertThat(pipeline.databasePipeline).isInstanceOf(DeIdentificationPipeline::class.java)
    }
    
    @Test
    fun `#getWithoutAuth should add TextualDeIdentificationPipeline for DeIdentifiedReference tables`() {
        val pipeline = factory.getWithoutAuth(
            modelClass = SimpleModel::class,
            tableClass = DeIdentifiedTable::class,
        )

        assertThat(pipeline).isInstanceOf(ConversionPipeline::class.java)

        pipeline as ConversionPipeline<*, *>

        assertThat(pipeline.databasePipeline).isInstanceOf(TextualDeIdentificationPipeline::class.java)
    }

    data class SimpleModel(
        override val id: UUID = RangeUUID.generate()
    ) : Model

    data class AuditableModel(
        override val id: UUID = RangeUUID.generate(),
        override var updatedBy: UpdatedBy? = null,
        override val version: Int
    ) : Model, UpdatedByReference
    
    data class ModelWithPersonId(
        override val id: UUID = RangeUUID.generate(),
        val personId: PersonId = PersonId()
    ) : Model

    data class SimpleTable(
        override val id: Any,
        override val version: Int,
        override val createdAt: LocalDateTime,
        override val updatedAt: LocalDateTime
    ) : Table<SimpleTable> {
        override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
            copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
    }

    data class DeIdentifiedTable(
        override val id: Any,
        override val version: Int,
        override val createdAt: LocalDateTime,
        override val updatedAt: LocalDateTime
    ) : Table<DeIdentifiedTable>, DeIdentifiedReference {
        override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
            copy(version = version, createdAt = createdAt, updatedAt = updatedAt)
    }
}
