package br.com.alice.data.layer.pipelines.subjects.builder.data

import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.data.layer.helpers.DataLayerTestModelFactory
import br.com.alice.data.layer.models.LegalGuardianAssociationModel
import br.com.alice.data.layer.pipelines.DatabasePipeline
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.pipelines.subjects.builders.data.LegalGuardianAssociationDataService
import br.com.alice.data.layer.services.LegalGuardianAssociationModelDataService
import br.com.alice.data.layer.tables.LegalGuardianAssociationTable
import br.com.alice.data.layer.tables.Table
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.reflect.KClass
import kotlin.test.AfterTest
import kotlin.test.Test

class LegalGuardianAssociationDataServiceTest {

    private val factory: DatabasePipelineFactory = mockk()
    private val dataBasePipeline : DatabasePipeline<LegalGuardianAssociationModel> = mockk()

    private val personId = PersonId()
    private val legalGuardianAssociation = DataLayerTestModelFactory.buildLegalGuardianAssociation()

    private val legalGuardianAssociationDataService = LegalGuardianAssociationDataService(factory)

    @AfterTest
    fun confirmMocks() = confirmVerified(
        factory,
        dataBasePipeline
    )

    @Test
    fun `#getLegalGuardians should query legal guardians`() = runBlocking {
        val query = Query(where = LegalGuardianAssociationModelDataService.PersonIdField().eq(personId))

        every {
            factory.getWithoutAuth(LegalGuardianAssociationModel::class, LegalGuardianAssociationTable::class)
        } returns dataBasePipeline
        coEvery { dataBasePipeline.findByQuery(query) } returns listOf(legalGuardianAssociation).success()

        val result = legalGuardianAssociationDataService.getLegalGuardians(personId)

        assertThat(result).isEqualTo(listOf(legalGuardianAssociation))

        verifyOnce { factory.getWithoutAuth(any<KClass<Model>>(), any<KClass<Table<*>>>()) }
        coVerifyOnce { dataBasePipeline.findByQuery(any()) }
    }

}
