# Alice Data Access Policies

Este diretório contém as políticas de acesso aos dados da Alice.

## Estrutura

Cada diretório representa um dos namespaces que tem suas próprias regras de autorização validadas no data-layer.
Eles devem ter os seguintes arquivos:
- `{namespace}.rego`: o arquivo com as definições de regras, que tipicamente só replicam a lógica geral definida no arquivo `data-layer.rego`
para o namespace em questão. Normalmente não é necessário mudar nada aqui.
- `data.json`: o arquivo com os dados estáticos que são usados pelas políticas. É aqui que normalmente precisamos mexer quando há mudanças
de regras de autorização.
- `{namespace}_test.rego`: o arquivo com os testes das policies do namespace. É boa prática toda alteração do data.json ser acompanhada 
por um teste correspondente.

### Formato do arquivo data.json (subdomínios)

- nó `rules`: regras a aplicar. Cada nó rules tem o seguinte formato:
  - nó `conditions`: uma lista de condições que devem ser satisfeitas para que a regra seja aplicada. 
  - nó `allow`: indica quais `actions` podem ser realizadas em quais `resources` quando a regra for satisfeita.
  - nó `branches`: uma lista de nós `rules` que serão também avaliados quando a regra atual for satisfeita. Se uma condição C1 
  tiver uma branch com uma condição C2, a semântica é a seguinte: "Se C1 é satisfeita, são concedidas as permissões definidas 
  no seu nó allow; ADICIONALMENTE, se C2 também é satisfeita, são concedidas ADICIONALMENTE as permissões definidas no nó allow de C2"
- nó `aggregate`: indica quais tipos de recursos devem ser agregados na hora de fazer o cálculo. A agregação é uma otimização
de performance que se aplica pela forma que usamos o OPA no data-layer. Veja mais detalhes na seção abaixo.

### Formato do arquivo data.json (top-level)

- nó `roles_graph`: grafo que representa a hierarquia de roles da Alice. Considere o exemplo:
```
"roles_graph": {
    "NURSE": [],
    "RISK_NURSE": ["NURSE"]
}
```
Isto significa que toda RISK_NURSE é também uma NURSE. Daí, se no data.json escrevemos uma condição assim:
```
"conditions": [
    "${subject.role} @= NURSE"
]
```
...ela será satisfeita tanto para NURSE quanto para RISK_NURSE.

### Formato do input (exemplo)

``` 
{
    "cases": [
        {
            "index": 0,
            "action": "view",
            "subject": {
                 "key": "<EMAIL>",
                 ... (outros campos do Subject)
                 "opaType": "Unauthenticated"
            },
            "resource": {
                 "email": "<EMAIL>",
                 "firstName": "Some",
                 "lastName": "One",
                 "role": "PRODUCT_TECH",
                 "type": "PITAYA",
                 ... (outros campos de StaffModel)
                 "opaType": "StaffModel"
            }
        }
    ]
}
```

`index`: índice do caso, para fins de identificação na resposta. O data-layer pode mandar até 50 casos de uma vez.

`action`: ação que o `subject` deseja realizar. Veja as ações possíveis em `authorization.kt`.

`subject`: quem deseja realizar a ação; é o usuário do sistema. As propriedades do `subject` variam de acordo com a 
classe do mesmo, cujo nome é escrito na propriedade `opaType`. No geral somente os campos do tipo UUID, PersonId e os campos anotados 
com `@Expose` do `subject` são escritos no nó do JSON, para fins de comparação com as políticas.

`resource`: o recurso que o `subject` deseja acessar. Qualquer `Model` pode ser um resource; as propriedades do `resource` 
também variam de acordo com a classe do mesmo, cujo nome é escrito na propriedade `opaType`. 
No geral somente os campos do tipo UUID, PersonId e os campos anotados
com `@Expose` do `resource` são escritos no nó do JSON, para fins de comparação com as políticas.

## Cláusulas que podem ser escritas nas condições

### Valores

- Operador de substituição: `${}`

  Substitui a expressão dentro dele pelo seu valor no case atual do `input` .

  Exemplo: `${subject.id}` é substituido pelo campo `id` do campo `subject` do case atual.

### Operadores

- Operador de igualdade: `==`
  
  Retorna `true` se os dois lados têm o mesmo valor.

  Exemplo: `${subject.id} == ${resource.personId}`

- Operador de desigualdade: `!=`

  Retorna `true` se os dois lados têm valores distintos.

  Exemplo: `${subject.id} != ${resource.personId}`

- Operador de OU: `||`

  Retorna `true` se um dos lados retorna `true`.

  Exemplo: `${resource.namespace} == A || ${resource.domain} == B`

- Operador de contenção: `in`

  Retorna `true` se o elemento do lado esquerdo está contido no array ou set do lado direito.

  Exemplo: `${resource.id} in ${subject.members}`

- Operador especial de herança de role: `@=`

  Retorna `true` se o valor da esquerda é um subtipo da role da direita.

  (O grafo de subtipos é definido em `/domains/app/data.json`)

  Exemplo: `${subject.role} @= NURSE` → Se a role for RISK_NURSE, que é um tipo de NURSE, a condição é satisfeita

## Sobre aggregates

A agregação é uma otimização de performance que se aplica pela forma que usamos o OPA no data-layer, pra quando a regra
de um domínio relativa a um resource não precisa validar atributos (ABAC) ou relacionamentos (ReBAC).

Como dentro de um mesmo request:
- o subject é sempre o mesmo
- todos os resources do request são do mesmo tipo
- e não há validações de tipo ABAC / ReBAC para esse resource (o que exigiria olhar um por um)

...então pode-se fazer o cálculo da regra de autorização só pro primeiro elemento e aplicar o resultado para todos os outros;
daí o request fica quase tão rápido quanto se fosse mandado um único elemento pra fazer a autorização.

Na prática, para adicionar um tipo de resource ao nó de aggregate, precisamos garantir que quaisquer condições de variáveis
que o envolvam referenciem somente os campos `opaType`, `role` e `opaSuperTypes` do subject. Por isso foi criada a task
`generateOPAAggregates` que já calcula todos os aggregates para todos os domínios.
