package app.exec_indicator_api_test

import rego.v1

import data.app.exec_indicator_api

test_view_count_models_allowed if {
	{1,2,3,4} == exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "BeneficiaryModel",
            },
		},
		{
            "index": 2,
            "action": "count",
            "resource": {
                "opaType": "BeneficiaryModel",
            },
        },
        {
            "index": 3,
            "action": "view",
            "resource": {
                "opaType": "BeneficiaryOnboardingModel",
            },
        },
        {
            "index": 4,
            "action": "count",
            "resource": {
                "opaType": "BeneficiaryOnboardingModel",
            },
        }]
	}
}

test_view_resource_bundle_specialty_allowed if {
    1 in exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "ResourceBundleSpecialtyModel",
            },
        }]
    }
}

test_view_resource_bundle_specialty_pricing_allowed if {
    1 in exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "ResourceBundleSpecialtyPricingModel",
            },
        }]
    }
}

test_unauth_view_update_create_models_allowed if {
	{1,2,3} == exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "MagicNumbersModel",
            },
		},
		{
            "index": 2,
            "action": "update",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "MagicNumbersModel",
            },
        },
        {
            "index": 3,
            "action": "create",
            "subject": {
                "opaType": "Unauthenticated",
            },
            "resource": {
                "opaType": "MagicNumbersModel",
            },
        }]
	}
}

test_unauth_with_key_view_staff_allowed if {
	1 in exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "key": "<EMAIL>",
                "opaType": "Unauthenticated",
            },
            "resource": {
                "email": "<EMAIL>",
                "opaType": "StaffModel",
            },
        }]
	}
}

test_unauth_with_key_view_staff_denied if {
	not 1 in exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "key": "<EMAIL>",
                "opaType": "Unauthenticated",
            },
            "resource": {
                "email": "<EMAIL>",
                "opaType": "StaffModel",
            },
        }]
	}
}

test_staff_view_herself_allowed if {
	1 in exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "id": "6bfae19d-f879-41b5-accc-0d57c614b600",
                "opaType": "StaffModel",
            },
            "resource": {
                "id": "6bfae19d-f879-41b5-accc-0d57c614b600",
                "opaType": "StaffModel",
            },
        }]
	}
}

test_staff_view_other_denied if {
	not 1 in exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "id": "6bfae19d-f879-41b5-accc-0d57c614b600",
                "opaType": "StaffModel",
            },
            "resource": {
                "id": "2da9b07e-fe3d-4a33-a620-89a0525d2000",
                "opaType": "StaffModel",
            },
        }]
	}
}

test_eita_user_view_count_update_create_models_allowed if {
	{1,2,3,4} == exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_OPS"
            },
            "resource": {
                "opaType": "MvAuthorizedProcedureModel",
            },
		},
		{
            "index": 2,
            "action": "count",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_OPS"
            },
            "resource": {
                "opaType": "MvAuthorizedProcedureModel",
            },
        },
        {
            "index": 3,
            "action": "update",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_OPS"
            },
            "resource": {
                "opaType": "MvAuthorizedProcedureModel",
            },
        },
        {
            "index": 4,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_OPS"
            },
            "resource": {
                "opaType": "MvAuthorizedProcedureModel",
            },
        }]
	}
}

test_eita_user_view_create_file_vault_allowed if {
	{1,2} == exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_OPS"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "eita",
                "namespace": "provider_health_document",
            },
		},
		{
            "index": 2,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_OPS"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "eita",
                "namespace": "provider_health_document",
            },
        }]
	}
}

test_eita_user_view_create_file_vault_denied if {
	not {1,2} == exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_OPS"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "other_domain",
                "namespace": "other_namespace",
            },
		},
		{
            "index": 2,
            "action": "create",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_OPS"
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "other_domain",
                "namespace": "other_namespace",
            },
        }]
	}
}

test_email_domain_view_update_create_file_vault_allowed if {
	{1,2,3} == exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "EmailDomain",
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "eita",
                "namespace": "provider_health_document",
            },
		},
        {
            "index": 2,
            "action": "update",
            "subject": {
                "opaType": "EmailDomain",
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "eita",
                "namespace": "provider_health_document",
            },
        },
        {
            "index": 3,
            "action": "create",
            "subject": {
                "opaType": "EmailDomain",
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "eita",
                "namespace": "provider_health_document",
            },
        }]
	}
}

test_email_domain_view_update_create_file_vault_denied if {
	not {1,2,3} == exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "EmailDomain",
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "other_domain",
                "namespace": "other_namespace",
            },
		},
        {
            "index": 2,
            "action": "update",
            "subject": {
                "opaType": "EmailDomain",
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "other_domain",
                "namespace": "other_namespace",
            },
        },
        {
            "index": 3,
            "action": "create",
            "subject": {
                "opaType": "EmailDomain",
            },
            "resource": {
                "opaType": "FileVault",
                "domain": "other_domain",
                "namespace": "other_namespace",
            },
        }]
	}
}

test_eita_authorizer_user_view_count_update_models_allowed if {
	{1,2,3} == exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_ADMINISTRATIVE"
            },
            "resource": {
                "opaType": "MvAuthorizedProcedureModel",
            },
		},
		{
            "index": 2,
            "action": "count",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_ADMINISTRATIVE"
            },
            "resource": {
                "opaType": "MvAuthorizedProcedureModel",
            },
        },
        {
            "index": 3,
            "action": "update",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_ADMINISTRATIVE"
            },
            "resource": {
                "opaType": "MvAuthorizedProcedureModel",
            },
        }]
	}
}

test_staff_view_model_denied if {
	not 1 in exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "PRODUCT_TECH"
            },
            "resource": {
                "opaType": "MvAuthorizedProcedureModel",
            },
		}]
	}
}

test_health_professional_model_view_count_allowed if {
    {1,2,3,4} == exec_indicator_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_ADMINISTRATIVE"
            },
            "resource": {
                "opaType": "HealthProfessionalModel",
            },
        },
        {
            "index": 2,
            "action": "count",
            "subject": {
                "opaType": "StaffModel",
                "role": "HEALTH_ADMINISTRATIVE"
            },
            "resource": {
                "opaType": "HealthProfessionalModel",
            },
        }, {
            "index": 3,
            "action": "view",
            "subject": {
                "opaType": "EmailDomain",
                "role": "HEALTH_OPS"
            },
            "resource": {
                "opaType": "HealthProfessionalModel",
            },
        },
        {
            "index": 4,
            "action": "count",
            "subject": {
                "opaType": "EmailDomain",
                "role": "HEALTH_OPS"
            },
            "resource": {
                "opaType": "HealthProfessionalModel",
            },
        }]
    }
}
