package app.limbo_api_test

import rego.v1

import data.app.limbo_api

test_DLQ_CRUD_allowed if {
	{1,2,3,4,5} == limbo_api.allow with input as {
        "cases": [{
            "index": 1,
            "action": "create",
            "resource": {
                "opaType": "DeadletterQueue",
            },
		},
		{
            "index": 2,
            "action": "view",
            "resource": {
                "opaType": "DeadletterQueue",
            },
        },
        {
            "index": 3,
            "action": "update",
            "resource": {
                "opaType": "DeadletterQueue",
            },
        },
        {
            "index": 4,
            "action": "delete",
            "resource": {
                "opaType": "DeadletterQueue",
            },
        },
        {
            "index": 5,
            "action": "count",
            "resource": {
                "opaType": "DeadletterQueue",
            },
        }]
	}
}
