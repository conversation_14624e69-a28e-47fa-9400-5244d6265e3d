package app.healthcare_ops_api_test

import data.app.healthcare_ops_api
import rego.v1

test_unauth_view_PersonModel_allow if {
	1 in healthcare_ops_api.allow with input as {"cases": [{
		"index": 1,
		"action": "view",
		"subject": {"opaType": "Unauthenticated"},
		"resource": {"opaType": "PersonModel"},
	}]}
}

test_unauth_create_update_Lead_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "Lead"},
		},
	]}
}

test_unauth_CRU_HealthDeclaration_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "HealthDeclaration"},
		},
	]}
}

test_unauth_CRU_PersonOnboarding_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
	]}
}

test_unauth_CRU_PersonRegistration_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
	]}
}

test_unauth_CRU_PromoCode_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "update",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PromoCodeModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PromoCodeModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "Unauthenticated"},
			"resource": {"opaType": "PromoCodeModel"},
		},
	]}
}

test_unauth_view_models_with_email_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "<EMAIL>"},
			"resource": {"opaType": "StaffModel", "email": "<EMAIL>"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "<EMAIL>"},
			"resource": {"opaType": "Lead", "email": "<EMAIL>"},
		},
	]}
}

test_unauth_CRU_PersonModel_with_nationalId_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "PersonModel", "nationalId": "**********"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "PersonModel", "nationalId": "**********"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "PersonModel", "nationalId": "**********"},
		},
	]}
}

test_unauth_CRU_PersonModel_with_nationalId_denied if {
	{1, 2, 3} != healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "PersonModel", "nationalId": "1234"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "PersonModel", "nationalId": "1234"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "PersonModel", "nationalId": "1234"},
		},
	]}
}

test_unauth_CRU_Lead_with_nationalId_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "Lead", "nationalId": "**********"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "Lead", "nationalId": "**********"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "Lead", "nationalId": "**********"},
		},
	]}
}

test_unauth_CRU_Lead_with_nationalId_denied if {
	{1, 2, 3} != healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "Lead", "nationalId": "1234"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "Lead", "nationalId": "1234"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "Lead", "nationalId": "1234"},
		},
	]}
}

test_unauth_CRU_PersonLogin_with_nationalId_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "PersonLoginModel", "nationalId": "**********"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "PersonLoginModel", "nationalId": "**********"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "PersonLoginModel", "nationalId": "**********"},
		},
	]}
}

test_unauth_CRU_PersonLogin_with_nationalId_denied if {
	{1, 2, 3} != healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "PersonLoginModel", "nationalId": "1234"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "PersonLoginModel", "nationalId": "1234"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "Unauthenticated", "key": "**********"},
			"resource": {"opaType": "PersonLoginModel", "nationalId": "1234"},
		},
	]}
}

test_staff_OPS_view_count_PersonContractualRiskModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonContractualRiskModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonContractualRiskModel"},
		},
	]}
}

test_staff_OPS_view_count_NullvsIntegrationLogModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "NullvsIntegrationLogModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "NullvsIntegrationLogModel"},
		},
	]}
}

test_staff_OPS_view_count_ProductOrder_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "ProductOrderModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "ProductOrderModel"},
		},
	]}
}

test_staff_OPS_view_count_create_HealthDeclaration_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 3,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "HealthDeclaration"},
		},
	]}
}

test_staff_OPS_view_count_MemberProductChangeSchedule_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberProductChangeScheduleModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberProductChangeScheduleModel"},
		},
	]}
}

test_staff_OPS_view_count_StaffModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "StaffModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "StaffModel"},
		},
	]}
}

test_staff_OPS_view_count_ProductBundleModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "ProductBundleModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "ProductBundleModel"},
		},
	]}
}

test_staff_OPS_view_count_OnboardingBackgroundCheckModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
	]}
}

test_staff_OPS_view_count_Lead_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "Lead"},
		},
	]}
}

test_staff_OPS_view_count_AppointmentSchedule_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "AppointmentScheduleModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "AppointmentScheduleModel"},
		},
	]}
}

test_staff_OPS_view_count_Channel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "Channel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "Channel"},
		},
	]}
}

test_staff_OPS_view_count_SalesFirm_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "SalesFirm"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "SalesFirm"},
		},
	]}
}

test_staff_OPS_view_count_SalesFirmStaff_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "SalesFirmStaff"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "SalesFirmStaff"},
		},
	]}
}

test_staff_OPS_view_count_BeneficiaryCompiledView_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "BeneficiaryCompiledViewModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "BeneficiaryCompiledViewModel"},
		},
	]}
}

test_staff_OPS_view_count_MemberContract_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberContractModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberContractModel"},
		},
	]}
}

test_staff_OPS_view_count_MemberContractTerm_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
	]}
}

test_staff_OPS_view_count_FileVault_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "FileVault"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "FileVault"},
		},
	]}
}

test_staff_OPS_RU_count_PersonModel_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonModel"},
		},
	]}
}

test_staff_OPS_RU_count_OngoingCompanyDeal_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
	]}
}

test_staff_OPS_CRU_MemberTelegramTracking_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberTelegramTrackingModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberTelegramTrackingModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberTelegramTrackingModel"},
		},
	]}
}

test_staff_OPS_CRU_MemberLifeCycleEvents_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberLifeCycleEventsModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberLifeCycleEventsModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberLifeCycleEventsModel"},
		},
	]}
}

test_staff_OPS_CRU_count_resources_allow if {
	resources = [
		"PersonPreferencesModel",
		"MemberModel",
		"PersonOnboardingModel",
		"OnboardingContractModel",
		"BeneficiaryModel",
		"CassiMemberModel",
		"BeneficiaryOnboardingModel",
		"BeneficiaryOnboardingPhaseModel",
		"InvoicePaymentModel",
		"PaymentDetailModel",
	]

	every resource in resources {
		{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
			{
				"index": 1,
				"action": "create",
				"subject": {"opaType": "StaffModel", "role": "OPS"},
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "view",
				"subject": {"opaType": "StaffModel", "role": "OPS"},
				"resource": {"opaType": resource},
			},
			{
				"index": 3,
				"action": "update",
				"subject": {"opaType": "StaffModel", "role": "OPS"},
				"resource": {"opaType": resource},
			},
			{
				"index": 4,
				"action": "count",
				"subject": {"opaType": "StaffModel", "role": "OPS"},
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_staff_OPS_CRU_count_resources_2_group_allow if {
	resources = [
		"BoletoPaymentDetailModel",
		"SimpleCreditCardPaymentDetailModel",
		"PixPaymentDetailModel",
		"BolepixPaymentDetailModel",
		"InvoiceItemModel",
		"BillingAccountablePartyModel",
		"InvoiceLiquidationModel",
		"MemberInvoiceModel",
		"MemberInvoiceGroupModel",
		"ProductModel",
	]

	every resource in resources {
		{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
			{
				"index": 1,
				"action": "create",
				"subject": {"opaType": "StaffModel", "role": "OPS"},
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "view",
				"subject": {"opaType": "StaffModel", "role": "OPS"},
				"resource": {"opaType": resource},
			},
			{
				"index": 3,
				"action": "update",
				"subject": {"opaType": "StaffModel", "role": "OPS"},
				"resource": {"opaType": resource},
			},
			{
				"index": 4,
				"action": "count",
				"subject": {"opaType": "StaffModel", "role": "OPS"},
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_staff_OPS_CRU_count_resources_3_group_allow if {
	resources = [
		"ProductPriceListingModel",
		"PriceListingModel",
		"ProductPriceAdjustmentModel",
		"PersonBillingAccountablePartyModel",
		"CompanyModel",
		"PersonLoginModel",
		"ResourceSignTokenModel"
	]

	every resource in resources {
		{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
			{
				"index": 1,
				"action": "create",
				"subject": {"opaType": "StaffModel", "role": "OPS"},
				"resource": {"opaType": resource},
			},
			{
				"index": 2,
				"action": "view",
				"subject": {"opaType": "StaffModel", "role": "OPS"},
				"resource": {"opaType": resource},
			},
			{
				"index": 3,
				"action": "update",
				"subject": {"opaType": "StaffModel", "role": "OPS"},
				"resource": {"opaType": resource},
			},
			{
				"index": 4,
				"action": "count",
				"subject": {"opaType": "StaffModel", "role": "OPS"},
				"resource": {"opaType": resource},
			},
		]}
	}
}

test_staff_OPS_CRU_count_CompanyContract_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanyContractModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanyContractModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanyContractModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanyContractModel"},
		},
	]}
}

test_staff_OPS_CRU_count_CompanySubContract_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
	]}
}

test_staff_OPS_CRU_count_GenericFileVault_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "GenericFileVault"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "GenericFileVault"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "GenericFileVault"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "GenericFileVault"},
		},
	]}
}

test_staff_OPS_CRU_count_CompanyStaff_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanyStaffModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanyStaffModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanyStaffModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanyStaffModel"},
		},
	]}
}

test_staff_OPS_CRU_count_MemberProductPriceModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
	]}
}

test_staff_OPS_CRU_count_UpdatedPersonContactInfoTemp_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
	]}
}

test_staff_OPS_CRU_count_PersonRegistration_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
	]}
}

test_staff_OPS_CRU_count_PersonModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonModel"},
		},
	]}
}

test_staff_OPS_CRU_count_Lead_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "Lead"},
		},
	]}
}

test_staff_OPS_CRU_count_PersonLogin_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonLoginModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonLoginModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonLoginModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PersonLoginModel"},
		},
	]}
}

test_staff_OPS_CRU_count_OnboardingBackgroundCheckModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
	]}
}

test_staff_OPS_CRU_count_ProductOrder_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "ProductOrderModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "ProductOrderModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "ProductOrderModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "ProductOrderModel"},
		},
	]}
}

test_staff_OPS_CRU_count_PromoCode_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PromoCodeModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PromoCodeModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PromoCodeModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "PromoCodeModel"},
		},
	]}
}

test_staff_OPS_CRUD_count_InsurancePortabilityHealthInsuranceModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
	]}
}

test_staff_OPS_CRUD_count_InsurancePortabilityRequestModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
	]}
}

test_staff_OPS_CRUD_count_InsurancePortabilityRequestFileModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
	]}
}

test_staff_OPS_CRUD_count_CompanyProductPriceListing_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
	]}
}

test_staff_OPS_CRUD_count_LegalGuardianAssociationModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
	]}
}

test_staff_OPS_CRUD_count_LegalGuardianInfoTempModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
	]}
}

test_staff_OPS_CRUD_count_MemberContract_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberContractModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberContractModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberContractModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberContractModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberContractModel"},
		},
	]}
}

test_staff_OPS_CRUD_count_MemberContractTermModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
	]}
}

test_staff_OPS_view_FileVault_with_member_documents_allow if {
	{1} == healthcare_ops_api.allow with input as {"cases": [{
		"index": 1,
		"action": "view",
		"subject": {"opaType": "StaffModel", "role": "OPS"},
		"resource": {"opaType": "FileVault", "domain": "member", "namespace": "documents"},
	}]}
}

test_staff_OPS_CRU_FileVault_with_member_contract_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "FileVault", "domain": "member", "namespace": "contract"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "FileVault", "domain": "member", "namespace": "contract"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "FileVault", "domain": "member", "namespace": "contract"},
		},
	]}
}

test_staff_OPS_CRU_FileVault_with_portability_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "FileVault", "namespace": "portability"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "FileVault", "namespace": "portability"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "FileVault", "namespace": "portability"},
		},
	]}
}

test_staff_OPS_view_models_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "ProviderModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "DeviceModel"},
		},
	]}
}

test_staff_OPS_create_update_MemberRegistration_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberRegistrationModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "MemberRegistrationModel"},
		},
	]}
}

test_staff_OPS_delete_PriceListingModel_allow if {
	{1} == healthcare_ops_api.allow with input as {"cases": [{
		"index": 1,
		"action": "delete",
		"subject": {"opaType": "StaffModel", "role": "OPS"},
		"resource": {"opaType": "PriceListingModel"},
	}]}
}

test_staff_OPS_CRU_count_AppointmentSchedule_with_type_HEALTH_DECLARATION_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "AppointmentScheduleModel", "type": "HEALTH_DECLARATION"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "AppointmentScheduleModel", "type": "HEALTH_DECLARATION"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "AppointmentScheduleModel", "type": "HEALTH_DECLARATION"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "OPS"},
			"resource": {"opaType": "AppointmentScheduleModel", "type": "HEALTH_DECLARATION"},
		},
	]}
}

test_staff_CX_OPS_view_count_MemberProductChangeSchedule_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "MemberProductChangeScheduleModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "MemberProductChangeScheduleModel"},
		},
	]}
}

test_staff_CX_OPS_view_count_InvoicePaymentModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
	]}
}

test_staff_CX_OPS_view_count_ResourceSignToken_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
	]}
}

test_staff_CX_OPS_view_count_PaymentDetailModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
	]}
}

test_staff_CX_OPS_view_count_BoletoPaymentDetailModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
	]}
}

test_staff_CX_OPS_view_count_SimpleCreditCardPaymentDetailModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
	]}
}

test_staff_CX_OPS_view_count_PixPaymentDetailModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
	]}
}

test_staff_CX_OPS_view_count_BolepixPaymentDetailModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
	]}
}

test_staff_CX_OPS_view_count_InvoiceItemModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
	]}
}

test_staff_CX_OPS_view_count_BillingAccountablePartyModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
	]}
}

test_staff_CX_OPS_view_count_InvoiceLiquidationModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
	]}
}

test_staff_CX_OPS_view_count_MemberInvoiceModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
	]}
}

test_staff_CX_OPS_view_count_MemberInvoiceGroupModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
	]}
}

test_staff_CX_OPS_view_models_allow if {
	resources = [
		"StaffModel",
		"ProductModel",
		"ProductBundleModel",
		"ProviderModel",
		"ProductOrderModel",
		"PersonModel",
		"DeviceModel",
		"PersonOnboardingModel",
		"PersonRegistrationModel",
		"MemberModel",
		"Lead",
		"PersonLoginModel",
		"ProductPriceListingModel",
		"PriceListingModel",
		"MemberProductPriceModel",
		"HealthDeclaration",
	]

	every resource in resources {
		{1} == healthcare_ops_api.allow with input as {"cases": [{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
			"resource": {"opaType": resource},
		}]}
	}
}

test_staff_CX_OPS_view_AppointmentSchedule_with_type_HEALTH_DECLARATION if {
	{1} == healthcare_ops_api.allow with input as {"cases": [{
		"index": 1,
		"action": "view",
		"subject": {"opaType": "StaffModel", "role": "CX_OPS"},
		"resource": {"opaType": "AppointmentScheduleModel", "type": "HEALTH_DECLARATION"},
	}]}
}

test_staff_NAVIGATOR_CRU_count_AppointmentSchedule_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "AppointmentScheduleModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "AppointmentScheduleModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "AppointmentScheduleModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "AppointmentScheduleModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_BillingAccountablePartyModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_BolepixPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_BoletoPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_CassiMember_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CassiMemberModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_CompanyProductPriceListing_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_HealthDeclaration_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "HealthDeclaration"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_InvoiceItemModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_InvoiceLiquidationModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_InvoicePaymentModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_ResourceSignTokenModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_Lead_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "Lead"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_MemberModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_MemberLifeCycleEvents_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberLifeCycleEventsModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberLifeCycleEventsModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberLifeCycleEventsModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberLifeCycleEventsModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_MemberInvoiceModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_MemberInvoiceGroupModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_MemberRegistration_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberRegistrationModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberRegistrationModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberRegistrationModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberRegistrationModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_PaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_PersonModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_PersonBillingAccountablePartyModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonBillingAccountablePartyModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonBillingAccountablePartyModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonBillingAccountablePartyModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonBillingAccountablePartyModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_PersonLogin_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonLoginModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonLoginModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonLoginModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonLoginModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_PersonOnboarding_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_PersonPreferences_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonPreferencesModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonPreferencesModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonPreferencesModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonPreferencesModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_PersonRegistration_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_PixPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_PriceListingModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PriceListingModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PriceListingModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PriceListingModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "PriceListingModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_ProductModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_ProductOrder_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductOrderModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductOrderModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductOrderModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductOrderModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_ProductPriceAdjustmentModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductPriceAdjustmentModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductPriceAdjustmentModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductPriceAdjustmentModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductPriceAdjustmentModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_ProductPriceListingModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductPriceListingModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductPriceListingModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductPriceListingModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductPriceListingModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_SimpleCreditCardPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRU_count_UpdatedPersonContactInfoTemp_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_Beneficiary_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BeneficiaryModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BeneficiaryModel"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_BeneficiaryCompiledView_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BeneficiaryCompiledViewModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "BeneficiaryCompiledViewModel"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_Company_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CompanyModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CompanyModel"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_CompanyActivationFiles_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CompanyActivationFilesModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CompanyActivationFilesModel"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_CompanyContract_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CompanyContractModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CompanyContractModel"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_CompanySubContract_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_Device_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "DeviceModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "DeviceModel"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_MemberProductChangeSchedule_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberProductChangeScheduleModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberProductChangeScheduleModel"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_MemberProductPriceModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_MemberProductPriceAdjustmentModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberProductPriceAdjustmentModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "MemberProductPriceAdjustmentModel"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_OnboardingBackgroundCheckModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_OnboardingContract_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "OnboardingContractModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "OnboardingContractModel"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_OngoingCompanyDeal_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_ProductBundleModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductBundleModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProductBundleModel"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_ProviderModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProviderModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "ProviderModel"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_GenericFileVault_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "GenericFileVault"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "GenericFileVault"},
		},
	]}
}

test_staff_NAVIGATOR_view_count_StaffModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "StaffModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "StaffModel"},
		},
	]}
}

test_staff_NAVIGATOR_CRUD_count_InsurancePortabilityHealthInsuranceModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 3,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 4,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 5,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "NAVIGATOR"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_AppointmentSchedule_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "AppointmentScheduleModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "AppointmentScheduleModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_Beneficiary_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BeneficiaryModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BeneficiaryModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_BeneficiaryCompiledView_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BeneficiaryCompiledViewModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BeneficiaryCompiledViewModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_CassiMember_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "CassiMemberModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_Company_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "CompanyModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "CompanyModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_CompanyActivationFiles_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "CompanyActivationFilesModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "CompanyActivationFilesModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_CompanyContract_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "CompanyContractModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "CompanyContractModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_CompanySubContract_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_Device_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "DeviceModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "DeviceModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_Lead_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "Lead"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_MemberModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_MemberProductPriceModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_MemberProductPriceAdjustmentModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberProductPriceAdjustmentModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberProductPriceAdjustmentModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_MemberRegistration_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberRegistrationModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberRegistrationModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_OnboardingBackgroundCheckModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_OnboardingContract_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "OnboardingContractModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "OnboardingContractModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_OngoingCompanyDeal_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_PersonModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PersonModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_PersonBillingAccountablePartyModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PersonBillingAccountablePartyModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PersonBillingAccountablePartyModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_PersonLogin_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PersonLoginModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PersonLoginModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_PersonOnboarding_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_PersonRegistration_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_PersonPreferences_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PersonPreferencesModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PersonPreferencesModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_PriceListingModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PriceListingModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PriceListingModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_ProductModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ProductModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ProductModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_ProductBundleModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ProductBundleModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ProductBundleModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_ProductOrder_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ProductOrderModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ProductOrderModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_ProductPriceAdjustmentModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ProductPriceAdjustmentModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ProductPriceAdjustmentModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_ProductPriceListingModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ProductPriceListingModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ProductPriceListingModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_ProviderModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ProviderModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ProviderModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_GenericFileVault_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "GenericFileVault"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "GenericFileVault"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_StaffModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "StaffModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "StaffModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_MemberContract_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberContractModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberContractModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_MemberContractTerm_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_FileVault_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "FileVault"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "FileVault"},
		},
	]}
}

test_staff_RISK_NURSE_view_count_InvoiceItemModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRU_count_BillingAccountablePartyModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRU_count_HealthDeclaration_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "HealthDeclaration"},
		},
	]}
}

test_staff_RISK_NURSE_CRU_count_PromoCode_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PromoCodeModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PromoCodeModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PromoCodeModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PromoCodeModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRU_count_UpdatedPersonContactInfoTemp_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRU_count_InvoicePaymentModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRU_count_ResourceSignTokenModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRU_count_PaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRU_count_BoletoPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRU_count_SimpleCreditCardPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRU_count_PixPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRU_count_BolepixPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRU_count_InvoiceItemModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRU_count_InvoiceLiquidationModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRU_count_MemberInvoiceModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRU_count_MemberInvoiceGroupModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRUD_count_InsurancePortabilityHealthInsuranceModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 4,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 5,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRUD_count_InsurancePortabilityRequestModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 4,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 5,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRUD_count_InsurancePortabilityRequestFileModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 4,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 5,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRUD_count_LegalGuardianAssociationModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 4,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 5,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
	]}
}

test_staff_RISK_NURSE_CRUD_count_LegalGuardianInfoTempModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 3,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 4,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 5,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
	]}
}

test_staff_RISK_NURSE_update_create_AppointmentSchedule_with_type_HEALTH_DECLARATION_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "AppointmentScheduleModel", "type": "HEALTH_DECLARATION"},
		},
		{
			"index": 2,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "RISK_NURSE"},
			"resource": {"opaType": "AppointmentScheduleModel", "type": "HEALTH_DECLARATION"},
		},
	]}
}

test_staff_HEALTH_OPS_view_models_allow if {
	resources = [
		"StaffModel",
		"ProductModel",
		"ProductBundleModel",
		"ProviderModel",
		"ProductOrderModel",
		"DeviceModel",
		"PersonOnboardingModel",
		"ProductPriceListingModel",
		"PriceListingModel",
		"MemberProductPriceModel",
		"HealthDeclaration",
	]

	every resource in resources {
		{1} == healthcare_ops_api.allow with input as {"cases": [{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": resource},
		}]}
	}
}

test_staff_HEALTH_OPS_CRU_PersonRegistration_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
	]}
}

test_staff_HEALTH_OPS_CRU_MemberModel_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "MemberModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "MemberModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "MemberModel"},
		},
	]}
}

test_staff_HEALTH_OPS_CRU_PersonModel_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "PersonModel"},
		},
	]}
}

test_staff_HEALTH_OPS_CRU_Lead_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "Lead"},
		},
	]}
}

test_staff_HEALTH_OPS_CRU_PersonLogin_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "PersonLoginModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "PersonLoginModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "PersonLoginModel"},
		},
	]}
}

test_staff_HEALTH_OPS_CRU_count_OutcomeConf_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "OutcomeConf"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "OutcomeConf"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "OutcomeConf"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "OutcomeConf"},
		},
	]}
}

test_staff_HEALTH_OPS_CRU_count_HealthDemandMonitoring_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "HealthDemandMonitoring"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "HealthDemandMonitoring"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "HealthDemandMonitoring"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "HealthDemandMonitoring"},
		},
	]}
}

test_staff_HEALTH_OPS_view_count_InvoicePaymentModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
	]}
}

test_staff_HEALTH_OPS_view_count_ResourceSignTokenModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
	]}
}

test_staff_HEALTH_OPS_view_count_PaymentDetailModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
	]}
}

test_staff_HEALTH_OPS_view_count_BoletoPaymentDetailModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
	]}
}

test_staff_HEALTH_OPS_view_count_SimpleCreditCardPaymentDetailModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
	]}
}

test_staff_HEALTH_OPS_view_count_PixPaymentDetailModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
	]}
}

test_staff_HEALTH_OPS_view_count_BolepixPaymentDetailModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
	]}
}

test_staff_HEALTH_OPS_view_count_InvoiceItemModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
	]}
}

test_staff_HEALTH_OPS_view_count_BillingAccountablePartyModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
	]}
}

test_staff_HEALTH_OPS_view_count_InvoiceLiquidationModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
	]}
}

test_staff_HEALTH_OPS_view_count_MemberInvoiceModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
	]}
}

test_staff_HEALTH_OPS_view_count_MemberInvoiceGroupModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
	]}
}

test_staff_HEALTH_OPS_view_AppointmentSchedule_with_type_HEALTH_DECLARATION_allow if {
	{1} == healthcare_ops_api.allow with input as {"cases": [{
		"index": 1,
		"action": "view",
		"subject": {"opaType": "StaffModel", "role": "HEALTH_OPS"},
		"resource": {"opaType": "AppointmentScheduleModel", "type": "HEALTH_DECLARATION"},
	}]}
}

test_staff_PRODUCT_TECH_view_models_allow if {
	resources = [
		"StaffModel",
		"BeneficiaryHubspotModel",
		"ProductBundleModel",
		"ProviderModel",
		"ProviderUnitModel",
		"StructuredAddress",
		"ContactModel",
		"OnboardingContractModel",
		"MemberProductPriceAdjustmentModel",
		"SalesFirm",
		"SalesFirmStaff",
	]

	every resource in resources {
		{1} == healthcare_ops_api.allow with input as {"cases": [{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": resource},
		}]}
	}
}

test_staff_PRODUCT_TECH_view_count_MemberContract_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberContractModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberContractModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_view_count_MemberContractTerm_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_view_count_FileVault_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "FileVault"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "FileVault"},
		},
	]}
}

test_staff_PRODUCT_TECH_view_count_AppointmentSchedule_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "AppointmentScheduleModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "AppointmentScheduleModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_view_count_BeneficiaryCompiledView_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryCompiledViewModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryCompiledViewModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_view_count_MemberProductChangeSchedule_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberProductChangeScheduleModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberProductChangeScheduleModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CR_count_HealthDeclaration_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "HealthDeclaration"},
		},
	]}
}

test_staff_PRODUCT_TECH_RU_count_CompanyActivationFiles_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyActivationFilesModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyActivationFilesModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyActivationFilesModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_MemberTelegramTracking_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberTelegramTrackingModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberTelegramTrackingModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberTelegramTrackingModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_MemberLifeCycleEvents_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberLifeCycleEventsModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberLifeCycleEventsModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberLifeCycleEventsModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_PersonOnboarding_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_PersonRegistration_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_MemberModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_PersonModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_Lead_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "Lead"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_PersonLogin_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonLoginModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonLoginModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonLoginModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonLoginModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_OnboardingContract_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "OnboardingContractModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "OnboardingContractModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "OnboardingContractModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "OnboardingContractModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_OnboardingBackgroundCheckModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_ProductOrder_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductOrderModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductOrderModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductOrderModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductOrderModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_PromoCode_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PromoCodeModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PromoCodeModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PromoCodeModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PromoCodeModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_InsurancePortabilityRequestModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_InsurancePortabilityRequestFileModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_BillingAccountablePartyModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_PersonBillingAccountablePartyModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonBillingAccountablePartyModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonBillingAccountablePartyModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonBillingAccountablePartyModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonBillingAccountablePartyModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_PersonPreferences_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonPreferencesModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonPreferencesModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonPreferencesModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonPreferencesModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_ProductPriceListingModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductPriceListingModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductPriceListingModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductPriceListingModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductPriceListingModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_MemberProductPriceModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_OngoingCompanyDeal_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_Beneficiary_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_CassiMember_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CassiMemberModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_BeneficiaryOnboarding_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_BeneficiaryOnboardingPhase_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingPhaseModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingPhaseModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingPhaseModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BeneficiaryOnboardingPhaseModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_ProductModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_ProductPriceAdjustmentModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductPriceAdjustmentModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductPriceAdjustmentModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductPriceAdjustmentModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ProductPriceAdjustmentModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_UpdatedPersonContactInfoTemp_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_InvoicePaymentModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_ResourceSignTokenModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_PaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_BoletoPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_SimpleCreditCardPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_PixPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_BolepixPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_InvoiceItemModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_InvoiceLiquidationModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_MemberInvoiceModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_MemberInvoiceGroupModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_GenericFileVault_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "GenericFileVault"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "GenericFileVault"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "GenericFileVault"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "GenericFileVault"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_CompanyContract_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyContractModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyContractModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyContractModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyContractModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_CompanySubContract_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_Company_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_PersonHealthConditionContractualRiskModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonHealthConditionContractualRiskModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonHealthConditionContractualRiskModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonHealthConditionContractualRiskModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PersonHealthConditionContractualRiskModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRUD_count_PriceListingModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PriceListingModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PriceListingModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PriceListingModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PriceListingModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "PriceListingModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRUD_count_LegalGuardianInfoTempModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRUD_count_LegalGuardianAssociationModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRUD_count_InsurancePortabilityHealthInsuranceModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRUD_count_InsurancePortabilityRequestModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRUD_count_InsurancePortabilityRequestFileModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRUD_count_CompanyProductPriceListing_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
	]}
}

test_staff_PRODUCT_TECH_CRU_count_AppointmentSchedule_with_type_HEALTH_DECLARATION_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "AppointmentScheduleModel", "type": "HEALTH_DECLARATION"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "AppointmentScheduleModel", "type": "HEALTH_DECLARATION"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "AppointmentScheduleModel", "type": "HEALTH_DECLARATION"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "AppointmentScheduleModel", "type": "HEALTH_DECLARATION"},
		},
	]}
}

test_staff_PRODUCT_TECH_CR_GenericFileVault_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "GenericFileVault", "namespace": "company_activation_files"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH"},
			"resource": {"opaType": "GenericFileVault", "namespace": "company_activation_files"},
		},
	]}
}

test_staff_PRODUCT_TECH_HEALTH_CRU_count_PersonContractualRiskModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
			"resource": {"opaType": "PersonContractualRiskModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
			"resource": {"opaType": "PersonContractualRiskModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
			"resource": {"opaType": "PersonContractualRiskModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "PRODUCT_TECH_HEALTH"},
			"resource": {"opaType": "PersonContractualRiskModel"},
		},
	]}
}

test_any_staff_view_count_Beneficiary_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "BeneficiaryModel", "role": "any_role"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "BeneficiaryModel", "role": "any_role"},
		},
	]}
}

test_any_staff_view_count_BeneficiaryOnboarding_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "BeneficiaryOnboardingModel", "role": "any_role"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "BeneficiaryOnboardingModel", "role": "any_role"},
		},
	]}
}

test_any_staff_view_count_BeneficiaryOnboardingPhase_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "BeneficiaryOnboardingPhaseModel", "role": "any_role"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "BeneficiaryOnboardingPhaseModel", "role": "any_role"},
		},
	]}
}

test_any_staff_view_count_CassiMember_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "CassiMemberModel", "role": "any_role"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "CassiMemberModel", "role": "any_role"},
		},
	]}
}

test_any_staff_view_count_Company_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "CompanyModel", "role": "any_role"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "CompanyModel", "role": "any_role"},
		},
	]}
}

test_any_staff_view_count_CompanyContract_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "CompanyContractModel", "role": "any_role"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "CompanyContractModel", "role": "any_role"},
		},
	]}
}

test_any_staff_view_count_CompanySubContract_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "CompanySubContractModel", "role": "any_role"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "CompanySubContractModel", "role": "any_role"},
		},
	]}
}

test_any_staff_view_count_GenericFileVault_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "GenericFileVault", "role": "any_role"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "GenericFileVault", "role": "any_role"},
		},
	]}
}

test_any_staff_view_count_CompanyActivationFiles_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "CompanyActivationFilesModel", "role": "any_role"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "CompanyActivationFilesModel", "role": "any_role"},
		},
	]}
}

test_any_staff_view_count_OngoingCompanyDeal_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "OngoingCompanyDeal", "role": "any_role"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "OngoingCompanyDeal", "role": "any_role"},
		},
	]}
}

test_any_staff_view_count_SalesFirm_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "SalesFirm", "role": "any_role"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "SalesFirm", "role": "any_role"},
		},
	]}
}

test_any_staff_view_count_SalesFirmStaff_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "SalesFirmStaff", "role": "any_role"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "SalesFirmStaff", "role": "any_role"},
		},
	]}
}

test_any_staff_view_count_FileVault_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "FileVault", "role": "any_role"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel"},
			"resource": {"opaType": "FileVault", "role": "any_role"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_AppointmentSchedule_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "AppointmentScheduleModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "AppointmentScheduleModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_Beneficiary_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BeneficiaryModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BeneficiaryModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_BeneficiaryCompiledView_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BeneficiaryCompiledViewModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BeneficiaryCompiledViewModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_Company_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CompanyModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CompanyModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_CompanyActivationFiles_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CompanyActivationFilesModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CompanyActivationFilesModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_CompanyContract_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CompanyContractModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CompanyContractModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_CompanyProductPriceListing_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_CompanySubContract_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CompanySubContractModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_Device_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "DeviceModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "DeviceModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_MemberProductPriceAdjustmentModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberProductPriceAdjustmentModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberProductPriceAdjustmentModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_OngoingCompanyDeal_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "OngoingCompanyDeal"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_ProductModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "ProductModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "ProductModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_ProductBundleModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "ProductBundleModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "ProductBundleModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_ProductPriceAdjustmentModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "ProductPriceAdjustmentModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "ProductPriceAdjustmentModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_ProviderModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "ProviderModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "ProviderModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_GenericFileVault_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "GenericFileVault"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "GenericFileVault"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_StaffModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "StaffModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "StaffModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_MemberInvoiceModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_MemberInvoiceGroupModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_InvoicePaymentModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InvoicePaymentModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_ResourceSignTokenModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "ResourceSignTokenModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_InvoiceItemModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_InvoiceLiquidationModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_MemberProductChangeSchedule_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberProductChangeScheduleModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberProductChangeScheduleModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_MemberContracte_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberContractModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberContractModel"},
		},
	]}
}

test_staff_CHIEF_RISK_view_count_MemberContractTerm_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberContractTermModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CR_count_PersonContractualRiskModel_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonContractualRiskModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonContractualRiskModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonContractualRiskModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_BillingAccountablePartyModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_CassiMember_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CassiMemberModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CassiMemberModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_HealthDeclaration_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "HealthDeclaration"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_Lead_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "Lead"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "Lead"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_MemberModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_MemberProductPriceModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_MemberRegistration_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberRegistrationModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberRegistrationModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberRegistrationModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "MemberRegistrationModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_OnboardingBackgroundCheckModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "OnboardingBackgroundCheckModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_OnboardingContract_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "OnboardingContractModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "OnboardingContractModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "OnboardingContractModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "OnboardingContractModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_PaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_PersonModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_PersonBillingAccountablePartyModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonBillingAccountablePartyModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonBillingAccountablePartyModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonBillingAccountablePartyModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonBillingAccountablePartyModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_PersonLogin_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonLoginModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonLoginModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonLoginModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonLoginModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_PersonOnboarding_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonOnboardingModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_PersonRegistration_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonRegistrationModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_PersonPreferences_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonPreferencesModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonPreferencesModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonPreferencesModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonPreferencesModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_PriceListingModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PriceListingModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PriceListingModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PriceListingModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PriceListingModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_ProductOrder_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "ProductOrderModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "ProductOrderModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "ProductOrderModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "ProductOrderModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_ProductPriceListingModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BillingAccountablePartyModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_PromoCode_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PromoCodeModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PromoCodeModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PromoCodeModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PromoCodeModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_BoletoPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_BolepixPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_PixPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_SimpleCreditCardPaymentDetailModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_UpdatedPersonContactInfoTemp_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "UpdatedPersonContactInfoTempModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_BeneficiaryMacoModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BeneficiaryMacoModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BeneficiaryMacoModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BeneficiaryMacoModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "BeneficiaryMacoModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_CompanyContractMacoModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CompanyContractMacoModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CompanyContractMacoModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CompanyContractMacoModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "CompanyContractMacoModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_PersonHealthConditionContractualRiskModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonHealthConditionContractualRiskModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonHealthConditionContractualRiskModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonHealthConditionContractualRiskModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "PersonHealthConditionContractualRiskModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRU_count_StandardCost_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "StandardCostModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "StandardCostModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "StandardCostModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "StandardCostModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRUD_count_InsurancePortabilityHealthInsuranceModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InsurancePortabilityHealthInsuranceModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRUD_count_InsurancePortabilityRequestModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InsurancePortabilityRequestModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRUD_count_InsurancePortabilityRequestFileModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "InsurancePortabilityRequestFileModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRUD_count_LegalGuardianAssociationModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "LegalGuardianAssociationModel"},
		},
	]}
}

test_staff_CHIEF_RISK_CRUD_count_LegalGuardianInfoTempModel_allow if {
	{1, 2, 3, 4, 5} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
		{
			"index": 5,
			"action": "delete",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "LegalGuardianInfoTempModel"},
		},
	]}
}

test_CHIEF_RISK_create_update_AppointmentSchedule_with_type_HEALTH_DECLARATION_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "AppointmentScheduleModel", "type": "HEALTH_DECLARATION"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "CHIEF_RISK"},
			"resource": {"opaType": "AppointmentScheduleModel", "type": "HEALTH_DECLARATION"},
		},
	]}
}

test_staff_FIN_OPS_view_models_allow if {
	resources = [
		"StaffModel",
		"ProductModel",
		"ProductBundleModel",
		"ProviderModel",
		"ProductOrderModel",
		"PersonModel",
		"DeviceModel",
		"PersonOnboardingModel",
		"PersonRegistrationModel",
		"MemberModel",
		"Lead",
		"PersonLoginModel",
		"ProductPriceListingModel",
		"PriceListingModel",
		"MemberProductPriceModel",
	]

	every resource in resources {
		{1} == healthcare_ops_api.allow with input as {"cases": [{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": resource},
		}]}
	}
}

test_staff_FIN_OPS_view_count_PaymentDetailModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "PaymentDetailModel"},
		},
	]}
}

test_staff_FIN_OPS_view_count_BoletoPaymentDetailModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "BoletoPaymentDetailModel"},
		},
	]}
}

test_staff_FIN_OPS_view_count_SimpleCreditCardPaymentDetailModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "SimpleCreditCardPaymentDetailModel"},
		},
	]}
}

test_staff_FIN_OPS_view_count_PixPaymentDetailModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "PixPaymentDetailModel"},
		},
	]}
}

test_staff_FIN_OPS_view_count_BolepixPaymentDetailModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "BolepixPaymentDetailModel"},
		},
	]}
}

test_staff_FIN_OPS_view_count_InvoiceItemModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "InvoiceItemModel"},
		},
	]}
}

test_staff_FIN_OPS_view_count_InvoiceLiquidationModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "InvoiceLiquidationModel"},
		},
	]}
}

test_staff_FIN_OPS_view_count_MemberInvoiceModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "MemberInvoiceModel"},
		},
	]}
}

test_staff_FIN_OPS_view_count_MemberInvoiceGroupModel_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "FIN_OPS"},
			"resource": {"opaType": "MemberInvoiceGroupModel"},
		},
	]}
}

test_staff_INSURANCE_OPS_HEALTH_INSTITUTION_OPS_view_models_allow if {
	resources = [
		"StaffModel",
		"ProductModel",
		"ProductBundleModel",
		"ProviderModel",
		"ProductOrderModel",
		"PersonModel",
		"DeviceModel",
		"PersonOnboardingModel",
		"PersonRegistrationModel",
		"MemberModel",
		"PersonInternalReference",
		"Lead",
		"PersonLoginModel",
		"HealthDeclaration",
		"ProductPriceAdjustmentModel",
		"OnboardingContractModel",
		"InsurancePortabilityRequestModel",
		"InsurancePortabilityRequestFileModel",
		"AppointmentScheduleModel",
	]
	every resource in resources {
		{1} == healthcare_ops_api.allow with input as {"cases": [{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
			"resource": {"opaType": resource},
		}]}
	}
}

test_staff_INSURANCE_OPS_HEALTH_INSTITUTION_OPS_CRU_count_MemberProductPriceModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
			"resource": {"opaType": "MemberProductPriceModel"},
		},
	]}
}

test_staff_INSURANCE_OPS_HEALTH_INSTITUTION_OPS_CRU_count_ProductPriceListingModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
			"resource": {"opaType": "ProductPriceListingModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
			"resource": {"opaType": "ProductPriceListingModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
			"resource": {"opaType": "ProductPriceListingModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
			"resource": {"opaType": "ProductPriceListingModel"},
		},
	]}
}

test_staff_INSURANCE_OPS_HEALTH_INSTITUTION_OPS_CRU_count_PriceListingModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
			"resource": {"opaType": "PriceListingModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
			"resource": {"opaType": "PriceListingModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
			"resource": {"opaType": "PriceListingModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "INSURANCE_OPS_HEALTH_INSTITUTION_OPS"},
			"resource": {"opaType": "PriceListingModel"},
		},
	]}
}

test_STAFF_HEALTH_COMMUNITY_view_models_allow if {
	resources = [
		"StaffModel",
		"ProductModel",
		"ProductBundleModel",
		"ProviderModel",
		"ProductOrderModel",
		"PersonModel",
		"DeviceModel",
		"PersonOnboardingModel",
		"PersonRegistrationModel",
		"MemberModel",
		"PersonInternalReference",
		"Lead",
		"PersonLoginModel",
		"ProductPriceListingModel",
		"PriceListingModel",
		"MemberProductPriceModel",
		"HealthDeclaration",
	]

	every resource in resources {
		{1} == healthcare_ops_api.allow with input as {"cases": [{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "HEALTH_COMMUNITY"},
			"resource": {"opaType": resource},
		}]}
	}
}

test_staff_MED_RISK_view_models_allow if {
	resources = [
		"PersonModel",
		"MemberModel",
		"ProductOrderModel",
		"PersonOnboardingModel",
		"PersonRegistrationModel",
		"Lead",
		"PersonLoginModel",
		"OnboardingContractModel",
		"OnboardingBackgroundCheckModel",
		"InsurancePortabilityRequestModel",
		"InsurancePortabilityRequestFileModel",
		"PersonPreferencesModel",
		"MemberProductPriceModel",
		"ProductModel",
		"ProductBundleModel",
		"ProductPriceListingModel",
		"PriceListingModel",
		"ProviderModel",
	]

	every resource in resources {
		{1} == healthcare_ops_api.allow with input as {"cases": [{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": resource},
		}]}
	}
}

test_staff_MED_RISK_view_count_CompanyProductPriceListing_allow if {
	{1, 2} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
		{
			"index": 2,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "CompanyProductPriceListingModel"},
		},
	]}
}

test_staff_MED_RISK_CRU_HealthDeclaration_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "HealthDeclaration"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "HealthDeclaration"},
		},
	]}
}

test_staff_MED_RISK_CRU_AppointmentSchedule_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "AppointmentScheduleModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "AppointmentScheduleModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "AppointmentScheduleModel"},
		},
	]}
}

test_staff_MED_RISK_CR_count_PersonContractualRiskModel_allow if {
	{1, 2, 3} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "PersonContractualRiskModel"},
		},
		{
			"index": 2,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "PersonContractualRiskModel"},
		},
		{
			"index": 3,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "PersonContractualRiskModel"},
		},
	]}
}

test_staff_MED_RISK_CRU_count_CompanyContractMacoModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "CompanyContractMacoModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "CompanyContractMacoModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "CompanyContractMacoModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "CompanyContractMacoModel"},
		},
	]}
}

test_staff_MED_RISK_CRU_count_BeneficiaryMacoModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "BeneficiaryMacoModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "BeneficiaryMacoModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "BeneficiaryMacoModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "BeneficiaryMacoModel"},
		},
	]}
}

test_staff_MED_RISK_CRU_count_PersonHealthConditionContractualRiskModel_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "PersonHealthConditionContractualRiskModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "PersonHealthConditionContractualRiskModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "PersonHealthConditionContractualRiskModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "PersonHealthConditionContractualRiskModel"},
		},
	]}
}

test_staff_MED_RISK_CRU_count_StandardCost_allow if {
	{1, 2, 3, 4} == healthcare_ops_api.allow with input as {"cases": [
		{
			"index": 1,
			"action": "create",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "StandardCostModel"},
		},
		{
			"index": 2,
			"action": "update",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "StandardCostModel"},
		},
		{
			"index": 3,
			"action": "view",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "StandardCostModel"},
		},
		{
			"index": 4,
			"action": "count",
			"subject": {"opaType": "StaffModel", "role": "MED_RISK"},
			"resource": {"opaType": "StandardCostModel"},
		},
	]}
}
