package app.reload_feature_config_test

import rego.v1

import data.app.reload_feature_config

test_view_feature_config_allowed if {
	1 in reload_feature_config.allow with input as {
        "cases": [{
            "index": 1,
            "action": "view",
            "resource": {
                "opaType": "FeatureConfigModel",
            },
		}]
	}
}

test_create_feature_config_not_allowed if {
	not 1 in reload_feature_config.allow with input as {
        "cases": [{
            "index": 1,
            "action": "create",
            "resource": {
                "opaType": "FeatureConfigModel",
            },
		}]
	}
}

test_update_feature_config_not_allowed if {
	not 1 in reload_feature_config.allow with input as {
        "cases": [{
            "index": 1,
            "action": "update",
            "resource": {
                "opaType": "FeatureConfigModel",
            },
		}]
	}
}

