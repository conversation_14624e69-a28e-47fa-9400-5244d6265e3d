{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["HealthDeclaration", "HealthConditionAxis", "Appointment", "HealthFormQuestionAnswer", "PersonModel", "Risk", "PersonClinicalAccount", "HealthPlanTask", "HealthcareTeamModel", "PersonInternalReference", "HealthProfessionalModel", "StructuredAddress", "ContactModel"], "actions": ["view"]}, {"resources": ["StaffModel"], "actions": ["view", "count"]}, {"resources": ["CaseRecord", "PersonCase", "HealthConditionTemplate", "Channel"], "actions": ["view", "create", "update"]}, {"resources": ["HealthCondition"], "actions": ["view", "update"]}, {"resources": ["HealthConditionRelated"], "actions": ["view", "create"]}, {"resources": ["MonitoringTriggerRecord", "MonitoringTriggerConfiguration", "MonitoringTriggerRecordAction"], "actions": ["view", "count", "create", "update"]}]}], "aggregate": ["Appointment", "CaseRecord", "Channel", "ContactModel", "HealthCondition", "HealthConditionAxis", "HealthConditionRelated", "HealthConditionTemplate", "HealthDeclaration", "HealthFormQuestionAnswer", "HealthPlanTask", "HealthProfessionalModel", "HealthcareTeamModel", "MonitoringTriggerConfiguration", "MonitoringTriggerRecord", "MonitoringTriggerRecordAction", "PersonCase", "PersonClinicalAccount", "PersonInternalReference", "PersonModel", "Risk", "StaffModel", "StructuredAddress"]}