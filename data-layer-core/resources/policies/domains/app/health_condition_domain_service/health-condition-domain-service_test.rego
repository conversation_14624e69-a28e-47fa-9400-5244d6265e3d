package app.health_condition_domain_service_test

import rego.v1

import data.app.health_condition_domain_service

test_unauth_view_models_allowed if {
    resources = [
        "HealthDeclaration",
        "HealthConditionAxis",
        "Appointment",
        "HealthFormQuestionAnswer",
        "PersonModel",
        "Risk",
        "PersonClinicalAccount",
        "HealthPlanTask",
        "HealthcareTeamModel",
        "PersonInternalReference",
        "MonitoringTriggerRecord",
        "MonitoringTriggerConfiguration",
        "MonitoringTriggerRecordAction",
        "Channel",
        "HealthProfessionalModel",
        "StructuredAddress",
        "ContactModel"
    ]

    every resource in resources {
        {1} == health_condition_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_and_create_and_update_models_allowed if {
    resources = [
        "CaseRecord",
        "PersonCase",
        "HealthConditionTemplate",
        "MonitoringTriggerRecord",
        "MonitoringTriggerConfiguration",
        "MonitoringTriggerRecordAction",
        "Channel"
    ]

    every resource in resources {
        {1, 2, 3} == health_condition_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "create",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_and_count_models_allowed if {
    resources = [
        "StaffModel",
        "MonitoringTriggerRecord",
        "MonitoringTriggerConfiguration"
    ]

    every resource in resources {
        {1, 2} == health_condition_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_and_update_models_allowed if {
    resources = [
        "HealthCondition"
    ]

    every resource in resources {
        {1, 2} == health_condition_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "update",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}

test_unauth_view_and_create_models_allowed if {
    resources = [
        "HealthConditionRelated"
    ]

    every resource in resources {
        {1, 2} == health_condition_domain_service.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            },
            {
                "index": 2,
                "action": "create",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource},
            }
        ]}
    }
}
