package app.sales_channel_api_test

import rego.v1

import data.app.sales_channel_api

test_unauthenticated_view_only_allowed if {
    resources = [
        "HubspotTicket"
    ]

    every resource in resources {
        {1} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_unauthenticated_view_count_allowed if {
    resources = [
        "CompanyModel",
        "CompanyContractModel",
        "CompanySubContractModel",
        "GenericFileVault",
        "HubspotTicket",
        "SalesFirm"
    ]

    every resource in resources {
        {1,2} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_unauthenticated_view_update_create_allowed if {
    resources = [
        "OngoingCompanyDeal",
        "SalesFirmStaff"
    ]

    every resource in resources {
        {1,2,3} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource}
            },
            {
                "index": 2,
                "action": "update",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource}
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_unauthenticated_view_count_update_create_allowed if {
    resources = [
        "SalesAgent"
    ]

    every resource in resources {
        {1,2,3,4} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource}
            },
            {
                "index": 4,
                "action": "create",
                "subject": {"opaType": "Unauthenticated"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_sales_firm_staff_view_only_allowed if {
    resources = [
        "BolepixPaymentDetailModel",
        "CompanyProductPriceListingModel",
        "HubspotTicket", 
        "InvoicePaymentModel",
        "ResourceSignTokenModel",
        "MemberInvoiceGroupModel",
        "MemberModel",
        "MemberProductPriceModel",
        "PaymentDetailModel",
        "PriceListingModel",
        "ProductPriceListingModel"
    ]

    every resource in resources {
        {1} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "SalesFirmStaff"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_sales_firm_staff_view_count_allowed_part1 if {
    resources = [
        "AppointmentScheduleModel",
        "BeneficiaryModel",
        "BeneficiaryCompiledViewModel",
        "BeneficiaryOnboarding",
        "BeneficiaryOnboardingPhase",
        "BillingAccountablePartyModel"
    ]

    every resource in resources {
        {1,2} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "SalesFirmStaff"},
                "resource": {"opaType": resource}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "SalesFirmStaff"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_sales_firm_staff_view_count_allowed_part2 if {
    resources = [
        "CassiMember",
        "CompanyModel",
        "CompanyContractModel",
        "CompanySubContractModel",
        "GenericFileVault",
        "OngoingCompanyDeal"
    ]

    every resource in resources {
        {1,2} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "SalesFirmStaff"},
                "resource": {"opaType": resource}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "SalesFirmStaff"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_sales_firm_staff_view_count_allowed_part3 if {
    resources = [
        "PersonModel",
        "PriceListingModel",
        "ProductModel",
        "ProductPriceListingModel",
        "SalesFirm",
        "SalesFirmAgentPartnership"
    ]

    every resource in resources {
        {1,2} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "SalesFirmStaff"},
                "resource": {"opaType": resource}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "SalesFirmStaff"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_sales_firm_staff_view_count_create_update_allowed if {
    resources = [
        "SalesAgent"
    ]

    every resource in resources {
        {1,2,3,4} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "SalesFirmStaff"},
                "resource": {"opaType": resource}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "SalesFirmStaff"},
                "resource": {"opaType": resource}
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "SalesFirmStaff"},
                "resource": {"opaType": resource}
            },
            {
                "index": 4,
                "action": "update",
                "subject": {"opaType": "SalesFirmStaff"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_sales_firm_staff_view_create_update_allowed if {
    resources = [
        "SalesFirmAgentPartnership",
        "SalesFirmStaff"
    ]

    every resource in resources {
        {1,2,3} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "SalesFirmStaff"},
                "resource": {"opaType": resource}
            },
            {
                "index": 2,
                "action": "create",
                "subject": {"opaType": "SalesFirmStaff"},
                "resource": {"opaType": resource}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "SalesFirmStaff"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_sales_agent_view_only_allowed if {
    resources = [
        "BolepixPaymentDetailModel",
        "CompanyProductPriceListingModel",
        "HubspotTicket",
        "InvoicePaymentModel",
        "ResourceSignTokenModel",
        "MemberInvoiceGroupModel",
        "MemberModel",
        "MemberProductPriceModel",
        "PaymentDetailModel",
        "PriceListingModel",
        "ProductPriceListingModel"
    ]

    every resource in resources {
        {1} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "SalesAgent"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_sales_agent_view_count_allowed_part1 if {
    resources = [
        "AppointmentScheduleModel",
        "BeneficiaryModel",
        "BeneficiaryCompiledViewModel",
        "BeneficiaryOnboarding",
        "BeneficiaryOnboardingPhase",
        "BillingAccountablePartyModel"
    ]

    every resource in resources {
        {1,2} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "SalesAgent"},
                "resource": {"opaType": resource}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "SalesAgent"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_sales_agent_view_count_allowed_part2 if {
    resources = [
        "CassiMember",
        "CompanyModel",
        "CompanyContractModel",
        "CompanySubContractModel",
        "GenericFileVault",
        "OngoingCompanyDeal"
    ]

    every resource in resources {
        {1,2} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "SalesAgent"},
                "resource": {"opaType": resource}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "SalesAgent"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_sales_agent_view_count_allowed_part3 if {
    resources = [
        "PersonModel",
        "PriceListingModel", 
        "ProductModel",
        "ProductPriceListingModel",
        "SalesFirm"
    ]

    every resource in resources {
        {1,2} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "SalesAgent"},
                "resource": {"opaType": resource}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "SalesAgent"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_sales_agent_view_create_update_allowed if {
    resources = [
        "SalesFirmAgentPartnership",
        "SalesFirmStaff"
    ]

    every resource in resources {
        {1,2,3} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "SalesAgent"},
                "resource": {"opaType": resource}
            },
            {
                "index": 2,
                "action": "create",
                "subject": {"opaType": "SalesAgent"},
                "resource": {"opaType": resource}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {"opaType": "SalesAgent"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_sales_agent_view_count_create_allowed if {
    resources = [
        "SalesAgent"
    ]

    every resource in resources {
        {1,2,3,4} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {"opaType": "SalesAgent"},
                "resource": {"opaType": resource}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {"opaType": "SalesAgent"},
                "resource": {"opaType": resource}
            },
            {
                "index": 3,
                "action": "create",
                "subject": {"opaType": "SalesAgent"},
                "resource": {"opaType": resource}
            },
            {
                "index": 4,
                "action": "update",
                "subject": {"opaType": "SalesAgent"},
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_staff_model_product_tech_view_count_create_update_allowed if {
    resources = [
        "SalesAgent"
    ]

    every resource in resources {
        {1,2,3,4} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH"
                },
                "resource": {"opaType": resource}
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH"
                },
                "resource": {"opaType": resource}
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH"
                },
                "resource": {"opaType": resource}
            },
            {
                "index": 4,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH"
                },
                "resource": {"opaType": resource}
            }
        ]}
    }
}

test_staff_model_product_tech_view_create_update_allowed if {
    resources = [
        "SalesFirmAgentPartnership"
    ]

    every resource in resources {
        {1,2,3} == sales_channel_api.allow with input as {"cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH"
                },
                "resource": {"opaType": resource}
            },
            {
                "index": 2,
                "action": "create",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH"
                },
                "resource": {"opaType": resource}
            },
            {
                "index": 3,
                "action": "update",
                "subject": {
                    "opaType": "StaffModel",
                    "role": "PRODUCT_TECH"
                },
                "resource": {"opaType": resource}
            }
        ]}
    }
}
