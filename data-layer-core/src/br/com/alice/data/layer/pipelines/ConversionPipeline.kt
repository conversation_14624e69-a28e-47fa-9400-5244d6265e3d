package br.com.alice.data.layer.pipelines

import br.com.alice.common.Converter
import br.com.alice.common.core.Model
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.data.layer.tables.Table
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import kotlin.reflect.KClass

//TODO: refactor other pipelines to work with Table so this is not a necessary pipeline step
internal class ConversionPipeline<M : Model, T : Table<T>>(
    private val modelClass: KClass<M>,
    val databasePipeline: DatabasePipeline<T>,
    private val converter: Converter<M, T>
) : DatabasePipeline<M> {

    private fun M.toTable(): T = converter.convert(this)
    private fun T.toModel(): M = converter.unconvert(this)

    override suspend fun get(id: Any) = span {
        databasePipeline.get(id).map { it.toModel() }
    }

    override suspend fun add(item: M) = span {
        databasePipeline.add(item.toTable()).map { it.toModel() }
    }

    override suspend fun update(item: M) = span {
        databasePipeline.update(item.toTable()).map { it.toModel() }
    }

    override suspend fun softDelete(item: M) = span {
        databasePipeline.softDelete(item.toTable())
    }

    override suspend fun delete(item: M) = span {
        databasePipeline.delete(item.toTable())
    }

    override suspend fun findByQuery(query: Query): Result<List<M>, Throwable> = span {
        databasePipeline.findByQuery(query).map { list -> list.pmap { it.toModel() } }
    }

    override suspend fun findAuthorizedByQuery(query: Query): Result<List<M>, Throwable> = findByQuery(query)

    override suspend fun countByQuery(query: Query): Result<Int, Throwable> = span {
        databasePipeline.countByQuery(query)
    }

    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable> = span {
        databasePipeline.countGroupedByQuery(query)
    }

    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable> = span {
        databasePipeline.existsByQuery(query)
    }

    override suspend fun anonymize(id: Any): Result<Boolean, Throwable> = span {
        databasePipeline.anonymize(id)
    }

}
