package br.com.alice.data.layer.pipelines

import br.com.alice.common.core.extensions.copyMappingNested
import br.com.alice.common.observability.opentelemetry.Tracer
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.data.layer.repositories.JdbiRepository
import br.com.alice.data.layer.services.NaiveTextualDeIdentificationService
import br.com.alice.data.layer.services.PersonTokenService
import br.com.alice.data.layer.tables.DeIdentifiedReference
import br.com.alice.data.layer.tables.PersonNonPiiReference
import br.com.alice.data.layer.tables.PersonTable
import br.com.alice.data.layer.tables.Table
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import io.opentelemetry.api.trace.Span
import io.opentelemetry.api.trace.SpanKind
import org.jdbi.v3.core.Jdbi

internal class TextualDeIdentificationPipeline<T : Table<T>>(
    private val databasePipeline: DatabasePipeline<T>,
    private val jdbi: Jdbi,
    private val personTokenService: PersonTokenService,
    private val personRepository: JdbiRepository<PersonTable> = JdbiRepository(jdbi, PersonTable::class)
) : DatabasePipeline<T> {

    private val textualDeIdentificationService = NaiveTextualDeIdentificationService

    override suspend fun get(id: Any): Result<T, Throwable> = span {
        identifyResult(databasePipeline.get(id))
    }

    override suspend fun add(item: T): Result<T, Throwable> = span {
        identifyResult(databasePipeline.add(deIdentifyModel(item)))
    }

    override suspend fun update(item: T): Result<T, Throwable> = span {
        identifyResult(databasePipeline.update(deIdentifyModel(item)))
    }

    override suspend fun softDelete(item: T): Result<Boolean, Throwable> = span {
        databasePipeline.softDelete(item)
    }

    override suspend fun delete(item: T): Result<Boolean, Throwable> = span {
        databasePipeline.delete(item)
    }

    override suspend fun findByQuery(query: Query): Result<List<T>, Throwable> = span {
        identifyResultList(databasePipeline.findByQuery(query))
    }

    override suspend fun findAuthorizedByQuery(query: Query): Result<List<T>, Throwable> = findByQuery(query)

    override suspend fun countByQuery(query: Query): Result<Int, Throwable> = span {
        databasePipeline.countByQuery(query)
    }

    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable> = span {
        databasePipeline.countGroupedByQuery(query)
    }

    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable> = span {
        databasePipeline.existsByQuery(query)
    }

    override suspend fun anonymize(id: Any): Result<Boolean, Throwable> = span {
        databasePipeline.anonymize(id)
    }

    private fun deIdentifyModel(item: T): T =
        deIdentifyOrIdentifyModel(item, IdentityOperation.DE_IDENTIFY)

    private fun identifyResult(result: Result<T, Throwable>): Result<T, Throwable> =
        result.map { item -> identifyModel(item) }

    private fun identifyResultList(result: Result<List<T>, Throwable>): Result<List<T>, Throwable> =
        result.map { list -> identifyModelList(list) }

    private fun identifyModel(item: T): T =
        deIdentifyOrIdentifyModel(item, IdentityOperation.IDENTIFY)

    @Suppress("UNCHECKED_CAST")
    private fun deIdentifyOrIdentifyModel(item: T, op: IdentityOperation): T =
        if (item.canProcess()) {
            val personPiiToken = personTokenService.getPersonPiiToken((item as PersonNonPiiReference).personId)!!

            val person = personRepository.get(personPiiToken)!!

            when (op) {
                IdentityOperation.IDENTIFY -> item.copyMappingNested<String> {
                    textualDeIdentificationService.identify(it, person.toPII())
                }
                IdentityOperation.DE_IDENTIFY -> item.copyMappingNested<String> {
                    textualDeIdentificationService.deIdentify(it, person.toPII())
                }
            } as T
        } else item

    @Suppress("UNCHECKED_CAST")
    private fun identifyModelList(list: List<T>): List<T> = span("identifyModelList") {
        if (list.isNotEmpty() && list.first().canProcess()) {
            val personNonPiiTokens = list.map { (it as PersonNonPiiReference).personId }.distinct()

            val nonPiiTokensToPiiTokens = personTokenService.getPersonPiiTokens(personNonPiiTokens)
            val piiTokens = nonPiiTokensToPiiTokens.values.map { it.id }.toList()
            val people = personRepository.find(Query(where = Predicate.inList(Field.UUIDField(PersonTable::id), piiTokens)))
            val peopleByPiiToken = people.associateBy { it.id }

            list.map { item ->
                val person = peopleByPiiToken[nonPiiTokensToPiiTokens[(item as PersonNonPiiReference).personId]]!!
                item.copyMappingNested<String> {
                    textualDeIdentificationService.identify(it, person.toPII())
                } as T
            }
        } else list
    }

    enum class IdentityOperation { IDENTIFY, DE_IDENTIFY }

    private fun <T> span(methodName: String, block: (Span) -> T): T =
        Tracer.spanSync("TextualDeIdentificationPipeline.$methodName", SpanKind.INTERNAL) { span ->
            span.setAttribute("service_type", "TextualDeIdentificationPipeline")
            block(span)
        }

    private fun T.canProcess() = this is PersonNonPiiReference && this is DeIdentifiedReference
}
