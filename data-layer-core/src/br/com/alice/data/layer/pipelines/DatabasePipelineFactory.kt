package br.com.alice.data.layer.pipelines

import br.com.alice.authentication.TokenVerifier
import br.com.alice.common.Converter
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.authorization.AuthorizationService
import br.com.alice.data.layer.models.UpdatedByReference
import br.com.alice.data.layer.pipelines.context.ContextService
import br.com.alice.data.layer.repositories.JdbiRepository
import br.com.alice.data.layer.services.PersonTokenService
import br.com.alice.data.layer.services.ReplicationLagService
import br.com.alice.data.layer.tables.DeIdentifiedReference
import br.com.alice.data.layer.tables.Table
import org.jdbi.v3.core.Jdbi
import kotlin.reflect.KClass
import kotlin.reflect.full.isSubclassOf
import kotlin.reflect.full.memberProperties

internal class DatabasePipelineFactory(
    private val jdbi: Jdbi,
    private val jdbiReadDb: Jdbi,
    private val authorizationService: AuthorizationService,
    private val tokenVerifier: TokenVerifier,
    private val personTokenService: PersonTokenService,
    private val contextService: ContextService,
    private val replicationLagService: ReplicationLagService,
) {

    fun <M : Model, T : Table<T>> get(
        modelClass: KClass<M>,
        tableClass: KClass<T>,
        converter: Converter<M, T>? = null
    ): DatabasePipeline<M> =
        AuthorizationPipeline(
            modelClass = modelClass,
            tableClass = tableClass,
            databasePipeline = getWithoutAuth(modelClass, tableClass, converter),
            authorizationService = authorizationService,
            contextService = contextService,
            factory = this
        )

    fun <M : Model, T : Table<T>> getWithoutAuth(
        modelClass: KClass<M>,
        tableClass: KClass<T>,
        converter: Converter<M, T>? = null
    ): DatabasePipeline<M> {
        val finalConverter = converter ?: ConverterExtension.converter(modelClass, tableClass, personTokenService)
        val repository = JdbiRepository(jdbi, tableClass, jdbiReadDb)

        return JdbiAdapterPipeline(repository, tokenVerifier, replicationLagService)
            .`if`(tableClass.isTextualDeIdentified()) { TextualDeIdentificationPipeline(it, jdbi, personTokenService) }
            .`if`(modelClass.isPersonReference()) { DeIdentificationPipeline(tableClass, it, personTokenService) }
            .let { ConversionPipeline(modelClass, it, finalConverter) }
            .`if`(modelClass.isAuditable()) { AuditPipeline(contextService, it, personTokenService) }
    }

    private fun <M : Model> KClass<M>.isAuditable() = this.isSubclassOf(UpdatedByReference::class)
    private fun <M : Model> KClass<M>.isPersonReference() = this.memberProperties.find { it.returnType.classifier == PersonId::class } != null
    private fun <T : Table<T>> KClass<T>.isTextualDeIdentified() = this.isSubclassOf(DeIdentifiedReference::class)

    private fun <T: Any> DatabasePipeline<T>.`if`(
        predicate: Boolean,
        block: (DatabasePipeline<T>) -> DatabasePipeline<T>
    ): DatabasePipeline<T> =
        if (predicate) block(this)
        else this

}

