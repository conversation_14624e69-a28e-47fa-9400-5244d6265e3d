package br.com.alice.data.layer.pipelines.subjects.builders.data

import br.com.alice.common.core.Model
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.Table

internal abstract class SubjectEnricherDataService(
    private val factory: DatabasePipelineFactory,
) {

    protected inline fun <reified M: Model, reified T: Table<T>> getPipeline() =
        factory.getWithoutAuth(M::class, T::class)
}
