package br.com.alice.data.layer.policies.features

import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.CaseRecord
import br.com.alice.data.layer.models.HealthCondition
import br.com.alice.data.layer.models.HealthConditionAxis
import br.com.alice.data.layer.models.HealthConditionRelated
import br.com.alice.data.layer.models.HealthConditionTemplate
import br.com.alice.data.layer.models.HealthDeclaration
import br.com.alice.data.layer.models.HealthFormQuestionAnswer
import br.com.alice.data.layer.models.HealthPlanTask
import br.com.alice.data.layer.models.HealthcareTeamModel
import br.com.alice.data.layer.models.MonitoringTriggerConfiguration
import br.com.alice.data.layer.models.MonitoringTriggerRecord
import br.com.alice.data.layer.models.MonitoringTriggerRecordAction
import br.com.alice.data.layer.models.PersonCase
import br.com.alice.data.layer.models.PersonClinicalAccount
import br.com.alice.data.layer.models.PersonInternalReference
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.models.Risk

val createCaseRecordForHealthDeclaration = policySet {
    match("can view", { action is View }) {
        match("any CaseRecord") { resourceIs(CaseRecord::class) }
        match("any HealthDeclaration") { resourceIs(HealthDeclaration::class) }
        match("any HealthCondition") { resourceIs(HealthCondition::class) }
        match("any HealthConditionTemplate") { resourceIs(HealthConditionTemplate::class) }
        match("any HealthConditionAxis") { resourceIs(HealthConditionAxis::class) }
        match("any HealthConditionRelated") { resourceIs(HealthConditionRelated::class) }
    }
    match("can Create", { action is Create }) {
        match("any CaseRecord") { resourceIs(CaseRecord::class) }
    }
}

val backfillCaseRecord = policySet {
    allows(CaseRecord::class, View, Update)
    allows(HealthCondition::class, View)
    allows(Appointment::class, View)
}

val createAndUpdatePersonCase = policySet {
    match("can view, create and updated", { action is View || action is Create || action is Update }) {
        match("any PersonCase") { resourceIs(PersonCase::class)}
    }
}

val viewHealthFormQuestionAnswer = policySet {
    match("can view", { action is View }) {
        match("any HealthFormQuestionAnswer") { resourceIs(HealthFormQuestionAnswer::class) }
    }
}

val viewPersonData = policySet {
    match("can view", { action is View }) {
        match("any PersonModel") { resourceIs(PersonModel::class) }
    }
}

val viewRiskData = policySet {
    match("can view", { action is View }) {
        match("any Risk") { resourceIs(Risk::class) }
    }
}

val addDemandOfCounterReferral = policySet {
    match("can view",  { action is View }) {
        match("any HealthPlanTask") { resourceIs(HealthPlanTask::class) }
        match("any PersonClinicalAccount") { resourceIs(PersonClinicalAccount::class)}
        match("any HealthcareTeamModel") { resourceIs(HealthcareTeamModel::class)}
    }
}

val backfillPersonCase = policySet {
    allows(PersonClinicalAccount::class, View)
    allows(CaseRecord::class, View)
    allows(PersonCase::class, View, Create, Update)
}

val backfillUpdateHealthCondition = policySet {
    allows(HealthCondition::class, View, Update)
}

val backfillCreateHealthConditionRelated = policySet {
    allows(HealthConditionRelated::class, View, Create)
    allows(HealthCondition::class, View)
}

val backfillUpsertHealthConditionTemplate = policySet {
    allows(HealthCondition::class, View)
    allows(HealthConditionTemplate::class, View, Create, Update)
}
val backfillCreateCaseRecordFromDisease = policySet {
    allows(PersonInternalReference::class, View)
}

val viewCreateUpdateMonitoringTriggers = policySet {
    allows(MonitoringTriggerRecord::class, View, Count, Create, Update)
    allows(MonitoringTriggerConfiguration::class, View, Count, Create, Update)
    allows(MonitoringTriggerRecordAction::class, View, Count, Create, Update)
}
