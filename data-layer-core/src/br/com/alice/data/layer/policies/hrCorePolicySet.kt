package br.com.alice.data.layer.policies

import br.com.alice.data.layer.HR_CORE_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.HR_CORE_DOMAIN_SUBSCRIBERS_SERVICE_NAME
import br.com.alice.data.layer.HR_CORE_ENVIRONMENT_BACKFILL
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.BeneficiaryModel
import br.com.alice.data.layer.models.ClinicalOutcomeRecord
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyModel
import br.com.alice.data.layer.models.CompanyScoreMagenta
import br.com.alice.data.layer.models.ConsolidatedHRCompanyReport
import br.com.alice.data.layer.models.HrMemberUploadTracking
import br.com.alice.data.layer.models.CompanyProductPriceListingModel
import br.com.alice.data.layer.models.CompanySubContractModel
import br.com.alice.data.layer.models.MemberModel
import br.com.alice.data.layer.models.PriceListingModel
import br.com.alice.data.layer.models.ProductModel
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.models.PersonBillingAccountablePartyModel
import br.com.alice.data.layer.models.PersonOnboardingModel
import br.com.alice.data.layer.models.PersonRegistrationModel
import br.com.alice.data.layer.models.ProductPriceListingModel
import br.com.alice.data.layer.models.CompanyContractModel
import br.com.alice.data.layer.models.BillingAccountablePartyModel
import br.com.alice.data.layer.models.HealthDeclaration
import br.com.alice.data.layer.subjects.Unauthenticated

val hrCorePolicySet = policySet {
    match("at hr-core subscribers", { rootService.name == HR_CORE_DOMAIN_SUBSCRIBERS_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            allows(CompanyModel::class, View)
            allows(ClinicalOutcomeRecord::class, View)

            allows(ConsolidatedHRCompanyReport::class, View, Create, Update)
            allows(CompanyScoreMagenta::class, View, Create, Update, Delete)
            allows(HrMemberUploadTracking::class, View, Count, Update, Create)
            allows(PriceListingModel::class, View, Count)
            allows(ProductModel::class, View, Count)
            allows(CompanySubContractModel::class, View, Count)
            allows(CompanyProductPriceListingModel::class, View, Count)
            allows(CompanyContractModel::class, View, Count)
            allows(ProductPriceListingModel::class, View, Count)

            allows(PersonBillingAccountablePartyModel::class, View, Count, Create, Update)
            allows(PersonOnboardingModel::class, View, Count, Create, Update)
            allows(PersonRegistrationModel::class, View, Count, Create, Update)
            allows(PersonModel::class, View, Count, Create, Update)
            allows(MemberModel::class, View, Count, Create, Update)
            allows(BillingAccountablePartyModel::class, View, Count, Create, Update)
            allows(BeneficiaryModel::class, View, Count, Create, Update)
            allows(HealthDeclaration::class, View, Count, Create, Update)

        }
    }

    match("at hr-core backfill", { rootService.name == HR_CORE_ENVIRONMENT_BACKFILL }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            allows(CompanyModel::class, View)
            allows(CompanyScoreMagenta::class, View, Delete)
        }
    }

    match("at hr-core recurrent", { rootService.name == HR_CORE_DOMAIN_ROOT_SERVICE_NAME }) {
        match("Unauthenticated", { subject is Unauthenticated }) {
            allows(CompanyModel::class, View)
            allows(CompanyScoreMagenta::class, View, Delete)
        }
    }
}
