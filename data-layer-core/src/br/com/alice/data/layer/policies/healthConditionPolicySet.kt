package br.com.alice.data.layer.policies

import br.com.alice.data.layer.HEALTH_CONDITION_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.policies.features.addDemandOfCounterReferral
import br.com.alice.data.layer.policies.features.backfillCaseRecord
import br.com.alice.data.layer.policies.features.backfillCreateCaseRecordFromDisease
import br.com.alice.data.layer.policies.features.backfillCreateHealthConditionRelated
import br.com.alice.data.layer.policies.features.backfillPersonCase
import br.com.alice.data.layer.policies.features.backfillUpdateHealthCondition
import br.com.alice.data.layer.policies.features.backfillUpsertHealthConditionTemplate
import br.com.alice.data.layer.policies.features.createAndUpdatePersonCase
import br.com.alice.data.layer.policies.features.createCaseRecordForHealthDeclaration
import br.com.alice.data.layer.policies.features.viewCreateUpdateChannel
import br.com.alice.data.layer.policies.features.viewCreateUpdateMonitoringTriggers
import br.com.alice.data.layer.policies.features.viewHealthFormQuestionAnswer
import br.com.alice.data.layer.policies.features.viewHealthProfessional
import br.com.alice.data.layer.policies.features.viewPersonData
import br.com.alice.data.layer.policies.features.viewRiskData
import br.com.alice.data.layer.policies.features.viewStaff

val healthConditionPolicySet = policySet {
    match("at health condition domain service", { rootService.name == HEALTH_CONDITION_DOMAIN_ROOT_SERVICE_NAME }) {
        includes(createCaseRecordForHealthDeclaration)
        includes(backfillCaseRecord)
        includes(createAndUpdatePersonCase)
        includes(viewHealthFormQuestionAnswer)
        includes(viewPersonData)
        includes(viewRiskData)
        includes(viewStaff)
        includes(backfillPersonCase)
        includes(addDemandOfCounterReferral)
        includes(backfillUpdateHealthCondition)
        includes(backfillCreateHealthConditionRelated)
        includes(backfillUpsertHealthConditionTemplate)
        includes(backfillCreateCaseRecordFromDisease)
        includes(viewCreateUpdateMonitoringTriggers)
        includes(viewHealthProfessional)
        includes(viewCreateUpdateChannel)
    }
}
