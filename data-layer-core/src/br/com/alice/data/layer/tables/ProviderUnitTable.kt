package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.MedicalSpecialtyProfileModel
import br.com.alice.data.layer.models.ProviderUnitModel
import br.com.alice.data.layer.models.Qualification
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.extensions.toCNPJMask
import br.com.alice.data.layer.models.PhoneNumber
import br.com.alice.data.layer.models.ProviderUnit
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class ProviderUnitTable(
    val type: ProviderUnit.Type,
    val name: String,
    val contractOrigin: ProviderUnit.Origin = ProviderUnit.Origin.ALICE,
    val site: String? = null,
    val cnpj: String? = null,
    val cnes: String? = null,
    val bankCode: String? = null,
    val agencyNumber: String? = null,
    val accountNumber: String? = null,
    val phones: List<PhoneNumber> = emptyList(),
    val workingPeriods: List<ProviderUnitModel.WorkingPeriodModel> = emptyList(),
    val qualifications: List<Qualification> = emptyList(),
    val imageUrl: String? = null,
    val providerId: UUID,
    val providerUnitGroupId: UUID? = null,
    val clinicalStaffIds: List<UUID>? = emptyList(),
    val administrativeStaff: List<UUID>? = emptyList(),
    val brand: Brand = Brand.ALICE,
    val externalBrandId: String? = null,
    val medicalSpecialtyProfile: List<MedicalSpecialtyProfileModel>? = emptyList(),
    val urlSlug: String? = null,
    val status: Status = Status.ACTIVE,
    var searchTokens: TsVector? = null,
    val deAccreditationDate: LocalDate? = null,
    val showOnScheduler: Boolean = false,
    val attendanceTypes: List<ProviderUnit.AttendanceType>? = emptyList(),
    val hasHospitalHealthTeam: Boolean = false,
    val showOnApp: Boolean = true,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UpdatedBy? = null
) : Table<ProviderUnitTable> {
    init {
        val value = listOfNotNull(name, cnpj, cnpj?.toCNPJMask(), cnes)
            .filter { it.isNotEmpty() }
            .joinToString(" ")
        searchTokens = TsVector(value.unaccent())
    }

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
