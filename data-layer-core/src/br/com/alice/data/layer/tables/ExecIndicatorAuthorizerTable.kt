package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import java.time.LocalDateTime
import java.util.UUID

internal data class ExecIndicatorAuthorizerTable(
    override val id: UUID = RangeUUID.generate(),
    val providerUnitId: UUID,
    val domain: String,
    val mvCdPrestador: Int,
    val mvCdLocalPrestador: Int,
    val mvCdLocal: Int,
    val passwordKey: String,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val deletedAt: LocalDateTime? = null
) : Table<ExecIndicatorAuthorizerTable>, SoftDeletable {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
