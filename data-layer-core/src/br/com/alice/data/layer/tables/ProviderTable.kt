package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.extensions.toCNPJMask
import br.com.alice.data.layer.models.PhoneNumber
import java.time.LocalDateTime
import java.util.UUID

data class ProviderTable(
    val name: String,
    val site: String? = null,
    val cnpj: String? = null,
    val phones: List<PhoneNumber> = emptyList(),
    val imageUrl: String? = null,
    val urlSlug: String? = null,
    var searchTokens: TsVector? = null,
    val type: ProviderType,
    val flagship: Boolean = false,
    val description: String? = null,
    val icon: String? = null,
    val logo: String? = null,
    val thumbnail: String? = null,
    val about: String? = null,
    val daysForPayment: Int = 30,
    val brand: Brand? = Brand.ALICE,
    val externalBrandId: String? = null,
    val status: Status = Status.ACTIVE,
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UpdatedBy? = null
) : Table<ProviderTable> {

    init {
        val value = listOfNotNull(name, cnpj, cnpj?.toCNPJMask())
            .filter { it.isNotEmpty() }
            .joinToString(" ")
        searchTokens = TsVector(value.unaccent())
    }

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
