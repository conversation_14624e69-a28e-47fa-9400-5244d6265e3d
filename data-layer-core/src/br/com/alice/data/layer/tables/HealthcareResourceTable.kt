package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.service.data.client.TsVector
import br.com.alice.data.layer.models.HealthcareResourceCategory
import br.com.alice.data.layer.models.HealthcareResourceType
import br.com.alice.data.layer.models.ResourceGracePeriodType
import java.time.LocalDateTime
import java.util.UUID

internal data class HealthcareResourceTable(
    override val id: UUID = RangeUUID.generate(),
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),

    val nrol: Boolean,
    val dut: Boolean,
    val pac: Boolean,
    val code: String,
    val description: String,
    var searchTokens: TsVector? = null,
    val type: HealthcareResourceType,
    val category: HealthcareResourceCategory,
    val gracePeriodType: ResourceGracePeriodType? = null,
    val tussCode: String? = null,
    val tableType: String? = null,
    var isOriginalTuss: Boolean? = null,
    val tussTableType: String? = null,
    val compositionHash: String? = null,
    val active: Boolean = true,
) : Table<HealthcareResourceTable> {

    init {
        val value = "$code $description ${type.description} $tussCode"
        searchTokens = TsVector(value.unaccent())
        isOriginalTuss = tussCode == code && tableType == tussTableType
    }

    override fun copyTable(
        version: Int,
        updatedAt: LocalDateTime,
        createdAt: LocalDateTime
    ) =
        copy(
            version = version,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
}
