openapi: 3.0.3
info:
  title: Backoffice BFF
  description: Backoffice BFF
  version: 1.0.0
servers:
  - url: https://backoffice-bff-api-dev2.dev.wonderland.engineering
    description: Dev2 Environment
  - url: https://backoffice-bff-api-dev1.dev.wonderland.engineering
    description: Dev1 Environment
  - url: https://backoffice-bff-api.staging.wonderland.engineering
    description: Staging Environment
  - url: https://backoffice-bff-api.wonderland.engineering
    description: Production Environment
paths:
  /signIn:
    post:
      operationId: signIn
      tags:
        - Auth
      summary: Set claims
      description: Set claims
      requestBody:
        description: Firebase token ID
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignIn'
      responses:
        '200':
          description: successful operation
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /featureConfig/{id}:
    get:
      operationId: getFeatureFlag
      security:
        - bearerAuth: []
      tags:
        - Feature Flags
      summary: Listar feature flags
      description: Listar feature flags
      parameters:
        - name: id
          in: path
          description: ID of feature flag to return
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureFlagFullResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    delete:
      operationId: deleteFeatureFlag
      security:
        - bearerAuth: []
      tags:
        - Feature Flags
      summary: Excluir feature flags
      description: Excluir feature flags
      parameters:
        - name: id
          in: path
          description: ID of feature flag to return
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
        '4XX':
          $ref: '#/components/responses/BadRequest'
    put:
      operationId: updateFeatureFlag
      security:
        - bearerAuth: []
      tags:
        - Feature Flags
      summary: Atualizar feature flags
      description: Atualizar feature flags
      parameters:
        - name: id
          in: path
          description: ID of feature flag to return
          required: true
          schema:
            type: string
      requestBody:
        description: Update an existent feature flag
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateFeatureFlag'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureFlagFullResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /featureConfig/namespaces:
    get:
      operationId: getFeatureFlagNamespaces
      security:
        - bearerAuth: []
      tags:
        - Feature Flags
      summary: Listar feature namespaces
      description: Listar feature namespaces
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureNamespaceResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /featureConfig/types:
    get:
      operationId: getFeatureFlagTypes
      security:
        - bearerAuth: []
      tags:
        - Feature Flags
      summary: Listar feature types
      description: Listar feature types
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureTypeResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /featureConfig:
    post:
      operationId: createFeatureFlag
      security:
        - bearerAuth: []
      tags:
        - Feature Flags
      summary: Criar feature flags
      description: Criar feature flags
      requestBody:
        description: Update an existent feature flag
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFeatureFlag'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureFlagFullResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    get:
      operationId: listFeatureFlags
      security:
        - bearerAuth: []
      tags:
        - Feature Flags
      summary: Listar feature flags
      description: Listar feature flags
      parameters:
        - name: filter
          in: query
          description: Namespace or key
          example: '{namespace: "schedule"}'
          required: false
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureConfigPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /company:
    get:
      operationId: listCompanies
      security:
        - bearerAuth: []
      tags:
        - Companies
      summary: Listar empresas
      description: Listar empresas
      parameters:
        - name: range
          in: query
          description: range para paginação
          required: true
          schema:
            type: string
        - name: filter
          in: query
          description: objeto com filtros
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /companyStaff:
    post:
      operationId: createCompanyStaff
      security:
        - bearerAuth: []
      tags:
        - Companies
      summary: Listar usuários do portal do RH
      description: Listar usuários do portal do RH
      parameters:
        - name: range
          in: query
          description: range para paginação
          required: true
          schema:
            type: string
        - name: filter
          in: query
          description: objeto com filtros
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCompanyStaffRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyStaffPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    get:
      operationId: listCompanyStaff
      security:
        - bearerAuth: []
      tags:
        - Companies
      summary: Listar usuários do portal do RH
      description: Listar usuários do portal do RH
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyStaffPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /companyStaff/{id}:
    put:
      operationId: updateCompanyStaff
      security:
        - bearerAuth: []
      tags:
        - Companies
      summary: Atualizar o usuário do portal do RH
      description: Atualizar o usuário do portal do RH
      parameters:
        - name: id
          in: path
          description: ID da staff
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCompanyStaffRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyStaffResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    delete:
      operationId: deleteCompanyStaff
      security:
        - bearerAuth: []
      tags:
        - Companies
      summary: Deletar usuário do portal do RH
      description: Deletar usuário do portal do RH
      parameters:
        - name: id
          in: path
          description: ID da staff
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
        '4XX':
          $ref: '#/components/responses/BadRequest'
    get:
      operationId: getCompanyStaff
      security:
        - bearerAuth: []
      tags:
        - Companies
      summary: Get usuário do portal do RH
      description: Get usuário do portal do RH
      parameters:
        - name: id
          in: path
          description: ID da staff
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyStaffResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /companyStaff/roles:
    get:
      operationId: getCompanyStaffRoles
      security:
        - bearerAuth: []
      tags:
        - Companies
      summary: Listar roles de company staff
      description: Listar roles de company staff
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureTypeResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /salesAgent:
    get:
      operationId: listSalesAgents
      security:
        - bearerAuth: []
      tags:
        - Sales Agents
      summary: Listar corretores
      description: Listar corretores
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalesAgentPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    post:
      operationId: createSalesAgent
      security:
        - bearerAuth: []
      tags:
        - Sales Agents
      summary: Criar Corretor
      description: Criar Corretor
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSalesAgentRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalesAgentResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /salesAgent/{id}:
    get:
      operationId: getSalesAgent
      security:
        - bearerAuth: []
      tags:
        - Sales Agents
      summary: Buscar Corretor
      description: Buscar Corretor
      parameters:
        - name: id
          in: path
          description: ID do corretor
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalesAgentResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    put:
      operationId: updateSalesAgent
      security:
        - bearerAuth: []
      tags:
        - Sales Agents
      summary: Editar Corretor
      description: Editar Corretor
      parameters:
        - name: id
          in: path
          description: ID do corretor
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSalesAgentRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalesAgentResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /salesAgent/salesFirm:
    get:
      operationId: listSalesFirms
      security:
        - bearerAuth: []
      tags:
        - Sales Agents
      summary: Listar corretoras
      description: Listar corretoras
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SalesFirmResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /healthcareTeam:
    get:
      operationId: listHealthcareTeams
      security:
        - bearerAuth: []
      tags:
        - Healthcare Team
      summary: Listar times de saúde
      description: Listar times de saúde
      parameters:
        - name: filter
          in: query
          description: objetivo com filtros - active (boolean para buscar times ativos ou inativos), name (string para filtrar times onde a médica tenha o nome de acordo com o filtro)
          example: { 'active': 'false', 'name': 'Jane' }
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Current page
          example: 1
          required: false
          schema:
            type: string
        - name: pageSize
          in: query
          description: Limit
          example: 10
          required: false
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthcareTeamPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    post:
      operationId: createHealthcareTeam
      security:
        - bearerAuth: []
      tags:
        - Healthcare Team
      summary: Criar time de saúde
      description: Criar time de saúde
      requestBody:
        description: Time de saúde para criar
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateHealthcareTeam'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthcareTeam'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    put:
      operationId: updateHealthcareTeam
      security:
        - bearerAuth: []
      tags:
        - Healthcare Team
      summary: Atualizar time de saúde
      description: Atualizar time de saúde
      requestBody:
        description: Time de saúde para atualizar
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateHealthcareTeam'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthcareTeam'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /healthcareTeam/{id}:
    get:
      operationId: getHealthcareTeam
      security:
        - bearerAuth: []
      tags:
        - Healthcare Team
      summary: Buscar time de saúde por id
      description: Buscar time de saúde por id
      parameters:
        - name: id
          in: path
          description: ID do time de saúde
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthcareTeam'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /healthcareTeam/physicians:
    get:
      operationId: listHealthcareTeamPhysicians
      security:
        - bearerAuth: []
      tags:
        - Healthcare Team
      summary: Buscar profissionais de saúde
      description: Buscar profissionais de saúde
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthcareTeamPhysicians'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /healthcareTeamAssociation:
    get:
      operationId: listHealthcareTeamAssociations
      security:
        - bearerAuth: []
      tags:
        - Healthcare Team Association
      summary: Listar associaçao dos times de saúde
      description: Listar associaçao dos times de saúde
      parameters:
        - name: filter
          in: query
          description: busca por membro através de searchToken
          example: { 'q': 'lucas' }
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Current page
          example: 1
          required: false
          schema:
            type: string
        - name: pageSize
          in: query
          description: Limit
          example: 10
          required: false
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthcareTeamAssociationPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    post:
      operationId: createHealthcareTeamAssociation
      security:
        - bearerAuth: []
      tags:
        - Healthcare Team Association
      summary: Criar associação de time de saúde
      description: Criar associação membro <> time de saúde
      requestBody:
        description: Time de saúde e membro para associar
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateHealthcareTeamAssociation'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthcareTeamAssociation'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    put:
      operationId: updateHealthcareTeamAssociation
      security:
        - bearerAuth: []
      tags:
        - Healthcare Team Association
      summary: Atualizar associação de time de saúde
      description: Atualizar associação membro <> time de saúde
      requestBody:
        description: Time de saúde e membro para associar
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HealthcareTeamAssociation'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthcareTeamAssociation'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /healthcareTeamAssociation/{id}:
    get:
      operationId: getHealthcareTeamAssociation
      security:
        - bearerAuth: []
      tags:
        - Healthcare Team Association
      summary: Busca associaçao dos times de saúde por id
      description: Busca associaçao dos times de saúde por id
      parameters:
        - name: id
          in: path
          description: ID da associação do time de saúde
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthcareTeamAssociation'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /healthcareTeamAssociation/healthcareTeams:
    get:
      operationId: listHealthcareTeamsForAssociation
      security:
        - bearerAuth: []
      tags:
        - Healthcare Team Association
      summary: Buscar times de saúde
      description: Buscar times de saúde
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthcareTeamDetail'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /healthcareTeamAssociation/persons:
    get:
      operationId: listHealthcareTeamPersons
      security:
        - bearerAuth: []
      tags:
        - Healthcare Team Association
      summary: Buscar membros
      description: Buscar membros
      parameters:
        - name: filter
          in: query
          description: busca por membro através de searchToken
          example: { 'q': 'lucas' }
          required: false
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthcareTeamPerson'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /medicalSpecialty:
    get:
      operationId: listMedicalSpecialties
      security:
        - bearerAuth: []
      tags:
        - Medical Specialty
      summary: Listar especialidades
      description: Listar especialidades
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MedicalSpecialtyShortResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    post:
      operationId: createMedicalSpecialty
      security:
        - bearerAuth: []
      tags:
        - Medical Specialty
      summary: Criar especialidade
      description: Criar especialidade
      requestBody:
        description: Exemplo dos campos para criar especialidades
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MedicalSpecialtyRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MedicalSpecialtyResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

    put:
      operationId: updateMedicalSpecialty
      security:
        - bearerAuth: []
      tags:
        - Medical Specialty
      summary: Atualizar especialidade
      description: Atualizar especialidade
      requestBody:
        description: Exemplo dos campos para atualizar especialidades
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MedicalSpecialtyUpdateRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MedicalSpecialtyUpdateResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /medicalSpecialty/{id}:
    get:
      operationId: getMedicalSpecialty
      security:
        - bearerAuth: []
      tags:
        - Medical Specialty
      summary: Buscar especialidade por id
      description: Buscar especialidade por id
      parameters:
        - name: id
          in: path
          description: ID da especialidade
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MedicalSpecialtyFullResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /medicalSpecialty/healthSpecialistResourceBundle:
    get:
      summary: Buscar especialidades para relacionar com procedimentos
      description: Buscar especialidades para relacionar com procedimentos
      tags:
        - Medical Specialty
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  medicalSpecialties:
                    type: array
                    items:
                      $ref: '#/components/schemas/MedicalSpecialtyShortResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /staff:
    get:
      operationId: listStaff
      security:
        - bearerAuth: []
      tags:
        - Staffs
      summary: Listar Staffs
      description: Listar Staffs
      parameters:
        - name: q
          in: query
          description: Termo de busca para filtrar staffs
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Número da página para paginação
          required: false
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          description: Número de itens por página
          required: false
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaffPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    post:
      operationId: createStaff
      security:
        - bearerAuth: []
      tags:
        - Staffs
      summary: Criar Staff
      description: Cria um novo membro da equipe (staff)
      requestBody:
        description: Dados do staff a ser criado
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateStaffRequest'
      responses:
        '200':
          description: Staff criado com sucesso
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaffFullResponse'
        '400':
          description: Dados inválidos
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /staff/{id}:
    get:
      operationId: getStaff
      security:
        - bearerAuth: []
      tags:
        - Staffs
      summary: Buscar Staff por ID
      description: Retorna os detalhes completos de um membro da equipe (staff) pelo ID
      parameters:
        - name: id
          in: path
          description: ID único do staff
          required: true
          schema:
            type: string
            format: uuid
            example: '123e4567-e89b-12d3-a456-************'
      responses:
        '200':
          description: Staff encontrado com sucesso
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaffFullResponse'
        '404':
          description: Staff não encontrado
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    put:
      operationId: updateStaff
      security:
        - bearerAuth: []
      tags:
        - Staffs
      summary: Atualizar staff
      description: Atualiza um staff existente
      parameters:
        - name: id
          in: path
          required: true
          description: ID do staff
          schema:
            $ref: '#/components/schemas/UUID'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateStaffRequest'
      responses:
        '200':
          description: Staff atualizado com sucesso
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaffFullResponse'
        '400':
          description: Dados inválidos
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '404':
          description: Staff não encontrado
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /staff/address/search:
    get:
      operationId: searchAddress
      security:
        - bearerAuth: []
      tags:
        - Staffs
      summary: Autocompletar endereço
      description: Busca endereços baseado em texto parcial usando o Google Maps API
      parameters:
        - name: q
          in: query
          description: Texto parcial do endereço para busca
          required: true
          schema:
            type: string
        - name: Session-Id
          in: header
          description: ID de sessão para manter consistência entre chamadas relacionadas
          required: false
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AutocompleteAddress'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /staff/address/{place_id}:
    get:
      operationId: getAddressDetails
      security:
        - bearerAuth: []
      tags:
        - Staffs
      summary: Obter detalhes do endereço por Place ID
      description: Retorna os detalhes completos de um endereço usando o Place ID do Google Maps
      parameters:
        - name: place_id
          in: path
          description: Place ID único do Google Maps obtido através do endpoint de autocomplete
          required: true
          schema:
            type: string
            example: 'ChIJN1t_tDeuEmsRUsoyG83frY4'
      responses:
        '200':
          description: Detalhes do endereço obtidos com sucesso
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddressDetails'
        '404':
          description: Endereço não encontrado para o Place ID fornecido
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /staff/build_staff:
    get:
      operationId: getStaffForm
      security:
        - bearerAuth: []
      tags:
        - Staffs
      summary: Obter formulário para criação/edição de staff
      description: Retorna os dados necessários para construir o formulário de staff baseado no tipo de staff
      parameters:
        - name: filter
          in: query
          description: Filtro com o tipo de staff
          required: true
          example: '{"type":"COMMUNITY_SPECIALIST"}'
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaffFormResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /staff/search_provider_units:
    get:
      operationId: searchProviderUnits
      security:
        - bearerAuth: []
      tags:
        - Staffs
      summary: Buscar unidades de atendimento
      description: Busca unidades de atendimento com base em um termo de busca
      parameters:
        - name: filter
          in: query
          description: Filtro com o termo de busca
          required: false
          example: '{"searchToken":"hospital"}'
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProviderUnit'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /staff/upload_profile_image:
    post:
      operationId: uploadProfileImage
      security:
        - bearerAuth: []
      tags:
        - Staffs
      summary: Upload de imagem de perfil
      description: Faz upload de uma imagem de perfil para um membro da equipe
      requestBody:
        description: Arquivo de imagem para upload
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: Arquivo de imagem (PNG, JPEG ou GIF)
              required:
                - file
      responses:
        '200':
          description: Upload realizado com sucesso
          content:
            application/json:
              schema:
                type: string
                description: URL da imagem carregada
                example: 'https://web.assets.alice.com.br/healthcare-team-assets/profile-image.png'
        '400':
          description: Arquivo inválido ou erro de validação
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /staff/roles:
    get:
      operationId: getStaffRoles
      security:
        - bearerAuth: []
      tags:
        - Staffs
      summary: Obter roles de staff por tipo
      description: Retorna uma lista de roles disponíveis para um tipo específico de staff
      parameters:
        - name: staffType
          in: query
          description: Tipo de staff para filtrar os roles
          required: true
          schema:
            type: string
            enum:
              [
                COMMUNITY_SPECIALIST,
                HEALTH_PROFESSIONAL,
                PARTNER_HEALTH_PROFESSIONAL,
                HEALTH_ADMINISTRATIVE,
                PITAYA,
                EXTERNAL_PAID_HEALTH_PROFESSIONAL,
              ]
            example: 'COMMUNITY_SPECIALIST'
      responses:
        '200':
          description: Lista de roles retornada com sucesso
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/StaffRolesResponse'
        '400':
          description: Parâmetro staffType ausente ou inválido
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /staff/sub_specialties:
    get:
      operationId: listSubSpecialties
      security:
        - bearerAuth: []
      tags:
        - Staffs
      summary: Buscar subespecialidades médicas
      description: Busca subespecialidades médicas com base em diferentes filtros
      parameters:
        - name: q
          in: query
          description: Termo de busca para filtrar subespecialidades por nome
          required: false
          schema:
            type: string
        - name: parent_specialty_id
          in: query
          description: ID da especialidade pai para filtrar subespecialidades
          required: false
          schema:
            type: string
            format: uuid
        - name: ids
          in: query
          description: Lista de IDs de subespecialidades para busca direta
          required: false
          schema:
            type: array
            items:
              type: string
              format: uuid
        - name: page
          in: query
          description: Número da página para paginação
          required: false
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          description: Número de itens por página
          required: false
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MedicalSpecialty'
          headers:
            Content-Range:
              schema:
                type: string
              description: Total number of items
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /staff/specialties:
    get:
      operationId: listSpecialties
      security:
        - bearerAuth: []
      tags:
        - Staffs
      summary: Buscar especialidades médicas
      description: Busca especialidades médicas
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MedicalSpecialty'

  /staff/tiers:
    get:
      operationId: getStaffTiers
      security:
        - bearerAuth: []
      tags:
        - Staffs
      summary: Obter tiers de staff
      description: Retorna uma lista de tiers disponíveis
      responses:
        '200':
          description: Lista de tiers retornada com sucesso
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/StaffTiersResponse'

  /siteAccreditedNetwork/flagships:
    post:
      operationId: createFlagship
      security:
        - bearerAuth: []
      tags:
        - Site Accredited Network
      summary: Criar flagship
      description: Criar flagship
      requestBody:
        description: Exemplo dos campos para criar flagship
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrUpdateFlagshipRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlagshipResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /siteAccreditedNetwork/:
    get:
      operationId: listSiteAccreditedNetworks
      security:
        - bearerAuth: []
      tags:
        - Site Accredited Network
      summary: Buscar os agrupamentos da rede credenciada
      description: Buscar os agrupamentos da rede credenciada
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/SiteAccreditedNetworkResponse'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /siteAccreditedNetwork/{id}:
    get:
      operationId: getSiteAccreditedNetwork
      security:
        - bearerAuth: []
      tags:
        - Site Accredited Network
      summary: Buscar um agrupamento da rede credenciada
      description: Buscar um agrupamento da rede credenciada
      parameters:
        - name: id
          in: path
          description: ID do agrupamento
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SiteAccreditedNetworkItemResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    put:
      operationId: updateSiteAccreditedNetwork
      security:
        - bearerAuth: []
      tags:
        - Site Accredited Network
      summary: Atualizar um agrupamento da rede credenciada
      description: Atualizar um agrupamento da rede credenciada
      parameters:
        - name: id
          in: path
          description: ID do agrupamento
          required: true
          schema:
            type: string
      requestBody:
        description: Exemplo dos campos para atualizar agrupamento
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSiteAccreditedNetworkRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateSiteAccreditedNetworkResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    delete:
      operationId: deleteSiteAccreditedNetwork
      security:
        - bearerAuth: []
      tags:
        - Site Accredited Network
      summary: Apagar um agrupamento da rede credenciada
      description: Apagar um agrupamento da rede credenciada
      parameters:
        - name: id
          in: path
          description: ID do agrupamento
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
                items:
                  $ref: '#/components/schemas/DeleteSiteAccreditedNetworkResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /healthSpecialistResourceBundle:
    get:
      operationId: listHealthSpecialistResourceBundles
      security:
        - bearerAuth: []
      tags:
        - Health Specialist Resource Bundle
      summary: Listar procedimentos de Especialistas
      description: Listar procedimentos de Especialistas
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HealthSpecialistResourceBundlePaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    patch:
      operationId: patchHealthSpecialistResourceBundles
      security:
        - bearerAuth: []
      tags:
        - Health Specialist Resource Bundle
      summary: Atualizar procedimentos de Especialistas
      description: Atualizar procedimentos de Especialistas
      requestBody:
        description: Exemplo dos campos para atualizar procedimentos de Especialistas
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HealthSpecialistResourceBundlePatchRequest'
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthSpecialistResourceBundlePatchResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /healthSpecialistResourceBundle/{id}/medicalSpecialties:
    get:
      operationId: listResourceBundleSpecialties
      security:
        - bearerAuth: []
      tags:
        - Health Specialist Resource Bundle
      summary: Listar especialidades de procedimentos de Especialistas
      description: Listar especialidades de procedimentos de Especialistas
      parameters:
        - name: id
          in: path
          description: ID do procedimento de especialista
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/ResourceBundleSpecialtyBffResponse'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /resourceBundleSpecialtyPricing/effectiveDates:
    get:
      operationId: getEffectiveDates
      security:
        - bearerAuth: []
      tags:
        - Resource Bundle Specialist Pricing
      summary: Devolve as datas de vigencia permitidas para subir o CSV
      description: Devolve as datas de vigencia permitidas para subir o CSV
      responses:
        '200':
          description: Datas de vigência recuperadas com sucesso
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EffectiveDatesResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /resourceBundleSpecialtyPricing/csv:
    post:
      operationId: downloadResourceBundleSpecialtiesCsv
      security:
        - bearerAuth: []
      tags:
        - Resource Bundle Specialist Pricing
      summary: Baixar o csv com os preços dos procedimentos
      description: Baixar o csv com os preços dos procedimentos
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                resourceBundleSpecialtyIds:
                  type: array
                  description: Lista de IDs de especialidades de pacotes de recursos para exportar
                  items:
                    type: string
                    format: uuid
              required:
                - resourceBundleSpecialtyIds
              example:
                resourceBundleSpecialtyIds:
                  ['38208bed-fbd4-4cc5-8a65-c61368462000']
      responses:
        '200':
          description: Arquivo CSV gerado com sucesso para download
          content:
            text/csv:
              schema:
                type: string
                format: binary
                description: Conteúdo do arquivo CSV para download
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /resourceBundleSpecialtyPricingUpdate/{id}/failedLinesFile:
    get:
      operationId: downloadFailedLinesFile
      security:
        - bearerAuth: []
      tags:
        - Resource Bundle Specialist Pricing
      summary: Baixar o csv com as linhas que falharam de um upload de preços específico
      description: Baixar o csv com as linhas que falharam de um upload de preços específico
      parameters:
        - name: id
          in: path
          description: ID do upload de preços
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Arquivo CSV gerado com sucesso para download
          content:
            text/csv:
              schema:
                type: string
                format: binary
                description: Conteúdo do arquivo CSV para download
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /resourceBundleSpecialtyPricing/processing:
    get:
      operationId: getProcessingStatus
      security:
        - bearerAuth: []
      summary: Obter status de processamento de precificação de especialidades
      description: Retorna o status de processamento de precificação de especialidades do bundle de recursos.
      tags:
        - Resource Bundle Specialist Pricing
      responses:
        '200':
          description: Status de processamento retornado com sucesso.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProcessingResourceBundleSpecialtyPricingUpdateResponse'
        '500':
          description: Erro interno do servidor.

  /resourceBundleSpecialtyPricing/upload:
    post:
      operationId: uploadResourceBundleSpecialtiesCsv
      security:
        - bearerAuth: []
      tags:
        - Resource Bundle Specialist Pricing
      summary: Upload de alterações de preços
      description: Realiza o upload de um arquivo CSV contendo alterações de preços para pacotes de especialidades.
      requestBody:
        description: Arquivo CSV com as alterações de preços e metadados
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: Arquivo CSV contendo as alterações de preços
                effectiveDate:
                  type: string
                  format: date
                  description: Data de início dos preços
                  example: '2024-09-01'
                staffId:
                  $ref: '#/components/schemas/UUID'
      responses:
        '200':
          description: Upload realizado com sucesso
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceBundleSpecialtyPricingUpdate'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /resourceBundleSpecialtyPricingUpdate:
    get:
      operationId: listResourceBundleSpecialtyPricingUpdates
      security:
        - bearerAuth: []
      tags:
        - Resource Bundle Specialist Pricing
      summary: Listar histórico de atualizações de preços
      description: Listar histórico de atualizações de preços com filtros opcionais
      parameters:
        - name: startDate
          in: query
          description: Data de início para filtrar atualizações (formato YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          description: Data de fim para filtrar atualizações (formato YYYY-MM-DD)
          required: false
          schema:
            type: string
            format: date
        - name: status
          in: query
          description: Status da atualização
          required: false
          schema:
            type: string
            enum: [PROCESSED, PROCESSING, PROCESSED_WITH_ERRORS, PARSING_ERROR]
        - name: page
          in: query
          description: Número da página
          required: false
          schema:
            type: integer
            default: 1
        - name: perPage
          in: query
          description: Itens por página
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: Lista de atualizações de preços retornada com sucesso
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/ResourceSpecialistPricingIndexResponse'
                  pagination:
                    $ref: '#/components/schemas/Pagination'

  /resourceBundleSpecialty/pending:
    get:
      operationId: listPendingResourceBundleSpecialties
      security:
        - bearerAuth: []
      tags:
        - Health Specialist Resource Bundle
      summary: Listar procedimentos de especialistas com precificação pendente
      description: Listar procedimentos de especialistas com precificação pendente
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UUID'

  /healthSpecialistResourceBundle/pricing:
    get:
      operationId: listPricingForHealthSpecialistResourceBundle
      security:
        - bearerAuth: []
      tags:
        - Health Specialist Resource Bundle
      summary: Listar precificação de procedimentos de especialistas
      description: Listar precificação de procedimentos de especialistas
      parameters:
        - name: filter
          in: query
          description: Filtros para a listagem
          required: false
          schema:
            type: object
            properties:
              query:
                type: string
                example: 'Consulta'
              pricingStatus:
                type: string
                enum:
                  - PRICED
                  - PENDING
                example: 'PRICED'
              medicalSpecialtyIds:
                type: array
                items:
                  type: string
                  format: uuid
                example: ['38208bed-fbd4-4cc5-8a65-c61368462000']
      responses:
        '200':
          description: Resposta bem-sucedida
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                    items:
                      $ref: '#/components/schemas/PricingForHealthSpecialistResourceBundleResponse'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /health-professionals:
    get:
      operationId: listHealthProfessionals
      security:
        - bearerAuth: []
      tags:
        - Health Professionals
      summary: Listar profissionais de saúde
      description: Listar profissionais de saúde
      parameters:
        - name: filter
          in: query
          description: busca por profissional através de searchToken
          example: { 'q': 'lucas' }
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Current page
          example: 1
          required: false
          schema:
            type: string
        - name: pageSize
          in: query
          description: Number of items per page
          example: 10
          required: false
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthProfessionalPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

    post:
      security:
        - bearerAuth: []
      tags:
        - Health Professionals
      summary: Criar profissional de saúde
      description: Criar profissional de saúde
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HealthProfessionalRequest'
      responses:
        '201':
          description: successful operation
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /health-professionals/{id}:
    put:
      operationId: updateHealthProfessional
      security:
        - bearerAuth: []
      tags:
        - Health Professionals
      summary: Atualizar profissional de saúde
      description: Atualizar profissional de saúde
      parameters:
        - name: id
          in: path
          description: ID health professional
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HealthProfessionalRequest'
      responses:
        '201':
          description: successful operation
        '4XX':
          $ref: '#/components/responses/BadRequest'
    get:
      security:
        - bearerAuth: []
      tags:
        - Health Professionals
      summary: Buscar profissional da saúde por id
      description: Buscar profissional da saúde por id
      parameters:
        - name: id
          in: path
          description: ID do profissional da saúde
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthProfessionalFullResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /health-professionals/states:
    get:
      operationId: getHealthProfessionalStates
      security:
        - bearerAuth: []
      tags:
        - Health Professionals
      summary: Listar estados
      description: Listar estados
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthProfessionalsStatesResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /health-professionals/council-names:
    get:
      operationId: getHealthProfessionalCouncilNames
      security:
        - bearerAuth: []
      tags:
        - Health Professionals
      summary: Listar conselhos dos profissionais de saúde
      description: Listar conselhos dos profissionais de saúde
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthProfessionalsCouncilNamesResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /suggestedProcedures:
    get:
      operationId: listSuggestedProcedures
      security:
        - bearerAuth: []
      tags:
        - Suggested Procedure
      summary: Listar procedimentos sugeridos
      description: Lista procedimentos sugeridos com base em filtros
      parameters:
        - name: filter
          in: query
          description: Filtros para a listagem
          required: false
          schema:
            type: object
            properties:
              q:
                type: string
                example: 'exame'
              specialtyId:
                $ref: '#/components/schemas/UUID'
        - name: page
          in: query
          description: Página atual
          required: false
          schema:
            type: string
            example: '1'
        - name: pageSize
          in: query
          description: Tamanho da página
          required: false
          schema:
            type: string
            example: '10'
      responses:
        '200':
          description: Lista de procedimentos sugeridos
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuggestedProcedurePaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /suggestedProcedures/specialties:
    get:
      operationId: listSuggestedSpecialties
      security:
        - bearerAuth: []
      tags:
        - Suggested Procedure
      summary: Listar especialidades sugeridas
      description: Retorna uma lista paginada de especialidades com base no filtro
      parameters:
        - name: filter
          in: query
          description: Filtros para a listagem
          required: false
          schema:
            type: object
            properties:
              q:
                type: string
                example: 'cardio'
        - name: page
          in: query
          description: Página atual
          required: false
          schema:
            type: string
            example: '1'
        - name: pageSize
          in: query
          description: Tamanho da página
          required: false
          schema:
            type: string
            example: '10'
      responses:
        '200':
          description: Listar especialidades para sugestão
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuggestedSpecialtyPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /previewEarningSummaries:
    get:
      operationId: listPreviewEarningSummaries
      security:
        - bearerAuth: []
      tags:
        - SpecialistEarnings
      summary: Lista todos batchs de resumo de granhos
      description: Lista todos batchs de resumo de granhos
      parameters:
        - name: page
          in: query
          description: Current page
          example: 1
          required: false
          schema:
            type: string
        - name: pageSize
          in: query
          description: Number of items per page
          example: 10
          required: false
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreviewEarningSummariesPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

    post:
      operationId: createPreviewEarningSummary
      security:
        - bearerAuth: []
      tags:
        - SpecialistEarnings
      summary: Cadastro novo batch de resumo de ganhos
      description: Cadastro novo batch de resumo de ganhos
      requestBody:
        content:
          text/csv:
            schema:
              type: string
              format: csv
      responses:
        '201':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreviewEarningSummariesCreatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
  /previewEarningSummaries/{id}:
    delete:
      operationId: deletePreviewEarningSummary
      security:
        - bearerAuth: []
      tags:
        - SpecialistEarnings
      summary: Apaga um batch cadastrado
      description: Apaga um batch cadastrado
      parameters:
        - name: id
          in: path
          description: ID do batch
          required: true
          schema:
            type: string
      responses:
        '200':
          description: successful operation
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /invoices:
    get:
      operationId: listInvoices
      security:
        - bearerAuth: []
      tags:
        - SpecialistEarnings
      summary: Listar faturas
      description: Listar faturas de especialistas
      parameters:
        - name: page
          in: query
          description: Página atual
          example: 1
          required: false
          schema:
            type: string
        - name: pageSize
          in: query
          description: Número de itens por página
          example: 10
          required: false
          schema:
            type: string
        - name: q
          in: query
          description: Termo de busca
          required: false
          schema:
            type: string
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoicePaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /staff_signup_review:
    get:
      operationId: listStaffSignupRequests
      security:
        - bearerAuth: []
      tags:
        - StaffSignupRequest
      summary: Lista requisições de cadastro de profissionais
      description: Retorna uma lista paginada de solicitações de cadastro com filtros opcionais por nome (`q`) e status.
      parameters:
        - name: q
          in: query
          description: Termo de busca para filtrar staffs por nome
          required: false
          schema:
            type: string
        - name: status
          in: query
          description: Status da solicitação (`PENDING`, `APPROVED`, `REJECTED`)
          required: false
          schema:
            type: string
            enum: [PENDING, APPROVED, REJECTED]
        - name: page
          in: query
          description: Página atual
          example: 1
          required: false
          schema:
            type: string
        - name: pageSize
          in: query
          description: Número de itens por página
          example: 10
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Lista de requisições de cadastro
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaffSignupRequestListResponse'

  /staff_signup_review/{id}:
    get:
      operationId: getStaffSignupRequest
      security:
        - bearerAuth: []
      tags:
        - StaffSignupRequest
      summary: Busca uma solicitação de cadastro de profissional
      description: Retorna os detalhes de uma solicitação de cadastro de profissional
      parameters:
        - name: id
          in: path
          description: ID da solicitação de cadastro
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StaffSignupRequestIntegrationContent'

  /staff_signup_review/{id}/reject:
    post:
      operationId: rejectStaffSignupRequest
      security:
        - bearerAuth: []
      tags:
        - StaffSignupRequest
      summary: Rejeita uma solicitação de cadastro de profissional
      description: Rejeita uma solicitação de cadastro de profissional, registrando o motivo da rejeição e o responsável pela revisão.
      parameters:
        - name: id
          in: path
          description: ID da solicitação de cadastro a ser rejeitada
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StaffSignupRequestRejectRequest'
      responses:
        '200':
          description: Solicitação rejeitada com sucesso

  /staff_signup_review/{id}/approve:
    post:
      operationId: approveStaffSignupRequest
      security:
        - bearerAuth: []
      tags:
        - StaffSignupRequest
      summary: Aceita uma solicitação de cadastro de profissional
      description: Aceita uma solicitação de cadastro de profissional, registrando o responsável pela revisão.
      parameters:
        - name: id
          in: path
          description: ID da solicitação de cadastro a ser aprovada
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StaffSignupRequestApproveRequest'
      responses:
        '200':
          description: Solicitação aprovada com sucesso

  /execAuthorizer:
    get:
      operationId: listExecAuthorizers
      security:
        - bearerAuth: []
      tags:
        - Exec Indicator Authorizers
      summary: Listar autorizadores de indicadores executivos
      description: Lista autorizadores de indicadores executivos com suporte a paginação e filtros
      parameters:
        - name: range
          in: query
          description: Range para paginação
          required: false
          schema:
            type: string
            example: '[0, 9]'
        - name: q
          in: query
          description: Filtro de busca por nome da unidade prestadora
          required: false
          schema:
            type: string
            example: 'Hospital'
      responses:
        '200':
          description: Lista de autorizadores retornada com sucesso
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExecAuthorizerPaginatedResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'
    post:
      operationId: createExecAuthorizer
      security:
        - bearerAuth: []
      tags:
        - Exec Indicator Authorizers
      summary: Criar autorizador de indicadores executivos
      description: Cria um novo autorizador de indicadores executivos
      requestBody:
        description: Dados do autorizador a ser criado
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizerRequest'
      responses:
        '200':
          description: Autorizador criado com sucesso
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizerResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /execAuthorizer/{id}:
    get:
      operationId: getExecAuthorizer
      security:
        - bearerAuth: []
      tags:
        - Exec Indicator Authorizers
      summary: Buscar autorizador por ID
      description: Retorna os detalhes de um autorizador de indicadores executivos pelo ID
      parameters:
        - name: id
          in: path
          description: ID único do autorizador
          required: true
          schema:
            $ref: '#/components/schemas/UUID'
      responses:
        '200':
          description: Autorizador encontrado com sucesso
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizerResponse'
        '404':
          description: Autorizador não encontrado
        '4XX':
          $ref: '#/components/responses/BadRequest'
    put:
      operationId: updateExecAuthorizer
      security:
        - bearerAuth: []
      tags:
        - Exec Indicator Authorizers
      summary: Atualizar autorizador
      description: Atualiza um autorizador de indicadores executivos existente
      parameters:
        - name: id
          in: path
          description: ID único do autorizador
          required: true
          schema:
            $ref: '#/components/schemas/UUID'
      requestBody:
        description: Dados do autorizador a ser atualizado
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizerRequest'
      responses:
        '200':
          description: Autorizador atualizado com sucesso
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizerResponse'
        '404':
          description: Autorizador não encontrado
        '4XX':
          $ref: '#/components/responses/BadRequest'
    delete:
      operationId: deleteExecAuthorizer
      security:
        - bearerAuth: []
      tags:
        - Exec Indicator Authorizers
      summary: Deletar autorizador
      description: Remove um autorizador de indicadores executivos
      parameters:
        - name: id
          in: path
          description: ID único do autorizador
          required: true
          schema:
            $ref: '#/components/schemas/UUID'
      responses:
        '200':
          description: Autorizador deletado com sucesso
        '404':
          description: Autorizador não encontrado
        '4XX':
          $ref: '#/components/responses/BadRequest'

  /provider_units:
    get:
      security:
        - bearerAuth: []
      tags:
        - Provider Units
      summary: Listar unidades prestadoras
      description: Lista as unidades prestadoras com filtros opcionais por nome, tipo, status e IDs específicos
      parameters:
        - name: range
          in: query
          description: Range para paginação (formato "start-end", ex. "0-19")
          required: false
          schema:
            type: string
            example: '0-19'
        - name: q
          in: query
          description: Termo de busca para filtrar por nome da unidade prestadora
          required: false
          schema:
            type: string
            example: 'Hospital São Paulo'
        - name: type
          in: query
          description: Tipos de unidades prestadoras separados por vírgula
          required: false
          schema:
            type: string
            example: 'HOSPITAL,LABORATORY'
          style: form
          explode: false
        - name: status
          in: query
          description: Status das unidades prestadoras separados por vírgula
          required: false
          schema:
            type: string
            example: 'ACTIVE,INACTIVE'
          style: form
          explode: false
        - name: ids
          in: query
          description: IDs específicos das unidades prestadoras separados por vírgula
          required: false
          schema:
            type: string
            example: '123e4567-e89b-12d3-a456-************,987fcdeb-51a2-43d1-9f12-123456789abc'
          style: form
          explode: false
      responses:
        '200':
          description: Lista de unidades prestadoras
          headers:
            Content-Range:
              description: Total de registros encontrados
              schema:
                type: string
                example: '150'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProviderUnitResponse'
        '4XX':
          $ref: '#/components/responses/BadRequest'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  responses:
    BadRequest:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
  schemas:
    UUID:
      type: string
      pattern: '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
      minLength: 36
      maxLength: 36

    Date:
      type: string
      format: date
      description: 'Date in ISO 8601 format, e.g., 2024-09-13'

    DateTime:
      type: string
      format: date-time
      description: 'Date and time in ISO 8601 format, e.g., 2024-09-13T14:30:00'

    SignIn:
      required:
        - id_token
      type: object
      properties:
        id_token:
          type: string

    CreateFeatureFlag:
      required:
        - namespace
        - key
        - type
        - value
        - description
        - active
        - isPublic
      type: object
      properties:
        namespace:
          type: string
          example: SCHEDULE
        key:
          type: string
          example: should_use_sara_url_on_health_declaration_appointment
        type:
          type: string
          example: BOOLEAN
        value:
          type: string
          example: '0a19f0ad-255b-4b5a-a9e3-322f71730400\t'
        description:
          type: string
          example: '0a19f0ad-255b-4b5a-a9e3-322f71730400\t'
        active:
          type: boolean
          example: true
        isPublic:
          type: boolean
          example: false

    UpdateFeatureFlag:
      required:
        - type
        - value
        - active
        - isPublic
        - description
      type: object
      properties:
        type:
          type: string
          example: BOOLEAN
        value:
          type: string
          example: '0a19f0ad-255b-4b5a-a9e3-322f71730400\t'
        description:
          type: string
          example: '0a19f0ad-255b-4b5a-a9e3-322f71730400\t'
        active:
          type: boolean
          example: true
        isPublic:
          type: boolean
          example: false

    FeatureFlagFullResponse:
      required:
        - namespace
        - description
        - key
        - type
        - value
        - active
        - isPublic
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        namespace:
          type: string
          example: SCHEDULE
        description:
          type: string
          example: 'text for ff description'
        key:
          type: string
          example: should_use_sara_url_on_health_declaration_appointment
        type:
          type: string
          example: BOOLEAN
        value:
          type: string
          example: '0a19f0ad-255b-4b5a-a9e3-322f71730400\t'
        active:
          type: boolean
          example: true
        isPublic:
          type: boolean
          example: false
        createdAt:
          type: string
          example: '2024-07-16T17:46:19.915Z'
        updatedAt:
          type: string
          example: '2024-07-16T17:46:19.915Z'

    FeatureFlagShortResponse:
      required:
        - namespace
        - key
        - type
        - value
        - active
        - isPublic
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        namespace:
          type: string
          example: SCHEDULE
        key:
          type: string
          example: should_use_sara_url_on_health_declaration_appointment
        type:
          type: string
          example: BOOLEAN
        value:
          type: string
          example: '0a19f0ad-255b-4b5a-a9e3-322f71730400\t'
        active:
          type: boolean
          example: true
        isPublic:
          type: boolean
          example: false

    FeatureConfigPaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/FeatureFlagShortResponse'
        pagination:
          $ref: '#/components/schemas/Pagination'

    CompanyPaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/CompanyShortResponse'
        total:
          type: number
          example: 354

    CompanyStaffPaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/CompanyStaffResponse'
        total:
          type: number
          example: 354

    SalesAgentPaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/SalesAgentResponse'
        total:
          type: number
          example: 354

    CompanyShortResponse:
      required:
        - name
        - legalName
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: Alice
        legalName:
          type: string
          example: Alice Tech LTDA

    CreateCompanyStaffRequest:
      required:
        - firstName
        - lastName
        - email
        - role
        - companyId
      type: object
      properties:
        companyId:
          $ref: '#/components/schemas/UUID'
        firstName:
          type: string
          example: Alice
        lastName:
          type: string
          example: Braga
        email:
          type: string
          example: <EMAIL>
        role:
          type: string
          example: MAIN_COMPANY_STAFF
        accessLevel:
          type: string
          example: ADMIN

    UpdateCompanyStaffRequest:
      required:
        - firstName
        - lastName
        - email
        - role
      type: object
      properties:
        firstName:
          type: string
          example: Alice
        lastName:
          type: string
          example: Braga
        email:
          type: string
          example: <EMAIL>
        role:
          type: string
          example: MAIN_COMPANY_STAFF
        accessLevel:
          type: string
          example: ADMIN

    CompanyStaffResponse:
      required:
        - firstName
        - lastName
        - email
        - role
        - company
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        firstName:
          type: string
          example: Alice
        lastName:
          type: string
          example: Braga
        email:
          type: string
          example: <EMAIL>
        role:
          type: string
          example: MAIN_COMPANY_STAFF
        accessLevel:
          type: string
          example: ADMIN
        company:
          $ref: '#/components/schemas/CompanyShortResponse'

    SalesAgentResponse:
      required:
        - id
        - name
        - documentNumber
        - email
        - phoneNumber
        - birthDate
        - salesFirmId
        - searchTokens
        - version
        - isActiveInCampaign
        - totalLeadsFromCampaign
        - leadsFromCampaignRound
        - createdAt
        - updatedAt
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: Alice
        documentNumber:
          type: string
          example: 00000000000
        email:
          type: string
          example: <EMAIL>
        phoneNumber:
          type: string
          example: 81999999999
        birthDate:
          $ref: '#/components/schemas/Date'
        salesFirmName:
          type: string
          example: Corretores SA
        salesFirmId:
          $ref: '#/components/schemas/UUID'
        searchTokens:
          type: string
          example: 00000000000 81999999999 Alice
        version:
          type: number
          example: 1
        isActiveInCampaign:
          type: boolean
          example: false
        totalLeadsFromCampaign:
          type: number
          example: 10
        leadsFromCampaignRound:
          type: number
          example: 10
        createdAt:
          $ref: '#/components/schemas/DateTime'
        updatedAt:
          $ref: '#/components/schemas/DateTime'

    CreateSalesAgentRequest:
      required:
        - name
        - documentNumber
        - email
        - phoneNumber
        - birthDate
        - salesFirmId
        - isActiveInCampaign
      type: object
      properties:
        name:
          type: string
          example: Alice
        documentNumber:
          type: string
          example: 00000000000
        email:
          type: string
          example: <EMAIL>
        phoneNumber:
          type: string
          example: 81999999999
        birthDate:
          $ref: '#/components/schemas/Date'
        salesFirmId:
          $ref: '#/components/schemas/UUID'
        isActiveInCampaign:
          type: boolean
          example: false

    UpdateSalesAgentRequest:
      required:
        - name
        - documentNumber
        - email
        - phoneNumber
        - birthDate
        - salesFirmId
        - isActiveInCampaign
      type: object
      properties:
        name:
          type: string
          example: Alice
        documentNumber:
          type: string
          example: 00000000000
        email:
          type: string
          example: <EMAIL>
        phoneNumber:
          type: string
          example: 81999999999
        birthDate:
          $ref: '#/components/schemas/Date'
        salesFirmId:
          $ref: '#/components/schemas/UUID'
        isActiveInCampaign:
          type: boolean
          example: false

    SalesFirmResponse:
      required:
        - id
        - name
        - legalName
        - cnpj
        - email
        - phoneNumber
        - version
        - createdAt
        - updatedAt
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: Corretora X
        legalName:
          type: string
          example: Corretora X Ltda.
        cnpj:
          type: string
          example: 11111111111
        email:
          type: string
          example: <EMAIL>
        phoneNumber:
          type: string
          example: 81999999999
        version:
          type: number
          example: 1
        createdAt:
          $ref: '#/components/schemas/DateTime'
        updatedAt:
          $ref: '#/components/schemas/DateTime'

    MedicalSpecialtyRequest:
      required:
        - name
        - urlSlug
        - type
        - isTherapy
        - requireSpecialist
        - generateGeneralistSubSpecialty
        - internal
        - active
      properties:
        name:
          type: string
          example: 'agendamento'
        urlSlug:
          type: string
          example: 'agendamento'
        type:
          type: string
          example: 'SPECIALTY'
        isTherapy:
          type: boolean
          example: false
        requireSpecialist:
          type: boolean
          example: false
        generateGeneralistSubSpecialty:
          type: boolean
          example: false
        internal:
          type: boolean
          example: false
        active:
          type: boolean
          example: true

    MedicalSpecialtyUpdateRequest:
      allOf:
        - $ref: '#/components/schemas/MedicalSpecialtyRequest'
        - type: object
          properties:
            subSpecialties:
              type: array
              items:
                $ref: '#/components/schemas/MedicalSubSpecialty'

    MedicalSpecialtyResponse:
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: 'agendamento'
        active:
          type: boolean
          example: false
        type:
          type: string
          example: 'SPECIALTY'
        urlSlug:
          type: string
          example: 'agendamento'
        isTherapy:
          type: boolean
          example: false
        requireSpecialist:
          type: boolean
          example: false
        generateGeneralistSubSpecialty:
          type: boolean
          example: false
        internal:
          type: boolean
          example: false

    MedicalSpecialtyUpdateResponse:
      allOf:
        - $ref: '#/components/schemas/MedicalSpecialtyResponse'
        - type: object
          required:
            - subSpecialties
          properties:
            subSpecialties:
              type: array
              items:
                $ref: '#/components/schemas/MedicalSubSpecialty'

    MedicalSubSpecialty:
      required:
        - name
        - active
        - urlSlug
        - isAdvancedAccess
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: 'agendamento'
        active:
          type: boolean
          example: false
        urlSlug:
          type: string
          example: 'agendamento'
        parentSpecialtyId:
          $ref: '#/components/schemas/UUID'
        isAdvancedAccess:
          type: boolean
          example: false

    MedicalSpecialtyShortResponse:
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: 'agendamento'
        active:
          type: boolean
          example: false
        type:
          type: string
          example: 'SPECIALTY'
        specialty:
          type: string
          example: 'agendamento'
        isTherapy:
          type: boolean
          example: false

    ItemValue:
      properties:
        id:
          type: string
        value:
          type: string

    FeatureNamespaceResponse:
      type: array
      items:
        $ref: '#/components/schemas/ItemValue'

    FeatureTypeResponse:
      type: array
      items:
        $ref: '#/components/schemas/ItemValue'

    Pagination:
      properties:
        totalPages:
          type: number
          example: 10
        pageSize:
          type: number
          example: 1

    Error:
      type: object
      properties:
        code:
          type: string
        message:
          type: string

    ValidationError:
      type: object
      properties:
        code:
          type: string
        message:
          type: string
        errors:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
              message:
                type: string

    HealthcareTeam:
      required:
        - id
        - physician
        - active
        - type
        - segment
        - maxMemberAssociation
        - address
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        physician:
          $ref: '#/components/schemas/HealthcareTeamPhysicians'
        active:
          type: boolean
          example: true
        type:
          type: string
          example: LEAN
        segment:
          type: string
          example: ADULT
        maxMemberAssociation:
          type: number
          example: 123
        address:
          $ref: '#/components/schemas/AddressResponse'

    HealthcareTeamPhysicians:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: Time de saúde X
        role:
          type: string
          example: 'MANAGER_PHYSICIAN'
        gender:
          type: string
          enum:
            - MALE
            - FEMALE
            - NON_BINARY
            - NO_ANSWER
            - null

    CreateHealthcareTeam:
      required:
        - physician
        - active
        - segment
        - maxMemberAssociation
      type: object
      properties:
        physician:
          $ref: '#/components/schemas/HealthcareTeamPhysicians'
        active:
          type: boolean
          example: true
        segment:
          type: string
          example: ADULT
        maxMemberAssociation:
          type: number
          example: 123

    UpdateHealthcareTeam:
      required:
        - id
        - physician
        - active
        - segment
        - maxMemberAssociation
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        physician:
          $ref: '#/components/schemas/HealthcareTeamPhysicians'
        active:
          type: boolean
          example: true
        segment:
          type: string
          example: ADULT
        maxMemberAssociation:
          type: number
          example: 123

    AddressResponse:
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        street:
          type: string
          example: Rua X
        number:
          type: string
          example: 11
        complement:
          type: string
          example: Apto 3
        neighborhood:
          type: string
          example: Centro
        city:
          type: string
          example: São Paulo
        state:
          type: string
          example: SP
        zipcode:
          type: string
          example: 123123
        referencedModelId:
          $ref: '#/components/schemas/UUID'
        referencedModelClass:
          type: string
          example: HEALTHCARE_TEAM
        active:
          type: boolean
          example: true
        label:
          type: string
          example: Casa
        latitude:
          type: string
          example: 123123
        longitude:
          type: string
          example: 12313
        version:
          type: number
          example: 1
        createdAt:
          $ref: '#/components/schemas/DateTime'
        updatedAt:
          $ref: '#/components/schemas/DateTime'

    HealthcareTeamAssociation:
      required:
        - person
        - healthcareTeam
        - multiStaffIds
        - id
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        person:
          $ref: '#/components/schemas/HealthcareTeamPerson'
        healthcareTeam:
          $ref: '#/components/schemas/HealthcareTeamDetail'
        multiStaffIds:
          type: array
          items:
            type: string

    CreateHealthcareTeamAssociation:
      required:
        - person
        - healthcareTeam
        - multiStaffIds
      type: object
      properties:
        person:
          $ref: '#/components/schemas/HealthcareTeamPerson'
        healthcareTeam:
          $ref: '#/components/schemas/HealthcareTeamDetail'
        multiStaffIds:
          type: array
          items:
            type: string

    HealthcareTeamPerson:
      required:
        - id
        - fullName
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        fullName:
          type: string
          example: 'Nome completo'

    HealthcareTeamDetail:
      required:
        - id
        - description
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        description:
          type: string
          example: 'Médico time de saúde'

    CreateOrUpdateFlagshipRequest:
      required:
        - googlePlaceId
        - data
      type: object
      properties:
        googlePlaceId:
          type: string
          example: 'ChIJN1t_tDeuEmsRUsoyG83frY4'
        data:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                enum:
                  - HOSPITAL
                  - LABORATORY
                  - EMERGENCY_UNITY
                  - MATERNITY
                  - SPECIALIST
              providerUnitId:
                $ref: '#/components/schemas/UUID'

    HealthcareTeamPaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/HealthcareTeam'
        total:
          type: number
          example: 354

    HealthcareTeamAssociationPaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/HealthcareTeamAssociation'
        total:
          type: number
          example: 354

    StaffPaginatedResponse:
      type: object
      properties:
        pagination:
          $ref: '#/components/schemas/Pagination'
        results:
          type: array
          items:
            $ref: '#/components/schemas/StaffShortResponse'

    StaffShortResponse:
      required:
        - id
        - firstName
        - lastName
        - email
        - active
        - role
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        firstName:
          type: string
          example: 'Fulano'
        lastName:
          type: string
          example: 'Sample'
        email:
          type: string
          example: '<EMAIL>'
        active:
          type: boolean
          example: true
        role:
          type: string
          example: 'consult_staff_roles'

    StaffFullResponse:
      required:
        - id
        - firstName
        - lastName
        - fullName
        - email
        - gender
        - role
        - type
        - active
        - version
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        firstName:
          type: string
          example: 'João'
          description: 'Primeiro nome do staff'
        lastName:
          type: string
          example: 'Silva'
          description: 'Sobrenome do staff'
        fullName:
          type: string
          example: 'João Silva'
          description: 'Nome completo do staff'
        email:
          type: string
          format: email
          example: '<EMAIL>'
          description: 'Email do staff'
        nationalId:
          type: string
          example: '12345678901'
          description: 'CPF do staff'
        birthdate:
          type: string
          format: date
          example: '1990-01-15'
          description: 'Data de nascimento'
        gender:
          type: string
          enum: [MALE, FEMALE, OTHER]
          example: 'MALE'
          description: 'Gênero do staff'
        profileImageUrl:
          type: string
          format: uri
          example: 'https://example.com/profile.jpg'
          description: 'URL da imagem de perfil'
        role:
          type: string
          enum: [MANAGER_PHYSICIAN, COMMUNITY, HEALTH_PROFESSIONAL, ADMIN]
          example: 'HEALTH_PROFESSIONAL'
          description: 'Papel do staff no sistema'
        type:
          type: string
          enum:
            [
              COMMUNITY_SPECIALIST,
              HEALTH_PROFESSIONAL,
              PARTNER_HEALTH_PROFESSIONAL,
              HEALTH_ADMINISTRATIVE,
              PITAYA,
            ]
          example: 'HEALTH_PROFESSIONAL'
          description: 'Tipo do staff'
        active:
          type: boolean
          example: true
          description: 'Se o staff está ativo'
        version:
          type: integer
          example: 1
          description: 'Versão do registro'
        urlSlug:
          type: string
          example: 'dr-joao-silva'
          description: 'Slug para URL do perfil'
        quote:
          type: string
          example: 'Cuidar é a essência da medicina'
          description: 'Frase do profissional'
        profileBio:
          type: string
          example: 'Médico especialista em cardiologia com 10 anos de experiência'
          description: 'Biografia do profissional'
        council:
          $ref: '#/components/schemas/CouncilDTO'
        specialty:
          $ref: '#/components/schemas/UUID'
        subSpecialties:
          type: array
          items:
            $ref: '#/components/schemas/UUID'
          description: 'Lista de subespecialidades'
        internalSpecialty:
          $ref: '#/components/schemas/UUID'
        internalSubSpecialties:
          type: array
          items:
            $ref: '#/components/schemas/UUID'
          description: 'Lista de subespecialidades internas'
        providerUnits:
          type: array
          items:
            $ref: '#/components/schemas/UUID'
          description: 'Lista de unidades prestadoras'
        qualifications:
          type: array
          items:
            $ref: '#/components/schemas/QualificationDTO'
          description: 'Qualificações do profissional'
        tier:
          type: string
          enum: [EXPERT, SENIOR, JUNIOR]
          example: 'SENIOR'
          description: 'Nível do especialista'
        theoristTier:
          type: string
          enum: [EXPERT, SENIOR, JUNIOR]
          example: 'EXPERT'
          description: 'Nível teórico do especialista'
        curiosity:
          type: string
          example: 'Interessado em novas tecnologias médicas'
          description: 'Curiosidades sobre o profissional'
        showOnApp:
          type: boolean
          example: true
          description: 'Se deve aparecer no app'
        education:
          type: array
          items:
            type: string
          example: ['Medicina - USP', 'Residência em Cardiologia - InCor']
          description: 'Formação educacional'
        healthSpecialistScore:
          type: string
          enum: [DOMINATING, LEARNING, IMPROVING]
          example: 'DOMINATING'
          description: 'Pontuação do especialista em saúde'
        deAccreditationDate:
          type: string
          format: date
          example: '2025-12-31'
          description: 'Data de descredenciamento'
        contacts:
          type: array
          items:
            $ref: '#/components/schemas/ContactDTO'
          description: 'Contatos do profissional'
        paymentFrequency:
          type: integer
          default: 0
          example: 30
          description: 'Frequência de pagamento em dias'
        memedStatus:
          type: string
          example: 'ACTIVE'
          description: 'Status no Memed'
        attendsToOnCall:
          type: boolean
          example: true
          description: 'Se atende plantão'
        onCallPaymentMethod:
          type: string
          enum: [HEALTH_INSTITUTION, ALICE]
          example: 'ALICE'
          description: 'Método de pagamento do plantão'
        onVacationStart:
          type: string
          format: date
          example: '2024-07-01'
          description: 'Início das férias'
        onVacationUntil:
          type: string
          format: date
          example: '2024-07-15'
          description: 'Fim das férias'

    UpdateStaffRequest:
      required:
        - firstName
        - lastName
        - email
        - gender
        - role
        - type
      type: object
      properties:
        firstName:
          type: string
          example: 'João'
          description: 'Primeiro nome do staff'
        lastName:
          type: string
          example: 'Silva'
          description: 'Sobrenome do staff'
        email:
          type: string
          format: email
          example: '<EMAIL>'
          description: 'Email do staff'
        nationalId:
          type: string
          example: '12345678901'
          description: 'CPF do staff'
        birthdate:
          type: string
          format: date
          example: '1990-01-15'
          description: 'Data de nascimento'
        gender:
          type: string
          enum: [MALE, FEMALE, OTHER]
          example: 'MALE'
          description: 'Gênero do staff'
        profileImageUrl:
          type: string
          format: uri
          example: 'https://example.com/profile.jpg'
          description: 'URL da imagem de perfil'
        role:
          type: string
          enum: [MANAGER_PHYSICIAN, COMMUNITY, HEALTH_PROFESSIONAL, ADMIN]
          example: 'HEALTH_PROFESSIONAL'
          description: 'Papel do staff no sistema'
        type:
          type: string
          enum:
            [
              COMMUNITY_SPECIALIST,
              HEALTH_PROFESSIONAL,
              PARTNER_HEALTH_PROFESSIONAL,
              HEALTH_ADMINISTRATIVE,
              PITAYA,
            ]
          example: 'HEALTH_PROFESSIONAL'
          description: 'Tipo do staff'
        active:
          type: boolean
          example: true
          description: 'Se o staff está ativo'
        urlSlug:
          type: string
          example: 'dr-joao-silva'
          description: 'Slug para URL do perfil'
        quote:
          type: string
          example: 'Cuidar é a essência da medicina'
          description: 'Frase do profissional'
        profileBio:
          type: string
          example: 'Médico especialista em cardiologia com 10 anos de experiência'
          description: 'Biografia do profissional'
        council:
          $ref: '#/components/schemas/CouncilDTO'
        specialty:
          $ref: '#/components/schemas/UUID'
          description: 'ID da especialidade principal'
        subSpecialties:
          type: array
          items:
            $ref: '#/components/schemas/UUID'
          description: 'IDs das subespecialidades'
        internalSpecialty:
          $ref: '#/components/schemas/UUID'
          description: 'ID da especialidade interna'
        internalSubSpecialties:
          type: array
          items:
            $ref: '#/components/schemas/UUID'
          description: 'IDs das subespecialidades internas'
        providerUnits:
          type: array
          items:
            $ref: '#/components/schemas/UUID'
          description: 'IDs das unidades prestadoras'
        qualifications:
          type: array
          items:
            $ref: '#/components/schemas/QualificationDTO'
          description: 'Qualificações do profissional'
        tier:
          type: string
          enum: [EXPERT, SENIOR, JUNIOR]
          example: 'SENIOR'
          description: 'Nível do especialista'
        theoristTier:
          type: string
          enum: [EXPERT, SENIOR, JUNIOR]
          example: 'EXPERT'
          description: 'Nível teórico do especialista'
        curiosity:
          type: string
          example: 'Interessado em novas tecnologias médicas'
          description: 'Curiosidades sobre o profissional'
        showOnApp:
          type: boolean
          example: true
          description: 'Se deve aparecer no app'
        education:
          type: array
          items:
            type: string
          example: ['Medicina - USP', 'Residência em Cardiologia - InCor']
          description: 'Formação educacional'
        healthSpecialistScore:
          type: string
          enum: [DOMINATING, LEARNING, IMPROVING]
          example: 'DOMINATING'
          description: 'Pontuação do especialista em saúde'
        deAccreditationDate:
          type: string
          format: date
          example: '2025-12-31'
          description: 'Data de descredenciamento'
        contacts:
          type: array
          items:
            $ref: '#/components/schemas/ContactDTO'
          description: 'Contatos do profissional'
        paymentFrequency:
          type: integer
          default: 0
          example: 30
          description: 'Frequência de pagamento em dias'
        attendsToOnCall:
          type: boolean
          example: true
          description: 'Se atende plantão'
        onCallPaymentMethod:
          type: string
          enum: [HEALTH_INSTITUTION, ALICE]
          example: 'ALICE'
          description: 'Método de pagamento do plantão'
        onVacationStart:
          type: string
          format: date
          example: '2024-07-01'
          description: 'Início das férias'
        onVacationUntil:
          type: string
          format: date
          example: '2024-07-15'
          description: 'Fim das férias'
        version:
          type: integer
          format: int64
          example: 26
          description: 'Versão do entity para controle de concorrência'

    CreateStaffRequest:
      required:
        - firstName
        - lastName
        - email
        - gender
        - role
        - type
      type: object
      properties:
        firstName:
          type: string
          example: 'João'
          description: 'Primeiro nome do staff'
        lastName:
          type: string
          example: 'Silva'
          description: 'Sobrenome do staff'
        email:
          type: string
          format: email
          example: '<EMAIL>'
          description: 'Email do staff'
        nationalId:
          type: string
          example: '12345678901'
          description: 'CPF do staff'
        birthdate:
          type: string
          format: date
          example: '1990-01-15'
          description: 'Data de nascimento'
        gender:
          type: string
          enum: [MALE, FEMALE, OTHER]
          example: 'MALE'
          description: 'Gênero do staff'
        profileImageUrl:
          type: string
          format: uri
          example: 'https://example.com/profile.jpg'
          description: 'URL da imagem de perfil'
        role:
          type: string
          enum: [MANAGER_PHYSICIAN, COMMUNITY, HEALTH_PROFESSIONAL, ADMIN]
          example: 'HEALTH_PROFESSIONAL'
          description: 'Papel do staff no sistema'
        type:
          type: string
          enum:
            [
              COMMUNITY_SPECIALIST,
              HEALTH_PROFESSIONAL,
              PARTNER_HEALTH_PROFESSIONAL,
              HEALTH_ADMINISTRATIVE,
              PITAYA,
            ]
          example: 'HEALTH_PROFESSIONAL'
          description: 'Tipo do staff'
        active:
          type: boolean
          default: true
          example: true
          description: 'Se o staff está ativo'
        urlSlug:
          type: string
          example: 'dr-joao-silva'
          description: 'Slug para URL do perfil'
        quote:
          type: string
          example: 'Cuidar é a essência da medicina'
          description: 'Frase do profissional'
        profileBio:
          type: string
          example: 'Médico especialista em cardiologia com 10 anos de experiência'
          description: 'Biografia do profissional'
        council:
          $ref: '#/components/schemas/CouncilDTO'
        specialty:
          $ref: '#/components/schemas/UUID'
          description: 'ID da especialidade principal'
        subSpecialties:
          type: array
          items:
            $ref: '#/components/schemas/UUID'
          description: 'IDs das subespecialidades'
        internalSpecialty:
          $ref: '#/components/schemas/UUID'
          description: 'ID da especialidade interna'
        internalSubSpecialties:
          type: array
          items:
            $ref: '#/components/schemas/UUID'
          description: 'IDs das subespecialidades internas'
        providerUnits:
          type: array
          items:
            $ref: '#/components/schemas/UUID'
          description: 'IDs das unidades prestadoras'
        qualifications:
          type: array
          items:
            $ref: '#/components/schemas/QualificationDTO'
          description: 'Qualificações do profissional'
        tier:
          type: string
          enum: [JUNIOR, PLENO, SENIOR, EXPERT]
          example: 'SENIOR'
          description: 'Nível do especialista'
        theoristTier:
          type: string
          enum: [JUNIOR, PLENO, SENIOR, EXPERT]
          example: 'EXPERT'
          description: 'Nível teórico do especialista'
        curiosity:
          type: string
          example: 'Gosta de ler sobre novas tecnologias médicas'
          description: 'Curiosidade do profissional'
        showOnApp:
          type: boolean
          example: true
          description: 'Se deve aparecer no app'
        education:
          type: array
          items:
            type: string
          example: ['Medicina - USP', 'Residência em Cardiologia - InCor']
          description: 'Formação educacional'
        healthSpecialistScore:
          type: string
          enum: [DOMINATING, EXPERT, ADVANCED, INTERMEDIATE, BEGINNER]
          example: 'EXPERT'
          description: 'Pontuação do especialista em saúde'
        deAccreditationDate:
          type: string
          format: date
          example: '2025-12-31'
          description: 'Data de descredenciamento'
        contacts:
          type: array
          items:
            $ref: '#/components/schemas/ContactDTO'
          description: 'Contatos do profissional'
        paymentFrequency:
          type: integer
          default: 0
          example: 30
          description: 'Frequência de pagamento em dias'
        attendsToOnCall:
          type: boolean
          example: true
          description: 'Se atende plantão'
        onCallPaymentMethod:
          type: string
          enum: [HEALTH_INSTITUTION, ALICE]
          example: 'ALICE'
          description: 'Método de pagamento do plantão'
        onVacationStart:
          type: string
          format: date
          example: '2024-07-01'
          description: 'Início das férias'
        onVacationUntil:
          type: string
          format: date
          example: '2024-07-15'
          description: 'Fim das férias'

    CouncilDTO:
      required:
        - number
        - state
      type: object
      properties:
        number:
          type: string
          example: '123456'
          description: 'Número do conselho'
        state:
          type: string
          enum:
            [
              AC,
              AL,
              AP,
              AM,
              BA,
              CE,
              DF,
              ES,
              GO,
              MA,
              MT,
              MS,
              MG,
              PA,
              PB,
              PR,
              PE,
              PI,
              RJ,
              RN,
              RS,
              RO,
              RR,
              SC,
              SP,
              SE,
              TO,
            ]
          example: 'SP'
          description: 'Estado do conselho'
        type:
          type: integer
          example: 6
          description: 'Tipo do conselho'

    ContactDTO:
      required:
        - modality
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        address:
          $ref: '#/components/schemas/AddressDTO'
        phones:
          type: array
          items:
            $ref: '#/components/schemas/PhoneNumberDTO'
        scheduleAvailabilityDays:
          type: integer
          example: 30
          description: 'Dias de disponibilidade para agendamento'
        modality:
          type: string
          enum: [PRESENTIAL, TELEMEDICINE, HYBRID]
          example: 'PRESENTIAL'
          description: 'Modalidade de atendimento'
        availableDays:
          type: array
          items:
            $ref: '#/components/schemas/AvailableDayDTO'
        website:
          type: string
          format: uri
          example: 'https://drjoao.com.br'
          description: 'Website do profissional'

    AddressDTO:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        street:
          type: string
          example: 'Rua das Flores'
        number:
          type: string
          example: '123'
        complement:
          type: string
          example: 'Apto 45'
        neighborhood:
          type: string
          example: 'Centro'
        state:
          type: string
          example: 'SP'
        city:
          type: string
          example: 'São Paulo'
        zipcode:
          type: string
          example: '01234-567'
        label:
          type: string
          example: 'Consultório Principal'
        active:
          type: boolean
          default: true
        latitude:
          type: string
          example: '-23.5505'
        longitude:
          type: string
          example: '-46.6333'

    PhoneNumberDTO:
      type: object
      properties:
        number:
          type: string
          example: '11987654321'
        type:
          type: string
          enum: [MOBILE, LANDLINE, WHATSAPP]
          example: 'MOBILE'

    QualificationDTO:
      type: object
      properties:
        name:
          type: string
          example: 'Especialização em Cardiologia Intervencionista'
        institution:
          type: string
          example: 'Hospital das Clínicas'
        year:
          type: integer
          example: 2020

    AvailableDayDTO:
      type: object
      properties:
        dayOfWeek:
          type: string
          enum: [MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY]
          example: 'MONDAY'
        startTime:
          type: string
          format: time
          example: '08:00'
        endTime:
          type: string
          format: time
          example: '18:00'

    StaffFormResponse:
      type: object
      properties:
        specialties:
          type: array
          items:
            $ref: '#/components/schemas/MedicalSpecialty'
        providerUnits:
          type: array
          items:
            $ref: '#/components/schemas/ProviderUnit'
        staffRoles:
          type: array
          items:
            $ref: '#/components/schemas/StaffRolesResponse'
        staffTiers:
          type: array
          items:
            $ref: '#/components/schemas/StaffTiersResponse'
        staffScore:
          type: array
          items:
            $ref: '#/components/schemas/StaffScoreResponse'
        councilTypes:
          type: array
          items:
            $ref: '#/components/schemas/CouncilTypeResponse'

    StaffRolesResponse:
      type: object
      properties:
        id:
          type: string
          example: 'MANAGER_PHYSICIAN'
        name:
          type: string
          example: 'Médico(a) Gestor(a)'
        value:
          type: string
          example: 'MANAGER_PHYSICIAN'

    StaffTiersResponse:
      type: object
      properties:
        id:
          type: string
          example: 'EXPERT'
        name:
          type: string
          example: 'Expert'
        value:
          type: string
          example: 'EXPERT'

    StaffScoreResponse:
      type: object
      properties:
        id:
          type: string
          example: 'DOMINATING'
        name:
          type: string
          example: 'Dominando a parada'
        value:
          type: string
          example: 'DOMINATING'

    CouncilTypeResponse:
      type: object
      properties:
        id:
          type: integer
          example: 6
        name:
          type: string
          example: 'CRM'

    MedicalSpecialty:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: 'Cardiologia'
        type:
          type: string
          example: 'SPECIALTY'
        active:
          type: boolean
          example: true

    ProviderUnit:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: 'Hospital Albert Einstein'
        type:
          type: string
          example: 'HOSPITAL'
        status:
          type: string
          example: 'ACTIVE'
        cnpj:
          type: string
          example: '12345678000190'
        cnes:
          type: string
          example: '1234567'
        site:
          type: string
          example: 'https://hospital.com.br'
        imageUrl:
          type: string
          example: 'https://example.com/image.jpg'
        urlSlug:
          type: string
          example: 'hospital-einstein'
        showOnScheduler:
          type: boolean
          example: true
        showOnApp:
          type: boolean
          example: true

    MedicalSpecialtyFullResponse:
      required:
        - id
        - name
        - type
        - active
        - urlSlug
        - isTherapy
        - requireSpecialist
        - generateGeneralistSubSpecialty
        - internal
        - subSpecialties
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: 'Especialidade X'
        type:
          type: string
          example: 'SPECIALTY'
        active:
          type: boolean
          example: true
        urlSlug:
          type: string
          example: 'especialidade-x'
        isTherapy:
          type: boolean
          example: true
        requireSpecialist:
          type: boolean
          example: true
        generateGeneralistSubSpecialty:
          type: boolean
          example: true
        internal:
          type: boolean
          example: true
        subSpecialties:
          type: array
          items:
            $ref: '#/components/schemas/MedicalSubSpecialtiesResponse'

    MedicalSubSpecialtiesResponse:
      required:
        - name
        - active
        - urlSlug
      type: object
      properties:
        name:
          type: string
          example: 'Especialidade Y'
        active:
          type: boolean
          example: true
        urlSlug:
          type: string
          example: 'especialidade-y'
        isAdvancedAccess:
          type: boolean
          example: true

    FlagshipResponse:
      required:
        - id
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'

    HealthProfessionalsStatesResponse:
      type: array
      items:
        $ref: '#/components/schemas/ItemValue'

    HealthProfessionalsCouncilNamesResponse:
      type: array
      items:
        $ref: '#/components/schemas/ItemValue'

    HealthProfessionalRequest:
      required:
        - fullName
        - councilName
        - councilNumber
        - councilState
      type: object
      properties:
        fullName:
          type: string
          example: 'Luiz Gustavo'
        councilName:
          type: string
          example: 'CRM'
        councilState:
          type: string
          example: 'AC'
        councilNumber:
          type: string
          example: '242571'

    HealthProfessionalFullResponse:
      required:
        - id
        - fullName
        - councilName
        - councilNumber
        - state
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        fullNane:
          type: string
          example: 'Luiz Gustavo'
        councilName:
          type: string
          example: 'CRM'
        councilState:
          type: string
          example: 'AC'
        councilNumber:
          type: string
          example: '242571'
    HealthSpecialistResourceBundlePaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/HealthSpecialistResourceBundleShortResponse'
        pagination:
          $ref: '#/components/schemas/Pagination'

    HealthSpecialistResourceBundleShortResponse:
      required:
        - id
        - primaryTuss
        - secondaryResources
        - executionAmount
        - executionEnvironment
        - aliceDescription
        - status
        - serviceType
        - aliceCode
      type: object
      properties:
        id:
          type: string
          example: 'd2057943-e6d6-47b4-9ebd-ff7b77137f1b'
        primaryTuss:
          type: string
          example: '1234123'
        executionAmount:
          type: integer
          example: 1
        executionEnvironment:
          type: string
          example: 'SURGICAL'
        status:
          type: string
          example: 'ACTIVE'
        aliceCode:
          type: string
          example: '**********'
        type:
          type: string
          example: 'BUNDLE'
        serviceType:
          type: object
          properties:
            friendlyName:
              type: string
              example: 'Exame'
            value:
              type: string
              example: 'EXAM'
            color:
              type: string
              example: 'RED'
        pricingStatus:
          type: object
          properties:
            friendlyName:
              type: string
              example: 'Precificado'
            value:
              type: string
              example: 'PRICED'
            color:
              type: string
              example: 'GREEN'
        specialtiesText:
          type: string
          example: 'Todas especialidades'

    HealthProfessionalPaginatedResponse:
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/HealthProfessionalFullResponse'
        pagination:
          $ref: '#/components/schemas/Pagination'

    PreviewEarningSummariesResponse:
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        status:
          type: string
          enum:
            - PROCESSING
            - ERROR
            - SUCCESS
          example: 'PROCESSING'
        file:
          type: object
          properties:
            fileName:
              type: string
              example: import.csv
            fileUrl:
              type: string
              example: http://link.com
            fileId:
              $ref: '#/components/schemas/UUID'
        errors:
          type: array
          items:
            $ref: '#/components/schemas/PreviewEarningSummariesErrorResponse'
        warnings:
          type: array
          items:
            $ref: '#/components/schemas/PreviewEarningSummariesErrorResponse'
        createdAt:
          type: string
          format: date-time

    PreviewEarningSummariesCreatedResponse:
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        status:
          type: string
          enum:
            - PROCESSING
            - ERROR
            - SUCCESS
          example: 'PROCESSING'
        file:
          type: object
          properties:
            fileName:
              type: string
              example: import.csv
            fileUrl:
              type: string
              example: http://link.com
            fileId:
              $ref: '#/components/schemas/UUID'
        createdAt:
          type: string
          format: date-time

    PreviewEarningSummariesErrorResponse:
      properties:
        line:
          type: number
          example: 2
        hash:
          type: string
          example: 'aoishdiuasfsaf90sd'
        message:
          type: string
          example: 'já cadastrado'

    HealthSpecialistResourceBundlePatchRequest:
      type: object
      properties:
        aliceDescription:
          type: string
          example: 'Luiz Gustavo'
        status:
          type: string
          example: 'ACTIVE'
        medicalSpecialtyIds:
          type: array
          items:
            type: string
            example: '3b414229-c43e-4f91-b58d-cee242df5600'

    HealthSpecialistResourceBundlePatchResponse:
      type: object
      properties:
        aliceDescription:
          type: string
          example: 'Luiz Gustavo'
        secondaryResources:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                example: 'd2057943-e6d6-47b4-9ebd-ff7b77137f1b'
        executionAmount:
          type: integer
          example: 1
        executionEnvironment:
          type: string
          example: 'SURGICAL'
        status:
          type: string
          example: 'ACTIVE'
        serviceType:
          type: string
          example: 'EXAM'
        medicalSpecialtyIds:
          type: array
          items:
            type: string
            example: '3b414229-c43e-4f91-b58d-cee242df5600'

    ResourceBundleSpecialtyBffResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: 'Consulta'
        isTherapy:
          type: boolean
          example: true
        pricingStatus:
          type: object
          properties:
            friendlyName:
              type: string
              example: 'Precificado'
            value:
              type: string
              example: 'PRICED'
            color:
              type: string
              example: 'GREEN'
        currentBeginAt:
          $ref: '#/components/schemas/DateTime'
        currentEndAt:
          $ref: '#/components/schemas/DateTime'
        hasScheduledPriceChange:
          type: boolean
          example: true
        medicalSpecialtyId:
          $ref: '#/components/schemas/UUID'

    SiteAccreditedNetworkResponse:
      required:
        - id
        - title
        - active
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        title:
          type: string
          example: 'Conforto +'
        active:
          type: boolean
          example: true

    UpdateSiteAccreditedNetworkRequest:
      required:
        - id
        - title
        - active
      type: object
      properties:
        title:
          type: string
          example: 'Conforto +'
        active:
          type: boolean
          example: true
        isDefaultSearch:
          type: boolean
          example: false
        productId:
          $ref: '#/components/schemas/UUID'

    SiteAccreditedNetworkItemResponse:
      required:
        - id
        - title
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        title:
          type: string
          example: 'Conforto +'
        active:
          type: boolean
          example: true
        productId:
          $ref: '#/components/schemas/UUID'
        bundles:
          type: array
          items:
            type: object
            properties:
              id:
                $ref: '#/components/schemas/UUID'
              title:
                type: string

    EffectiveDatesResponse:
      type: object
      properties:
        dates:
          type: array
          description: Lista de datas de vigência disponíveis (formato dd/MM/yyyy)
          items:
            type: string
            example: '01/05/2025'
      required:
        - dates

    ResourceSpecialistPricingIndexResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        filename:
          type: string
          description: 'Nome do arquivo de atualização de preços'
          example: 'pricing_update.csv'
        downloadFileUrl:
          type: string
          description: 'URL para download do arquivo original'
          example: 'https://example.com/files/pricing_update.csv'
        downloadFailedLinesFileUrl:
          type: string
          description: 'URL para download do arquivo com linhas que falharam'
          example: 'https://example.com/files/failed_lines.csv'
        createdAt:
          type: string
          description: 'Data e hora de criação formatada'
          example: '13/09/2024 | 14:30:00'
        createdBy:
          type: string
          description: 'Nome do usuário que criou a atualização'
          example: 'João Silva'
        status:
          $ref: '#/components/schemas/FriendlyEnumResponse'
        processingDetails:
          $ref: '#/components/schemas/ResourceSpecialistPricingProcessingDetailsResponse'

    ResourceSpecialistPricingProcessingDetailsResponse:
      type: object
      properties:
        totalItems:
          type: integer
          description: 'Número total de itens no arquivo'
          example: 100
        errorItems:
          type: integer
          description: 'Número de itens com erro'
          example: 5
        friendlyDescription:
          type: string
          description: 'Descrição amigável do status de processamento'
          example: '95/100 itens processados com sucesso'

    UpdateSiteAccreditedNetworkResponse:
      required:
        - id
        - title
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        title:
          type: string
          example: 'Conforto +'
        active:
          type: boolean
          example: true
        productId:
          $ref: '#/components/schemas/UUID'
        bundles:
          type: array
          items:
            type: object
            properties:
              id:
                $ref: '#/components/schemas/UUID'
              title:
                type: string

    DeleteSiteAccreditedNetworkResponse:
      type: boolean
      example: true

    PricingForHealthSpecialistResourceBundleResponse:
      type: object
      properties:
        healthSpecialistResourceBundleId:
          type: string
          format: uuid
          description: ID do pacote de recursos do especialista
        primaryTuss:
          type: string
          description: Código TUSS primário
        aliceCode:
          type: string
          description: Código Alice
        description:
          type: string
          description: Descrição do pacote
        serviceType:
          type: string
          description: Tipo de serviço
        pendingNumber:
          type: integer
          description: Número de pendências
        medicalSpecialties:
          type: array
          description: Lista de especialidades médicas associadas
          items:
            $ref: '#/components/schemas/PricingForHealthSpecialistMedicalSpecialtyResponse'

    PricingForHealthSpecialistMedicalSpecialtyResponse:
      type: object
      properties:
        medicalSpecialtyId:
          type: string
          format: uuid
          description: ID da especialidade médica
        description:
          type: string
          description: Descrição da especialidade médica
        prices:
          type: array
          description: Lista de preços associados
          items:
            $ref: '#/components/schemas/ResourceBundleSpecialtyPrice'
        pendingNumber:
          type: integer
          description: Número de pendências
        beginAt:
          type: string
          format: date
          description: Data de início
        changeBeginAt:
          type: string
          format: date
          description: Data de início da alteração

    ResourceBundleSpecialtyPrice:
      type: object
      properties:
        tier:
          type: string
          example: TALENTED
        productTier:
          type: string
          example: TIER_0
        price:
          type: string
          format: decimal
          description: Preço associado

    ProcessingResourceBundleSpecialtyPricingUpdateResponse:
      type: object
      properties:
        isProcessing:
          type: boolean
          description: Indica se o processamento está em andamento.
        resourceBundleSpecialtyPricingUpdate:
          $ref: '#/components/schemas/ResourceBundleSpecialtyPricingUpdate'

    ResourceBundleSpecialtyPricingUpdate:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        fileName:
          type: string
          example: 'pricing_update.csv'
        fileVaultId:
          $ref: '#/components/schemas/UUID'
        createdByStaffId:
          $ref: '#/components/schemas/UUID'
        processingAt:
          type: string
          format: date-time
          description: 'Data e hora de início do processamento'
          example: '2024-09-13T14:30:00'
        completedAt:
          type: string
          format: date-time
          description: 'Data e hora de conclusão do processamento'
          example: '2024-09-13T15:00:00'
        rowsCount:
          type: integer
          description: 'Número total de linhas no arquivo'
          example: 100
        failedRowsCount:
          type: integer
          description: 'Número de linhas com erros'
          example: 5
        failedRowsErrors:
          type: array
          description: 'Lista de erros nas linhas com falha'
          items:
            $ref: '#/components/schemas/CSVPricingUpdateError'
        parsingError:
          type: string
          description: 'Erro de parsing, se houver'
          example: 'Erro ao processar cabeçalho do arquivo'
        pricesBeginAt:
          type: string
          format: date
          description: 'Data de início dos preços'
          example: '2024-09-01'
        createdAt:
          type: string
          format: date-time
          description: 'Data e hora de criação'
          example: '2024-09-13T14:00:00'
        updatedAt:
          type: string
          format: date-time
          description: 'Data e hora da última atualização'
          example: '2024-09-13T14:30:00'
        version:
          type: integer
          description: 'Versão do registro'
          example: 1

    CSVPricingUpdateError:
      type: object
      properties:
        row:
          type: integer
          description: 'Número da linha com erro'
          example: 10
        error:
          type: string
          description: 'Descrição do erro'
          example: "Valor inválido na coluna 'price'"
    SuggestedProcedurePaginatedResponse:
      type: object
      properties:
        pagination:
          $ref: '#/components/schemas/Pagination'
        results:
          type: array
          items:
            $ref: '#/components/schemas/SuggestedProcedureTransport'

    SuggestedProcedureTransport:
      type: object
      required:
        - id
        - friendlyDescription
        - active
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        friendlyDescription:
          type: string
          example: '10101012 - 9910101012 - consulta'
        active:
          type: boolean
          example: true
    SuggestedSpecialtyPaginatedResponse:
      type: object
      properties:
        pagination:
          $ref: '#/components/schemas/Pagination'
        results:
          type: array
          items:
            $ref: '#/components/schemas/SpecialtyResponse'

    SpecialtyResponse:
      type: object
      required:
        - id
        - name
        - active
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: 'Cardiologia'
        active:
          type: boolean
          example: true

    PreviewEarningSummariesPaginatedResponse:
      type: object
      properties:
        pagination:
          $ref: '#/components/schemas/Pagination'
        results:
          type: array
          items:
            $ref: '#/components/schemas/PreviewEarningSummaryResponse'

    PreviewEarningSummaryResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        createdAt:
          $ref: '#/components/schemas/DateTime'
        updatedAt:
          $ref: '#/components/schemas/DateTime'
        status:
          type: string
          example: CREATED
        fileName:
          type: string
          example: 'file.csv'
        totalRows:
          type: integer
          example: 10

    InvoicePaginatedResponse:
      type: object
      properties:
        pagination:
          $ref: '#/components/schemas/Pagination'
        results:
          type: array
          items:
            $ref: '#/components/schemas/InvoiceIndexResponse'

    InvoiceIndexResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        code:
          type: string
          example: 'INV-123'
        status:
          $ref: '#/components/schemas/FriendlyEnumResponse'
        expenseType:
          $ref: '#/components/schemas/FriendlyEnumResponse'
        providerUnit:
          $ref: '#/components/schemas/ProviderUnitInvoiceIndexResponse'
        staff:
          $ref: '#/components/schemas/StaffInvoiceIndexResponse'
        guiaQuantity:
          type: number
          example: 5
        earningsAmount:
          type: number
          format: double
          example: 1000.50
        createdAt:
          type: string
          example: '01/01/2023 | 14:30'

    ProviderUnitInvoiceIndexResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: 'Provider Name'

    StaffInvoiceIndexResponse:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: 'Staff Name'

    FriendlyEnumResponse:
      type: object
      properties:
        friendlyName:
          type: string
          example: 'Pagamento Realizado'
        value:
          type: string
          example: 'PAYMENT_DONE'

    AutocompleteAddress:
      type: object
      properties:
        placeId:
          type: string
          example: 'ChIJN1t_tDeuEmsRUsoyG83frY4'
          description: 'ID único do local no Google Maps'
        description:
          type: string
          example: 'Av. Rebouças, São Paulo - SP, Brasil'
          description: 'Descrição completa do endereço'
        mainText:
          type: string
          example: 'Av. Rebouças'
          description: 'Parte principal do endereço (geralmente o nome da rua)'
        secondaryText:
          type: string
          example: 'São Paulo - SP, Brasil'
          description: 'Informações secundárias do endereço (cidade, estado, país)'

    AddressDetails:
      type: object
      properties:
        id:
          type: string
          example: 'ChIJN1t_tDeuEmsRUsoyG83frY4'
          description: 'Place ID único do Google Maps'
        street:
          type: string
          example: 'Avenida Rebouças'
          description: 'Nome da rua'
        number:
          type: string
          example: '3506'
          description: 'Número do endereço'
        neighbourhood:
          type: string
          example: 'Pinheiros'
          description: 'Bairro'
        city:
          type: string
          example: 'São Paulo'
          description: 'Cidade'
        state:
          type: string
          example: 'SP'
          description: 'Estado'
        country:
          type: string
          example: 'Brasil'
          description: 'País'
        lat:
          type: number
          format: double
          example: -23.5718116
          description: 'Latitude'
        lng:
          type: number
          format: double
          example: -46.69273700000001
          description: 'Longitude'
        postalCode:
          type: string
          example: '05402-600'
          description: 'CEP'

    PaginationResponse:
      type: object
      properties:
        pageSize:
          type: integer
          example: 10
        totalPages:
          type: integer
          example: 5
        page:
          type: integer
          example: 1

    StaffSignupRequestSummaryDTO:
      type: object
      properties:
        id:
          type: string
          format: uuid
        status:
          type: string
          description: Descrição do status
          example: 'Pendente'
        name:
          type: string
          description: Nome completo do profissional
          example: 'Maria Silva'

    StaffSignupRequestListResponse:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/StaffSignupRequestSummaryDTO'
        pagination:
          $ref: '#/components/schemas/PaginationResponse'

    StaffSignupRequestRejectRequest:
      type: object
      properties:
        rejectionReason:
          type: string
          description: Motivo da rejeição
          example: 'Informações inconsistentes'

    AuthorizerRequest:
      type: object
      required:
        - providerUnitId
        - domain
        - mvCdPrestador
        - mvCdLocalPrestador
        - mvCdLocal
      properties:
        providerUnitId:
          type: string
          format: uuid
          description: ID da unidade prestadora
          example: '123e4567-e89b-12d3-a456-************'
        domain:
          type: string
          description: Domínio do autorizador
          example: 'hospital.com.br'
        mvCdPrestador:
          type: integer
          description: Código MV do prestador
          example: 123
        mvCdLocalPrestador:
          type: integer
          description: Código MV local do prestador
          example: 456
        mvCdLocal:
          type: integer
          description: Código MV local
          example: 789
        passwordKey:
          type: string
          description: Chave de senha (opcional, padrão será 'alicePass' se não fornecida)
          example: 'mySecretKey'

    AuthorizerResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: ID único do autorizador
          example: '123e4567-e89b-12d3-a456-************'
        providerUnitId:
          type: string
          format: uuid
          description: ID da unidade prestadora
          example: '123e4567-e89b-12d3-a456-************'
        providerUnitName:
          type: string
          description: Nome da unidade prestadora
          example: 'Hospital São Paulo'
        providerUnitType:
          type: string
          description: Tipo da unidade prestadora
          enum:
            [HOSPITAL, CLINIC, LABORATORY, DIAGNOSTIC_CENTER, PHARMACY, OTHER]
          example: 'HOSPITAL'
        domain:
          type: string
          description: Domínio do autorizador
          example: 'hospital.com.br'
        mvCdPrestador:
          type: integer
          description: Código MV do prestador
          example: 123
        mvCdLocalPrestador:
          type: integer
          description: Código MV local do prestador
          example: 456
        mvCdLocal:
          type: integer
          description: Código MV local
          example: 789
        passwordKey:
          type: string
          description: Chave de senha
          example: 'alicePass'
        createdAt:
          type: string
          format: date-time
          description: Data de criação
          example: '2024-01-15T10:30:00Z'
        updatedAt:
          type: string
          format: date-time
          description: Data de última atualização
          example: '2024-01-15T10:30:00Z'

    ExecAuthorizerPaginatedResponse:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/AuthorizerResponse'
        pagination:
          $ref: '#/components/schemas/Pagination'

    StaffSignupRequestApproveRequest:
      type: object
      properties:
        tier:
          type: string
          description: Nível do profissional
          example: 'EXPERT'
        theoristTier:
          type: string
          description: Nível teórico do profissional
          example: 'EXPERT'
        showOnApp:
          type: boolean
          description: Mostrar no aplicativo
          example: true
        specialtyId:
          type: string
          format: uuid
          description: ID da especialidade
          example: '123e4567-e89b-12d3-a456-************'
        subSpecialtyIds:
          type: array
          items:
            type: string
            format: uuid
          description: IDs das sub-especialidades
          example:
            [
              '123e4567-e89b-12d3-a456-************',
              '123e4567-e89b-12d3-a456-************',
            ]

    StaffSignupRequestIntegrationContent:
      type: object
      properties:
        healthProfessional:
          $ref: '#/components/schemas/StaffSignupRequestHealthProfessional'
        provider:
          $ref: '#/components/schemas/StaffSignupRequestProvider'

    StaffSignupRequestHealthProfessional:
      type: object
      properties:
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
          format: email
        nationalId:
          type: string
          nullable: true
        birthdate:
          type: string
          format: date
          nullable: true
        gender:
          type: string
        profileImageUrl:
          type: string
          format: uri
          nullable: true
        profileBio:
          type: string
          nullable: true
        education:
          type: string
          nullable: true
        curiosity:
          type: string
          nullable: true
        councilType:
          type: string
        councilNumber:
          type: string
        councilState:
          type: string
        specialty:
          type: string
          nullable: true
        subSpecialties:
          type: array
          items:
            type: string
        contacts:
          type: array
          items:
            $ref: '#/components/schemas/StaffSignupRequestContact'

    ProviderUnitResponse:
      required:
        - id
        - name
        - type
        - status
        - createdAt
        - updatedAt
      type: object
      properties:
        id:
          $ref: '#/components/schemas/UUID'
        name:
          type: string
          example: 'Hospital São Paulo'
          description: 'Nome da unidade prestadora'
        type:
          type: string
          enum:
            [
              HOSPITAL,
              HOSPITAL_CHILDREN,
              LABORATORY,
              ALICE_HOUSE,
              EMERGENCY_UNITY,
              EMERGENCY_UNITY_CHILDREN,
              MATERNITY,
              CLINICAL_COMMUNITY,
              CLINICAL,
              VACCINE,
            ]
          example: 'HOSPITAL'
          description: 'Tipo da unidade prestadora'
        status:
          type: string
          enum: [ACTIVE, INACTIVE, DELETED]
          example: 'ACTIVE'
          description: 'Status da unidade prestadora'
        cnpj:
          type: string
          nullable: true
          example: '12345678000195'
          description: 'CNPJ da unidade prestadora'
        cnes:
          type: string
          nullable: true
          example: '1234567'
          description: 'Código CNES da unidade prestadora'
        providerId:
          $ref: '#/components/schemas/UUID'
          nullable: true
          description: 'ID do prestador pai'
        createdAt:
          $ref: '#/components/schemas/DateTime'
        updatedAt:
          $ref: '#/components/schemas/DateTime'

    StaffSignupRequestProvider:
      type: object
      properties:
        name:
          type: string
        cnpj:
          type: string
        cnes:
          type: string
          nullable: true
        bankCode:
          type: string
          nullable: true
        agencyNumber:
          type: string
          nullable: true
        accountNumber:
          type: string
          nullable: true
        phones:
          type: array
          items:
            $ref: '#/components/schemas/StaffSignupRequestPhone'
        address:
          $ref: '#/components/schemas/StaffSignupRequestAddress'

    StaffSignupRequestContact:
      type: object
      properties:
        address:
          $ref: '#/components/schemas/StaffSignupRequestAddress'
        phones:
          type: array
          items:
            $ref: '#/components/schemas/StaffSignupRequestPhone'
        modality:
          type: string

    StaffSignupRequestPhone:
      type: object
      properties:
        type:
          type: string
        number:
          type: string

    StaffSignupRequestAddress:
      type: object
      properties:
        street:
          type: string
        number:
          type: string
        complement:
          type: string
          nullable: true
        neighborhood:
          type: string
        state:
          type: string
        city:
          type: string
        zipcode:
          type: string
        country:
          type: string
          nullable: true
