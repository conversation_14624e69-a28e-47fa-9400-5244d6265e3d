package br.com.alice.api.backoffice.controllers.execIndicator

import br.com.alice.api.backoffice.ControllerTestHelper
import br.com.alice.api.backoffice.transfers.execIndicator.AuthorizerRequest
import br.com.alice.api.backoffice.transfers.execIndicator.AuthorizerResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsBFFJson
import br.com.alice.common.transfers.ListPaginatedResponse
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ExecIndicatorAuthorizer
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.exec.indicator.client.ExecIndicatorAuthorizerService
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test


class ExecIndicatorAuthorizersControllerTest : ControllerTestHelper() {

    private val execIndicatorAuthorizerService: ExecIndicatorAuthorizerService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val execIndicatorAuthorizersController = ExecIndicatorAuthorizersController(
        execIndicatorAuthorizerService,
        providerUnitService
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        clearMocks(execIndicatorAuthorizerService, providerUnitService)
        module.single { execIndicatorAuthorizersController }
    }

    private val providerUnit = TestModelFactory.buildProviderUnit()
    private val authorizer = TestModelFactory.buildExecIndicatorAuthorizer(
        domain = "alice.com.br",
        mvProviderId = 50,
        providerUnitId = providerUnit.id
    )

    @Test
    fun `should create ExecIndicatorAuthorizer successfully`() {
        val request = AuthorizerRequest(
            providerUnitId = providerUnit.id.toString(),
            domain = "alice.com.br",
            mvCdPrestador = 14,
            mvCdLocal = 1,
            mvCdLocalPrestador = 1,
            passwordKey = "passwordKey"
        )

        coEvery {
            execIndicatorAuthorizerService.add(match {
                it.providerUnitId == request.providerUnitId.toUUID()
                        && it.domain == request.domain
                        && it.mvCdPrestador == request.mvCdPrestador
            })
        } returns authorizer.success()
        coEvery {
            providerUnitService.get(authorizer.providerUnitId)
        } returns providerUnit.success()

        authenticatedAs(idToken, staff) {
            post(to = "/execAuthorizer", body = request) { response ->
                val execAuthorizerResponse: AuthorizerResponse = response.bodyAsBFFJson()
                
                assertThat(execAuthorizerResponse.id).isEqualTo(authorizer.id.toString())
                assertThat(execAuthorizerResponse.providerUnitId).isEqualTo(authorizer.providerUnitId.toString())
                assertThat(execAuthorizerResponse.providerUnitName).isEqualTo(providerUnit.name)
                assertThat(execAuthorizerResponse.providerUnitType).isEqualTo(providerUnit.type)
                assertThat(execAuthorizerResponse.domain).isEqualTo(authorizer.domain)
                assertThat(execAuthorizerResponse.mvCdPrestador).isEqualTo(authorizer.mvCdPrestador)
                assertThat(execAuthorizerResponse.passwordKey).isEqualTo(authorizer.passwordKey)

                assertThat(response).isSuccessfulJson()
                
                coVerify { execIndicatorAuthorizerService.add(any()) }
                coVerify { providerUnitService.get(authorizer.providerUnitId) }
            }
        }
    }

    @Test
    fun `should fail to create ExecIndicatorAuthorizer when service fails`() {
        val request = AuthorizerRequest(
            providerUnitId = providerUnit.id.toString(),
            domain = "alice.com.br",
            mvCdPrestador = 14,
            mvCdLocal = 1,
            mvCdLocalPrestador = 1,
            passwordKey = "passwordKey"
        )

        coEvery {
            execIndicatorAuthorizerService.add(any())
        } returns RuntimeException("Service error").failure()

        authenticatedAs(idToken, staff) {
            post(to = "/execAuthorizer", body = request) { response ->
                assertThat(response).isInternalServerError()
            }
        }
    }

    @Test
    fun `should get ExecIndicatorAuthorizer by id successfully`() {
        coEvery { 
            execIndicatorAuthorizerService.get(authorizer.id) 
        } returns authorizer.success()
        coEvery {
            providerUnitService.get(authorizer.providerUnitId)
        } returns providerUnit.success()

        authenticatedAs(idToken, staff) {
            get("/execAuthorizer/${authorizer.id}") { response ->
                val execAuthorizerResponse: AuthorizerResponse = response.bodyAsBFFJson()
                
                assertThat(execAuthorizerResponse.id).isEqualTo(authorizer.id.toString())
                assertThat(execAuthorizerResponse.domain).isEqualTo(authorizer.domain)
                assertThat(response).isSuccessfulJson()
                
                coVerify { execIndicatorAuthorizerService.get(authorizer.id) }
                coVerify { providerUnitService.get(authorizer.providerUnitId) }
            }
        }
    }

    @Test
    fun `should fail to get ExecIndicatorAuthorizer when not found`() {
        val nonExistentId = RangeUUID.generate()
        
        coEvery { 
            execIndicatorAuthorizerService.get(nonExistentId) 
        } returns NotFoundException("Authorizer not found").failure()

        authenticatedAs(idToken, staff) {
            get("/execAuthorizer/$nonExistentId") { response ->
                assertThat(response).isNotFound()
            }
        }
    }

    @Test
    fun `should update ExecIndicatorAuthorizer successfully`() {
        val updatedAuthorizer = authorizer.copy(domain = "updated.com.br")
        val request = AuthorizerRequest(
            providerUnitId = providerUnit.id.toString(),
            domain = "updated.com.br",
            mvCdPrestador = 15,
            mvCdLocal = 2,
            mvCdLocalPrestador = 2,
            passwordKey = "newPassword"
        )

        coEvery { 
            execIndicatorAuthorizerService.get(authorizer.id) 
        } returns authorizer.success()
        coEvery {
            execIndicatorAuthorizerService.update(match {
                it.domain == request.domain
                        && it.mvCdPrestador == request.mvCdPrestador
                        && it.passwordKey == request.passwordKey
            })
        } returns updatedAuthorizer.success()
        coEvery {
            providerUnitService.get(authorizer.providerUnitId)
        } returns providerUnit.success()

        authenticatedAs(idToken, staff) {
            put(to = "/execAuthorizer/${authorizer.id}", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                val execAuthorizerResponse: AuthorizerResponse = response.bodyAsBFFJson()
                assertThat(execAuthorizerResponse.id).isEqualTo(authorizer.id.toString())
                
                coVerify { execIndicatorAuthorizerService.get(authorizer.id) }
                coVerify { execIndicatorAuthorizerService.update(any()) }
            }
        }
    }

    @Test
    fun `should fail to update ExecIndicatorAuthorizer when not found`() {
        val nonExistentId = RangeUUID.generate()
        val request = AuthorizerRequest(
            providerUnitId = providerUnit.id.toString(),
            domain = "updated.com.br",
            mvCdPrestador = 15,
            mvCdLocal = 2,
            mvCdLocalPrestador = 2
        )

        coEvery {
            execIndicatorAuthorizerService.get(nonExistentId)
        } returns NotFoundException("Authorizer not found").failure()

        authenticatedAs(idToken, staff) {
            put(to = "/execAuthorizer/$nonExistentId", body = request) { response ->
                assertThat(response).isNotFound()

                coVerify { execIndicatorAuthorizerService.get(nonExistentId) }
                coVerify(exactly = 0) { execIndicatorAuthorizerService.update(any()) }
            }
        }
    }

    @Test
    fun `should list ExecIndicatorAuthorizers with pagination`() {
        val startRange = 0
        val endRange = 19
        val authorizers = listOf(authorizer)

        coEvery {
            execIndicatorAuthorizerService.getByRange(IntRange(startRange, endRange))
        } returns authorizers.success()
        coEvery {
            providerUnitService.getByIds(listOf(authorizer.providerUnitId), withAddress = false)
        } returns listOf(providerUnit).success()
        coEvery {
            execIndicatorAuthorizerService.countAll()
        } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/execAuthorizer?range=[$startRange,$endRange]") { response ->
                assertThat(response).isSuccessfulJson()

                val execAuthorizerResponses: ListPaginatedResponse<AuthorizerResponse> = response.bodyAsBFFJson()

                assertThat(execAuthorizerResponses.results).hasSize(1)

                val execAuthorizerResponse = execAuthorizerResponses.results.first()
                assertThat(execAuthorizerResponse.id).isEqualTo(authorizer.id.toString())
                assertThat(execAuthorizerResponse.providerUnitId).isEqualTo(authorizer.providerUnitId.toString())
                assertThat(execAuthorizerResponse.providerUnitName).isEqualTo(providerUnit.name)
                assertThat(execAuthorizerResponse.providerUnitType).isEqualTo(providerUnit.type)

                coVerify { execIndicatorAuthorizerService.getByRange(IntRange(startRange, endRange)) }
                coVerify { execIndicatorAuthorizerService.countAll() }
                coVerify { providerUnitService.getByIds(any(), withAddress = false) }
            }
        }
    }

    @Test
    fun `should return empty list when no authorizers found`() {
        val startRange = 0
        val endRange = 19

        coEvery {
            execIndicatorAuthorizerService.getByRange(IntRange(startRange, endRange))
        } returns emptyList<ExecIndicatorAuthorizer>().success()
        coEvery {
            execIndicatorAuthorizerService.countAll()
        } returns 0.success()

        authenticatedAs(idToken, staff) {
            get("/execAuthorizer?range=[$startRange,$endRange]") { response ->
                assertThat(response).isSuccessfulJson()

                val execAuthorizerResponses: ListPaginatedResponse<AuthorizerResponse> = response.bodyAsBFFJson()

                assertThat(execAuthorizerResponses.results).isEmpty()
            }
        }
    }

    @Test
    fun `should filter ExecIndicatorAuthorizers by provider unit name`() {
        val query = providerUnit.name

        coEvery {
            providerUnitService.getByName(query)
        } returns listOf(providerUnit).success()
        coEvery { execIndicatorAuthorizerService.getByProviderUnitIds(listOf(providerUnit.id), 0..9) } returns listOf(authorizer).success()
        coEvery { execIndicatorAuthorizerService.countByProviderUnitIds(listOf(providerUnit.id)) } returns 1.success()
        coEvery {
            providerUnitService.getByIds(listOf(authorizer.providerUnitId), withAddress = false)
        } returns listOf(providerUnit).success()

        authenticatedAs(idToken, staff) {
            get("/execAuthorizer?filter={\"q\":\"$query\"}") { response ->
                assertThat(response).isSuccessfulJson()

                val execAuthorizerResponses: ListPaginatedResponse<AuthorizerResponse> = response.bodyAsBFFJson()

                assertThat(execAuthorizerResponses.results).hasSize(1)

                val execAuthorizerResponse = execAuthorizerResponses.results.first()
                assertThat(execAuthorizerResponse.id).isEqualTo(authorizer.id.toString())
                assertThat(execAuthorizerResponse.providerUnitName).isEqualTo(providerUnit.name)

                coVerify { providerUnitService.getByName(query) }
                coVerify { execIndicatorAuthorizerService.getByProviderUnitIds(listOf(providerUnit.id), 0..9) }
            }
        }
    }

    @Test
    fun `should return empty list when filtering by non-existent provider unit name`() {
        val query = "non-existent-provider"

        coEvery {
            providerUnitService.getByName(query)
        } returns emptyList<ProviderUnit>().success()
        coEvery {
            execIndicatorAuthorizerService.getByProviderUnitIds(emptyList(), 0..9)
        } returns emptyList<ExecIndicatorAuthorizer>().success()
        coEvery {
            execIndicatorAuthorizerService.countByProviderUnitIds(emptyList())
        } returns 0.success()

        authenticatedAs(idToken, staff) {
            get("/execAuthorizer?filter={\"q\":\"$query\"}") { response ->
                assertThat(response).isSuccessfulJson()

                val execAuthorizerResponses: ListPaginatedResponse<AuthorizerResponse> = response.bodyAsBFFJson()
                assertThat(execAuthorizerResponses.results).isEmpty()
            }
        }
    }

    @Test
    fun `should handle multiple provider units in response formatting`() {
        val providerUnit2 = TestModelFactory.buildProviderUnit()
        val authorizer2 = TestModelFactory.buildExecIndicatorAuthorizer(
            domain = "test.com.br",
            mvProviderId = 60,
            providerUnitId = providerUnit2.id
        )
        val authorizers = listOf(authorizer, authorizer2)
        val providerUnits = listOf(providerUnit, providerUnit2)

        coEvery {
            execIndicatorAuthorizerService.getByRange(IntRange(0, 19))
        } returns authorizers.success()
        coEvery {
            providerUnitService.getByIds(listOf(authorizer.providerUnitId, authorizer2.providerUnitId), withAddress = false)
        } returns providerUnits.success()
        coEvery {
            execIndicatorAuthorizerService.countAll()
        } returns 2.success()

        authenticatedAs(idToken, staff) {
            get("/execAuthorizer?range=[0,19]") { response ->
                assertThat(response).isSuccessfulJson()

                val execAuthorizerResponses: ListPaginatedResponse<AuthorizerResponse> = response.bodyAsBFFJson()
                assertThat(execAuthorizerResponses.results).hasSize(2)

                // Verify both authorizers are properly formatted with their respective provider units
                val response1 = execAuthorizerResponses.results.find { it.id == authorizer.id.toString() }
                val response2 = execAuthorizerResponses.results.find { it.id == authorizer2.id.toString() }

                assertThat(response1).isNotNull
                assertThat(response1!!.providerUnitName).isEqualTo(providerUnit.name)

                assertThat(response2).isNotNull
                assertThat(response2!!.providerUnitName).isEqualTo(providerUnit2.name)
            }
        }
    }

    @Test
    fun `should handle service failure when listing authorizers`() {
        coEvery {
            execIndicatorAuthorizerService.getByRange(any())
        } returns RuntimeException("Database error").failure()

        authenticatedAs(idToken, staff) {
            get("/execAuthorizer?range=[0,19]") { response ->
                assertThat(response).isInternalServerError()
            }
        }
    }

    @Test
    fun `should handle provider unit service failure when formatting response`() {
        coEvery {
            execIndicatorAuthorizerService.get(authorizer.id)
        } returns authorizer.success()
        coEvery {
            providerUnitService.get(authorizer.providerUnitId)
        } returns RuntimeException("Provider service error").failure()

        authenticatedAs(idToken, staff) {
            get("/execAuthorizer/${authorizer.id}") { response ->
                assertThat(response).isInternalServerError()
            }
        }
    }

    @Test
    fun `should handle invalid UUID in path parameter`() {
        authenticatedAs(idToken, staff) {
            get("/execAuthorizer/invalid-uuid") { response ->
                assertThat(response).isBadRequest()
            }
        }
    }

    @Test
    fun `should handle empty password key by setting default`() {
        val request = AuthorizerRequest(
            providerUnitId = providerUnit.id.toString(),
            domain = "alice.com.br",
            mvCdPrestador = 14,
            mvCdLocal = 1,
            mvCdLocalPrestador = 1,
            passwordKey = ""
        )

        coEvery {
            execIndicatorAuthorizerService.add(match {
                it.passwordKey == "alicePass" // Should use default when empty
            })
        } returns authorizer.success()
        coEvery {
            providerUnitService.get(authorizer.providerUnitId)
        } returns providerUnit.success()

        authenticatedAs(idToken, staff) {
            post(to = "/execAuthorizer", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                coVerify {
                    execIndicatorAuthorizerService.add(match {
                        it.passwordKey == "alicePass"
                    })
                }
            }
        }
    }

    @Test
    fun `should preserve original password when updating with empty password`() {
        val request = AuthorizerRequest(
            providerUnitId = providerUnit.id.toString(),
            domain = "updated.com.br",
            mvCdPrestador = 15,
            mvCdLocal = 2,
            mvCdLocalPrestador = 2,
            passwordKey = ""
        )

        coEvery {
            execIndicatorAuthorizerService.get(authorizer.id)
        } returns authorizer.success()
        coEvery {
            execIndicatorAuthorizerService.update(match {
                it.passwordKey == "" // Should preserve empty when explicitly set
            })
        } returns authorizer.success()
        coEvery {
            providerUnitService.get(authorizer.providerUnitId)
        } returns providerUnit.success()

        authenticatedAs(idToken, staff) {
            put(to = "/execAuthorizer/${authorizer.id}", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                coVerify {
                    execIndicatorAuthorizerService.update(match {
                        it.passwordKey == ""
                    })
                }
            }
        }
    }

    @Test
    fun `should handle concurrent requests properly`() {
        // This test ensures the controller can handle multiple concurrent requests
        val requests = (1..5).map { i ->
            AuthorizerRequest(
                providerUnitId = providerUnit.id.toString(),
                domain = "test$i.com.br",
                mvCdPrestador = i,
                mvCdLocal = i,
                mvCdLocalPrestador = i,
                passwordKey = "password$i"
            )
        }

        requests.forEach { request ->
            val testAuthorizer = authorizer.copy(
                domain = request.domain,
                mvCdPrestador = request.mvCdPrestador
            )

            coEvery {
                execIndicatorAuthorizerService.add(match {
                    it.domain == request.domain && it.mvCdPrestador == request.mvCdPrestador
                })
            } returns testAuthorizer.success()
        }

        coEvery {
            providerUnitService.get(any())
        } returns providerUnit.success()

        authenticatedAs(idToken, staff) {
            requests.forEach { request ->
                post(to = "/execAuthorizer", body = request) { response ->
                    assertThat(response).isSuccessfulJson()
                }
            }
        }
    }

    @Test
    fun `should delete ExecIndicatorAuthorizer successfully`() {
        coEvery {
            execIndicatorAuthorizerService.delete(authorizer.id)
        } returns true.success()

        authenticatedAs(idToken, staff) {
            delete("/execAuthorizer/${authorizer.id}") { response ->
                assertThat(response).isSuccessfulJson()

                coVerify { execIndicatorAuthorizerService.delete(authorizer.id) }
            }
        }
    }
}
