package br.com.alice.api.backoffice.controllers.providers

import br.com.alice.api.backoffice.ControllerTestHelper
import br.com.alice.api.backoffice.mappers.providers.ProviderUnitOutputMapper
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.data.dsl.matchers.BFFResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.provider.client.ProviderUnitFilter
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.success
import io.ktor.http.parametersOf
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import kotlin.test.BeforeTest

class ProviderUnitControllerTest : ControllerTestHelper() {

    private val providerUnitService: ProviderUnitService = mockk()
    private val controller = ProviderUnitController(providerUnitService)

    private val providerUnit1 = TestModelFactory.buildProviderUnit(
        id = RangeUUID.generate(),
        name = "Hospital São Paulo",
        type = ProviderUnit.Type.HOSPITAL,
        status = Status.ACTIVE,
        cnpj = "12345678000195",
        cnes = "1234567"
    )

    private val providerUnit2 = TestModelFactory.buildProviderUnit(
        id = RangeUUID.generate(),
        name = "Laboratório Fleury",
        type = ProviderUnit.Type.LABORATORY,
        status = Status.ACTIVE,
        cnpj = "98765432000123",
        cnes = "7654321"
    )

    private val providerUnit3 = TestModelFactory.buildProviderUnit(
        id = RangeUUID.generate(),
        name = "Casa Alice Centro",
        type = ProviderUnit.Type.ALICE_HOUSE,
        status = Status.INACTIVE,
        cnpj = "11111111000111",
        cnes = "1111111"
    )

    private val defaultRange = IntRange(0, 19)
    private val queryParams = parametersOf("page" to listOf("1"), "pageSize" to listOf("20"))

    @BeforeTest
    override fun setup() {
        clearAllMocks()
        super.setup()
        module.single { controller }
    }

    @BeforeTest
    fun confirmMocks() = confirmVerified(providerUnitService)

    @Test
    fun `#index should return all provider units with default filters`() = runBlocking {
        val providerUnits = listOf(providerUnit1, providerUnit2, providerUnit3)
        val total = 3

        coEvery {
            providerUnitService.getByFilterWithRange(
                filter = ProviderUnitFilter(null, emptyList(), emptyList(), emptyList()),
                range = defaultRange
            )
        } returns providerUnits.success()

        coEvery {
            providerUnitService.countByFilter(
                filter = ProviderUnitFilter(null, emptyList(), emptyList(), emptyList())
            )
        } returns total.success()

        val expectedResponse = ProviderUnitOutputMapper.toPaginatedResponse(providerUnits, total, queryParams)

        authenticatedAs(idToken, staff) {
            get("/provider_units?page=1&pageSize=20") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { providerUnitService.getByFilterWithRange(any(), any()) }
        coVerifyOnce { providerUnitService.countByFilter(any()) }
    }
}
