package br.com.alice.api.backoffice.mappers.execIndicator

import br.com.alice.api.backoffice.transfers.execIndicator.AuthorizerResponse
import br.com.alice.common.mappers.CommonOutputMapper
import br.com.alice.common.transfers.ListPaginatedResponse
import io.ktor.http.Parameters

object ExecIndicatorOutputMapper {

    fun toPaginatedResponse(items: List<AuthorizerResponse>, total: Int, queryParams: Parameters): ListPaginatedResponse<AuthorizerResponse> =
        ListPaginatedResponse(
            pagination = CommonOutputMapper.toPaginationResponse(queryParams, total),
            results = items
        )
}
