package br.com.alice.api.backoffice.mappers.providers

import br.com.alice.api.backoffice.transfers.provider.ProviderUnitShortResponse
import br.com.alice.common.mappers.CommonOutputMapper
import br.com.alice.common.transfers.ListPaginatedResponse
import br.com.alice.data.layer.models.ProviderUnit
import io.ktor.http.Parameters

object ProviderUnitOutputMapper {

    fun toPaginatedResponse(items: List<ProviderUnit>, total: Int, queryParams: Parameters): ListPaginatedResponse<ProviderUnitShortResponse> =
        ListPaginatedResponse(
            pagination = CommonOutputMapper.toPaginationResponse(queryParams, total),
            results = items.map { toShorResponse(it) }
        )


    fun toShorResponse(providerUnit: ProviderUnit): ProviderUnitShortResponse {
        return ProviderUnitShortResponse(
            id = providerUnit.id,
            name = providerUnit.name,
            type = providerUnit.type,
            status = providerUnit.status,
            cnpj = providerUnit.cnpj,
            cnes = providerUnit.cnes,
            providerId = providerUnit.providerId,
        )
    }
}
