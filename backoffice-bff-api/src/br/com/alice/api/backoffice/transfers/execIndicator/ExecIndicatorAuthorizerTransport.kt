package br.com.alice.api.backoffice.transfers.execIndicator

import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.ExecIndicatorAuthorizer
import br.com.alice.data.layer.models.ProviderUnit

data class AuthorizerRequest(
    val providerUnitId: String,
    val domain: String,
    val mvCdPrestador: Int,
    val mvCdLocalPrestador: Int,
    val mvCdLocal: Int,
    val passwordKey: String? = ""
)

data class AuthorizerResponse(
    val id: String,
    val providerUnitId: String,
    val providerUnitName: String,
    val providerUnitType: ProviderUnit.Type,
    val domain: String,
    val mvCdPrestador: Int,
    val mvCdLocalPrestador: Int,
    val mvCdLocal: Int,
    val createdAt: String,
    val updatedAt: String,
    val passwordKey: String
)

object AuthorizerResponseConverter :
    Converter<ExecIndicatorAuthorizer, AuthorizerResponse>(ExecIndicatorAuthorizer::class, AuthorizerResponse::class) {

    fun convert(source: ExecIndicatorAuthorizer, providerUnit: ProviderUnit): AuthorizerResponse {
        return convert(
            source,
            map(AuthorizerResponse::providerUnitName) from providerUnit.name,
            map(AuthorizerResponse::providerUnitType) from providerUnit.type,
        )
    }
}
