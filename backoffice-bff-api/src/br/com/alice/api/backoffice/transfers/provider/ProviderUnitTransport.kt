package br.com.alice.api.backoffice.transfers.provider

import br.com.alice.common.core.Status
import br.com.alice.data.layer.models.ProviderUnit
import java.util.UUID

// Simple response model for backoffice
data class ProviderUnitShortResponse(
    val id: UUID,
    val name: String,
    val type: ProviderUnit.Type,
    val status: Status,
    val cnpj: String?,
    val cnes: String?,
    val providerId: UUID?,
)
