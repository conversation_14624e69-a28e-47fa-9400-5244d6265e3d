package br.com.alice.api.backoffice.routes

import br.com.alice.api.backoffice.controllers.providers.ProviderUnitController
import br.com.alice.common.coHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.providerUnitRoutes() {
    authenticate {
        val providerUnitController by inject<ProviderUnitController>()

        route("provider_units") {
            get { coHandler(providerUnitController::index) }
        }
    }
}
