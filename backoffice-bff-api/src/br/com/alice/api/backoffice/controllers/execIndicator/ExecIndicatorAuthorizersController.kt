package br.com.alice.api.backoffice.controllers.execIndicator

import br.com.alice.api.backoffice.mappers.execIndicator.ExecIndicatorOutputMapper
import br.com.alice.api.backoffice.transfers.execIndicator.AuthorizerRequest
import br.com.alice.api.backoffice.transfers.execIndicator.AuthorizerResponse
import br.com.alice.api.backoffice.transfers.execIndicator.AuthorizerResponseConverter
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.convertTo
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.foldResponse
import br.com.alice.common.mappers.CommonInputMapper
import br.com.alice.data.layer.models.ExecIndicatorAuthorizer
import br.com.alice.exec.indicator.client.ExecIndicatorAuthorizerService
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class ExecIndicatorAuthorizersController(
    private val execIndicatorAuthorizerService: ExecIndicatorAuthorizerService,
    private val providerUnitService: ProviderUnitService,
): Controller() {

    suspend fun index(queryParams: Parameters): Response = coroutineScope {
        val range = CommonInputMapper.getPaginationParams(queryParams)
        val filter = CommonInputMapper.getFilterParams<String>(queryParams, "q")

        val (items, count) = when {
            filter != null -> {
                val providerUnitIds = providerUnitService.getByName(filter)
                    .mapEach { it.id}.get()

                val countDeferred = async { execIndicatorAuthorizerService.countByProviderUnitIds(providerUnitIds).get() }
                execIndicatorAuthorizerService.getByProviderUnitIds(providerUnitIds = providerUnitIds, range = range)
                    .flatMap { formatResponseList(it) }
                    .map { items -> Pair(items, countDeferred.await()) }
            }
            else -> {
                val countDeferred = async { execIndicatorAuthorizerService.countAll().get() }
                execIndicatorAuthorizerService.getByRange(range)
                    .flatMap { formatResponseList(it) }
                    .map { items -> Pair(items, countDeferred.await()) }
            }
        }.get()

        Response(
            status = HttpStatusCode.OK,
            message = ExecIndicatorOutputMapper.toPaginatedResponse(items, count, queryParams),
        )
    }

    suspend fun getById(id: UUID) =
        execIndicatorAuthorizerService.get(id)
            .flatMap { formatResponse(it) }
            .foldResponse()

    suspend fun create(request: AuthorizerRequest) =
        execIndicatorAuthorizerService.add(formatRequest(request))
            .flatMap { formatResponse(it) }
            .foldResponse()

    suspend fun delete(id: UUID) =
        execIndicatorAuthorizerService.delete(id)
            .foldResponse()

    suspend fun update(id: UUID, request: AuthorizerRequest) =
        execIndicatorAuthorizerService.get(id)
            .map {
                it.copy(
                    providerUnitId = request.providerUnitId.toUUID(),
                    domain = request.domain,
                    mvCdPrestador = request.mvCdPrestador,
                    mvCdLocalPrestador = request.mvCdLocalPrestador,
                    mvCdLocal = request.mvCdLocal,
                    passwordKey = request.passwordKey ?: ""
                )
            }
            .flatMap { model -> execIndicatorAuthorizerService.update(model) }
            .flatMap { formatResponse(it) }
            .foldResponse()


     private fun formatRequest(request: AuthorizerRequest): ExecIndicatorAuthorizer =
        request.copy(passwordKey =
                if (request.passwordKey.isNullOrEmpty()) "alicePass"
                else request.passwordKey
        ).convertTo(ExecIndicatorAuthorizer::class)


    private suspend fun formatResponse(item: ExecIndicatorAuthorizer) =
        providerUnitService.get(item.providerUnitId)
            .map { providerUnit -> AuthorizerResponseConverter.convert(item, providerUnit) }

    private suspend fun formatResponseList(items: List<ExecIndicatorAuthorizer>) =
        if (items.isEmpty()) emptyList<AuthorizerResponse>().success()
        else providerUnitService.getByIds(items.map { it.providerUnitId }, withAddress = false)
            .map { providers -> providers.associateBy { it.id } }
            .map { providerUnitsMap ->

                items.map { item ->
                    AuthorizerResponseConverter.convert(item, providerUnitsMap.getValue(item.providerUnitId))
                }
            }
}
