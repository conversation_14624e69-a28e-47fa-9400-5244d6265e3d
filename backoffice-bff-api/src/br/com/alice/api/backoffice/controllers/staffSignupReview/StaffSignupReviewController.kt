package br.com.alice.api.backoffice.controllers.staffSignupReview

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.foldEmptyResponse
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.mappers.CommonInputMapper
import br.com.alice.common.mappers.CommonOutputMapper
import br.com.alice.common.toResponse
import br.com.alice.common.transfers.PaginationResponse
import br.com.alice.data.layer.models.StaffSignupRequest
import br.com.alice.data.layer.models.StaffSignupRequestAdditionalInfo
import br.com.alice.data.layer.models.StaffSignupRequestHealthProfessional
import br.com.alice.data.layer.models.StaffSignupRequestProvider
import br.com.alice.data.layer.models.StaffSignupRequestStatus
import br.com.alice.staff.client.ApproveRequestCommand
import br.com.alice.staff.client.RejectRequestCommand
import br.com.alice.staff.client.StaffSignupRequestFilter
import br.com.alice.staff.client.StaffSignupRequestService
import com.github.kittinunf.result.map
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class StaffSignupReviewController(
    private val staffSignupRequestService: StaffSignupRequestService
) : Controller() {

    suspend fun index(queryParams: Parameters): Response = coroutineScope {
        val range = CommonInputMapper.getPaginationParams(queryParams)
        val nameParam = CommonInputMapper.getFilterParams<String>(queryParams, "q")
        val statusParam = CommonInputMapper.getFilterParams<String>(queryParams, "status").let {
            it?.let { StaffSignupRequestStatus.valueOf(it) }
        }

        val filters = StaffSignupRequestFilter(
            namePrefix = nameParam,
            status = statusParam
        )

        val countDeferred = async {
            staffSignupRequestService.countBy(filters).get()
        }

        val items = staffSignupRequestService.findBy(filters, range)

        val total = countDeferred.await()
        val pagination = CommonOutputMapper.toPaginationResponse(queryParams, total)

        StaffSignupRequestListResponse(
            results = items.get().map { it.toSummaryDTO() },
            pagination = pagination
        ).toResponse()
    }

    suspend fun getById(id: UUID): Response =
        staffSignupRequestService.get(id).map {
            StaffSignupRequestDetailsResponse(
                healthProfessional = it.integrationContent.healthProfessional,
                provider = it.integrationContent.provider,
                additionalInfo = it.additionalInfo,
                rejectionReason = it.rejectionReason,
                status = StaffSignupRequestStatusResponse(
                    id = it.status,
                    name = it.status.description
                )
            )
        }.foldResponse()

    suspend fun rejectRequest(id: UUID, request: StaffSignupRequestRejectRequest): Response {
        val reviewerId = currentUserIdKey().toString().toUUID()

        logger.info(
            "StaffSignupReviewController::rejectRequest",
            "current_staff_id" to reviewerId,
            "reject_reason" to request.rejectionReason,
            "reject_request_id" to id
        )

        return staffSignupRequestService.reject(
            RejectRequestCommand(
                id = id,
                rejectionReason = request.rejectionReason,
                reviewerId = reviewerId
            )
        ).foldEmptyResponse(httpStatus = HttpStatusCode.OK)
    }

    suspend fun approveRequest(id: UUID, request: StaffSignupRequestApproveRequest): Response {
        val reviewerId = currentUserIdKey().toString().toUUID()

        logger.info(
            "StaffSignupReviewController::approveRequest",
            "current_staff_id" to reviewerId,
            "approve_request_id" to id,
            "tier" to request.tier,
            "theorist_tier" to request.theoristTier,
            "show_on_app" to request.showOnApp
        )

        return staffSignupRequestService.approve(
            ApproveRequestCommand(
            id = id,
            reviewerId = reviewerId,
            tier = request.tier,
            theoristTier = request.theoristTier,
            showOnApp = request.showOnApp,
            specialtyId = request.specialtyId.toUUID(),
            subSpecialtyIds = request.subSpecialtyIds.map { it.toUUID() },
            imageUrl = request.imageUrl
        )).foldEmptyResponse(httpStatus = HttpStatusCode.OK)
    }

    private fun StaffSignupRequest.toSummaryDTO() = StaffSignupRequestSummaryDTO(
        id = this.id,
        status = this.status.description,
        name = this.integrationContent.healthProfessional.let {
            "${it.firstName} ${it.lastName}"
        }
    )

    data class StaffSignupRequestSummaryDTO(
        val id: UUID,
        val status: String,
        val name: String
    )

    data class StaffSignupRequestListResponse(
        val results: List<StaffSignupRequestSummaryDTO>,
        val pagination: PaginationResponse
    )

    data class StaffSignupRequestRejectRequest(
        val rejectionReason: String? = null
    )

    data class StaffSignupRequestApproveRequest(
        val tier: String,
        val theoristTier: String,
        val showOnApp: Boolean,
        val specialtyId: String,
        val subSpecialtyIds: List<String>,
        val imageUrl: String? = null
    )

    data class StaffSignupRequestDetailsResponse(
        val healthProfessional: StaffSignupRequestHealthProfessional,
        val provider: StaffSignupRequestProvider,
        val additionalInfo: StaffSignupRequestAdditionalInfo? = null,
        val rejectionReason: String? = null,
        val status: StaffSignupRequestStatusResponse
    )

    data class StaffSignupRequestStatusResponse(
        val id: StaffSignupRequestStatus,
        val name: String
    )
}
