package br.com.alice.api.backoffice.controllers.providers

import br.com.alice.api.backoffice.mappers.providers.ProviderUnitOutputMapper
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.Status
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.provider.client.ProviderUnitFilter
import br.com.alice.provider.client.ProviderUnitService
import io.ktor.http.Parameters
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class ProviderUnitController(
    private val providerUnitService: ProviderUnitService,
) : Controller() {

    suspend fun index(queryParams: Parameters): Response = coroutineScope {
        val range = parseRange(queryParams)
        val filterQuery = parseQuery(queryParams)
        val providersType =
            parseFilter<String>(queryParams, "type")?.split(",")?.map { ProviderUnit.Type.valueOf(it) } ?: emptyList()
        val status = parseStatus(queryParams)?.split(",")?.map { Status.valueOf(it) } ?: emptyList()
        val ids = parseIds(queryParams)?.map { it.toUUID() } ?: emptyList()

        val response =
            async { getByFilter(query = filterQuery, type = providersType, status = status, ids = ids, range = range) }
        val totalCount =
            async { countByFilter(query = filterQuery, type = providersType, status = status, ids = ids) }

        ProviderUnitOutputMapper.toPaginatedResponse(
            response.await(),
            totalCount.await(),
            queryParams
        ).toResponse()
    }

    private suspend fun getByFilter(
        query: String?,
        type: List<ProviderUnit.Type>,
        status: List<Status>,
        ids: List<UUID>,
        range: IntRange
    ): List<ProviderUnit> =
        providerUnitService
            .getByFilterWithRange(filter = ProviderUnitFilter(query, status, type, ids), range)
            .get()

    private suspend fun countByFilter(
        query: String?,
        type: List<ProviderUnit.Type>,
        status: List<Status>,
        ids: List<UUID>,
    ): Int = providerUnitService.countByFilter(filter = ProviderUnitFilter(query, status, type, ids)).get()
}
