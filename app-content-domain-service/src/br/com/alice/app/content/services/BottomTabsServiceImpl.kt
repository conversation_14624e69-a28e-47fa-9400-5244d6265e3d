package br.com.alice.app.content.services

import br.com.alice.app.content.client.AppContentABTestService
import br.com.alice.app.content.client.BottomTabsService
import br.com.alice.app.content.constants.ALICE_NOW_BOTTOM_TAB
import br.com.alice.app.content.constants.DUQUESA_HOME_BOTTOM_TAB
import br.com.alice.app.content.constants.DUQUESA_MAIN_MENU_BOTTOM_TAB
import br.com.alice.app.content.constants.DUQUESA_SERVICE_BOTTOM_TAB
import br.com.alice.app.content.constants.EXPLORER_BOTTOM_TAB
import br.com.alice.app.content.constants.MAIN_MENU_BOTTOM_TAB
import br.com.alice.app.content.constants.REDESIGN_ALICE_AGORA_BOTTOM_TAB
import br.com.alice.app.content.constants.REDESIGN_HEALTH_TAB
import br.com.alice.app.content.constants.UNIFIED_HEALTH_TAB
import br.com.alice.common.extensions.foldBoolean
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.Person
import br.com.alice.featureconfig.core.FeatureService
import com.github.kittinunf.result.success

class BottomTabsServiceImpl(
    private val appContentABTestService: AppContentABTestService,
): BottomTabsService, Spannable {

    private val unifiedTabs = listOf(
        UNIFIED_HEALTH_TAB,
        ALICE_NOW_BOTTOM_TAB,
        MAIN_MENU_BOTTOM_TAB
    )

    private val redesignWhitRedesignAATabs = listOf(
        REDESIGN_HEALTH_TAB,
        REDESIGN_ALICE_AGORA_BOTTOM_TAB,
        EXPLORER_BOTTOM_TAB,
    )

    private val duquesaTabs = listOf(
        DUQUESA_HOME_BOTTOM_TAB,
        DUQUESA_SERVICE_BOTTOM_TAB,
        DUQUESA_MAIN_MENU_BOTTOM_TAB,
    )

    override suspend fun get(person: Person, member: Member?, appVersion: SemanticVersion?) =
        span("getBottomTabs") { span ->
            span.setAttribute("person_id", person.id)
            span.setAttribute("member_brand", member?.brand)

            if (member?.isPending == true && member.isB2BOrAdesao) {
                return@span listOf(UNIFIED_HEALTH_TAB).success()
            }

            if (member?.isDuquesa == true && !isEnableDuquesaMember(person.nationalId)) return@span duquesaTabs.success()

            appContentABTestService.isRedesignEnabledForUser(person.id, appVersion)
                .foldBoolean(
                    { redesignWhitRedesignAATabs.success() },
                    { unifiedTabs.success() }
                )
        }

    private fun isEnableDuquesaMember(nationalId: String?): Boolean =
        isEnableAllDuquesaMember() || isAllowListDuquesaMember(nationalId)

    private fun isAllowListDuquesaMember(nationalId: String?) =
        nationalId?.let {
            FeatureService.inList(
                namespace = FeatureNamespace.ALICE_APP,
                key = "enable_duquesa_member",
                testValue = nationalId
            )
        } ?: false

    private fun isEnableAllDuquesaMember(): Boolean =
        FeatureService.get(
            namespace = FeatureNamespace.ALICE_APP,
            key = "enable_all_access_app_to_duquesa_member",
            defaultValue = false
        )
}

