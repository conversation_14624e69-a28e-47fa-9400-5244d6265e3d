package br.com.alice.healthlogic.logics.handlers

import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.Condition
import br.com.alice.healthlogic.metrics.Metrics.incrementHealthLogicHandler
import br.com.alice.healthlogic.metrics.Result.SUCCESS

class EqualityHandler(
    private val nextHandler: ConditionHandler? = null
) : ConditionHandler {

    override fun validate(condition: Condition, value: Any): Boolean? =
        if (condition.isEquality()) {
            //received value is list and configured value is unique
            if (
                value is List<*>
                && value.size == 1
                && condition.value !is List<*>
            ) {
                value.first().toString() == condition.value.toString()
            }
            // received value is list and configured value is list
            else if (
                value is List<*>
                && value.size == 1
                && condition.value is List<*>
                && (condition.value as List<*>).size == 1
            ) {

                value.first().toString() == (condition.value as List<*>).first().toString()
            }
            // received value is not list and configured value is list
            else if (
                value !is List<*>
                && condition.value is List<*>
                && (condition.value as List<*>).size == 1
            ) {

                value.toString() == (condition.value as List<*>).first().toString()
            }
            // received value is not list and configured value is not list
            else {

                condition.value.toString() == value.toString()
            }.also { incrementHealthLogicHandler(SUCCESS, condition, it) }
        } else nextHandler?.validate(condition, value)

}
