package br.com.alice.healthlogic.services.bud

import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChatMessageRequest
import br.com.alice.channel.models.CreateChatRequest
import br.com.alice.channel.models.MessageType.TEXT
import br.com.alice.channel.models.acuteImmediateTag
import br.com.alice.channel.models.budTriageTag
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelCategory.ASSISTANCE
import br.com.alice.data.layer.models.ChannelCreationParameters
import br.com.alice.data.layer.models.ChannelKind.CHAT
import br.com.alice.data.layer.models.ChannelScreeningNavigation
import br.com.alice.data.layer.models.ChannelScreeningNavigationStatus
import br.com.alice.data.layer.models.ChannelStatus
import br.com.alice.data.layer.models.ChannelSubCategory.SCREENING
import br.com.alice.data.layer.models.ScreeningNavigation
import br.com.alice.data.layer.models.ScreeningNavigationStatus.CLOSED
import br.com.alice.data.layer.models.ScreeningNavigationStatus.DROPPED
import br.com.alice.data.layer.models.ScreeningNavigationStatus.FINISHED
import com.github.kittinunf.result.Result
import io.opentelemetry.api.trace.Span

class ChatService(
    private val channelService: ChannelService
): Spannable {

    suspend fun createChat(
        screeningNavigation: ScreeningNavigation,
        channelCreationParameters: ChannelCreationParameters?,
        userAgent: String?
    ): Result<ChannelDocument, Throwable> = span("createChat") { span ->
        val chatParamsWithValidations = getChatParams(
            channelCreationParameters = channelCreationParameters
        )
        val createChatRequest = CreateChatRequest(
            personId = screeningNavigation.personId,
            allowOnCallFlow = true,
            origin = "newChannel",
            message = chatParamsWithValidations.content?.let {
                ChatMessageRequest(
                    content = chatParamsWithValidations.content,
                    type = TEXT,
                    appVersion = userAgent,
                )
            },
            category = chatParamsWithValidations.category,
            subCategory = chatParamsWithValidations.subCategory,
            tags = chatParamsWithValidations.tags,
            screeningNavigation = ChannelScreeningNavigation(
                screeningNavigation.id,
                hasProtocol = false,
                status = getChannelStatus(screeningNavigation)
            ),
            appVersion = userAgent
        )

        span.setCreateChatRequest(createChatRequest)

        channelService.addChatV2(createChatRequest)
            .recordResult(span)
    }

    suspend fun getChatByScreeningNavigationId(
        screeningNavigation: ScreeningNavigation
    ) =
        channelService.getByScreeningNavigationId(
            personId = screeningNavigation.personId,
            screeningNavigationId = screeningNavigation.id,
            statuses = listOf(ChannelStatus.ACTIVE)
        )

    suspend fun sendTextMessage(
        chat: ChannelDocument,
        content: String,
        sendFromAlice: Boolean
    ) =
        channelService.sendTextMessage(chat, content, sendFromAlice)

    private fun getChatParams(
        channelCreationParameters: ChannelCreationParameters?,
    ) = ChannelCreationParameters(
            content = getChatContent(channelCreationParameters),
            kind = channelCreationParameters?.kind ?: CHAT,
            category = channelCreationParameters?.category ?: ASSISTANCE,
            subCategory = takeIf { channelCreationParameters?.category != ChannelCategory.ADMINISTRATIVE }?.let {
                channelCreationParameters?.subCategory ?: SCREENING
            },
            tags = channelCreationParameters?.tags.takeIf { it.isNotNullOrEmpty() }
                ?: listOf(acuteImmediateTag, budTriageTag)
        )

    private fun getChatContent(
        channelCreationParameters: ChannelCreationParameters?
    ) =
        if (channelCreationParameters?.category != ChannelCategory.ADMINISTRATIVE && channelCreationParameters?.content == null) {
            "Preciso de ajuda nesse momento"
        } else channelCreationParameters.content


    private fun getChannelStatus(
        screeningNavigation: ScreeningNavigation
    ) =
        when (screeningNavigation.status) {
            FINISHED -> ChannelScreeningNavigationStatus.COMPLETED
            DROPPED -> ChannelScreeningNavigationStatus.DROPPED
            CLOSED -> ChannelScreeningNavigationStatus.CLOSED
            else -> ChannelScreeningNavigationStatus.INCOMPLETED
        }

    private fun Span.setCreateChatRequest(createChatRequest: CreateChatRequest) {
        this.setAttribute("category", createChatRequest.category.toString())
        this.setAttribute("sub_category", createChatRequest.subCategory.toString())
        this.setAttribute("sub_category_classifier", createChatRequest.subCategoryClassifier.toString())
        this.setAttribute("tags", createChatRequest.tags.toString())
        this.setAttribute("screening_navigation_id", createChatRequest.screeningNavigation?.id.toString())
        this.setAttribute("segment", createChatRequest.channelSegment.toString())
        this.setAttribute("app_version", createChatRequest.appVersion.toString())
    }
}
