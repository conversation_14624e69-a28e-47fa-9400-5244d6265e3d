package br.com.alice.healthlogic.logics.handlers

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.Condition
import br.com.alice.data.layer.models.ServiceScriptOperator.CONTAINS
import br.com.alice.data.layer.models.ServiceScriptOperator.EQUALITY
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class EqualityHandlerTest {

    private val nextHandler = mockk<ConditionHandler>()

    private val condition = Condition(
        key = "ciaps",
        operator = EQUALITY,
        value = "N01"
    )
    private val value = "N01"

    @Test
    fun `#isValid returns null if does not have next operator and does not match`() {
        val condition = condition.copy(operator = CONTAINS)

        val result = EqualityHandler(null).validate(condition, value)

        assertThat(result).isNull()
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid calls next handler if condition is not equality`() {
        val condition = condition.copy(operator = CONTAINS)
        every { nextHandler.validate(any(), any()) } returns mockk()

        EqualityHandler(nextHandler).validate(condition, value)

        verify { nextHandler.validate(condition, value) }
    }

    @Test
    fun `#isValid returns true if simple content is equal`() {
        val result = EqualityHandler(nextHandler).validate(condition, value)

        assertThat(result).isTrue
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns true if UUID content is equal`() {
        val uuid = RangeUUID.generate()
        val condition = Condition(
            key = "health_logic_id",
            operator = EQUALITY,
            value = uuid
        )

        val result = EqualityHandler(nextHandler).validate(condition, uuid)

        assertThat(result).isTrue
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns false if list content is not equal`() {
        val condition = condition.copy(value = "A09")

        val result = EqualityHandler(nextHandler).validate(condition, value)

        assertThat(result).isFalse
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns true if list condition is equal if list`() {
        val value = listOf("N01")

        val result = EqualityHandler(nextHandler).validate(condition, value)

        assertThat(result).isTrue
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns true if UUID content is equal to list`() {
        val uuid = listOf(RangeUUID.generate())
        val condition = Condition(
            key = "health_logic_id",
            operator = EQUALITY,
            value = uuid
        )

        val result = EqualityHandler(nextHandler).validate(condition, uuid)

        assertThat(result).isTrue
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns true if UUID content is equal to UUID string`() {
        val uuid = RangeUUID.generate()
        val value = uuid
        val condition = Condition(
            key = "health_logic_id",
            operator = EQUALITY,
            value = uuid.toString()
        )

        val result = EqualityHandler(nextHandler).validate(condition, value)

        assertThat(result).isTrue
        verify { nextHandler wasNot called }
    }


    @Test
    fun `#isValid returns false if unique list element is not the same`() {
        val value = listOf("A09")

        val result = EqualityHandler(nextHandler).validate(condition, value)

        assertThat(result).isFalse
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns false if list has more than one element`() {
        val value = listOf("N01", "A09")

        val result = EqualityHandler(nextHandler).validate(condition, value)

        assertThat(result).isFalse
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns true if lists are equal`() {
        val value = listOf("N01", "A09")
        val condition = condition.copy(value = listOf("N01", "A09"))

        val result = EqualityHandler(nextHandler).validate(condition, value)

        assertThat(result).isTrue
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns true if receives unique and has list configured`() {
        val value = "N01"
        val condition = condition.copy(value = listOf("N01"))

        val result = EqualityHandler(nextHandler).validate(condition, value)

        assertThat(result).isTrue
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns true if receives unique and has list configured as uuid`() {
        val value = RangeUUID.generate()
        val condition = condition.copy(value = listOf(value))

        val result = EqualityHandler(nextHandler).validate(condition, value)

        assertThat(result).isTrue
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid returns true if receives list and has list configured`() {
        val value = listOf("N01")
        val condition = condition.copy(value = listOf("N01"))

        val result = EqualityHandler(nextHandler).validate(condition, value)

        assertThat(result).isTrue
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid - EqualityListHandler returns true if receives list with multiple and has list with multiple`() {
        val value1 = RangeUUID.generate()
        val value2 = RangeUUID.generate()
        val condition = condition.copy(value = listOf(value1, value2))

        val result = EqualityListHandler(nextHandler).validate(condition, listOf(value1, value2))

        assertThat(result).isTrue
        verify { nextHandler wasNot called }
    }

    @Test
    fun `#isValid - EqualityListHandler returns false if receives diff list`() {
        val value1 = RangeUUID.generate()
        val value2 = RangeUUID.generate()
        val value3 = RangeUUID.generate()
        val condition = condition.copy(value = listOf(value1, value2))

        val result = EqualityListHandler(nextHandler).validate(condition, listOf(value1, value3))

        assertThat(result).isFalse
    }

}
