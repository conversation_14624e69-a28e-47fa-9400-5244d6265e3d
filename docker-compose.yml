version: "3"

x-env-file:
  &env_file
  - .env

services:
  db_test:
    image: "postgres:11.5"
    container_name: "postgres_data_test"
    command: postgres -c 'max_connections=600'
    environment:
      - PGUSER=alice
      - PGPASSWORD=alice
    ports:
      - "5432:5432"
    volumes:
      - postgres-data-test:/var/lib/postgresql/data:delegated
    extra_hosts:
      - "host.docker.internal:host-gateway"

  db_development:
    image: "postgres:11.5"
    container_name: "postgres_data_development"
    command: postgres -c 'max_connections=600' -p 5433
    environment:
      - PGUSER=alice
      - PGPASSWORD=alice
    ports:
      - "5433:5433"
    volumes:
      - postgres-data-development:/var/lib/postgresql/data:delegated
    extra_hosts:
      - "host.docker.internal:host-gateway"

  db_client:
    image: "postgres:11.5"
    command: echo db_client
    depends_on:
      - db_development
      - db_test
    extra_hosts:
      - "host.docker.internal:host-gateway"

  redis:
    image: "redis:6.0.5"
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    extra_hosts:
      - "host.docker.internal:host-gateway"

  example-api:
    image: "example-api:0.0.1"
    ports:
      - "8001:5000"
    environment:
      - SYSTEM_ENV=development
      - ENVIRONMENT=staging
      - SERVICE=example-api
      - PORT=5000
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  flyway_main:
    image: flyway/flyway
    command: -url=****************************************** -user=alice -password=alice -outOfOrder=true -ignoreMigrationPatterns='*:missing' -locations=filesystem:"/migration/main" -validateMigrationNaming=true -postgresql.transactional.lock=false migrate
    volumes:
      - ./data-layer-core/resources/db/migration:/migration
    depends_on:
      - db_development
    extra_hosts:
      - "host.docker.internal:host-gateway"

  flyway_token:
    image: flyway/flyway
    command: -url=******************************************* -user=alice -password=alice -outOfOrder='true' -ignoreMigrationPatterns='*:missing' -locations=filesystem:"/migration/token/" -validateMigrationNaming=true -postgresql.transactional.lock=false migrate
    volumes:
      - ./data-layer-core/resources/db/migration:/migration
    depends_on:
      - db_development
    extra_hosts:
      - "host.docker.internal:host-gateway"

  data-layer:
    image: "data-layer:0.0.1"
    environment:
      - SYSTEM_ENV=development
      - DATABASE_CONNECTION_TIMEOUT_MS=3000
    env_file: *env_file
    ports:
      - "8070:8080"
    extra_hosts:
      - "host.docker.internal:host-gateway"

  limbo-api:
    image: "limbo-api:0.0.1"
    ports:
      - "8051:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  ehr-api:
    image: "ehr-api:0.0.1"
    ports:
      - "8090:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  einstein-bff-api:
    image: "einstein-bff-api:0.0.1"
    ports:
      - "8098:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  test-result-domain-service:
    image: "test-result-domain-service:0.0.1"
    ports:
      - "8066:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  wanda-bff-api:
    image: "wanda-bff-api:0.0.1"
    ports:
      - "8097:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  wanda-domain-service:
    image: "wanda-domain-service:0.0.1"
    ports:
      - "8099:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  schedule-domain-service:
    image: "schedule-domain-service:0.0.1"
    ports:
      - "8100:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  ehr-domain-service:
    image: "ehr-domain-service:0.0.1"
    ports:
      - "8091:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  member-api:
    image: "member-api:0.0.1"
    ports:
      - "8080:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  channel-domain-service:
    image: "channel-domain-service:0.0.1"
    ports:
      - "8061:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  fleury-integration-service:
    image: "fleury-integration-service:0.0.1"
    ports:
      - "8040:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  haoc-integration-service:
    image: "haoc-integration-service:0.0.1"
    ports:
      - "8041:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  einstein-integration-service:
    image: "einstein-integration-service:0.0.1"
    ports:
      - "8042:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  healthcare-ops-api:
    image: "healthcare-ops-api:0.0.1"
    ports:
      - "8050:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  money-in-domain-service:
    image: "money-in-domain-service:0.0.1"
    ports:
      - "8060:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  system-ops-bff-api:
    image: "system-ops-bff-api:0.0.1"
    ports:
      - "8094:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  feature-config-domain-service:
    image: "feature-config-domain-service:0.0.1"
    ports:
      - "8093:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  file-vault-service:
    image: "file-vault-service:0.0.1"
    ports:
      - "8095:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  db-integration-service:
    image: "db-integration-service:0.0.1"
    ports:
      - "8096:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  membership-domain-service:
    image: "membership-domain-service:0.0.1"
    ports:
      - "8062:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  exec-indicator-domain-service:
    image: "exec-indicator-domain-service:0.0.1"
    ports:
      - "8063:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  amas-bff-api:
    image: "amas-bff-api:0.0.1"
    ports:
      - "8088:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  exec-indicator-api:
    image: "exec-indicator-api:0.0.1"
    ports:
      - "8075:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  member-wannabe-api:
    image: "member-wannabe-api:0.0.1"
    ports:
      - "8089:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file

  health-logic-domain-service:
    image: "health-logic-domain-service:0.0.1"
    ports:
      - "8077:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  health-condition-domain-service:
    image: "health-condition-domain-service:0.0.1"
    ports:
      - "8087:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  questionnaire-domain-service:
    image: "questionnaire-domain-service:0.0.1"
    ports:
      - "8078:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  bottini-domain-service:
    image: "bottini-domain-service:0.0.1"
    ports:
      - "8079:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  scheduler-bff-api:
    image: "scheduler-bff-api:0.0.1"
    ports:
      - "8110:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  channel-bff-api:
    image: "channel-bff-api:0.0.1"
    ports:
      - "8111:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  provider-domain-service:
    image: "provider-domain-service:0.0.1"
    ports:
      - "8112:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  fhir-domain-service:
    image: "fhir-domain-service:0.0.1"
    ports:
      - "8113:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  fhir-bff-api:
    image: "fhir-bff-api:0.0.1"
    ports:
      - "8114:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  hippocrates-domain-service:
    image: "hippocrates-domain-service:0.0.1"
    ports:
      - "8115:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  amas-domain-service:
    image: "amas-domain-service:0.0.1"
    ports:
      - "8116:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  eventinder-domain-service:
    image: "eventinder-domain-service:0.0.1"
    ports:
      - "8116:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  business-domain-service:
    image: "business-domain-service:0.0.1"
    ports:
      - "8116:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  business-risk-domain-service:
    image: "business-risk-domain-service:0.0.1"
    ports:
      - "8068:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  atlas-domain-service:
    image: "atlas-domain-service:0.0.1"
    ports:
      - "8116:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  product-domain-service:
    image: "product-domain-service:0.0.1"
    ports:
      - "8118:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  onboarding-domain-service:
    image: "onboarding-domain-service:0.0.1"
    ports:
      - "8119:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  sherlock-domain-service:
    image: "sherlock-domain-service:0.0.1"
    ports:
      - "8120:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  sherlock-api:
    image: "sherlock-api:0.0.1"
    ports:
      - "8121:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  business-platform-bff:
    image: "business-platform-bff:0.0.1"
    ports:
      - "8122:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  staff-domain-service:
    image: "staff-domain-service:0.0.1"
    ports:
      - "8092:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  person-domain-service:
    image: "person-domain-service:0.0.1"
    ports:
      - "8021:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  marauders-map-domain-service:
    image: "marauders-map-domain-service:0.0.1"
    ports:
      - "8101:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  event-api:
    image: "event-api:0.0.1"
    ports:
      - "8999:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  coverage-domain-service:
    image: "coverage-domain-service:0.0.1"
    ports:
      - "8024:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  sorting-hat-domain-service:
    image: "sorting-hat-domain-service:0.0.1"
    ports:
      - "8888:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  action-plan-domain-service:
    image: "action-plan-domain-service:0.0.1"
    ports:
      - "8777:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  app-content-domain-service:
    image: "app-content-domain-service:0.0.1"
    ports:
      - "8124:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  dragon-radar-domain-service:
    image: "dragon-radar-domain-service:0.0.1"
    ports:
      - "8097:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  dragon-radar-bff-api:
    image: "dragon-radar-bff-api:0.0.1"
    ports:
      - "8197:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  health-logics-api:
    image: "health-logics-api:0.0.1"
    ports:
      - "8198:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  appointment-radar-domain-service:
    image: "appointment-domain-service:0.0.1"
    ports:
      - "8922:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  health-plan-domain-service:
    image: "health-plan-domain-service:0.0.1"
    ports:
      - "8820:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  clinical-account-domain-service:
    image: "clinical-account-domain-service:0.0.1"
    ports:
      - "8822:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  member-onboarding-domain-service:
    image: "member-onboarding-domain-service:0.0.1"
    ports:
      - "8823:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  nullvs-integration-service:
    image: "nullvs-integration-service:0.0.1"
    ports:
      - "8824:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  itau-integration-service:
    image: "itau-integration-service:0.0.1"
    ports:
      - "8834:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  eita-external-api:
    image: "eita-external-api:0.0.1"
    ports:
      - "8825:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  duquesa-domain-service:
    image: "duquesa-domain-service:0.0.1"
    ports:
      - "8297:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  tiss-domain-service:
    image: "tiss-domain-service:0.0.1"
    ports:
      - "8826:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  akinator-domain-service:
    image: "akinator-domain-service:0.0.1"
    ports:
      - "8827:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  screening-domain-service:
    image: "screening-domain-service:0.0.1"
    ports:
      - "8102:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  refund-domain-service:
    image: "refund-domain-service:0.0.1"
    ports:
      - "8828:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  sales-channel-domain-service:
    image: "sales-channel-domain-service:0.0.1"
    ports:
      - "8829:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  sales-channel-api:
    image: "sales-channel-api:0.0.1"
    ports:
      - "8830:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"


  zendesk-integration-service:
    image: "zendesk-integration-service:0.0.1"
    ports:
      - "8831:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  
  backoffice-bff-api:
    image: "backoffice-bff-api:0.0.1"
    ports:
      - "8094:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  bud-domain-service:
    image: "bud-domain-service:0.0.1"
    ports:
      - "8064:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  acquisition-domain-service:
    image: "acquisition-domain-service:0.0.1"
    ports:
      - "8916:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  hr-core-domain-service:
    image: "hr-core-domain-service:0.0.1"
    ports:
      - "8917:8080"
    environment:
      - SYSTEM_ENV=development
    env_file: *env_file
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.3
    networks:
      - broker-kafka
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  kafka:
    image: confluentinc/cp-kafka:5.2.2
    networks:
      - broker-kafka
    depends_on:
      - zookeeper
    ports:
      - 9092:9092
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: "zookeeper:2181"
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://host.docker.internal:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_LOG4J_LOGGERS: "kafka.controller=INFO,kafka.producer.async.DefaultEventHandler=INFO,state.change.logger=INFO"

  kafdrop:
    image: obsidiandynamics/kafdrop:4.0.1

    networks:
      - broker-kafka
    depends_on:
      - kafka
    ports:
      - 19000:9000
    environment:
      KAFKA_BROKERCONNECT: kafka:29092


  opa_auth:
    image: "openpolicyagent/opa:1.4.0"
    command: "run --server --log-level debug --watch /policies"
    volumes:
      - ./data-layer-core/resources/policies:/policies
    ports:
      - "8181:8181"
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  broker-kafka:
    driver: bridge

volumes:
  postgres-data-development:
  postgres-data-test:
  redis_data:
