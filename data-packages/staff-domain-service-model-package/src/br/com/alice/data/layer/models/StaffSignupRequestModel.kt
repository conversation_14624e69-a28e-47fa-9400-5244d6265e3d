package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.extensions.catchResult
import br.com.alice.common.serialization.JsonSerializable
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

data class StaffSignupRequestModel(
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val status: StaffSignupRequestStatusModel,
    val rejectionReason: String? = null,
    val reviewedBy: UUID? = null,
    val staffId: UUID? = null,
    val integrationContent: StaffSignupRequestIntegrationContentModel,
    val type: StaffSignupRequestTypeModel,
    val additionalInfo: StaffSignupRequestAdditionalInfoModel? = null,
    val searchTokens: String? = null,
) : Model {
    suspend fun validate() = catchResult<StaffSignupRequestModel, Throwable> {
        if (status == StaffSignupRequestStatusModel.REJECTED && rejectionReason.isNullOrBlank()) {
            throw InvalidArgumentException("O motivo da rejeição deve ser informado quando o status for REJEITADO.")
        }

        return@catchResult this.success()
    }
}

data class StaffSignupRequestIntegrationContentModel(
    val healthProfessional: StaffSignupRequestHealthProfessionalModel,
    val provider: StaffSignupRequestProviderModel
) : JsonSerializable

data class StaffSignupRequestHealthProfessionalModel(
    val firstName: String,
    val lastName: String,
    val email: String,
    val nationalId: String?,
    val birthdate: String?,
    val gender: String,
    val profileImageUrl: String?,
    val profileBio: String?,
    val education: String?,
    val curiosity: String?,
    val councilType: String,
    val councilNumber: String,
    val councilState: String,
    val specialty: String?,
    val subSpecialties: List<String> = emptyList(),
    val contacts: List<StaffSignupRequestContactModel>
) : JsonSerializable

data class StaffSignupRequestProviderModel(
    val name: String,
    val cnpj: String,
    val cnes: String?,
    val bankCode: String?,
    val agencyNumber: String?,
    val accountNumber: String?,
    val phones: List<StaffSignupRequestPhoneModel> = emptyList(),
    val address: StaffSignupRequestAddressModel
) : JsonSerializable

data class StaffSignupRequestContactModel(
    val address: StaffSignupRequestAddressModel,
    val phones: List<StaffSignupRequestPhoneModel>,
    val modality: String
) : JsonSerializable

data class StaffSignupRequestPhoneModel(
    val type: String,
    val number: String
) : JsonSerializable

data class StaffSignupRequestAddressModel(
    val street: String,
    val number: String,
    val complement: String?,
    val neighborhood: String,
    val state: String,
    val city: String,
    val zipcode: String,
    val country: String?
) : JsonSerializable

data class StaffSignupRequestAdditionalInfoModel(
    val tier: String,
    val theoristTier: String,
    val showOnApp: Boolean,
    val specialtyId: UUID,
    val subSpecialtyIds: List<UUID>,
    val imageUrl: String? = null
) : JsonSerializable

enum class StaffSignupRequestTypeModel(val description: String) {
    HEALTH_PROFESSIONAL("Profissional de Saúde")
}

enum class StaffSignupRequestStatusModel(val description: String) {
    PENDING("Pendente"),
    APPROVED("Aprovado"),
    REJECTED("Rejeitado")
}
