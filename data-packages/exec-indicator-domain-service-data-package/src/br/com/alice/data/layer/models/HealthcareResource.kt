package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import java.time.LocalDateTime
import java.util.UUID

data class HealthcareResource(
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    val nrol: Boolean,
    val dut: Boolean,
    val pac: Boolean,
    val code: String,
    val description: String,
    var searchTokens: String? = null,
    val type: HealthcareResourceType,
    val category: HealthcareResourceCategory,
    val gracePeriodType: ResourceGracePeriodType? = null,
    val tussCode: String? = null,
    val tableType: String? = null,
    var isOriginalTuss: Boolean? = null,
    val tussTableType: String? = null,
    val compositionHash: String? = null,
    val active: Boolean = true,
)

enum class HealthcareResourceType(val description: String) {
    EXAM("Exame"),
    FEE("Taxas"),
    MATERIAL("Materiais"),
    DAILY_STAY("Diária"),
    MEDICINE("Medicamento"),
    THERAPY("Terapia"),
    COMPLICATIONS("Intercorrências"),
    MEDICAL_FEE("Honorários Médicos"),
    OPME("OPME"),
    BUNDLE("Pacote"),
    PROCEDURE("Procedimento"),
    MEDICAL_GASES("Gases Medicinais"),
    RENT("Alugueis"),
    OTHER("Outros"),
    UNKNOWN("Desconhecido"),
    APPOINTMENT("Consulta"),
    CLINICAL_PROCEDURE("Procedimento Clínico"),
    SURGICAL_PROCEDURE("Procedimento Cirúrgico"),
}

enum class HealthcareResourceCategory {
    HEALTH_INSTITUTION,
    HEALTH_SPECIALIST
}

enum class ResourceGracePeriodType {
    ELECTIVE_SURGERY,
    THERAPY,
    PAC,
    BIRTH,
    EMERGENCY,
    SPECIAL_EXAMS,
    HOSPITALIZATION,
    HOSPITALIZATION_DUE_TO_PERSONAL_ACCIDENT,
    OTHERS
}
