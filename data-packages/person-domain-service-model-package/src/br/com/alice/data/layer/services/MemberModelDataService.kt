package br.com.alice.data.layer.services

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.client.UpdaterList
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.not
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.basePredicateForFilters
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.data.layer.models.MemberModel
import br.com.alice.data.layer.models.MemberStatus
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface MemberModelDataService : Service,
    Finder<MemberModelDataService.FieldOptions, MemberModelDataService.OrderingOptions, MemberModel>,
    Counter<MemberModelDataService.FieldOptions, MemberModelDataService.OrderingOptions, MemberModel>,
    Updater<MemberModel>,
    UpdaterList<MemberModel>,
    Getter<MemberModel>,
    Adder<MemberModel> {

    override val namespace: String
        get() = "member"
    override val serviceName: String
        get() = "member"

    class IdField : Field.UUIDField(MemberModel::id) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ArchivedField : Field.BooleanField(MemberModel::archived)

    class PersonIdField : Field.TableIdField(MemberModel::personId) {
        fun inList(value: List<PersonId>) = Predicate.inList(this, value)
    }

    class ParentMemberField : Field.UUIDField(MemberModel::parentMember) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<PersonId>) = Predicate.inList(this, value)
    }

    class StatusField : Field.TextField(MemberModel::status) {
        fun eq(value: MemberStatus) = Predicate.eq(this, value)
        fun inList(value: List<MemberStatus>) = Predicate.inList(this, value)
    }

    class BrandField : Field.TextField(MemberModel::brand) {
        fun eq(value: Brand) = Predicate.eq(this, value)
    }

    class ActivationDate : Field.DateTimeField(MemberModel::activationDate) {
        fun isNull() = Predicate.isNull(this)
        fun isNotNull() = not(Predicate.isNull(this))
        fun greaterEq(date: LocalDateTime) = Predicate.greaterEq(this, date)
        fun lessEq(date: LocalDateTime) = Predicate.lessEq(this, date)
    }
    class ProductIdField : Field.JsonbField(MemberModel::selectedProduct) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: UUID) = Predicate.jsonSearch(this, "{\"id\":\"$value\"}")
    }

    class CreatedAt : Field.DateTimeField(MemberModel::createdAt) {
        fun greaterEq(date: LocalDateTime) = Predicate.greaterEq(this, date)
        fun lessEq(date: LocalDateTime) = Predicate.lessEq(this, date)
    }

    class CanceledAt : Field.DateTimeField(MemberModel::canceledAt) {
        fun isNull() = Predicate.isNull(this)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class BeneficiaryIdField : Field.UUIDField(MemberModel::beneficiaryId) {
        fun isNull() = Predicate.isNull(this)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class ParentBeneficiaryField : Field.JsonbField(MemberModel::beneficiary) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: UUID) = Predicate.jsonSearch(this, "{\"parent_beneficiary\":\"${value}\"}")
    }

    class BeneficiaryTypeField : Field.JsonbField(MemberModel::beneficiary) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: BeneficiaryType) = Predicate.jsonSearch(this, "{\"type\":\"${value}\"}")
    }

    class CompanyIdField : Field.UUIDField(MemberModel::companyId) {
        fun isNull() = Predicate.isNull(this)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class CompanySubContractIdField : Field.UUIDField(MemberModel::companySubContractId) {
        fun isNull() = Predicate.isNull(this)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class ParentPersonField : Field.TableIdField(MemberModel::parentPerson)

    class FieldOptions {
        val id = IdField()
        val personId = PersonIdField()
        val archived = ArchivedField()
        val status = StatusField()
        val activationDate = ActivationDate()
        val brand = BrandField()
        val productId = ProductIdField()
        val createdAt = CreatedAt()
        val canceledAt = CanceledAt()
        val parentMember = ParentMemberField()
        val beneficiaryId = BeneficiaryIdField()
        val companyId = CompanyIdField()
        val companySubContractId = CompanySubContractIdField()
        val parentBeneficiary = ParentBeneficiaryField()
        val parentPersonId = ParentPersonField()
        val beneficiaryType = BeneficiaryTypeField()
    }

    class OrderingOptions {
        val id = IdField()
        val activationDate = ActivationDate()
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    @Deprecated("Use findByFilters instead")
    suspend fun findByFiltersWithoutPagination(filter: Filter, range: IntRange?) = find {
        range?.let { addSortAndPagination(buildWhereByFilters(filter), range) }
            ?: buildWhereByFilters(filter)
    }

    @OptIn(WithFilterPredicateUsage::class)
    suspend fun findByFilters(filter: Filter, range: IntRange) = find {
        QueryBuilder(FieldOptions(), OrderingOptions()).where {
            this.archived.eq(false)
                .withFilter(filter.ids) { this.id.inList(it) }
                .withFilter(filter.personIds) { this.personId.inList(it) }
                .withFilter(filter.statuses) { this.status.inList(it) }
                .withFilter(filter.productId) { this.productId.eq(it) }
                .withFilter(filter.parentBeneficiaryId) { this.parentBeneficiary.eq(it) }
                .withFilter(filter.beneficiaryIds) { this.beneficiaryId.inList(it) }
                .withFilter(filter.companyIds) { this.companyId.inList(it) }
                .withFilter(filter.companySubContractIds) { this.companySubContractId.inList(it) }
                .withFilter(filter.cancellationRange) {
                    this.canceledAt.greaterEq(it.start) and this.canceledAt.lessEq(it.endInclusive)
                }
                .withFilter(filter.parentPersonId) { this.parentPersonId.eq(it) }
                .withFilter(filter.parentMemberId) { this.parentMember.eq(it) }!!
        }.run { addSortAndPagination(this, range) }
    }

    suspend fun countByFilters(filter: Filter) = count {
        buildWhereByFilters(filter)
    }

    @OptIn(WithFilterPredicateUsage::class)
    private fun buildWhereByFilters(filter: Filter) =
        QueryBuilder(FieldOptions(), OrderingOptions()).where {
            basePredicateForFilters()
                .withFilter(filter.ids) { this.id.inList(it) }
                .withFilter(filter.personIds) { this.personId.inList(it) }
                .withFilter(filter.statuses) { this.status.inList(it) }
                .withFilter(filter.productId) { this.productId.eq(it) }
                .withFilter(filter.parentBeneficiaryId) { this.parentBeneficiary.eq(it) }
                .withFilter(filter.beneficiaryIds) { this.beneficiaryId.inList(it) }
                .withFilter(filter.companyIds) { this.companyId.inList(it) }
                .withFilter(filter.companySubContractIds) { this.companySubContractId.inList(it) }
                .withFilter(filter.cancellationRange) {
                    this.canceledAt.greaterEq(it.start) and this.canceledAt.lessEq(it.endInclusive)
                }
                .withFilter(filter.parentPersonId) { this.parentPersonId.eq(it) }
                .withFilter(filter.parentMemberId) { this.parentMember.eq(it) }?.let {
                    it and this.archived.eq(false)
                } ?: throw IllegalArgumentException("There is no valid filter")
        }

    override suspend fun add(model: MemberModel): Result<MemberModel, Throwable>

    override suspend fun findByQuery(query: Query): Result<List<MemberModel>, Throwable>

    override suspend fun update(model: MemberModel): Result<MemberModel, Throwable>
    override suspend fun updateList(models: List<MemberModel>, returnOnFailure: Boolean): Result<List<MemberModel>, Throwable>
    override suspend fun get(id: UUID): Result<MemberModel, Throwable>

    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>

    @OptIn(WithFilterPredicateUsage::class)
    suspend fun countByCompanyIdAndStatus(
        companyId: UUID,
        status: MemberStatus,
        type: BeneficiaryType? = null,
        withNoPendingCancellation: Boolean = false
    ) = count {
        where {
            this.archived.eq(false) and this.companyId.eq(companyId) and this.status.eq(MemberStatus.ACTIVE)
                .withFilter(type) { this.beneficiaryType.eq(it) }
                .withFilter(takeIf { withNoPendingCancellation }) { this.canceledAt.isNull() }!!
        }
    }

    private fun addSortAndPagination(query: QueryBuilder<FieldOptions, OrderingOptions>, range: IntRange) = query
        .orderBy { createdAt }
        .sortOrder { asc }
        .offset { range.first }
        .limit { range.last }

    data class Filter(
        val ids: List<UUID> = emptyList(),
        val personIds: List<PersonId> = emptyList(),
        val statuses: List<MemberStatus> = emptyList(),
        val productId: UUID? = null,
        val parentMemberId: UUID? = null,
        val parentPersonId: PersonId? = null,
        val beneficiaryIds: List<UUID> = emptyList(),
        val companyIds: List<UUID> = emptyList(),
        val companySubContractIds: List<UUID> = emptyList(),
        val parentBeneficiaryId: UUID? = null,
        val cancellationRange: ClosedRange<LocalDateTime>? = null,
    )
}
