package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class MonitoringTriggerConfiguration(
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    val name: String,
    val features: List<MonitoringTriggerFeature> = emptyList(),
    val additionalProperties: MonitoringTriggerAdditionalProperties? = null
) : Model

data class MonitoringTriggerFeature(
    val feature: MonitoringTriggerFeatureType,
    val order: Int
) : JsonSerializable

enum class MonitoringTriggerFeatureType {
    CREATE_CHANNEL,
    SEND_WHATSAPP_MESSAGE
}

data class MonitoringTriggerAdditionalProperties(
    val channelInitialMessage: String? = null,
    val whatsAppTemplate: MonitoringTriggerWhatsAppTemplate? = null,
) : JsonSerializable

data class MonitoringTriggerWhatsAppTemplate(
    val name: String,
    val variables: List<MonitoringTriggerWhatsAppTemplateVariable>? = null
) : JsonSerializable

data class MonitoringTriggerWhatsAppTemplateVariable(
    val domain: String,
    val attribute: String,
) : JsonSerializable
