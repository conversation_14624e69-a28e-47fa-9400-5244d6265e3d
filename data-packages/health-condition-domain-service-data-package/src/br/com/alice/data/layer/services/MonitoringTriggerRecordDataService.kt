package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.AdderList
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.MonitoringTriggerRecord
import br.com.alice.data.layer.services.MonitoringTriggerRecordDataService.FieldOptions
import br.com.alice.data.layer.services.MonitoringTriggerRecordDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface MonitoringTriggerRecordDataService : Service,
    Adder<MonitoringTriggerRecord>,
    AdderList<MonitoringTriggerRecord>,
    Getter<MonitoringTriggerRecord>,
    Updater<MonitoringTriggerRecord>,
    Counter<FieldOptions, OrderingOptions, MonitoringTriggerRecord>,
    Finder<FieldOptions, OrderingOptions, MonitoringTriggerRecord> {

    override val namespace: String
        get() = "health_condition"
    override val serviceName: String
        get() = "monitoring_trigger_record"

    class IdField : Field.UUIDField(MonitoringTriggerRecord::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class PersonIdField : Field.UUIDField(MonitoringTriggerRecord::personId) {
        fun eq(value: PersonId) = Predicate.eq(this, value)
        fun inList(value: List<PersonId>) = Predicate.inList(this, value)
    }

    class ExternalTriggerIdField : Field.TextField(MonitoringTriggerRecord::externalTriggerId) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class ChannelIdField : Field.JsonbField(MonitoringTriggerRecord::extraData) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: String) = Predicate.jsonSearch(this, "{\"channelId\":\"${value}\"}")
    }

    class CreatedAt : Field.DateTimeField(MonitoringTriggerRecord::createdAt)

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    class FieldOptions {
        val id = IdField()
        val personId = PersonIdField()
        val externalTriggerId = ExternalTriggerIdField()
        val channelId = ChannelIdField()
    }

    override suspend fun add(model: MonitoringTriggerRecord): Result<MonitoringTriggerRecord, Throwable>
    override suspend fun addList(models: List<MonitoringTriggerRecord>): Result<List<MonitoringTriggerRecord>, Throwable>
    override suspend fun update(model: MonitoringTriggerRecord): Result<MonitoringTriggerRecord, Throwable>
    override suspend fun get(id: UUID): Result<MonitoringTriggerRecord, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<MonitoringTriggerRecord>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>

    override fun queryBuilder() = QueryBuilder(FieldOptions(), OrderingOptions())
}
