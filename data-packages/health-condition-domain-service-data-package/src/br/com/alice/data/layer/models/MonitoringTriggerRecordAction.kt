package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class MonitoringTriggerRecordAction(
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    override val personId: PersonId,
    val triggerId: UUID,
    val externalTriggerId: String,
    val monitoringTriggerRecordId: UUID,
    val action: MonitoringTriggerRecordActionType,
    val extraInfo: MonitoringTriggerRecordActionExtraInfo? = null
) : Model, PersonReference

enum class MonitoringTriggerRecordActionType {
    NEW_TRIGGER_IDENTIFIED,
    CHANNEL_CREATED,
    WHATSAPP_MESSAGE_SENT,
    CHANNEL_ARCHIVED_BY_INACTIVITY
}

data class MonitoringTriggerRecordActionExtraInfo(
    val channelId: String? = null
) : JsonSerializable
