package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.AdderList
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.client.UpdaterList
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.HrMemberUploadTracking
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HrMemberUploadTrackingDataService : Service,
        Adder<HrMemberUploadTracking>,
        AdderList<HrMemberUploadTracking>,
        Getter<HrMemberUploadTracking>,
        Updater<HrMemberUploadTracking>,
        UpdaterList<HrMemberUploadTracking>,
        Finder<HrMemberUploadTrackingDataService.FieldOptions, HrMemberUploadTrackingDataService.OrderingOptions, HrMemberUploadTracking> {

    override val namespace: String
        get() = "hr-core"
    override val serviceName: String
        get() = "hr_member_upload_tracking"

    class UploadIdField : Field.UUIDField(HrMemberUploadTracking::uploadId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class CompanyIdField : Field.UUIDField(HrMemberUploadTracking::companyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class MemberNationalIdField : Field.TextField(HrMemberUploadTracking::memberNationalId) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class ReportNotifiedAtField : Field.DateTimeField(HrMemberUploadTracking::reportNotifiedAt) {
        fun isNull() = Predicate.isNull(this)
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class CreatedAtField: Field.DateTimeField(HrMemberUploadTracking::createdAt)

    class FieldOptions {
        val companyId = CompanyIdField()
        val uploadId = UploadIdField()
        val memberNationalId = MemberNationalIdField()
        val reportNotifiedAt = ReportNotifiedAtField()
    }

    class OrderingOptions {
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<HrMemberUploadTracking, Throwable>
    override suspend fun add(model: HrMemberUploadTracking): Result<HrMemberUploadTracking, Throwable>
    override suspend fun addList(models: List<HrMemberUploadTracking>): Result<List<HrMemberUploadTracking>, Throwable>
    override suspend fun update(model: HrMemberUploadTracking): Result<HrMemberUploadTracking, Throwable>
    override suspend fun updateList(models: List<HrMemberUploadTracking>, returnOnFailure: Boolean): Result<List<HrMemberUploadTracking>, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<HrMemberUploadTracking>, Throwable>
    suspend fun findByMemberNationalId(nationalId: String): Result<HrMemberUploadTracking, Throwable> = useReadDatabase {
        findOne {
            where {
                this.memberNationalId.eq(nationalId)
            }
        }
    }
    suspend fun findLastByMemberNationalId(nationalId: String): Result<HrMemberUploadTracking, Throwable> = useReadDatabase {
        findOne {
            where {
                this.memberNationalId.eq(nationalId)
            }.orderBy {
                this.createdAt
            }.sortOrder {
                SortOrder.Descending
            }
        }
    }
    suspend fun findByMemberNationalIdAndUploadId(nationalId: String, uploadId: UUID): Result<HrMemberUploadTracking, Throwable> = useReadDatabase {
        findOne {
            where {
                this.memberNationalId.eq(nationalId) and this.uploadId.eq(uploadId)
            }
        }
    }
    suspend fun findByMemberNationalIdsAndUploadId(nationalIds: List<String>, uploadId: UUID): Result<List<HrMemberUploadTracking>, Throwable> = useReadDatabase {
        find {
            where {
                this.memberNationalId.inList(nationalIds) and this.uploadId.eq(uploadId)
            }
        }
    }
}
