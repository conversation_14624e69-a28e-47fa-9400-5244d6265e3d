package br.com.alice.data.layer.services

import br.com.alice.common.Brand
import br.com.alice.common.core.Status
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.client.UpdaterList
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ProviderModel
import br.com.alice.data.layer.models.ProviderType
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ProviderModelDataService : Service,
    Adder<ProviderModel>,
    Finder<ProviderModelDataService.FieldOptions, ProviderModelDataService.OrderingOptions, ProviderModel>,
    Counter<ProviderModelDataService.FieldOptions, ProviderModelDataService.OrderingOptions, ProviderModel>,
    Updater<ProviderModel>,
    UpdaterList<ProviderModel>,
    Getter<ProviderModel> {

    override val namespace: String
        get() = "provider"
    override val serviceName: String
        get() = "provider"

    class Id : Field.UUIDField(ProviderModel::id) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class Name : Field.TextField(ProviderModel::name) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class UrlSlug : Field.TextField(ProviderModel::urlSlug) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class SearchTokens : Field.TextField(ProviderModel::searchTokens) {
        fun search(value: String) = Predicate.search(this, value)
    }

    class Type : Field.TextField(ProviderModel::type) {
        fun eq(value: ProviderType) = Predicate.eq(this, value)
    }

    class Cnpj : Field.TextField(ProviderModel::cnpj) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class BrandField : Field.TextField(ProviderModel::brand) {
        fun eq(value: Brand) = Predicate.eq(this, value)
        fun inList(value: List<Brand>) = Predicate.inList(this, value)
    }

    class StatusField : Field.TextField(ProviderModel::status) {
        fun eq(value: Status) = Predicate.eq(this, value)
        fun inList(value: List<Status>) = Predicate.inList(this, value)
    }

    class CreatedAtField : Field.DateTimeField(ProviderModel::createdAt)

    class Flagship : Field.BooleanField(ProviderModel::flagship)

    class FieldOptions {
        val id = Id()
        val name = Name()
        val urlSlug = UrlSlug()
        val searchTokens = SearchTokens()
        val type = Type()
        val cnpj = Cnpj()
        val brand = BrandField()
        val status = StatusField()
        val flagship = Flagship()
    }

    class OrderingOptions {
        val name = Name()
        val createdAt = CreatedAtField()
        val flagship = Flagship()
    }

    override suspend fun add(model: ProviderModel): Result<ProviderModel, Throwable>

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<ProviderModel>, Throwable>
    override suspend fun get(id: UUID): Result<ProviderModel, Throwable>
    override suspend fun update(model: ProviderModel): Result<ProviderModel, Throwable>
    override suspend fun updateList(
        models: List<ProviderModel>,
        returnOnFailure: Boolean
    ): Result<List<ProviderModel>, Throwable>

    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
}
