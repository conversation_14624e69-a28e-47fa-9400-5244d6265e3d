package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class HealthcareResourceModel(
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    val nrol: Boolean,
    val dut: Boolean,
    val pac: Boolean,
    val code: String,
    val description: String,
    var searchTokens: String? = null,
    val type: HealthcareResourceType,
    val category: HealthcareResourceCategory,
    val gracePeriodType: ResourceGracePeriodType? = null,
    val tussCode: String? = null,
    val tableType: String? = null,
    var isOriginalTuss: Boolean? = null,
    val tussTableType: String? = null,
    val compositionHash: String? = null,
    val active: Boolean = true,
) : Model {
    override fun sanitize() = this.copy(
        description = this.description.lowercase().capitalize(),
    )
}
