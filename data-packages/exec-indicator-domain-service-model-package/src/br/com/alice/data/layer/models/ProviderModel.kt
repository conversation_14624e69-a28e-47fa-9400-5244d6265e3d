package br.com.alice.data.layer.models

import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import br.com.alice.common.core.SkipReadAuthz
import br.com.alice.common.core.Status
import br.com.alice.common.core.extensions.normalizeCnpjWithoutMask
import br.com.alice.common.core.extensions.nullIfBlank
import java.time.LocalDateTime
import java.util.UUID

@SkipReadAuthz
data class ProviderModel(
    override val id: UUID = RangeUUID.generate(),
    val name: String,
    val site: String?,
    val cnpj: String?,
    val phones: List<PhoneNumber>,
    val imageUrl: String?,
    val urlSlug: String? = null,
    val searchTokens: String? = null,
    val type: ProviderType,
    val flagship: Boolean = false,
    val description: String? = null,
    val icon: String? = null,
    val logo: String? = null,
    val thumbnail: String? = null,
    val about: String? = null,
    val daysForPayment: Int = 30,
    override val version: Int = 0,
    val brand: Brand? = Brand.ALICE,
    val externalBrandId: String? = null,
    val status: Status = Status.ACTIVE,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override var updatedBy: UpdatedBy? = null,
) : Model, UpdatedByReference {

    override fun sanitize(): Model = this.copy(
        imageUrl = imageUrl?.trim().nullIfBlank(),
        cnpj = cnpj?.normalizeCnpjWithoutMask()
    )
}
