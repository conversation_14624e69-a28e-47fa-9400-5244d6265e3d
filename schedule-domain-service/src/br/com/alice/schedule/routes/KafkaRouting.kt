package br.com.alice.schedule.routes

import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.interfaces.AdditionalProperties
import br.com.alice.common.kafka.internals.ConsumerJob
import br.com.alice.schedule.consumers.*
import br.com.alice.schedule.model.events.*
import br.com.alice.staff.event.StaffUpdatedEvent
import br.com.alice.wanda.event.AppointmentCoordinationCreatedEvent
import br.com.alice.wanda.event.PersonHealthEventUpdatedEvent
import java.time.Duration
import java.time.temporal.ChronoUnit

fun ConsumerJob.Configuration.kafkaRoutes() {

    val schedulerConsumer by inject<SchedulerConsumer>()
    consume(
        "schedule-appointment-schedule-event",
        AppointmentScheduleCreationRequestedEvent.name,
        schedulerConsumer::schedule
    )
    consume(
        "reschedule-appointment-schedule-event",
        AppointmentScheduleRescheduleRequestedEvent.name,
        schedulerConsumer::reschedule
    )

    val appointmentScheduleCancelRequestedConsumer by inject<AppointmentScheduleCancelRequestedConsumer>()
    consume(
        "appointment-schedule-cancel-request",
        AppointmentScheduleCancelRequestEvent.name,
        appointmentScheduleCancelRequestedConsumer::cancel
    )

    val googleCalendarEventsConsumer by inject<GoogleCalendarEventsConsumer>()
    consume(
        "upsert-google-calendar-event",
        GoogleCalendarEventCreationRequestedEvent.name,
        googleCalendarEventsConsumer::createGoogleCalendarEvent,
        additionalProperties = AdditionalProperties(pollingInterval = Duration.of(30, ChronoUnit.MILLIS))
    )
    consume(
        "upsert-google-calendar-recurring-event-creation",
        GoogleCalendarRecurringEventCreationRequestedEvent.name,
        googleCalendarEventsConsumer::createGoogleRecurringCalendarEvent,
        additionalProperties = AdditionalProperties(pollingInterval = Duration.of(30, ChronoUnit.MILLIS))
    )
    consume(
        "handle-google-calendar-synchronization-requested",
        GoogleCalendarSynchronizationRequestedEvent.name,
        googleCalendarEventsConsumer::synchronizeGoogleCalendar
    )
    consume(
        "handle-google-calendar-start-events-webhook",
        GoogleCalendarWebhookSubscriptionRequestedEvent.name,
        googleCalendarEventsConsumer::createWebhookChannelToReceiveEventUpdates
    )
    consume(
        "handle-google-calendar-update-event-webhook",
        GoogleCalendarEventNotificationEvent.name,
        googleCalendarEventsConsumer::createOrUpdateEventFromWebhookNotification
    )
    consume(
        "handle-google-calendar-sync-finished",
        GoogleCalendarFinishedEventsQueryEvent.name,
        googleCalendarEventsConsumer::processGoogleCalendarFinishedEventsQueryEvent
    )
    consume(
        "handle-google-calendar-query-events",
        GoogleCalendarQueryEventsEvent.name,
        googleCalendarEventsConsumer::queryGoogleCalendarEvents
    )
    consume(
        "handle-google-calendar-query-events-for-recurrence",
        GoogleCalendarEventsFromRecurrenceRequestedEvent.name,
        googleCalendarEventsConsumer::queryGoogleCalendarEventsForRecurrence
    )
    consume(
        "handle-google-calendar-fetch-instances-for-recurrent-events",
        GoogleCalendarFetchInstancesForRecurrentEventsEvent.name,
        googleCalendarEventsConsumer::fetchInstancesForRecurrentEvents
    )
    consume(
        "handle-google-calendar-fetch-instances-for-recurrent-events-for-page",
        GoogleCalendarFetchInstancesForRecurrentEventsForPageEvent.name,
        googleCalendarEventsConsumer::fetchInstancesForRecurrentEventsForPage
    )
    consume(
        "handle-google-calendar-renew-webhook-channels",
        RenewGoogleCalendarWebhooksRequestedEvent.name,
        googleCalendarEventsConsumer::renewWebhookChannelsCloseToExpiration
    )

    val personHealthEventConsumer by inject<PersonHealthEventConsumer>()
    consume(
        "update-appointment-schedule",
        PersonHealthEventUpdatedEvent.name,
        personHealthEventConsumer::updateAppointmentSchedule
    )

    val calendlyNotificationConsumer by inject<CalendlyNotificationConsumer>()
    consume(
        "calendly-schedule-or-cancel",
        CalendlyNotificationReceivedEvent.name,
        calendlyNotificationConsumer::scheduleOrCancel
    )

    val staffScheduleConsumer by inject<StaffScheduleConsumer>()
    consume(
        "create-non-healthcare-events-at-google",
        StaffScheduleCreatedEvent.name,
        staffScheduleConsumer::createNonHealthCareEventsAtGoogle
    )
    consume(
        "stop-non-healthcare-event-at-google",
        StaffScheduleDeletedEvent.name,
        staffScheduleConsumer::stopNonHealthCareEventAtGoogle
    )
    consume(
        "update-non-healthcare-event-at-google",
        StaffScheduleUpdatedEvent.name,
        staffScheduleConsumer::updateNonHealthCareEventAtGoogle
    )

    val externalCalendarEventConsumer by inject<ExternalCalendarEventConsumer>()
    consume(
        "external-calendar-event-updated-cancel-appointment-schedule",
        ExternalCalendarEventUpdated.name,
        externalCalendarEventConsumer::cancelAppointmentScheduleIfNeeded
    )
    consume(
        "external-calendar-event-created-notify-attendants",
        ExternalCalendarEventCreated.name,
        externalCalendarEventConsumer::updateAppointmentScheduleAndNotifyAttendants
    )
    consume(
        "external-calendar-event-updated-update-appointment-schedule",
        ExternalCalendarEventUpdated.name,
        externalCalendarEventConsumer::updateAppointmentSchedule
    )

    val externalCalendarRecurrentEventConsumer by inject<ExternalCalendarRecurrentEventConsumer>()
    consume(
        "google-calendar-get-single-events-from-recurrence",
        ExternalCalendarRecurrentEventCreated.name,
        externalCalendarRecurrentEventConsumer::getSingleEventsFromRecurrence
    )
    consume(
        "google-calendar-update-single-events-from-recurrence",
        ExternalCalendarRecurrentEventUpdated.name,
        externalCalendarRecurrentEventConsumer::updateSingleEventsFromRecurrence
    )

    val zoomRefreshTokenUpdatedConsumer by inject<ZoomRefreshTokenUpdatedConsumer>()
    consume(
        "zoom-refresh-token-updated-updates-schedule-preference",
        ZoomRefreshTokenUpdatedEvent.name,
        zoomRefreshTokenUpdatedConsumer::updateSchedulePreferenceZoomRefreshToken,
    )

    val appointmentReminderConsumer by inject<AppointmentReminderConsumer>()
    consume(
        "appointment-reminder-sent-create-appointment-reminder",
        AppointmentReminderSentEvent.name,
        appointmentReminderConsumer::createAppointmentReminder,
    )

    val schedulePreferenceConsumer by inject<SchedulePreferenceConsumer>()
    consume(
        "schedule-preference-created-create-staff-schedule-preference",
        SchedulePreferenceCreatedEvent.name,
        schedulePreferenceConsumer::createStaffSchedulePreference,
    )
    consume(
        "update-schedule-preference-has-staff-schedules",
        StaffScheduleCreatedEvent.name,
        schedulePreferenceConsumer::updateHasStaffSchedules,
    )

    val appointmentScheduleCreatedConsumer by inject<AppointmentScheduleCreatedConsumer>()
    consume(
        "appointment-schedule-associate-healthcare-team-if-needed",
        AppointmentScheduleCreatedEvent.name,
        appointmentScheduleCreatedConsumer::associateHealthcareTeamIfNeeded,
    )
    consume(
        "appointment-schedule-update-recommendation-team-if-needed",
        AppointmentScheduleCreatedEvent.name,
        appointmentScheduleCreatedConsumer::updateRecommendationIfNeeded,
    )
    consume(
        "appointment-schedule-update-notify-attendants",
        AppointmentScheduleCreatedEvent.name,
        appointmentScheduleCreatedConsumer::notifyAttendants,
    )
    consume(
        "internal-appointment-schedule-created-google-integration",
        InternalAppointmentScheduleCreatedEvent.name,
        appointmentScheduleCreatedConsumer::createEventAtGoogleCalendar
    )
    consume(
        "check-third-party-members-health-declaration-schedules",
        AppointmentScheduleCreatedEvent.name,
        appointmentScheduleCreatedConsumer::checkThirdPartyMembersHealthDeclarationSchedules
    )
    consume(
        "trigger-hubspot-event",
        AppointmentScheduleCreatedEvent.name,
        appointmentScheduleCreatedConsumer::triggerHubspotEvent
    )

    val appointmentScheduleEventTypeConsumer by inject<AppointmentScheduleEventTypeConsumer>()
    consume(
        "appointment-schedule-event-type-updates-schedule-options",
        AppointmentScheduleEventTypeUpdated.name,
        appointmentScheduleEventTypeConsumer::updatedAssociatedScheduleOptions,
    )

    val appointmentScheduleCancelledConsumer by inject<AppointmentScheduleCancelledConsumer>()
    consume(
        "appointment-schedule-cancelled-notify-attendants",
        AppointmentScheduleCancelledEvent.name,
        appointmentScheduleCancelledConsumer::notifyAttendants,
    )

    consume(
        "appointment-schedule-cancelled-drop-appointment-schedule-check-in",
        AppointmentScheduleCancelledEvent.name,
        appointmentScheduleCancelledConsumer::dropAppointmentScheduleCheckIn,
    )

    consume(
        "appointment-schedule-cancelled-trigger-hubspot-client-canceled-event",
        AppointmentScheduleCancelledEvent.name,
        appointmentScheduleCancelledConsumer::triggerHubspotCanceledEvent,
    )

    val appointmentScheduleCancelConsumer by inject<AppointmentScheduleCancelConsumer>()
    consume(
        "remove-appointment-schedule-cancelled-from-google-calendar",
        AppointmentScheduleCancelledEvent.name,
        appointmentScheduleCancelConsumer::removeFromGoogleCalendar
    )

    val staffUpdatedConsumer by inject<StaffUpdatedConsumer>()
    consume(
        "staff-updated-updates-schedule-preference",
        StaffUpdatedEvent.name,
        staffUpdatedConsumer::updateSchedulePreferencesForUpdatedStaff
    )

    val appointmentCoordinationConsumer by inject<AppointmentCoordinationConsumer>()
    consume(
        "send-scheduled-appointment-braze-event",
        AppointmentCoordinationCreatedEvent.name,
        appointmentCoordinationConsumer::sendBrazeEvent,
    )

    val appointmentScheduleOptionConsumer by inject<AppointmentScheduleOptionConsumer>()
    consume(
        "invalidate-cache-from-schedule-option-create",
        AppointmentScheduleOptionCreatedEvent.name,
        appointmentScheduleOptionConsumer::invalidateCacheFromAppointmentScheduleOptionCreation
    )
    consume(
        "invalidate-cache-from-schedule-option-update",
        AppointmentScheduleOptionUpdatedEvent.name,
        appointmentScheduleOptionConsumer::invalidateCacheFromAppointmentScheduleOptionUpdate
    )

    val appointmentScheduleNotificationConsumer by inject<AppointmentScheduleNotificationConsumer>()
    consume(
        "send-appointment-schedule-notifications",
        SendAppointmentScheduleNotificationsEvent.name,
        appointmentScheduleNotificationConsumer::sendAppointmentScheduleNotifications
    )
    consume(
        "send-appointment-schedule-notification",
        SendAppointmentScheduleNotificationEvent.name,
        appointmentScheduleNotificationConsumer::sendAppointmentScheduleNotification
    )

    val hubspotConsumer by inject<HubspotConsumer>()
    consume(
        "hubspot-trigger-appointment-schedule-created-event",
        HubspotTriggerAppointmentScheduleCreatedEvent.name,
        hubspotConsumer::createMeeting
    )

    consume(
        "hubspot-trigger-appointment-schedule-canceled-event",
        HubspotTriggerAppointmentScheduleCanceledEvent.name,
        hubspotConsumer::cancelMeeting
    )

    val livanceAppointmentScheduleConsumer by inject<LivanceAppointmentScheduleConsumer>()
    consume(
        "create-livance-appointment-on-appointment-schedule-created-event",
        AppointmentScheduleCreatedEvent.name,
        livanceAppointmentScheduleConsumer::onAppointmentScheduleCreatedEvent
    )
    consume(
        "cancel-livance-appointment-on-appointment-schedule-cancelled-event",
        AppointmentScheduleCancelledEvent.name,
        livanceAppointmentScheduleConsumer::onAppointmentScheduleCancelledEvent
    )

}
