package br.com.alice.schedule.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.containsAny
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.foldBoolean
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.ExternalEventStatus
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import br.com.alice.schedule.model.events.HubspotTriggerAppointmentScheduleCreatedEvent
import br.com.alice.schedule.model.events.InternalAppointmentScheduleCreatedEvent
import br.com.alice.schedule.services.ExternalCalendarEventServiceImpl
import br.com.alice.schedule.services.GoogleCalendarEventServiceImpl
import br.com.alice.schedule.services.internal.AppointmentScheduledNotifier
import br.com.alice.schedule.services.internal.ImmersionAppointmentScheduleService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDateTime
import java.util.UUID

class AppointmentScheduleCreatedConsumer(
    private val immersionAppointmentScheduleService: ImmersionAppointmentScheduleService,
    private val appointmentScheduledNotifier: AppointmentScheduledNotifier,
    private val googleCalendarEventService: GoogleCalendarEventServiceImpl,
    private val externalCalendarEventService: ExternalCalendarEventServiceImpl,
    private val personService: PersonService,
    private val memberService: MemberService,
    private val personInternalReferenceService: PersonInternalReferenceService,
    private val beneficiaryService: BeneficiaryService,
    private val companyService: CompanyService,
    private val staffService: StaffService,
    private val kafkaProducerService: KafkaProducerService
) : Consumer() {

    suspend fun associateHealthcareTeamIfNeeded(
        appointmentScheduleCreatedEvent: AppointmentScheduleCreatedEvent
    ): Result<Any, Throwable> {
        val newSchedule = appointmentScheduleCreatedEvent.payload.appointmentSchedule

        logger.info(
            "AppointmentScheduleCreatedConsumer::associateHealthcareTeamIfNeeded",
            "appointment_schedule_id" to newSchedule.id,
        )
        return withSubscribersEnvironment {
            immersionAppointmentScheduleService.associateHealthcareTeamIfNeeded(newSchedule)
        }
    }

    suspend fun updateRecommendationIfNeeded(
        appointmentScheduleCreatedEvent: AppointmentScheduleCreatedEvent
    ): Result<Any, Throwable> {
        val newSchedule = appointmentScheduleCreatedEvent.payload.appointmentSchedule

        logger.info(
            "AppointmentScheduleCreatedConsumer::updateRecommendationIfNeeded",
            "appointment_schedule_id" to newSchedule.id,
        )
        return withSubscribersEnvironment {
            immersionAppointmentScheduleService.updateRecommendationIfNeeded(newSchedule)
        }
    }

    suspend fun notifyAttendants(
        appointmentScheduleCreatedEvent: AppointmentScheduleCreatedEvent
    ): Result<Any, Throwable> {
        val newSchedule = appointmentScheduleCreatedEvent.payload.appointmentSchedule
        val shouldUseGoogleMeet = FeatureService.get(
            namespace = FeatureNamespace.SCHEDULE,
            key = "should_use_google_meet_to_generate_digital_links",
            defaultValue = false
        )
        val isUsingInternalScheduler = appointmentScheduleCreatedEvent.payload.isUsingInternalScheduler
        val notifyAttendantsByEmail = isUsingInternalScheduler && !shouldUseGoogleMeet

        logger.info(
            "AppointmentScheduleCreatedConsumer::notifyAttendants",
            "appointment_schedule_id" to newSchedule.id,
            "should_notify_attendants" to notifyAttendantsByEmail,
            "should_use_google_meet" to shouldUseGoogleMeet
        )
        return withSubscribersEnvironment {
            if (notifyAttendantsByEmail) {
                appointmentScheduledNotifier.notifyAttendants(
                    newSchedule,
                    appointmentScheduleCreatedEvent.payload.professional,
                    appointmentScheduleCreatedEvent.payload.person
                ).success()
            } else {
                true.success()
            }
        }
    }

    suspend fun triggerHubspotEvent(
        event: AppointmentScheduleCreatedEvent
    ) = withSubscribersEnvironment {
        val appointmentSchedule = event.payload.appointmentSchedule

        if (appointmentSchedule.type != AppointmentScheduleType.HEALTH_DECLARATION || !appointmentSchedule.scheduledByInternalScheduler) return@withSubscribersEnvironment true.success()

        HubspotTriggerAppointmentScheduleCreatedEvent(
            event.payload.person.personId,
            appointmentSchedule,
        ).let {
            kafkaProducerService.produce(it)
        }

        true.success()
    }

    suspend fun createEventAtGoogleCalendar(event: InternalAppointmentScheduleCreatedEvent) =
        span("createEventAtGoogleCalendar") { span ->
            withSubscribersEnvironment {
                val appointmentSchedule = event.payload.appointmentSchedule
                val appointmentScheduleId = appointmentSchedule.id

                span.setAttribute("appointment_schedule_id", appointmentScheduleId)

                externalCalendarEventService.getByAppointmentScheduleId(appointmentScheduleId)
                    .map { externalCalendarEvents ->
                        span.setAttribute("external_calendar_events", externalCalendarEvents.size)
                        externalCalendarEvents.none {
                            it.status == ExternalEventStatus.CONFIRMED &&
                                    it.startTime == appointmentSchedule.startTime &&
                                    it.endTime == appointmentSchedule.endTime
                        }
                    }
                    .recordResult(span)
                    .foldBoolean(
                        { googleCalendarEventService.createEventAtGoogleCalendar(appointmentScheduleId) },
                        { true.success() }
                    )
            }
        }

    suspend fun checkThirdPartyMembersHealthDeclarationSchedules(
        appointmentScheduleCreatedEvent: AppointmentScheduleCreatedEvent
    ): Result<Any, Throwable> = span("checkThirdPartyMembersHealthDeclarationSchedules") { span ->
        val appointmentSchedule = appointmentScheduleCreatedEvent.payload.appointmentSchedule
        val personId = appointmentSchedule.personId
        val staffId = appointmentSchedule.staffId
        val appointmentScheduleId = appointmentSchedule.id

        span.setAttribute("appointment_schedule_id", appointmentScheduleId)
        span.setAttribute("person_id", personId)

        if (appointmentSchedule.type != AppointmentScheduleType.HEALTH_DECLARATION) return@span true.success()

        withSubscribersEnvironment {
            personService.get(id = personId).map { person ->
                if (person.tags?.containsAny(listOf("3p", "3P")) == true) {
                    logInfoFor3pMembersWithScheduledHealthDeclarationAppointment(
                        staffId = staffId,
                        personId = personId,
                        appointmentScheduleStartTime = appointmentSchedule.startTime,
                        appointmentScheduleEndTime = appointmentSchedule.endTime
                    )
                }
                true
            }
        }
    }

    private suspend fun getStaffEmailAsync(staffId: UUID?) = coroutineScope {
        async { staffId?.let { staffService.get(id = staffId).getOrNull()?.email } ?: "" }
    }

    private suspend fun getPersonInternalCodeAsync(personId: PersonId) = coroutineScope {
        async { personInternalReferenceService.getForPerson(personId = personId).get().internalCode }
    }

    private suspend fun getCompanyNameAsync(memberId: UUID) =
        beneficiaryService.findByMemberId(memberId = memberId)
            .map { beneficiary -> companyService.get(id = beneficiary.companyId).getOrNull()?.name }
            .getOrNull()
            ?: ""

    private suspend fun logInfoFor3pMembersWithScheduledHealthDeclarationAppointment(
        staffId: UUID?,
        personId: PersonId,
        appointmentScheduleStartTime: LocalDateTime,
        appointmentScheduleEndTime: LocalDateTime?
    ) {
        val staffEmailDeferred = getStaffEmailAsync(staffId)
        val personInternalReferenceDeferred = getPersonInternalCodeAsync(personId)

        memberService.getCurrent(personId)
            .map { member ->
                val (companyName, productType) = when {
                    member.isB2B -> getCompanyNameAsync(member.id) to "B2B"
                    member.isB2C -> "none" to "B2C"
                    else -> "none" to "none"
                }

                logger.info(
                    "AppointmentScheduleCreatedConsumer::check3PMembersHealthDeclarationSchedules health declaration appointment scheduled of a member coming from third party",
                    "member_internal_code" to personInternalReferenceDeferred.await(),
                    "product_type" to productType,
                    "company_name" to companyName,
                    "staff_email" to staffEmailDeferred.await(),
                    "start_time" to appointmentScheduleStartTime,
                    "end_time" to (appointmentScheduleEndTime ?: "")
                )
            }.coFoldNotFound {
                logger.info(
                    "AppointmentScheduleCreatedConsumer::check3PMembersHealthDeclarationSchedules health declaration appointment scheduled of a member coming from third party",
                    "member_internal_code" to personInternalReferenceDeferred.await(),
                    "product_type" to "B2C",
                    "company_name" to "none",
                    "staff_email" to staffEmailDeferred.await(),
                    "start_time" to appointmentScheduleStartTime,
                    "end_time" to (appointmentScheduleEndTime ?: "")
                )
                true.success()
            }
    }
}
