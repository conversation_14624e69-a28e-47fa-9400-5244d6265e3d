package br.com.alice.schedule.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.ExternalCalendarEvent
import br.com.alice.data.layer.models.ExternalEventStatus
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ProductType
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import br.com.alice.schedule.model.events.AppointmentSchedulePersonPayload
import br.com.alice.schedule.model.events.HubspotTriggerAppointmentScheduleCreatedEvent
import br.com.alice.schedule.model.events.InternalAppointmentScheduleCreatedEvent
import br.com.alice.schedule.model.events.Professional
import br.com.alice.schedule.services.ExternalCalendarEventServiceImpl
import br.com.alice.schedule.services.GoogleCalendarEventServiceImpl
import br.com.alice.schedule.services.internal.AppointmentScheduledNotifier
import br.com.alice.schedule.services.internal.ImmersionAppointmentScheduleService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

class AppointmentScheduleCreatedConsumerTest : ConsumerTest() {

    private val immersionAppointmentScheduleService: ImmersionAppointmentScheduleService = mockk()
    private val appointmentScheduledNotifier: AppointmentScheduledNotifier = mockk()
    private val googleCalendarEventService: GoogleCalendarEventServiceImpl = mockk()
    private val externalCalendarEventService: ExternalCalendarEventServiceImpl = mockk()
    private val personService: PersonService = mockk()
    private val memberService: MemberService = mockk()
    private val personInternalReferenceService: PersonInternalReferenceService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val companyService: CompanyService = mockk()
    private val staffService: StaffService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val appointmentScheduleCreatedConsumer = AppointmentScheduleCreatedConsumer(
        immersionAppointmentScheduleService,
        appointmentScheduledNotifier,
        googleCalendarEventService,
        externalCalendarEventService,
        personService,
        memberService,
        personInternalReferenceService,
        beneficiaryService,
        companyService,
        staffService,
        kafkaProducerService
    )

    private val personId = PersonId()
    private val person = TestModelFactory.buildPerson(personId = personId)
    private val staffId = RangeUUID.generate()
    private val staff = TestModelFactory.buildStaff(id = staffId)
    private val appointmentScheduleId = RangeUUID.generate()
    private val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(
        id = appointmentScheduleId,
        personId = personId,
        staffId = staffId
    )

    private val personPayload = AppointmentSchedulePersonPayload(
        personId,
        person.firstName,
        person.contactName,
        person.email,
        person.fullSocialName,
    )

    private val externalCalendarEvent =
        TestModelFactory.buildExternalCalendarEvent(appointmentScheduleId = appointmentScheduleId)

    private val internalAppointmentScheduleCreatedEvent = InternalAppointmentScheduleCreatedEvent(appointmentSchedule)
    private val personInternalReference = TestModelFactory.buildPersonInternalReference(personId = personId)
    private val member = TestModelFactory.buildMember(personId = personId, productType = ProductType.B2B)
    private val company = TestModelFactory.buildCompany()
    private val beneficiary = TestModelFactory.buildBeneficiary(personId = personId)

    @AfterTest
    fun confirmMocks() = confirmVerified(
        immersionAppointmentScheduleService,
        appointmentScheduledNotifier,
        googleCalendarEventService,
        externalCalendarEventService,
        personService,
        memberService,
        personInternalReferenceService,
        beneficiaryService,
        companyService,
        staffService,
        kafkaProducerService
    )

    @Test
    fun `#associateHealthcareTeamIfNeeded should call method to associate`() = runBlocking {
        val event = AppointmentScheduleCreatedEvent(
            person = person,
            appointmentSchedule = appointmentSchedule,
            professional = Professional.from(staff)
        )

        coEvery {
            immersionAppointmentScheduleService.associateHealthcareTeamIfNeeded(appointmentSchedule)
        } returns true.success()

        val result = appointmentScheduleCreatedConsumer.associateHealthcareTeamIfNeeded(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { immersionAppointmentScheduleService.associateHealthcareTeamIfNeeded(any()) }
    }

    @Test
    fun `#updateRecommendationIfNeeded should call method to updateRecommendationIfNeeded`() = runBlocking {
        val event = AppointmentScheduleCreatedEvent(
            person = person,
            appointmentSchedule = appointmentSchedule,
            professional = Professional.from(staff)
        )

        coEvery {
            immersionAppointmentScheduleService.updateRecommendationIfNeeded(appointmentSchedule)
        } returns true.success()

        val result = appointmentScheduleCreatedConsumer.updateRecommendationIfNeeded(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { immersionAppointmentScheduleService.updateRecommendationIfNeeded(any()) }
    }

    @Test
    fun `#notifyAttendants should call method to notifyAttendants`() = runBlocking {
        withFeatureFlag(FeatureNamespace.SCHEDULE, "should_use_google_meet_to_generate_digital_links", false) {
            val professional = Professional.from(staff)
            val event = AppointmentScheduleCreatedEvent(
                person = person,
                appointmentSchedule = appointmentSchedule,
                professional = professional,
                isUsingInternalScheduler = true
            )

            coEvery {
                appointmentScheduledNotifier.notifyAttendants(appointmentSchedule, professional, personPayload)
            } returns emptyList()

            val result = appointmentScheduleCreatedConsumer.notifyAttendants(event)
            assertThat(result).isSuccessWithData(emptyList<EmailReceipt>())

            coVerifyOnce { appointmentScheduledNotifier.notifyAttendants(any(), any(), any()) }
        }
    }

    @Test
    fun `#notifyAttendants should not call method to notifyAttendants if payload does not need it`() = runBlocking {
        val event = AppointmentScheduleCreatedEvent(
            person = person,
            appointmentSchedule = appointmentSchedule,
            professional = Professional.from(staff)
        )

        val result = appointmentScheduleCreatedConsumer.notifyAttendants(event)
        assertThat(result).isSuccessWithData(true)
    }

    @Test
    fun `#createEventAtGoogleCalendar calls googleCalendarEventService createEventAtGoogleCalendar`() = runBlocking {
        coEvery { googleCalendarEventService.createEventAtGoogleCalendar(appointmentScheduleId) } returns appointmentSchedule.success()
        coEvery {
            externalCalendarEventService.getByAppointmentScheduleId(appointmentScheduleId)
        } returns emptyList<ExternalCalendarEvent>().success()

        val result =
            appointmentScheduleCreatedConsumer.createEventAtGoogleCalendar(internalAppointmentScheduleCreatedEvent)
        assertThat(result).isSuccessWithData(appointmentSchedule)

        coVerifyOnce { externalCalendarEventService.getByAppointmentScheduleId(any()) }
        coVerifyOnce { googleCalendarEventService.createEventAtGoogleCalendar(any()) }
    }

    @Test
    fun `#createEventAtGoogleCalendar should createEventAtGoogleCalendar if event already exists but status is canceled`() =
        runBlocking {
            coEvery {
                externalCalendarEventService.getByAppointmentScheduleId(appointmentScheduleId)
            } returns listOf(externalCalendarEvent.copy(status = ExternalEventStatus.CANCELLED)).success()
            coEvery {
                googleCalendarEventService.createEventAtGoogleCalendar(appointmentScheduleId)
            } returns appointmentSchedule.success()

            val result =
                appointmentScheduleCreatedConsumer.createEventAtGoogleCalendar(internalAppointmentScheduleCreatedEvent)
            assertThat(result).isSuccessWithData(appointmentSchedule)

            coVerifyOnce { externalCalendarEventService.getByAppointmentScheduleId(any()) }
            coVerifyOnce { googleCalendarEventService.createEventAtGoogleCalendar(any()) }
        }

    @Test
    fun `#checkThirdPartyMembersHealthDeclarationSchedules should log schedule information`() = runBlocking {
        val event = AppointmentScheduleCreatedEvent(person = person, appointmentSchedule = appointmentSchedule)

        coEvery { personService.get(personId) } returns person.copy(tags = listOf("3p")).success()
        coEvery { staffService.get(staffId) } returns staff.success()
        coEvery { personInternalReferenceService.getForPerson(personId) } returns personInternalReference.success()
        coEvery { memberService.getCurrent(personId) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id) } returns beneficiary.success()
        coEvery { companyService.get(beneficiary.companyId) } returns company.success()

        val result = appointmentScheduleCreatedConsumer.checkThirdPartyMembersHealthDeclarationSchedules(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { personInternalReferenceService.getForPerson(any()) }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { beneficiaryService.findByMemberId(any()) }
        coVerifyOnce { companyService.get(any()) }
    }

    @Test
    fun `#checkThirdPartyMembersHealthDeclarationSchedules should log schedule information when member is not found`() =
        runBlocking {
            val event = AppointmentScheduleCreatedEvent(person = person, appointmentSchedule = appointmentSchedule)

            coEvery { personService.get(personId) } returns person.copy(tags = listOf("3p")).success()
            coEvery { staffService.get(staffId) } returns staff.success()
            coEvery { personInternalReferenceService.getForPerson(personId) } returns personInternalReference.success()
            coEvery { memberService.getCurrent(personId) } returns NotFoundException("not_found").failure()

            val result = appointmentScheduleCreatedConsumer.checkThirdPartyMembersHealthDeclarationSchedules(event)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerifyOnce { personInternalReferenceService.getForPerson(any()) }
            coVerifyOnce { memberService.getCurrent(any()) }
        }

    @Test
    fun `#checkThirdPartyMembersHealthDeclarationSchedules should not log schedule information when member is not coming from third party`() =
        runBlocking {
            val event = AppointmentScheduleCreatedEvent(person = person, appointmentSchedule = appointmentSchedule)

            coEvery { personService.get(personId) } returns person.success()

            val result = appointmentScheduleCreatedConsumer.checkThirdPartyMembersHealthDeclarationSchedules(event)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { personService.get(any()) }
        }

    @Test
    fun `#triggerHubspotEvent should trigger hubspot event when is HEALTH_DECLARATION`() = runBlocking {
        val appointmentSchedule = appointmentSchedule.copy(
            type = AppointmentScheduleType.HEALTH_DECLARATION,
            scheduledByInternalScheduler = true
        )
        val event = AppointmentScheduleCreatedEvent(person = person, appointmentSchedule = appointmentSchedule)
        val hubspotEvent = HubspotTriggerAppointmentScheduleCreatedEvent(person.id, appointmentSchedule)

        coEvery { kafkaProducerService.produce(hubspotEvent) } returns mockk()

        val result = appointmentScheduleCreatedConsumer.triggerHubspotEvent(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { kafkaProducerService.produce(hubspotEvent) }
    }

    @Test
    fun `#triggerHubspotEvent shouldn't trigger hubspot event when is NOT HEALTH_DECLARATION`() = runBlocking {
        val appointmentSchedule = appointmentSchedule.copy(
            type = AppointmentScheduleType.PHYSIOTHERAPIST,
            scheduledByInternalScheduler = true
        )
        val event = AppointmentScheduleCreatedEvent(person = person, appointmentSchedule = appointmentSchedule)

        val result = appointmentScheduleCreatedConsumer.triggerHubspotEvent(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { kafkaProducerService wasNot called }
    }

    @Test
    fun `#triggerHubspotEvent shouldn't trigger hubspot event when is scheduled by calendly`() = runBlocking {
        val appointmentSchedule = appointmentSchedule.copy(
            type = AppointmentScheduleType.HEALTH_DECLARATION,
            scheduledByInternalScheduler = false
        )
        val event = AppointmentScheduleCreatedEvent(person = person, appointmentSchedule = appointmentSchedule)

        val result = appointmentScheduleCreatedConsumer.triggerHubspotEvent(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { kafkaProducerService wasNot called }
    }
}
