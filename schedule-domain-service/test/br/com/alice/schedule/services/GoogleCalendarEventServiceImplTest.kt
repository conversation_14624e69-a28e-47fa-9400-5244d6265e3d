package br.com.alice.schedule.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.Status
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.fromRfc3339ToLocalDateTime
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDate
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ExternalEventStatus
import br.com.alice.data.layer.models.ExternalEventTransparency
import br.com.alice.data.layer.models.StaffScheduleType
import br.com.alice.data.layer.models.Weekday
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.ServiceConfig
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.converters.GoogleCalendarEventPayloadConverter
import br.com.alice.schedule.converters.toModel
import br.com.alice.schedule.integrations.googlecalendar.GoogleCalendarApi
import br.com.alice.schedule.model.RecurrenceRule
import br.com.alice.schedule.model.RecurrenceRuleFrequency
import br.com.alice.schedule.model.events.FetchInstancesForRecurrentEventsType
import br.com.alice.schedule.model.events.GoogleCalendarEventCreationRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarEventsFromRecurrenceRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarFetchInstancesForRecurrentEventsForPageEvent
import br.com.alice.schedule.model.events.GoogleCalendarFinishedEventsQueryEvent
import br.com.alice.schedule.model.events.GoogleCalendarQueryEventsEvent
import br.com.alice.schedule.model.events.GoogleCalendarRecurringEventCreationRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarSynchronizationRequestedEvent
import br.com.alice.schedule.model.events.GoogleCalendarWebhookSubscriptionRequestedEvent
import br.com.alice.schedule.model.googlecalendar.GoogleCalendarEventPayload
import br.com.alice.schedule.model.googlecalendar.GoogleCalendarEventWebhookNotification
import br.com.alice.schedule.model.googlecalendar.GoogleCalendarEventsQueryFilters
import br.com.alice.schedule.model.googlecalendar.buildEvent
import br.com.alice.schedule.toGoogleLocalTime
import br.com.alice.staff.client.StaffService
import com.google.api.client.googleapis.auth.oauth2.GoogleTokenResponse
import com.google.api.client.googleapis.json.GoogleJsonResponseException
import com.google.api.client.util.DateTime
import com.google.api.services.calendar.Calendar
import com.google.api.services.calendar.model.Channel
import com.google.api.services.calendar.model.Event
import com.google.api.services.calendar.model.EventAttendee
import com.google.api.services.calendar.model.EventDateTime
import com.google.api.services.calendar.model.Events
import io.ktor.util.date.WeekDay
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.io.PrintWriter
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.Date
import kotlin.test.AfterTest
import kotlin.test.Test

class GoogleCalendarEventServiceImplTest {

    private val externalCalendarEventService: ExternalCalendarEventServiceImpl = mockk()
    private val schedulePreferenceService: SchedulePreferenceServiceImpl = mockk()
    private val staffService: StaffService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val googleCalendarApi: GoogleCalendarApi = mockk()
    private val personService: PersonService = mockk()
    private val externalCalendarRecurrentEventService: ExternalCalendarRecurrentEventServiceImpl = mockk()
    private val appointmentScheduleService: AppointmentScheduleService = mockk()
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService = mockk()
    private val calendarService: Calendar.Events = mockk()

    private val googleCalendarEventService = GoogleCalendarEventServiceImpl(
        externalCalendarEventService,
        schedulePreferenceService,
        staffService,
        kafkaProducerService,
        googleCalendarApi,
        personService,
        externalCalendarRecurrentEventService,
        appointmentScheduleService,
        appointmentScheduleEventTypeService
    )

    private val googleTokenResponse = GoogleTokenResponse().setRefreshToken("1")

    private val staffId = RangeUUID.generate()
    private val staff = TestModelFactory.buildStaff(id = staffId)
    private val personId = PersonId()
    private val person = TestModelFactory.buildPerson(personId = personId, email = "<EMAIL>")
    private val personWithExternalEmail = TestModelFactory.buildPerson(email = "<EMAIL>")
    private val appointmentScheduleEventTypeId = RangeUUID.generate()
    private val appointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType(
        id = appointmentScheduleEventTypeId
    )
    private val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(
        staffId = staffId,
        personId = personId,
        endTime = LocalDateTime.now(),
        appointmentScheduleEventTypeId = appointmentScheduleEventTypeId
    )
    private val googleRefreshToken = "refresh_token"
    private val schedulePreference = TestModelFactory.buildSchedulePreference(
        weeklyHours = 10,
        intervalBetweenEvents = 10,
        staffId = staffId,
        googleRefreshToken = googleRefreshToken
    ).copy(hasStaffSchedules = true)
    private val staffSchedule = TestModelFactory.buildStaffSchedule(
        startHour = LocalTime.of(10, 0),
        untilHour = LocalTime.of(11, 0),
        staffId = staffId,
        weekDay = WeekDay.WEDNESDAY,
        type = StaffScheduleType.ADM
    )

    private val externalUpdatedAt = LocalDate.now().atStartOfDay()
    private val externalUpdatedAtZoned = externalUpdatedAt.atZone(ZoneId.of("UTC"))
    private val externalRecurrentEvent = TestModelFactory.buildExternalCalendarRecurrentEvent(
        staffId = staffId,
        staffScheduleId = staffSchedule.id
    )
    private val externalCalendarEvent = TestModelFactory.buildExternalCalendarEvent()

    private val event = buildBasicEvent()
        .setStatus("confirmed")
        .setId("SomeId")
    private val events = Events()
        .setNextPageToken("1")
        .setItems(mutableListOf(event))

    private val queryFilters = GoogleCalendarEventsQueryFilters(
        calendarId = "primary",
        showDeleted = true,
        maxResults = 10,
        singleEvents = false,
        syncToken = null,
        pageToken = "1"
    )

    private val googleCalendarQueryEventsEvent = GoogleCalendarQueryEventsEvent(
        staffId = staffId,
        queryFilters = queryFilters,
        refreshToken = googleRefreshToken
    )

    private val googleCalendarEvent =
        buildEvent(
            appointmentSchedule = appointmentSchedule,
            endDateTime = appointmentSchedule.endTime!!,
            staffName = staff.fullName,
            person = person,
            appointmentScheduleEventTypeDescription = appointmentScheduleEventType.description
        ).setStatus("confirmed").setId("SomeId")

    @AfterTest
    fun confirmMocks() = confirmVerified(
        externalCalendarEventService,
        schedulePreferenceService,
        staffService,
        kafkaProducerService,
        googleCalendarApi,
        personService,
        externalCalendarRecurrentEventService,
        appointmentScheduleService,
        appointmentScheduleEventTypeService,
        calendarService
    )

    @Test
    fun `#updateSchedulePreferenceWithLastQueryInformation returns updated schedule preference with google calendar sync data`() =
        runBlocking {
            val date = LocalDateTime.now()
            val nextSyncToken = googleRefreshToken

            val toUpdate = schedulePreference.copy(
                googleCalendarLastUpdated = date,
                googleNextSyncToken = nextSyncToken,
                isDoingFullSync = false
            )

            coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
            coEvery { schedulePreferenceService.update(toUpdate) } returns toUpdate

            val result = googleCalendarEventService.updateSchedulePreferenceWithLastQueryInformation(
                staffId = staffId,
                lastUpdatedTime = date,
                nextSyncToken = nextSyncToken
            )
            assertThat(result).isSuccessWithData(toUpdate)

            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyOnce { schedulePreferenceService.update(any()) }
        }

    @Test
    fun `#queryGoogleCalendarEvents should query first page of calendar events`() = runBlocking {
        val event = buildBasicEvent()
            .setStatus("confirmed")
            .setId("SomeId")
            .setAttendees(
                listOf(
                    EventAttendee().setEmail(person.email),
                    EventAttendee().setEmail(personWithExternalEmail.email)
                )
            )
        val expectedEvents = Events().setNextPageToken("1").setItems(mutableListOf(event))

        coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
        coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
        coEvery { staffService.get(staffId) } returns staff
        coEvery {
            calendarService.list("primary")
                .setShowDeleted(true)
                .setMaxResults(10)
                .setSingleEvents(false)
                .setPageToken("1")
                .execute()
        } returns expectedEvents
        coEvery {
            externalCalendarEventService.getByEventIdsAndStaffId(listOf("SomeId"), staffId)
        } returns emptyList()
        coEvery {
            externalCalendarRecurrentEventService.getByExternalIds(staffId, listOf("SomeId"))
        } returns emptyList()
        coEvery { personService.findByEmails(listOf("<EMAIL>")) } returns listOf(person)
        coEvery {
            kafkaProducerService.produce(
                GoogleCalendarEventCreationRequestedEvent(
                    GoogleCalendarEventPayloadConverter.convert(event, staff, listOf(person)),
                    staffId
                ),
                staffId.toString()
            )
        } returns mockk()
        coEvery { kafkaProducerService.produce(googleCalendarQueryEventsEvent) } returns mockk()

        val result = googleCalendarEventService.queryGoogleCalendarEvents(queryFilters, staffId, googleRefreshToken)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
        coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { calendarService.list(any()) }
        coVerifyOnce { externalCalendarEventService.getByEventIdsAndStaffId(any(), any()) }
        coVerifyOnce { externalCalendarRecurrentEventService.getByExternalIds(any(), any()) }
        coVerifyOnce { personService.findByEmails(any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#queryGoogleCalendarEvents should create event with recurrence that finishes in future`() = runBlocking {
        val event = buildPastEventWithRecurrenceThatFinishesInFuture()
            .setStatus("confirmed")
            .setId("SomeId")
            .setAttendees(
                listOf(
                    EventAttendee().setEmail(person.email),
                    EventAttendee().setEmail(personWithExternalEmail.email)
                )
            )
        val expectedEvents = Events().setNextPageToken("1").setItems(mutableListOf(event))

        coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
        coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
        coEvery { staffService.get(staffId) } returns staff
        coEvery {
            calendarService.list("primary")
                .setShowDeleted(true)
                .setMaxResults(10)
                .setSingleEvents(false)
                .setPageToken("1")
                .execute()
        } returns expectedEvents
        coEvery {
            externalCalendarEventService.getByEventIdsAndStaffId(listOf("SomeId"), staffId)
        } returns emptyList()
        coEvery {
            externalCalendarRecurrentEventService.getByExternalIds(staffId, listOf("SomeId"))
        } returns emptyList()
        coEvery { personService.findByEmails(listOf("<EMAIL>")) } returns listOf(person)
        coEvery {
            kafkaProducerService.produce(
                GoogleCalendarEventCreationRequestedEvent(
                    GoogleCalendarEventPayloadConverter.convert(event, staff, listOf(person)),
                    staffId
                ),
                staffId.toString()
            )
        } returns mockk()
        coEvery { kafkaProducerService.produce(googleCalendarQueryEventsEvent) } returns mockk()

        val result = googleCalendarEventService.queryGoogleCalendarEvents(queryFilters, staffId, googleRefreshToken)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
        coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { calendarService.list(any()) }
        coVerifyOnce { externalCalendarEventService.getByEventIdsAndStaffId(any(), any()) }
        coVerifyOnce { externalCalendarRecurrentEventService.getByExternalIds(any(), any()) }
        coVerifyOnce { personService.findByEmails(any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#queryGoogleCalendarEvents do nothing and return false when staff doesn't have schedules`() =
        runBlocking {

            val schedulePreference = schedulePreference.copy(hasStaffSchedules = false)

            val queryFilters = queryFilters.copy(pageToken = null)

            coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference

            val result = googleCalendarEventService.queryGoogleCalendarEvents(queryFilters, staffId, googleRefreshToken)
            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerify { googleCalendarApi wasNot called }
            coVerify { staffService wasNot called }
            coVerify { calendarService wasNot called }
            coVerify { externalCalendarEventService wasNot called }
            coVerify { externalCalendarRecurrentEventService wasNot called }
            coVerify { personService wasNot called }
            coVerify { kafkaProducerService wasNot called }
        }

    @Test
    fun `#queryGoogleCalendarEvents should create event with recurrence that doesn't have date to finish`() =
        runBlocking {
            val queryFilters = queryFilters.copy(pageToken = null)
            val googleCalendarQueryEventsEvent = GoogleCalendarQueryEventsEvent(
                staffId = staffId,
                queryFilters = queryFilters.copy(pageToken = "0"),
                refreshToken = googleRefreshToken
            )

            val event = buildPastEventWithRecurrenceThatFinishesDoesNotHaveUntilDate()
                .setStatus("confirmed")
                .setId("SomeId")
                .setAttendees(
                    listOf(
                        EventAttendee().setEmail(person.email),
                        EventAttendee().setEmail(personWithExternalEmail.email)
                    )
                )
            val expectedEvents = Events().setNextPageToken("0").setItems(mutableListOf(event))

            coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
            coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
            coEvery { staffService.get(staffId) } returns staff
            coEvery {
                calendarService.list("primary")
                    .setShowDeleted(true)
                    .setMaxResults(10)
                    .setSingleEvents(false)
                    .execute()
            } returns expectedEvents
            coEvery {
                externalCalendarEventService.getByEventIdsAndStaffId(listOf("SomeId"), staffId)
            } returns emptyList()
            coEvery {
                externalCalendarRecurrentEventService.getByExternalIds(staffId, listOf("SomeId"))
            } returns emptyList()
            coEvery { personService.findByEmails(listOf("<EMAIL>")) } returns listOf(person)
            coEvery {
                kafkaProducerService.produce(
                    GoogleCalendarEventCreationRequestedEvent(
                        GoogleCalendarEventPayloadConverter.convert(event, staff, listOf(person)),
                        staffId
                    ),
                    staffId.toString()
                )
            } returns mockk()
            coEvery { kafkaProducerService.produce(googleCalendarQueryEventsEvent) } returns mockk()

            val result = googleCalendarEventService.queryGoogleCalendarEvents(queryFilters, staffId, googleRefreshToken)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerifyOnce { calendarService.list(any()) }
            coVerifyOnce { externalCalendarEventService.getByEventIdsAndStaffId(any(), any()) }
            coVerifyOnce { externalCalendarRecurrentEventService.getByExternalIds(any(), any()) }
            coVerifyOnce { personService.findByEmails(any()) }
            coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
        }

    @Test
    fun `#queryGoogleCalendarEvents should use sync token when needed`() = runBlocking {
        val schedulePreference = schedulePreference.copy(googleNextSyncToken = "test")
        val queryFilters = queryFilters.copy(
            syncToken = googleRefreshToken,
            pageToken = null
        )
        val googleCalendarQueryEventsEvent = GoogleCalendarQueryEventsEvent(
            staffId = staffId,
            queryFilters = queryFilters.copy(pageToken = "1"),
            refreshToken = googleRefreshToken
        )

        coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
        coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
        coEvery { staffService.get(staffId) } returns staff
        coEvery {
            calendarService.list("primary")
                .setShowDeleted(true)
                .setMaxResults(10)
                .setSingleEvents(false)
                .setSyncToken("test")
                .execute()
        } returns events
        coEvery {
            externalCalendarEventService.getByEventIdsAndStaffId(listOf("SomeId"), staffId)
        } returns emptyList()
        coEvery {
            externalCalendarRecurrentEventService.getByExternalIds(staffId, listOf("SomeId"))
        } returns emptyList()
        coEvery {
            kafkaProducerService.produce(
                GoogleCalendarEventCreationRequestedEvent(
                    GoogleCalendarEventPayloadConverter.convert(event, staff, emptyList()),
                    staffId
                ),
                staffId.toString()
            )
        } returns mockk()
        coEvery { kafkaProducerService.produce(googleCalendarQueryEventsEvent) } returns mockk()

        val result = googleCalendarEventService.queryGoogleCalendarEvents(queryFilters, staffId, googleRefreshToken)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
        coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { calendarService.list(any()) }
        coVerifyOnce { externalCalendarEventService.getByEventIdsAndStaffId(any(), any()) }
        coVerifyOnce { externalCalendarRecurrentEventService.getByExternalIds(any(), any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#queryGoogleCalendarEvents should produce GoogleCalendarFinishedEventsQueryEvent on last page`() =
        runBlocking {
            val lastUpdatedTime = LocalDateTime.now()

            val googleCalendarEventCreationRequestedEvent = GoogleCalendarEventCreationRequestedEvent(
                GoogleCalendarEventPayloadConverter.convert(event, staff, emptyList()),
                staffId
            )

            val googleCalendarFinishedEventsQueryEvent = GoogleCalendarFinishedEventsQueryEvent(
                lastUpdatedTime,
                googleRefreshToken,
                staffId
            )

            mockLocalDateTime(lastUpdatedTime) {
                events.nextPageToken = null
                events.nextSyncToken = googleRefreshToken

                coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
                coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
                coEvery { staffService.get(staffId) } returns staff
                coEvery {
                    calendarService.list("primary")
                        .setShowDeleted(true)
                        .setMaxResults(10)
                        .setSingleEvents(false)
                        .setPageToken("1")
                        .execute()
                } returns events
                coEvery {
                    externalCalendarEventService.getByEventIdsAndStaffId(listOf("SomeId"), staffId)
                } returns emptyList()
                coEvery {
                    externalCalendarRecurrentEventService.getByExternalIds(staffId, listOf("SomeId"))
                } returns emptyList()
                coEvery {
                    kafkaProducerService.produce(
                        googleCalendarEventCreationRequestedEvent,
                        staffId.toString()
                    )
                } returns mockk()
                coEvery {
                    kafkaProducerService.produce(googleCalendarFinishedEventsQueryEvent)
                } returns mockk()

                val result =
                    googleCalendarEventService.queryGoogleCalendarEvents(queryFilters, staffId, googleRefreshToken)
                assertThat(result).isSuccessWithData(true)

                coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
                coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
                coVerifyOnce { staffService.get(any()) }
                coVerifyOnce { calendarService.list(any()) }
                coVerifyOnce { externalCalendarEventService.getByEventIdsAndStaffId(any(), any()) }
                coVerifyOnce { externalCalendarRecurrentEventService.getByExternalIds(any(), any()) }
                coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
            }
        }

    @Test
    fun `#queryGoogleCalendarEvents should use try resynchronize token when Google returns 410`() = runBlocking {
        val schedulePreference = schedulePreference.copy(googleNextSyncToken = "invalid-sync-token")
        val queryFilters = queryFilters.copy(
            syncToken = schedulePreference.googleNextSyncToken,
            pageToken = "page-token-1"
        )
        val exception: GoogleJsonResponseException = mockk()

        coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
        coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
        coEvery {
            calendarService.list(queryFilters.calendarId)
                .setShowDeleted(queryFilters.showDeleted)
                .setMaxResults(queryFilters.maxResults)
                .setSingleEvents(queryFilters.singleEvents)
                .setSyncToken(queryFilters.syncToken)
                .setPageToken(queryFilters.pageToken)
                .execute()
        } throws exception

        every { exception.message } returns "Already processed"
        every { exception.statusCode } returns 410

        coEvery {
            schedulePreferenceService.update(
                schedulePreference.copy(
                    googleNextSyncToken = null
                )
            )
        } returns schedulePreference
        coEvery {
            kafkaProducerService.produce(
                match { it: GoogleCalendarQueryEventsEvent ->
                    it.payload.alreadyRepublished
                            && it.payload.queryFilters.pageToken.isNullOrBlank()
                }
            )
        } returns mockk()

        val result = googleCalendarEventService.queryGoogleCalendarEvents(queryFilters, staffId, googleRefreshToken)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
        coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
        coVerifyOnce { schedulePreferenceService.update(any()) }
        coVerifyOnce { calendarService.list(any()) }
        coVerifyOnce { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#queryGoogleCalendarEvents shouldn't try resynchronize token when Google DOES NOT returns 410`() =
        runBlocking {
            val schedulePreference = schedulePreference.copy(googleNextSyncToken = "test")
            val queryFilters = queryFilters.copy(
                syncToken = googleRefreshToken,
                pageToken = null
            )

            val exception: GoogleJsonResponseException = mockk()

            coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
            coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
            coEvery {
                calendarService.list("primary")
                    .setShowDeleted(true)
                    .setMaxResults(10)
                    .setSingleEvents(false)
                    .setSyncToken("test")
                    .execute()
            } throws exception

            every { exception.message } returns "Bad Request"
            every { exception.statusCode } returns 400

            val result = googleCalendarEventService.queryGoogleCalendarEvents(queryFilters, staffId, googleRefreshToken)
            assertThat(result).isFailure()

            coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyOnce { calendarService.list(any()) }
        }

    @Test
    fun `#synchronize google agenda should produce event to start google calendar query`() = runBlocking {
        val queryFilters = GoogleCalendarEventsQueryFilters(
            calendarId = "primary",
            showDeleted = false,
            maxResults = 50,
            singleEvents = false,
            syncToken = null,
            pageToken = null
        )

        coEvery {
            kafkaProducerService.produce(
                GoogleCalendarQueryEventsEvent(
                    staffId = staffId,
                    queryFilters = queryFilters,
                    refreshToken = googleRefreshToken
                )
            )
        } returns mockk()

        val result = googleCalendarEventService.synchronizeGoogleCalendar(schedulePreference)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#getAndStoreRefreshToken should update schedule preference with refresh token`() = runBlocking {
        val schedulePreferenceUpdated = schedulePreference.copy(googleRefreshToken = "1")

        coEvery { staffService.findByEmail(staff.email) } returns staff
        coEvery { schedulePreferenceService.getOrCreate(staffId) } returns schedulePreference
        coEvery { googleCalendarApi.getRefreshToken("1") } returns googleTokenResponse
        coEvery { schedulePreferenceService.update(schedulePreferenceUpdated) } returns schedulePreferenceUpdated

        val result = googleCalendarEventService.getAndStoreRefreshToken("1", staff.email)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { staffService.findByEmail(any()) }
        coVerifyOnce { schedulePreferenceService.getOrCreate(any()) }
        coVerifyOnce { googleCalendarApi.getRefreshToken(any()) }
        coVerifyOnce { schedulePreferenceService.update(any()) }
    }

    @Test
    fun `#createOrUpdateEvent should upsert an external calendar event`() = mockLocalDateTime { date ->
        mockRangeUUID { uuid ->

            val event = buildBasicEvent()

            val eventPayload = GoogleCalendarEventPayload(
                status = event.status,
                startTime = event.start.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                endTime = event.end.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                id = event.id,
                externalUpdatedAt,
                transparency = ExternalEventTransparency.BUSY
            )

            val googleCalendarEvent = TestModelFactory.buildExternalCalendarEvent(
                id = uuid,
                externalEventId = event.id,
                staffId = staffId,
                status = ExternalEventStatus.CONFIRMED,
                startTime = event.start.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                endTime = event.end.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                externalUpdatedAt = LocalDate.now().atStartOfDay()
            ).copy(
                createdAt = date,
                updatedAt = date
            )

            coEvery {
                externalCalendarRecurrentEventService.getByExternalId(
                    googleCalendarEvent.staffId,
                    googleCalendarEvent.externalEventId,
                )
            } returns NotFoundException()
            coEvery { externalCalendarEventService.upsert(googleCalendarEvent) } returns googleCalendarEvent

            val result = googleCalendarEventService.createOrUpdateEvent(eventPayload, staffId)
            assertThat(result).isSuccessWithData(googleCalendarEvent)

            coVerifyOnce { externalCalendarEventService.upsert(any()) }
            coVerifyOnce { externalCalendarRecurrentEventService.getByExternalId(any(), any()) }
        }
    }

    @Test
    fun `#createOrUpdateEvent should upsert an external calendar event and remove existing external calendar recurrent event if there is one`() =
        mockLocalDateTime { date ->
            mockRangeUUID { uuid ->
                val event = buildBasicEvent()

                val eventPayload = GoogleCalendarEventPayload(
                    status = event.status,
                    startTime = event.start.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    endTime = event.end.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    id = event.id,
                    externalUpdatedAt,
                    transparency = ExternalEventTransparency.BUSY,
                )

                val googleCalendarEvent = TestModelFactory.buildExternalCalendarEvent(
                    id = uuid,
                    externalEventId = event.id,
                    staffId = staffId,
                    status = googleCalendarEventService.getGoogleStatus(event.status),
                    startTime = event.start.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    endTime = event.end.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    externalUpdatedAt = LocalDate.now().atStartOfDay()
                ).copy(
                    createdAt = date,
                    updatedAt = date
                )

                coEvery {
                    externalCalendarRecurrentEventService.getByExternalId(
                        googleCalendarEvent.staffId,
                        googleCalendarEvent.externalEventId,
                    )
                } returns externalRecurrentEvent

                coEvery {
                    externalCalendarRecurrentEventService.update(
                        externalRecurrentEvent.copy(
                            status = ExternalEventStatus.CANCELLED,
                            externalUpdatedAt = googleCalendarEvent.externalUpdatedAt,
                        ),
                        externalRecurrentEvent
                    )
                } returns externalRecurrentEvent.toModel()
                coEvery { externalCalendarEventService.upsert(googleCalendarEvent) } returns googleCalendarEvent

                val result = googleCalendarEventService.createOrUpdateEvent(eventPayload, staffId)
                assertThat(result).isSuccessWithData(googleCalendarEvent)

                coVerifyOnce { externalCalendarRecurrentEventService.update(any(), any()) }
                coVerifyOnce { externalCalendarEventService.upsert(any()) }
                coVerifyOnce { externalCalendarRecurrentEventService.getByExternalId(any(), any()) }
            }
        }

    @Test
    fun `#createOrUpdateEvent with recurrentEventId should add reference to recurrence and upsert an external calendar event`() =
        mockLocalDateTime { date ->
            mockRangeUUID { uuid ->
                val event = buildBasicEvent()

                val eventPayload = GoogleCalendarEventPayload(
                    status = event.status,
                    startTime = event.start.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    endTime = event.end.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    id = event.id,
                    externalUpdatedAt = externalUpdatedAt,
                    recurringEventId = "asd",
                    transparency = ExternalEventTransparency.BUSY,
                )

                val recurringEvent = TestModelFactory.buildExternalCalendarRecurrentEvent()
                val googleCalendarEvent = TestModelFactory.buildExternalCalendarEvent(
                    id = uuid,
                    externalEventId = event.id,
                    staffId = staffId,
                    status = googleCalendarEventService.getGoogleStatus(event.status),
                    startTime = event.start.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    endTime = event.end.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    externalUpdatedAt = LocalDate.now().atStartOfDay(),
                    externalCalendarRecurrentEventId = recurringEvent.id,
                ).copy(
                    createdAt = date,
                    updatedAt = date
                )

                coEvery {
                    externalCalendarRecurrentEventService.getByExternalId(staffId, eventPayload.recurringEventId!!)
                } returns recurringEvent
                coEvery {
                    externalCalendarRecurrentEventService.getByExternalId(staffId, googleCalendarEvent.externalEventId)
                } returns NotFoundException()

                coEvery { externalCalendarEventService.upsert(googleCalendarEvent) } returns googleCalendarEvent

                val result = googleCalendarEventService.createOrUpdateEvent(eventPayload, staffId)
                assertThat(result).isSuccessWithData(googleCalendarEvent)

                coVerifyOnce { externalCalendarEventService.upsert(any()) }
                coVerify(exactly = 2) { externalCalendarRecurrentEventService.getByExternalId(any(), any()) }
            }
        }

    @Test
    fun `#createOrUpdateEvent with recurrentEventId should upsert an external calendar event without reference to recurrent event if do not find one`() =
        mockLocalDateTime { date ->
            mockRangeUUID { uuid ->
                val event = buildBasicEvent()

                val eventPayload = GoogleCalendarEventPayload(
                    status = event.status,
                    startTime = event.start.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    endTime = event.end.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    id = event.id,
                    externalUpdatedAt = externalUpdatedAt,
                    recurringEventId = "asd",
                    transparency = ExternalEventTransparency.BUSY,
                )

                val googleCalendarEvent = TestModelFactory.buildExternalCalendarEvent(
                    id = uuid,
                    externalEventId = event.id,
                    staffId = staffId,
                    status = googleCalendarEventService.getGoogleStatus(event.status),
                    startTime = event.start.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    endTime = event.end.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    externalUpdatedAt = LocalDate.now().atStartOfDay(),
                ).copy(
                    createdAt = date,
                    updatedAt = date
                )

                coEvery {
                    externalCalendarRecurrentEventService.getByExternalId(staffId, eventPayload.recurringEventId!!)
                } returns NotFoundException()
                coEvery {
                    externalCalendarRecurrentEventService.getByExternalId(staffId, googleCalendarEvent.externalEventId)
                } returns NotFoundException()
                coEvery { externalCalendarEventService.upsert(googleCalendarEvent) } returns googleCalendarEvent

                val result = googleCalendarEventService.createOrUpdateEvent(eventPayload, staffId)
                assertThat(result).isSuccessWithData(googleCalendarEvent)

                coVerifyOnce { externalCalendarEventService.upsert(any()) }
                coVerify(exactly = 2) { externalCalendarRecurrentEventService.getByExternalId(any(), any()) }
            }
        }

    @Test
    fun `#createOrUpdateEvent a recurrent event should add recurrent event`() = mockLocalDateTime { date ->
        mockRangeUUID { uuid ->
            val event = buildBasicEvent()

            val eventPayload = GoogleCalendarEventPayload(
                status = event.status,
                startTime = event.start.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                endTime = event.end.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                id = event.id,
                externalUpdatedAt = externalUpdatedAt,
                recurrence = listOf("RRULE:FREQ=WEEKLY;UNTIL=20200316T025959Z;BYDAY=MO"),
                transparency = ExternalEventTransparency.BUSY,
            )

            val recurringEvent = TestModelFactory.buildExternalCalendarRecurrentEvent(
                id = uuid,
                recurrence = eventPayload.recurrence!!,
                staffId = staffId,
                status = googleCalendarEventService.getGoogleStatus(event.status),
                externalEventId = event.id,
                byDay = listOf(Weekday.MONDAY),
                untilDateTime = LocalDateTime.of(2020, 3, 16, 2, 59, 59)
            ).copy(
                firstEventStartTime = eventPayload.startTime,
                externalUpdatedAt = externalUpdatedAt,
                createdAt = date,
                updatedAt = date
            )

            coEvery {
                externalCalendarEventService.getByEventIdAndStaffId(event.id, staffId)
            } returns NotFoundException()

            coEvery {
                externalCalendarRecurrentEventService.upsert(match {

                    println("test $recurringEvent")
                    println("code $it")

                    it == recurringEvent
                })
            } returns recurringEvent

            val result = googleCalendarEventService.createOrUpdateEvent(eventPayload, staffId)
            assertThat(result).isSuccessWithData(recurringEvent)

            coVerifyOnce { externalCalendarEventService.getByEventIdAndStaffId(any(), any()) }
            coVerifyOnce { externalCalendarRecurrentEventService.upsert(any()) }
        }
    }

    @Test
    fun `#createOrUpdateEvent a recurrent event should add recurrent event and cancel existing external calendar event if there is one`() =
        mockLocalDateTime { date ->
            mockRangeUUID { uuid ->
                val event = buildBasicEvent()

                val eventPayload = GoogleCalendarEventPayload(
                    status = event.status,
                    startTime = event.start.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    endTime = event.end.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    id = event.id,
                    externalUpdatedAt = externalUpdatedAt,
                    recurrence = listOf("RRULE:FREQ=WEEKLY;UNTIL=20200316T025959Z;BYDAY=MO"),
                    transparency = ExternalEventTransparency.BUSY,
                )

                val recurringEvent = TestModelFactory.buildExternalCalendarRecurrentEvent(
                    id = uuid,
                    recurrence = eventPayload.recurrence!!,
                    staffId = staffId,
                    status = googleCalendarEventService.getGoogleStatus(event.status),
                    externalEventId = event.id,
                    byDay = listOf(Weekday.MONDAY),
                    untilDateTime = LocalDateTime.of(2020, 3, 16, 2, 59, 59)
                ).copy(
                    firstEventStartTime = eventPayload.startTime,
                    externalUpdatedAt = externalUpdatedAt,
                    createdAt = date,
                    updatedAt = date
                )

                val externalCalendarEventToUpdate = externalCalendarEvent.copy(
                    status = ExternalEventStatus.CANCELLED
                )

                coEvery {
                    externalCalendarEventService.getByEventIdAndStaffId(event.id, staffId)
                } returns externalCalendarEvent

                coEvery {
                    externalCalendarEventService.update(externalCalendarEventToUpdate)
                } returns externalCalendarEventToUpdate.toModel()

                coEvery {
                    externalCalendarRecurrentEventService.upsert(recurringEvent)
                } returns recurringEvent

                val result = googleCalendarEventService.createOrUpdateEvent(eventPayload, staffId)
                assertThat(result).isSuccessWithData(recurringEvent)

                coVerifyOnce { externalCalendarEventService.getByEventIdAndStaffId(any(), any()) }
                coVerifyOnce { externalCalendarEventService.update(any()) }
                coVerifyOnce { externalCalendarRecurrentEventService.upsert(any()) }
            }
        }

    @Test
    fun `#createOrUpdateEvent with empty recurrentEventId should upsert an external calendar event without call external calendar recurrent event service`() =
        mockLocalDateTime { date ->
            mockRangeUUID { uuid ->
                val event = buildBasicEvent()

                val eventPayload = GoogleCalendarEventPayload(
                    status = event.status,
                    startTime = event.start.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    endTime = event.end.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    id = event.id,
                    externalUpdatedAt = externalUpdatedAt,
                    recurringEventId = "   ",
                    transparency = ExternalEventTransparency.BUSY,
                )

                val googleCalendarEvent = TestModelFactory.buildExternalCalendarEvent(
                    id = uuid,
                    externalEventId = event.id,
                    staffId = staffId,
                    status = googleCalendarEventService.getGoogleStatus(event.status),
                    startTime = event.start.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    endTime = event.end.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                    externalUpdatedAt = LocalDate.now().atStartOfDay(),
                ).copy(
                    createdAt = date,
                    updatedAt = date
                )

                coEvery {
                    externalCalendarRecurrentEventService.getByExternalId(
                        googleCalendarEvent.staffId,
                        googleCalendarEvent.externalEventId,
                    )
                } returns NotFoundException()

                coEvery {
                    externalCalendarEventService.upsert(googleCalendarEvent)
                } returns googleCalendarEvent

                val result = googleCalendarEventService.createOrUpdateEvent(eventPayload, staffId)
                assertThat(result).isSuccessWithData(googleCalendarEvent)

                coVerifyOnce { externalCalendarRecurrentEventService.getByExternalId(any(), any()) }
                coVerifyOnce { externalCalendarEventService.upsert(any()) }
            }
        }


    @Test
    fun `#getAndStoreUserAgenda should not produce kafka event to synchronize agendas if it is doing full sync`() =
        runBlocking {
            coEvery {
                schedulePreferenceService.getByStaffId(staffId)
            } returns schedulePreference.copy(isDoingFullSync = true)

            val getGoogleAgendaResponse = googleCalendarEventService.getAndStoreUserGoogleCalendarEvents(staffId)
            assertThat(getGoogleAgendaResponse).isSuccessWithData(true)

            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
        }


    @Test
    fun `#getAndStoreUserAgenda should produce kafka event to synchronize agendas`() = runBlocking {
        val expected = schedulePreference.copy(
            isDoingFullSync = true,
            googleNextSyncToken = null
        )
        coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
        coEvery { schedulePreferenceService.update(expected) } returns expected
        coEvery {
            kafkaProducerService.produce(GoogleCalendarSynchronizationRequestedEvent(expected))
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(GoogleCalendarWebhookSubscriptionRequestedEvent(expected))
        } returns mockk()

        val getGoogleAgendaResponse = googleCalendarEventService.getAndStoreUserGoogleCalendarEvents(staffId)
        assertThat(getGoogleAgendaResponse).isSuccessWithData(true)

        coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
        coVerifyOnce { schedulePreferenceService.update(any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#registerWebhookChannel should call google calendar API to register webhook`() = mockRangeUUID { uuid ->
        val channel = Channel()
            .setAddress("${ServiceConfig.SchedulerBffApi.baseUrl()}/webhooks/google/calendar/event_updates")
            .setId(uuid.toString())
            .setType("web_hook")

        val channelWithExpiration = Channel()
            .setExpiration(LocalDateTime.now().toEpochSecond(ZoneOffset.UTC))
            .setId(RangeUUID.generate().toString())

        val toUpdate = schedulePreference.copy(
            googleCalendarWebhookExpiration = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(channelWithExpiration.expiration),
                ZoneId.of("UTC")
            ),
            googleCalendarWebhookChannelId = channel.id.toUUID()
        )

        coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
        coEvery { calendarService.watch("primary", channel).execute() } returns channelWithExpiration
        coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
        coEvery { schedulePreferenceService.update(toUpdate) } returns schedulePreference

        val result = googleCalendarEventService.registerWebhookChannel(schedulePreference)
        assertThat(result).isSuccessWithData(schedulePreference)

        coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
        coVerifyOnce { calendarService.watch(any(), any()) }
        coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
        coVerifyOnce { schedulePreferenceService.update(any()) }
    }

    @Test
    fun `#createOrUpdateEventFromCalendarWebhook should get google calendar event using calendar API and produce kafka event to create or update the event`() =
        runBlocking {
            val eventNotification = GoogleCalendarEventWebhookNotification(
                channelId = staffId,
                resourceId = "event_id",
                resourceUri = "google.com/events"
            )

            val queryFilters = GoogleCalendarEventsQueryFilters(
                calendarId = "primary",
                showDeleted = false,
                maxResults = 50,
                singleEvents = false,
                syncToken = null,
                pageToken = null
            )

            coEvery {
                schedulePreferenceService.getByGoogleCalendarWebhookChannelId(eventNotification.channelId)
            } returns schedulePreference

            coEvery {
                kafkaProducerService.produce(
                    GoogleCalendarQueryEventsEvent(
                        staffId = staffId,
                        queryFilters = queryFilters,
                        refreshToken = googleRefreshToken
                    )
                )
            } returns mockk()

            val result = googleCalendarEventService.createOrUpdateEventFromCalendarWebhook(eventNotification)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { schedulePreferenceService.getByGoogleCalendarWebhookChannelId(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#createOrUpdateEventFromCalendarWebhook should not  get google calendar event when a schedule full sync is already running`() =
        runBlocking {
            val eventNotification = GoogleCalendarEventWebhookNotification(
                channelId = staffId,
                resourceId = "event_id",
                resourceUri = "google.com/events"
            )

            coEvery {
                schedulePreferenceService.getByGoogleCalendarWebhookChannelId(eventNotification.channelId)
            } returns schedulePreference.copy(isDoingFullSync = true)

            val result = googleCalendarEventService.createOrUpdateEventFromCalendarWebhook(eventNotification)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { schedulePreferenceService.getByGoogleCalendarWebhookChannelId(any()) }
            coVerify { kafkaProducerService wasNot called }
        }

    @Test
    fun `#renewWebhookChannelsCloseToExpiration should produce kafka event to renew webhook subscription`() =
        runBlocking {
            val date = LocalDate.now()

            coEvery {
                schedulePreferenceService.getByGoogleCalendarWebhookCloseToExpiration(date)
            } returns listOf(schedulePreference)
            coEvery {
                kafkaProducerService.produce(GoogleCalendarWebhookSubscriptionRequestedEvent(schedulePreference))
            } returns mockk()

            val result = googleCalendarEventService.renewWebhookChannelsCloseToExpiration(date)
            assertThat(result).isSuccessWithData(listOf(true))

            coVerifyOnce { schedulePreferenceService.getByGoogleCalendarWebhookCloseToExpiration(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#getFutureGoogleCalendarEventsForRecurrence should query instances of events of the recurrence`() =
        runBlocking {
            val localDateTime = LocalDateTime.now()

            val from = DateTime(Date.from(ZonedDateTime.of(localDateTime, ZoneId.of("UTC")).toInstant()))
            val until = DateTime(Date.from(ZonedDateTime.of(localDateTime, ZoneId.of("UTC")).toInstant()))

            val externalCalendarRecurrentEvent =
                TestModelFactory.buildExternalCalendarRecurrentEvent(staffId = staffId)

            val queryFilters = GoogleCalendarEventsQueryFilters(
                calendarId = "primary",
                showDeleted = true,
                maxResults = 10,
                singleEvents = false,
                timeMin = from,
                timeMax = until,
                recurringEventId = externalCalendarRecurrentEvent.externalEventId
            )

            coEvery {
                kafkaProducerService.produce(
                    GoogleCalendarEventsFromRecurrenceRequestedEvent(
                        staffId = staffId,
                        queryFilters = queryFilters
                    )
                )
            } returns mockk()

            val result = googleCalendarEventService.getFutureGoogleCalendarEventsForRecurrence(
                externalCalendarRecurrentEvent,
                localDateTime,
                localDateTime
            )
            assertThat(result).isSuccessWithData(externalCalendarRecurrentEvent)

            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#queryGoogleCalendarEventsForRecurrence returns true and drop flow when staff not is a health professional`() =
        runBlocking {
            val staff = staff.copy(role = Role.OPS)

            val queryFilters = GoogleCalendarEventsQueryFilters(
                calendarId = "primary",
                showDeleted = true,
                maxResults = 10,
                singleEvents = false,
                timeMin = DateTime(System.currentTimeMillis()),
                timeMax = DateTime(System.currentTimeMillis()),
                recurringEventId = externalRecurrentEvent.externalEventId,
            )

            coEvery { schedulePreferenceService.getByStaffId(staffId = staffId) } returns schedulePreference
            coEvery { staffService.get(staffId) } returns staff

            val result = googleCalendarEventService.queryGoogleCalendarEventsForRecurrence(queryFilters, staffId)
            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerify { googleCalendarApi wasNot called }
            coVerify { calendarService wasNot called }
            coVerify { externalCalendarEventService wasNot called }
            coVerify { externalCalendarRecurrentEventService wasNot called }
            coVerify { kafkaProducerService wasNot called }
        }

    @Test
    fun `#queryGoogleCalendarEventsForRecurrence should query first page of calendar events`() = runBlocking {
        val event = buildBasicEvent()
            .setStatus("confirmed")
            .setId("SomeId")
        val expectedEvents = Events()
            .setNextPageToken("1")
            .setItems(mutableListOf(event))

        val now = DateTime(System.currentTimeMillis())
        val untilZonedDateTime = ZonedDateTime.now(ZoneId.of("UTC")).plusDays(90)
        val until = DateTime(Date.from(untilZonedDateTime.toInstant()))

        val queryFilters = GoogleCalendarEventsQueryFilters(
            calendarId = "primary",
            showDeleted = true,
            maxResults = 10,
            singleEvents = false,
            timeMin = now,
            timeMax = until,
            recurringEventId = externalRecurrentEvent.externalEventId,
        )

        coEvery { schedulePreferenceService.getByStaffId(staffId = staffId) } returns schedulePreference
        coEvery { staffService.get(staffId) } returns staff
        coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
        coEvery {
            calendarService.instances("primary", externalRecurrentEvent.externalEventId)
                .setShowDeleted(queryFilters.showDeleted)
                .setMaxResults(queryFilters.maxResults)
                .setTimeMin(queryFilters.timeMin)
                .setTimeMax(queryFilters.timeMax)
                .execute()
        } returns expectedEvents
        coEvery {
            externalCalendarEventService.getByEventIdsAndStaffId(listOf("SomeId"), staffId)
        } returns emptyList()
        coEvery {
            externalCalendarRecurrentEventService.getByExternalIds(staffId, listOf("SomeId"))
        } returns emptyList()

        coEvery {
            kafkaProducerService.produce(
                GoogleCalendarEventCreationRequestedEvent(
                    GoogleCalendarEventPayloadConverter.convert(event, staff, emptyList()),
                    staffId
                ),
                staffId.toString()
            )
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(
                GoogleCalendarEventsFromRecurrenceRequestedEvent(
                    staffId,
                    queryFilters.copy(pageToken = "1")
                )
            )
        } returns mockk()

        val result = googleCalendarEventService.queryGoogleCalendarEventsForRecurrence(queryFilters, staffId)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
        coVerifyOnce { calendarService.instances(any(), any()) }
        coVerifyOnce { externalCalendarEventService.getByEventIdsAndStaffId(any(), any()) }
        coVerifyOnce { externalCalendarRecurrentEventService.getByExternalIds(any(), any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#queryGoogleCalendarEventsForRecurrence should do nothing of related schedule preference is inactive`() =
        runBlocking {
            val now = DateTime(System.currentTimeMillis())
            val untilZonedDateTime = ZonedDateTime.now(ZoneId.of("UTC")).plusDays(90)
            val until = DateTime(Date.from(untilZonedDateTime.toInstant()))

            val queryFilters = GoogleCalendarEventsQueryFilters(
                calendarId = "primary",
                showDeleted = true,
                maxResults = 10,
                singleEvents = false,
                timeMin = now,
                timeMax = until,
                recurringEventId = externalRecurrentEvent.externalEventId,
            )

            coEvery {
                schedulePreferenceService.getByStaffId(staffId)
            } returns schedulePreference.copy(status = Status.INACTIVE)

            coEvery { staffService.get(staffId) } returns staff

            val result = googleCalendarEventService.queryGoogleCalendarEventsForRecurrence(queryFilters, staffId)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyOnce { staffService.get(any()) }
        }

    @Test
    fun `#queryGoogleCalendarEventsForRecurrence do nothing when staff is inactive`() =
        runBlocking {
            val staff = staff.copy(active = false)

            val queryFilters = GoogleCalendarEventsQueryFilters(
                calendarId = "primary",
                showDeleted = true,
                maxResults = 10,
                singleEvents = false
            )

            coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
            coEvery { staffService.get(staffId) } returns staff

            val result = googleCalendarEventService.queryGoogleCalendarEventsForRecurrence(queryFilters, staffId)
            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyOnce { staffService.get(any()) }
        }

    @Test
    fun `#queryGoogleCalendarEventsForRecurrence do nothing when staff does not have staff schedules and not doing full sync`() =
        runBlocking {
            val queryFilters = GoogleCalendarEventsQueryFilters(
                calendarId = "primary",
                showDeleted = true,
                maxResults = 10,
                singleEvents = false
            )

            val schedulePreferenceWithoutSchedule = schedulePreference.copy(hasStaffSchedules = false)
            coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreferenceWithoutSchedule

            val result = googleCalendarEventService.queryGoogleCalendarEventsForRecurrence(queryFilters, staffId)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyNone {
                staffService.get(any())
                externalCalendarEventService.getByEventIdsAndStaffId(any(), any())
                externalCalendarRecurrentEventService.getByExternalIds(any(), any())
                googleCalendarApi.getCalendarService(any())
                calendarService.instances(any(), any())
                kafkaProducerService.produce(any(), any())
            }
        }

    @Test
    fun `#reFetchFutureGoogleCalendarEventsForRecurrence should reFetch future events for recurrence to update their status`() =
        runBlocking {
            val externalCalendarEvent = TestModelFactory.buildExternalCalendarEvent(staffId = staffId)

            val event = buildBasicEvent()

            coEvery {
                schedulePreferenceService.getByStaffId(externalCalendarEvent.staffId)
            } returns schedulePreference
            coEvery {
                externalCalendarEventService.getFutureEventsForRecurrence(externalRecurrentEvent.id)
            } returns listOf(externalCalendarEvent)
            coEvery { staffService.get(staffId) } returns staff
            coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
            coEvery {
                calendarService.get("primary", externalCalendarEvent.externalEventId).execute()
            } returns event
            coEvery {
                kafkaProducerService.produce(
                    GoogleCalendarEventCreationRequestedEvent(
                        GoogleCalendarEventPayloadConverter.convert(event, staff, emptyList()),
                        staffId
                    ),
                    externalCalendarEvent.staffId.toString()
                )
            } returns mockk()

            val result =
                googleCalendarEventService.reFetchFutureGoogleCalendarEventsForRecurrence(externalRecurrentEvent)
            assertThat(result).isSuccessWithData(listOf(externalCalendarEvent))

            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyOnce { externalCalendarEventService.getFutureEventsForRecurrence(any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
            coVerifyOnce { calendarService.get(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(any(), any()) }
        }

    @Test
    fun `#reFetchFutureGoogleCalendarEventsForRecurrence should reFetch future events for recurrence to update their status and cancel if it is not found`() =
        mockLocalDateTime { date ->
            val externalCalendarEvent = TestModelFactory.buildExternalCalendarEvent(staffId = staffId)

            val googleCalendarRecurringEventCreationRequestedEvent = GoogleCalendarRecurringEventCreationRequestedEvent(
                GoogleCalendarEventPayload.fromExternalCalendarEvent(
                    externalCalendarEvent.copy(status = ExternalEventStatus.CANCELLED, externalUpdatedAt = date),
                    externalRecurrentEvent.externalEventId
                ),
                staffId
            )

            val exception: GoogleJsonResponseException = mockk()
            every { exception.statusCode } returns 404
            every { exception.message } returns ""
            every { exception.printStackTrace(any<PrintWriter>()) } returns Unit

            coEvery {
                schedulePreferenceService.getByStaffId(externalCalendarEvent.staffId)
            } returns schedulePreference
            coEvery {
                externalCalendarEventService.getFutureEventsForRecurrence(externalRecurrentEvent.id)
            } returns listOf(externalCalendarEvent)
            coEvery { staffService.get(staffId) } returns staff
            coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
            coEvery {
                calendarService.get("primary", externalCalendarEvent.externalEventId).execute()
            } throws exception
            coEvery {
                kafkaProducerService.produce(
                    googleCalendarRecurringEventCreationRequestedEvent,
                    staffId.toString()
                )
            } returns mockk()

            val result =
                googleCalendarEventService.reFetchFutureGoogleCalendarEventsForRecurrence(externalRecurrentEvent)
            assertThat(result).isSuccessWithData(listOf(externalCalendarEvent))

            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyOnce { externalCalendarEventService.getFutureEventsForRecurrence(any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
            coVerifyOnce { calendarService.get(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(any(), any()) }
        }

    @Test
    fun `#reFetchRecurrentEvent should reFetch recurrent event`() =
        runBlocking {
            val event = buildBasicEvent()

            coEvery { schedulePreferenceService.getByStaffId(staffId = staffId) } returns schedulePreference
            coEvery { staffService.get(staffId) } returns staff
            coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
            coEvery {
                calendarService.get("primary", externalRecurrentEvent.externalEventId).execute()
            } returns event

            coEvery {
                kafkaProducerService.produce(
                    GoogleCalendarEventCreationRequestedEvent(
                        GoogleCalendarEventPayloadConverter.convert(event, staff, emptyList()),
                        staffId
                    ),
                    externalRecurrentEvent.staffId.toString()
                )
            } returns mockk()

            val result = googleCalendarEventService.reFetchRecurrentEvent(externalRecurrentEvent)
            assertThat(result).isSuccessWithData(externalRecurrentEvent)

            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
            coVerifyOnce { calendarService.get(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(any(), any()) }
        }

    @Test
    fun `#reFetchRecurrentEvent should not reFetch recurrent event when event is invalid`() =
        runBlocking {
            val now = DateTime(System.currentTimeMillis())
            val updated = DateTime(Date.from(externalUpdatedAtZoned.toInstant()))
            val eventWithoutEndTime = Event()
                .setId("1")
                .setStart(EventDateTime().setDateTime(now))
                .setStatus("invalid")
                .setUpdated(updated)

            coEvery { schedulePreferenceService.getByStaffId(staffId = staffId) } returns schedulePreference
            coEvery { staffService.get(staffId) } returns staff
            coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
            coEvery {
                calendarService.get("primary", externalRecurrentEvent.externalEventId).execute()
            } returns eventWithoutEndTime

            val result = googleCalendarEventService.reFetchRecurrentEvent(externalRecurrentEvent)
            assertThat(result).isSuccessWithData(externalRecurrentEvent)

            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
            coVerifyOnce { calendarService.get(any(), any()) }
            coVerifyNone { kafkaProducerService.produce(any(), any()) }
        }

    @Test
    fun `#getFutureGoogleCalendarEventsActiveRecurrencesForOneDay get count for events and produce pages to query`() =
        runBlocking {
            val referenceDate = LocalDate.now()

            val externalCalendarRecurrentEvent = TestModelFactory.buildExternalCalendarRecurrentEvent(staffId = staffId)

            coEvery {
                externalCalendarRecurrentEventService.countActiveForWeekdayWithUntilDate(
                    Weekday.get(referenceDate.dayOfWeek.toString().substring(0, 2))!!
                )
            } returns 60

            coEvery {
                externalCalendarRecurrentEventService.countActiveForWeekdayWithoutUntilDate(
                    Weekday.get(referenceDate.dayOfWeek.toString().substring(0, 2))!!
                )
            } returns 10

            coEvery {
                externalCalendarRecurrentEventService.countActiveWithoutUntilDateWithoutWeekday()
            } returns 10

            coEvery {
                externalCalendarRecurrentEventService.countActiveWithUntilDateWithoutWeekday()
            } returns 10

            coEvery { schedulePreferenceService.getByStaffId(externalCalendarRecurrentEvent.staffId) } returns schedulePreference

            coEvery { kafkaProducerService.produce(any()) } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    GoogleCalendarFetchInstancesForRecurrentEventsForPageEvent(
                        referenceDate = referenceDate,
                        page = 0,
                        type = FetchInstancesForRecurrentEventsType.WITH_UNTIL_DATE_WITHOUT_WEEKDAY
                    )
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    GoogleCalendarFetchInstancesForRecurrentEventsForPageEvent(
                        referenceDate = referenceDate,
                        page = 0,
                        type = FetchInstancesForRecurrentEventsType.WITHOUT_UNTIL_DATE_WITHOUT_WEEKDAY
                    )
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    GoogleCalendarFetchInstancesForRecurrentEventsForPageEvent(
                        referenceDate = referenceDate,
                        page = 0,
                        type = FetchInstancesForRecurrentEventsType.FOR_WEEKDAY_WITH_UNTIL_DATE
                    )
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    GoogleCalendarFetchInstancesForRecurrentEventsForPageEvent(
                        referenceDate = referenceDate,
                        page = 1,
                        type = FetchInstancesForRecurrentEventsType.FOR_WEEKDAY_WITH_UNTIL_DATE
                    )
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    GoogleCalendarFetchInstancesForRecurrentEventsForPageEvent(
                        referenceDate = referenceDate,
                        page = 0,
                        type = FetchInstancesForRecurrentEventsType.FOR_WEEKDAY_WITHOUT_UNTIL_DATE
                    )
                )
            } returns mockk()

            val result =
                googleCalendarEventService.getFutureGoogleCalendarEventsActiveRecurrencesForOneDay(referenceDate)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { externalCalendarRecurrentEventService.countActiveForWeekdayWithUntilDate(any()) }
            coVerifyOnce { externalCalendarRecurrentEventService.countActiveWithUntilDateWithoutWeekday() }
            coVerifyOnce { externalCalendarRecurrentEventService.countActiveForWeekdayWithoutUntilDate(any()) }
            coVerifyOnce { externalCalendarRecurrentEventService.countActiveWithoutUntilDateWithoutWeekday() }
            coVerify(exactly = 5) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#getFutureGoogleCalendarEventsActiveRecurrencesForOneDayForPage should get events for weekday with until date`() =
        runBlocking {
            val referenceDate = LocalDate.now()

            val externalCalendarRecurrentEvent =
                TestModelFactory.buildExternalCalendarRecurrentEvent(staffId = staffId)

            coEvery {
                externalCalendarRecurrentEventService.getActiveForWeekdayWithUntilDate(
                    Weekday.get(referenceDate.dayOfWeek.toString().substring(0, 2))!!,
                    0,
                    50,
                )
            } returns listOf(externalCalendarRecurrentEvent)
            coEvery {
                kafkaProducerService.produce(GoogleCalendarEventsFromRecurrenceRequestedEvent(staffId, queryFilters))
            } returns mockk()

            val result =
                googleCalendarEventService.getFutureGoogleCalendarEventsActiveRecurrencesForOneDayForPage(
                    referenceDate,
                    page = 0,
                    type = FetchInstancesForRecurrentEventsType.FOR_WEEKDAY_WITH_UNTIL_DATE
                )
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { externalCalendarRecurrentEventService.getActiveForWeekdayWithUntilDate(any(), any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#getFutureGoogleCalendarEventsActiveRecurrencesForOneDayForPage should get events for weekday without until date`() =
        runBlocking {
            val referenceDate = LocalDate.now()

            val externalCalendarRecurrentEvent =
                TestModelFactory.buildExternalCalendarRecurrentEvent(staffId = staffId)

            coEvery {
                externalCalendarRecurrentEventService.getActiveForWeekdayWithoutUntilDate(
                    Weekday.get(referenceDate.dayOfWeek.toString().substring(0, 2))!!,
                    0,
                    50
                )
            } returns listOf(externalCalendarRecurrentEvent)
            coEvery {
                kafkaProducerService.produce(GoogleCalendarEventsFromRecurrenceRequestedEvent(staffId, queryFilters))
            } returns mockk()

            val result =
                googleCalendarEventService.getFutureGoogleCalendarEventsActiveRecurrencesForOneDayForPage(
                    referenceDate,
                    page = 0,
                    type = FetchInstancesForRecurrentEventsType.FOR_WEEKDAY_WITHOUT_UNTIL_DATE
                )
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce {
                externalCalendarRecurrentEventService.getActiveForWeekdayWithoutUntilDate(
                    any(),
                    any(),
                    any()
                )
            }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#getFutureGoogleCalendarEventsActiveRecurrencesForOneDayForPage should get events with until date without weekday`() =
        runBlocking {
            val referenceDate = LocalDate.now()

            val externalCalendarRecurrentEvent = TestModelFactory.buildExternalCalendarRecurrentEvent(staffId = staffId)

            coEvery {
                externalCalendarRecurrentEventService.getActiveWithUntilDateWithoutWeekday(
                    0,
                    50,
                )
            } returns listOf(externalCalendarRecurrentEvent)
            coEvery {
                kafkaProducerService.produce(GoogleCalendarEventsFromRecurrenceRequestedEvent(staffId, queryFilters))
            } returns mockk()

            val result =
                googleCalendarEventService.getFutureGoogleCalendarEventsActiveRecurrencesForOneDayForPage(
                    referenceDate,
                    page = 0,
                    type = FetchInstancesForRecurrentEventsType.WITH_UNTIL_DATE_WITHOUT_WEEKDAY
                )
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { externalCalendarRecurrentEventService.getActiveWithUntilDateWithoutWeekday(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#getFutureGoogleCalendarEventsActiveRecurrencesForOneDayForPage should get events without until date without weekday`() =
        runBlocking {
            val referenceDate = LocalDate.now()

            val externalCalendarRecurrentEvent = TestModelFactory.buildExternalCalendarRecurrentEvent(staffId = staffId)

            coEvery {
                externalCalendarRecurrentEventService.getActiveWithoutUntilDateWithoutWeekday(
                    0,
                    50,
                )
            } returns listOf(externalCalendarRecurrentEvent)
            coEvery {
                kafkaProducerService.produce(GoogleCalendarEventsFromRecurrenceRequestedEvent(staffId, queryFilters))
            } returns mockk()

            val result =
                googleCalendarEventService.getFutureGoogleCalendarEventsActiveRecurrencesForOneDayForPage(
                    referenceDate,
                    page = 0,
                    type = FetchInstancesForRecurrentEventsType.WITHOUT_UNTIL_DATE_WITHOUT_WEEKDAY
                )
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { externalCalendarRecurrentEventService.getActiveWithoutUntilDateWithoutWeekday(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#queryGoogleCalendarEventsForRecurrence should query other page of calendar events`() = runBlocking {
        val now = DateTime(System.currentTimeMillis())
        val untilZonedDateTime = ZonedDateTime.now(ZoneId.of("UTC")).plusDays(90)
        val until = DateTime(Date.from(untilZonedDateTime.toInstant()))

        val queryFilters = GoogleCalendarEventsQueryFilters(
            calendarId = "primary",
            showDeleted = true,
            maxResults = 10,
            singleEvents = false,
            timeMin = now,
            timeMax = until,
            recurringEventId = externalRecurrentEvent.externalEventId,
            pageToken = googleRefreshToken
        )

        coEvery { schedulePreferenceService.getByStaffId(staffId = staffId) } returns schedulePreference
        coEvery { staffService.get(staffId) } returns staff
        coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
        coEvery {
            calendarService.instances("primary", externalRecurrentEvent.externalEventId)
                .setShowDeleted(queryFilters.showDeleted)
                .setMaxResults(queryFilters.maxResults)
                .setTimeMin(queryFilters.timeMin)
                .setTimeMax(queryFilters.timeMax)
                .setPageToken(googleRefreshToken)
                .execute()
        } returns events
        coEvery {
            externalCalendarEventService.getByEventIdsAndStaffId(listOf("SomeId"), staffId)
        } returns emptyList()
        coEvery {
            externalCalendarRecurrentEventService.getByExternalIds(staffId, listOf("SomeId"))
        } returns emptyList()
        coEvery {
            kafkaProducerService.produce(
                GoogleCalendarEventCreationRequestedEvent(
                    GoogleCalendarEventPayloadConverter.convert(event, staff, emptyList()),
                    staffId
                ),
                staffId.toString()
            )
        } returns mockk()
        coEvery {
            kafkaProducerService.produce(
                GoogleCalendarEventsFromRecurrenceRequestedEvent(
                    staffId = staffId,
                    queryFilters = queryFilters.copy(pageToken = "1")
                )
            )
        } returns mockk()

        val result = googleCalendarEventService.queryGoogleCalendarEventsForRecurrence(queryFilters, staffId)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
        coVerifyOnce { calendarService.instances(any(), any()) }
        coVerifyOnce { externalCalendarEventService.getByEventIdsAndStaffId(any(), any()) }
        coVerifyOnce { externalCalendarRecurrentEventService.getByExternalIds(any(), any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any(), any()) }
    }

    @Test
    fun `#createRecurrentEventAtGoogleCalendar should create recurrent event based on staff schedule definition`() =
        runBlocking {
            val date = LocalDate.of(1971, 1, 1)

            mockLocalDate(date) {
                val extendedProperties = Event.ExtendedProperties()
                    .setPrivate(
                        mapOf(
                            "staffScheduleId" to staffSchedule.id.toString(),
                            "staffScheduleType" to staffSchedule.type.toString()
                        )
                    )
                val event = Event()
                    .setExtendedProperties(extendedProperties)
                    .setStart(
                        EventDateTime().setDateTime(
                            LocalDateTime.of(1971, 1, 6, 10, 0, 0).toGoogleLocalTime()
                        ).setTimeZone("UTC")
                    )
                    .setEnd(
                        EventDateTime().setDateTime(
                            LocalDateTime.of(1971, 1, 6, 11, 0, 0).toGoogleLocalTime()
                        ).setTimeZone("UTC")
                    )
                    .setSummary("ADM")
                    .setRecurrence(
                        listOf(
                            RecurrenceRule(
                                byDay = listOf(Weekday.valueOf(staffSchedule.weekDay.toString())),
                                frequency = RecurrenceRuleFrequency.WEEKLY,
                                interval = 1,
                            ).toString()
                        )
                    )

                coEvery {
                    schedulePreferenceService.getByStaffId(staffId)
                } returns schedulePreference.copy(hasStaffSchedules = false, isDoingFullSync = true)
                coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
                coEvery { calendarService.insert("primary", event).setSendUpdates("all").execute() } returns event

                val result = googleCalendarEventService.createRecurrentEventAtGoogleCalendar(staffSchedule)
                assertThat(result).isSuccessWithData(true)

                coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
                coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
                coVerifyOnce { calendarService.insert(any(), any()) }
            }
        }

    @Test
    fun `#createRecurrentEventAtGoogleCalendar should create recurrent event based on staff schedule definition that end on next day if staff schedule passes day`() =
        runBlocking {

            val date = LocalDate.of(1971, 1, 1)

            mockLocalDate(date) {
                val extendedProperties = Event.ExtendedProperties()
                    .setPrivate(
                        mapOf(
                            "staffScheduleId" to staffSchedule.id.toString(),
                            "staffScheduleType" to staffSchedule.type.toString()
                        )
                    )
                val event = Event()
                    .setExtendedProperties(extendedProperties)
                    .setStart(
                        EventDateTime().setDateTime(
                            LocalDateTime.of(1971, 1, 6, 10, 0, 0).toGoogleLocalTime()
                        ).setTimeZone("UTC")
                    )
                    .setEnd(
                        EventDateTime().setDateTime(
                            LocalDateTime.of(1971, 1, 7, 2, 0, 0).toGoogleLocalTime()
                        ).setTimeZone("UTC")
                    )
                    .setSummary("ADM")
                    .setRecurrence(
                        listOf(
                            RecurrenceRule(
                                byDay = listOf(Weekday.valueOf(staffSchedule.weekDay.toString())),
                                frequency = RecurrenceRuleFrequency.WEEKLY,
                                interval = 1,
                            ).toString()
                        )
                    )

                coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
                coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
                coEvery { calendarService.insert("primary", event).setSendUpdates("all").execute() } returns event

                val result = googleCalendarEventService.createRecurrentEventAtGoogleCalendar(
                    staffSchedule.copy(untilHour = LocalTime.of(2, 0))
                )
                assertThat(result).isSuccessWithData(true)

                coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
                coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
                coVerifyOnce { calendarService.insert(any(), any()) }
            }
        }

    @Test
    fun `#stopRecurrenceOfGoogleCalendarEvent should stop recurrence from google calendar event`() =
        runBlocking {

            val currentDate = LocalDateTime.of(1971, 1, 1, 10, 0, 0)

            mockLocalDateTime(currentDate) {
                val event = buildBasicEvent().setRecurrence(
                    listOf(
                        RecurrenceRule.fromGoogleRecurrence(listOf("RRULE:FREQ=WEEKLY;BYDAY=WE;INTERVAL=2")).get()
                            .toString()
                    )
                )
                val eventAfterUpdate =
                    event.setRecurrence(listOf("RRULE:FREQ=WEEKLY;BYDAY=WE;INTERVAL=2;UNTIL=19710101T100000Z"))

                coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
                coEvery {
                    externalCalendarRecurrentEventService.getActiveRecurrentEventByStaffScheduleId(staffScheduleId = staffSchedule.id)
                } returns externalRecurrentEvent
                coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
                coEvery {
                    calendarService.get("primary", externalRecurrentEvent.externalEventId).execute()
                } returns event
                coEvery {
                    externalCalendarEventService.getFirstEventForRecurrence(externalRecurrentEvent.id)
                } returns externalCalendarEvent.copy(startTime = currentDate.minusDays(1))
                coEvery {
                    calendarService.update("primary", event.id, event).setSendUpdates("all").execute()
                } returns eventAfterUpdate

                val result = googleCalendarEventService.stopRecurrenceOfGoogleCalendarEvent(staffSchedule)
                assertThat(result).isSuccessWithData(true)

                coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
                coVerifyOnce { externalCalendarRecurrentEventService.getActiveRecurrentEventByStaffScheduleId(any()) }
                coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
                coVerifyOnce { calendarService.get(any(), any()) }
                coVerifyOnce { externalCalendarEventService.getFirstEventForRecurrence(any()) }
                coVerifyOnce { calendarService.update(any(), any(), any()) }
            }

        }

    @Test
    fun `#stopRecurrenceOfGoogleCalendarEvent should stop recurrence from google calendar event and cancel event if its canceled before the first one`() =
        runBlocking {
            val currentDate = LocalDateTime.of(1971, 1, 1, 10, 0, 0)

            mockLocalDateTime(currentDate) {

                val event = buildBasicEvent()
                    .setRecurrence(
                        listOf(
                            RecurrenceRule.fromGoogleRecurrence(listOf("RRULE:FREQ=WEEKLY;BYDAY=WE;INTERVAL=2")).get()
                                .toString()
                        )
                    )


                coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
                coEvery {
                    externalCalendarRecurrentEventService.getActiveRecurrentEventByStaffScheduleId(staffScheduleId = staffSchedule.id)
                } returns externalRecurrentEvent
                coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
                coEvery {
                    calendarService.get("primary", externalRecurrentEvent.externalEventId).execute()
                } returns event
                coEvery {
                    externalCalendarEventService.getFirstEventForRecurrence(externalRecurrentEvent.id)
                } returns externalCalendarEvent.copy(startTime = currentDate.plusDays(1))
                coEvery {
                    calendarService.update("primary", event.id, event).setSendUpdates("all").execute()
                } returns event

                val result = googleCalendarEventService.stopRecurrenceOfGoogleCalendarEvent(staffSchedule)
                assertThat(result).isSuccessWithData(true)

                coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
                coVerifyOnce { externalCalendarRecurrentEventService.getActiveRecurrentEventByStaffScheduleId(any()) }
                coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
                coVerifyOnce { calendarService.get(any(), any()) }
                coVerifyOnce { externalCalendarEventService.getFirstEventForRecurrence(any()) }
                coVerifyOnce { calendarService.update(any(), any(), any()) }
            }
        }

    @Test
    fun `#createEventAtGoogleCalendar should create event at google calendar API using appointment schedule information`() =
        runBlocking {
            val googleCalendarEvent = buildEvent(
                appointmentSchedule = appointmentSchedule,
                endDateTime = appointmentSchedule.startTime,
                staffName = staff.fullName,
                person = person,
                appointmentScheduleEventTypeDescription = appointmentScheduleEventType.description
            )

            coEvery { personService.get(personId, false) } returns person
            coEvery { staffService.get(staffId) } returns staff
            coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
            coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
            coEvery { appointmentScheduleService.get(appointmentSchedule.id) } returns appointmentSchedule
            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId)
            } returns appointmentScheduleEventType
            coEvery {
                calendarService.insert("primary", any()).setConferenceDataVersion(1).setSendUpdates("all").execute()
            } returns googleCalendarEvent

            val result = googleCalendarEventService.createEventAtGoogleCalendar(appointmentSchedule.id)
            assertThat(result).isSuccessWithData(appointmentSchedule)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
            coVerifyOnce { appointmentScheduleService.get(any()) }
            coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
            coVerifyOnce { calendarService.insert(any(), any()) }
        }

    @Test
    fun `#createEventAtGoogleCalendar should create event with event type description if appointment schedule has an event type`() =
        runBlocking {
            val googleCalendarEvent = buildEvent(
                appointmentSchedule = appointmentSchedule,
                endDateTime = appointmentSchedule.endTime!!,
                staffName = staff.fullName,
                person = person,
                appointmentScheduleEventTypeDescription = appointmentScheduleEventType.description,
                useGoogleMeet = true
            )

            coEvery { personService.get(personId, false) } returns person
            coEvery { staffService.get(staffId) } returns staff
            coEvery { schedulePreferenceService.getByStaffId(staffId) } returns schedulePreference
            coEvery {
                appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id)
            } returns appointmentScheduleEventType
            coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
            coEvery { appointmentScheduleService.get(appointmentSchedule.id) } returns appointmentSchedule
            coEvery {
                calendarService.insert("primary", googleCalendarEvent).setConferenceDataVersion(1).setSendUpdates("all")
                    .execute()
            } returns googleCalendarEvent

            val result = googleCalendarEventService.createEventAtGoogleCalendar(appointmentSchedule.id)
            assertThat(result).isSuccessWithData(appointmentSchedule)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { staffService.get(any()) }
            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
            coVerifyOnce { appointmentScheduleService.get(any()) }
            coVerifyOnce { appointmentScheduleEventTypeService.get(any()) }
            coVerifyOnce { calendarService.insert(any(), any()) }
        }

    @Test
    fun `#removeFromGoogleCalendar should return appointment schedule id when event has already removed`() =
        runBlocking {
            val googleCalendarEvent = TestModelFactory.buildExternalCalendarEvent(
                externalEventId = googleCalendarEvent.id,
                staffId = staffId,
                status = googleCalendarEventService.getGoogleStatus(googleCalendarEvent.status),
                startTime = googleCalendarEvent.start.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                endTime = googleCalendarEvent.end.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                externalUpdatedAt = LocalDate.now().atStartOfDay()
            )

            val exception: GoogleJsonResponseException = mockk()

            coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
            coEvery { schedulePreferenceService.getByStaffId(staffId = staffId) } returns schedulePreference
            coEvery {
                externalCalendarEventService.getByAppointmentScheduleId(appointmentSchedule.id)
            } returns listOf(googleCalendarEvent)
            coEvery {
                calendarService.delete("primary", googleCalendarEvent.externalEventId)
                    .setSendUpdates("all")
                    .execute()
            } throws exception
            every { exception.message } returns "Already processed"
            every { exception.statusCode } returns 410

            val result = googleCalendarEventService.removeFromGoogleCalendar(
                appointmentScheduleId = appointmentSchedule.id
            )
            assertThat(result).isSuccessWithData(listOf(appointmentSchedule.id))

            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
            coVerifyOnce { externalCalendarEventService.getByAppointmentScheduleId(any()) }
            coVerifyOnce { calendarService.delete(any(), any()) }
        }

    @Test
    fun `#removeFromGoogleCalendar should return appointment schedule id when event is not found`() =
        runBlocking {
            val googleCalendarEvent = TestModelFactory.buildExternalCalendarEvent(
                externalEventId = googleCalendarEvent.id,
                staffId = staffId,
                status = googleCalendarEventService.getGoogleStatus(googleCalendarEvent.status),
                startTime = googleCalendarEvent.start.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                endTime = googleCalendarEvent.end.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                externalUpdatedAt = LocalDate.now().atStartOfDay()
            )

            val exception: GoogleJsonResponseException = mockk()

            coEvery { googleCalendarApi.getCalendarService(googleRefreshToken) } returns calendarService
            coEvery { schedulePreferenceService.getByStaffId(staffId = staffId) } returns schedulePreference
            coEvery {
                externalCalendarEventService.getByAppointmentScheduleId(appointmentSchedule.id)
            } returns listOf(googleCalendarEvent)
            coEvery {
                calendarService.delete("primary", googleCalendarEvent.externalEventId)
                    .setSendUpdates("all")
                    .execute()
            } throws exception
            every { exception.message } returns "Not Found"
            every { exception.statusCode } returns 404

            val result = googleCalendarEventService.removeFromGoogleCalendar(
                appointmentScheduleId = appointmentSchedule.id
            )
            assertThat(result).isSuccessWithData(listOf(appointmentSchedule.id))

            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyOnce { googleCalendarApi.getCalendarService(any()) }
            coVerifyOnce { externalCalendarEventService.getByAppointmentScheduleId(any()) }
            coVerifyOnce { calendarService.delete(any(), any()) }
        }

    @Test
    fun `#removeFromGoogleCalendar should do nothing if staff is inactive`() =
        runBlocking {
            val googleCalendarEvent = TestModelFactory.buildExternalCalendarEvent(
                externalEventId = googleCalendarEvent.id,
                staffId = staffId,
                status = googleCalendarEventService.getGoogleStatus(googleCalendarEvent.status),
                startTime = googleCalendarEvent.start.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                endTime = googleCalendarEvent.end.dateTime.toStringRfc3339().fromRfc3339ToLocalDateTime(),
                externalUpdatedAt = LocalDate.now().atStartOfDay()
            )

            coEvery {
                schedulePreferenceService.getByStaffId(staffId = staffId)
            } returns schedulePreference.copy(status = Status.INACTIVE)
            coEvery {
                externalCalendarEventService.getByAppointmentScheduleId(appointmentSchedule.id)
            } returns listOf(googleCalendarEvent)

            val result = googleCalendarEventService.removeFromGoogleCalendar(
                appointmentScheduleId = appointmentSchedule.id
            )
            assertThat(result).isFailureOfType(NotFoundException::class)

            coVerifyOnce { schedulePreferenceService.getByStaffId(any()) }
            coVerifyOnce { externalCalendarEventService.getByAppointmentScheduleId(any()) }
        }

    @Test
    fun `#synchronizeGoogleCalendar should produce query event containing startDate parameter if provided`() =
        runBlocking {
            val refreshToken = "test-refresh-token"
            val startDate = LocalDateTime.now()
            val schedulePreference = TestModelFactory.buildSchedulePreference(
                staffId = staffId,
                googleRefreshToken = refreshToken,
            )

            coEvery { kafkaProducerService.produce(any<GoogleCalendarQueryEventsEvent>()) } returns mockk()

            val result = googleCalendarEventService.synchronizeGoogleCalendar(
                schedulePreference = schedulePreference,
                startDate = startDate,
            )
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce {
                kafkaProducerService.produce(
                    GoogleCalendarQueryEventsEvent(
                        staffId = staffId,
                        queryFilters = GoogleCalendarEventsQueryFilters(
                            calendarId = "primary",
                            showDeleted = false,
                            maxResults = 50,
                            singleEvents = false,
                            syncToken = null,
                            pageToken = null,
                            timeMin = startDate.toGoogleLocalTime(),
                        ),
                        refreshToken = refreshToken,
                    )
                )
            }
        }

    private fun buildBasicEvent(): Event {
        val now = DateTime(System.currentTimeMillis())
        val untilZonedDateTime = ZonedDateTime.now(ZoneId.of("UTC")).plusDays(30)
        val until = DateTime(Date.from(untilZonedDateTime.toInstant()))
        val updated = DateTime(Date.from(externalUpdatedAtZoned.toInstant()))

        return Event()
            .setId("1")
            .setStart(EventDateTime().setDateTime(now))
            .setEnd(EventDateTime().setDateTime(until))
            .setStatus("invalid")
            .setUpdated(updated)
    }

    private fun buildPastEventWithRecurrenceThatFinishesInFuture(): Event {
        val now = DateTime(LocalDateTime.now().minusDays(1).toEpochSecond(ZoneOffset.UTC))
        val until = DateTime(LocalDateTime.now().minusDays(1).plusHours(1).toEpochSecond(ZoneOffset.UTC))
        val updated = DateTime(Date.from(externalUpdatedAtZoned.toInstant()))

        return Event()
            .setId("1")
            .setStart(EventDateTime().setDateTime(now))
            .setEnd(EventDateTime().setDateTime(until))
            .setStatus("invalid")
            .setUpdated(updated)
            .setRecurrence(listOf("RRULE:FREQ=WEEKLY;WKST=SU;INTERVAL=2;UNTIL=20401006T025959Z;BYDAY=WE"))
    }

    private fun buildPastEventWithRecurrenceThatFinishesDoesNotHaveUntilDate(): Event {
        val now = DateTime(LocalDateTime.now().minusDays(1).toEpochSecond(ZoneOffset.UTC))
        val until = DateTime(LocalDateTime.now().minusDays(1).plusHours(1).toEpochSecond(ZoneOffset.UTC))
        val updated = DateTime(Date.from(externalUpdatedAtZoned.toInstant()))

        return Event()
            .setId("1")
            .setStart(EventDateTime().setDateTime(now))
            .setEnd(EventDateTime().setDateTime(until))
            .setStatus("invalid")
            .setUpdated(updated)
            .setRecurrence(listOf("RRULE:FREQ=WEEKLY;WKST=SU;INTERVAL=2;BYDAY=WE"))
    }

}
