package br.com.alice.member.api

import M
import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.app.content.client.AppContentScreenDetailService
import br.com.alice.app.content.client.BottomTabsService
import br.com.alice.app.content.model.BottomTabsAction
import br.com.alice.app.content.model.BottomTabsActionMethod
import br.com.alice.app.content.model.BottomTabsActionType
import br.com.alice.app.content.model.BottomTabsActionsData
import br.com.alice.app.content.model.BottomTabsIcon
import br.com.alice.app.content.model.BottomTabsTransport
import br.com.alice.app.content.model.ScreenType
import br.com.alice.bottini.client.OpportunityService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.extensions.VoipToken
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.common.redis.GenericCache
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.CoPaymentType
import br.com.alice.data.layer.models.CompanySize
import br.com.alice.data.layer.models.Device
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthcareModelType
import br.com.alice.data.layer.models.MemberContractTerm
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.OnboardingPhase.CONTRACT
import br.com.alice.data.layer.models.OnboardingPhase.FINISHED
import br.com.alice.data.layer.models.OnboardingPhase.SHOPPING
import br.com.alice.data.layer.models.Opportunity
import br.com.alice.data.layer.models.PersonOnboarding
import br.com.alice.data.layer.models.PrimaryAttentionType
import br.com.alice.data.layer.models.ProductInfo
import br.com.alice.data.layer.models.ProductType
import br.com.alice.data.layer.models.RefundType
import br.com.alice.data.layer.models.TermType
import br.com.alice.data.layer.models.TierType
import br.com.alice.member.api.controllers.SessionsController
import br.com.alice.member.api.models.AppMessage
import br.com.alice.member.api.models.Link
import br.com.alice.member.api.models.Links
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.PersonData
import br.com.alice.member.api.models.PersonMissingData
import br.com.alice.member.api.models.PersonMissingDataNavigationResponse
import br.com.alice.member.api.services.SessionNavigationService
import br.com.alice.membership.client.DeviceService
import br.com.alice.membership.client.MemberContractTermService
import br.com.alice.membership.client.PersonPreferencesService
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.onboarding.client.InsurancePortabilityService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpHeaders
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertNotNull

class SessionRoutesTest : RoutesTestHelper() {

    private val deviceService: DeviceService = mockk()
    private val onboardingService: OnboardingService = mockk()
    private val sessionNavigationService: SessionNavigationService = mockk()
    private val portabilityService: InsurancePortabilityService = mockk()
    private val personService: PersonService = mockk()
    private val memberService: MemberService = mockk()
    private val memberContractTermService: MemberContractTermService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val personPreferencesService: PersonPreferencesService = mockk()
    private val bottomTabsService: BottomTabsService = mockk()
    private val actionPlanTaskService: ActionPlanTaskService = mockk()
    private val cache: GenericCache = mockk()
    private val appContentScreenDetailService: AppContentScreenDetailService = mockk()
    private val opportunityService: OpportunityService = mockk()
    private val companyService: CompanyService = mockk()

    private val sessionsController = SessionsController(
        deviceService,
        onboardingService,
        sessionNavigationService,
        portabilityService,
        personService,
        memberService,
        memberContractTermService,
        beneficiaryService,
        personPreferencesService,
        bottomTabsService,
        actionPlanTaskService,
        cache,
        appContentScreenDetailService,
        opportunityService,
        companyService
    )

    private val deviceId = "d3v1c31d"
    private val headers = mapOf("X-FCM-TOKEN" to deviceId, HttpHeaders.UserAgent to "unknown_device")

    private val token = RangeUUID.generate().toString()

    private val person = TestModelFactory.buildPerson()
    private val member = TestModelFactory.buildMember(
        personId = person.id,
        activationDate = LocalDateTime.now(),
        status = MemberStatus.ACTIVE,
    )

    private val bottomTabs = listOf(
        ScreenType.UNIFIED_HEALTH, ScreenType.ALICE_AGORA, ScreenType.MAIN_MENU
    ).map {
        BottomTabsTransport(
            id = it.toString(),
            name = "",
            icon = BottomTabsIcon(selected = "", unselected = ""),
            action = BottomTabsAction(
                type = BottomTabsActionType.ASYNC_REQUEST.value,
                data = BottomTabsActionsData(method = BottomTabsActionMethod.GET, endpoint = "", screen_id = it.value)
            )
        )
    }

    private val finishedOnboarding = PersonOnboarding(
        personId = person.id,
        currentPhase = FINISHED,
        finishedAt = LocalDateTime.now()
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        this.module.single { sessionsController }

        coEvery {
            cache.get("member:${person.id}:check-tasks", Boolean::class, any(), any(), any())
        } coAnswers {
            true
        }

        coEvery {
            cache.get("member:${person.id}:check-health-forms", Boolean::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> Boolean>(4).invoke()
            true
        }

        mockkObject(ServiceConfig)
        coEvery { ServiceConfig.signUpUrl } returns "https://url"

        coEvery {
            beneficiaryService.findByMemberId(member.id, BeneficiaryService.FindOptions(withOnboarding = true))
        } returns NotFoundException().failure()

        coEvery { onboardingService.findByPerson(person.id) } returns finishedOnboarding.success()
        coEvery { portabilityService.findByPerson(person.id) } returns TestModelFactory.buildInsurancePortabilityRequest()
            .success()

        coEvery { sessionNavigationService.getOnboardingPhase(person, finishedOnboarding) } returns FINISHED
        coEvery { sessionNavigationService.getCrmKey(person.nationalId) } returns "someKey".success()

        coEvery { deviceService.upsertDevice(person.id, deviceId) } returns Device(person.id, deviceId).success()
    }

    @Test
    fun `#beforeSession should return a session id when user starts a new session`() {
        internalAuthentication {
            post(to = "/before_session") { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#startSession should return ok and persist deviceId`() {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post("/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                val expectedKeys = listOf(M.TITLE_OUVIDORIA, M.CONTENT_OUVIDORIA)
                val messageKeys = sessionResponse.messages.map { it.key }

                coVerify(exactly = 1) { deviceService.upsertDevice(person.id, deviceId) }
                coVerify(exactly = 1) { bottomTabsService.get(person, member) }
                assertThat(messageKeys).hasSameElementsAs(expectedKeys)
            }
        }
    }

    @Test
    fun `#startSession should return ok and persist deviceId 2222`() {
        val appVersion = "1.23.45"

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, member, SemanticVersion(appVersion)) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(
                "/start_session",
                headers = (headers + mapOf(HttpHeaders.UserAgent to "iOS/${appVersion}-19307 (iPhone14,5/15.6)"))
            ) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                val expectedKeys = listOf(M.TITLE_OUVIDORIA, M.CONTENT_OUVIDORIA)
                val messageKeys = sessionResponse.messages.map { it.key }

                coVerify(exactly = 1) { deviceService.upsertDevice(person.id, deviceId, "1.23.45") }
                coVerify(exactly = 1) { bottomTabsService.get(any(), any(), any()) }
                assertThat(messageKeys).hasSameElementsAs(expectedKeys)
            }
        }
    }

    @Test
    fun `#startSession should return ok and persist voip token in device`() {
        val appVersion = "1.23.45"
        val voipToken = "voipToken"
        val device = Device(person.id, deviceId, appVersion, voipToken = voipToken)

        coEvery { deviceService.upsertDevice(person.id, deviceId, appVersion, voipToken) } returns device.success()
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { bottomTabsService.get(person, member, SemanticVersion(appVersion)) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(
                "/start_session",
                headers = (headers + mapOf(
                    HttpHeaders.UserAgent to "iOS/${appVersion}-19307 (iPhone14,5/15.6)",
                    HttpHeaders.VoipToken to voipToken
                ))
            ) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                val expectedKeys = listOf(M.TITLE_OUVIDORIA, M.CONTENT_OUVIDORIA)
                val messageKeys = sessionResponse.messages.map { it.key }

                coVerifyOnce { deviceService.upsertDevice(any(), any(), any(), any()) }
                coVerifyOnce { personService.get(any(), any()) }
                coVerifyOnce { memberService.findActiveOrPendingMembership(any(), any()) }
                coVerifyOnce { bottomTabsService.get(any(), any(), any()) }

                coVerifyNone { beneficiaryService.findByMemberId(any(), any()) }
                assertThat(messageKeys).hasSameElementsAs(expectedKeys)
            }
        }
    }

    @Test
    fun `#startSession should return a session id when user starts a new session`() {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()

                coVerify(exactly = 1) { deviceService.upsertDevice(person.id, deviceId) }
                coVerify(exactly = 1) { bottomTabsService.get(person, member) }
                assertNotNull(sessionResponse.id)
            }
        }
    }

    @Test
    fun `#startSession should return all ouvidoria messages`() {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
        coEvery { deviceService.upsertDevice(person.id, deviceId ) } returns Device(person.id, deviceId).success()
        coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                val expectedKeys = listOf(M.TITLE_OUVIDORIA, M.CONTENT_OUVIDORIA)
                val messageKeys = sessionResponse.messages.map { it.key }

                coVerify(exactly = 1) { bottomTabsService.get(person, member) }
                coVerify(exactly = 1) { deviceService.upsertDevice(person.id, deviceId ) }
                assertThat(messageKeys).hasSameElementsAs(expectedKeys)
            }
        }
    }

    @Test
    fun `#startSession should redirect to GAS phase when user hasn't finished registration process`() {
        val onboardingOnShoppingPhase = PersonOnboarding(
            finishedAt = null,
            currentPhase = SHOPPING,
            personId = person.id
        )

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { onboardingService.findByPerson(person.id) } returns onboardingOnShoppingPhase.success()
        coEvery { sessionNavigationService.getOnboardingPhase(person, onboardingOnShoppingPhase) } returns CONTRACT
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns NotFoundException("not found").failure()
        coEvery { personPreferencesService.findByPersonId(person.id) } returns NotFoundException("not found").failure()
        coEvery { bottomTabsService.get(person, null) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                coVerify(exactly = 1) { bottomTabsService.get(person, null) }
                assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.CONTRACT_SIGNING)
            }
        }
    }

    @Test
    fun `#startSession should redirect to GAS phase when user hasn't finished registration process and is b2b`() {
        val onboardingOnShoppingPhase = PersonOnboarding(
            finishedAt = null,
            currentPhase = SHOPPING,
            personId = person.id
        )

        val person = person.copy(
            productInfo = ProductInfo(
                brand = Brand.ALICE,
                primaryAttention = PrimaryAttentionType.ALICE,
                tier = TierType.TIER_1,
                coPayment = CoPaymentType.NONE,
                healthcareModelType = HealthcareModelType.V3,
                refund = RefundType.FULL,
                productType = ProductType.B2B
            )
        )

        val b2bMember = TestModelFactory.buildMember(
            personId = person.id,
            status = MemberStatus.PENDING,
            productType = ProductType.B2B
        )

        val beneficiary = TestModelFactory.buildBeneficiary(
            memberId = b2bMember.id
        )

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { onboardingService.findByPerson(person.id) } returns onboardingOnShoppingPhase.success()
        coEvery { sessionNavigationService.getOnboardingPhase(person, onboardingOnShoppingPhase) } returns CONTRACT
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns b2bMember.success()
        coEvery { personPreferencesService.findByPersonId(person.id) } returns NotFoundException("not found").failure()
        coEvery { bottomTabsService.get(person, b2bMember) } returns bottomTabs.success()
        coEvery {
            beneficiaryService.findByMemberId(b2bMember.id, BeneficiaryService.FindOptions(withOnboarding = true))
        } returns beneficiary.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                coVerify(exactly = 1) { bottomTabsService.get(person, b2bMember) }
                coVerifyNone { opportunityService.get(any()) }
                coVerifyOnce {beneficiaryService.findByMemberId(any(), any()) }
                assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.CONTRACT_SIGNING)
            }
        }
    }

    @Test
    fun `#startSession should redirect to GAS phase when user hasn't finished registration process, is b2c and still has valid opportunity`() =
        runBlocking {
            val onboardingOnShoppingPhase = PersonOnboarding(
                finishedAt = null,
                currentPhase = SHOPPING,
                personId = person.id
            )

            val person = person.copy(
                productInfo = ProductInfo(
                    brand = Brand.ALICE,
                    primaryAttention = PrimaryAttentionType.ALICE,
                    tier = TierType.TIER_1,
                    coPayment = CoPaymentType.NONE,
                    healthcareModelType = HealthcareModelType.V3,
                    refund = RefundType.FULL,
                    productType = ProductType.B2C
                ),
                opportunityId = RangeUUID.generate()
            )

            val opportunity = Opportunity(
                id = person.opportunityId!!,
                simulationId = RangeUUID.generate(),
                productId = RangeUUID.generate(),
                prices = emptyList(),
                productPriceListingId = RangeUUID.generate(),
                expiresAt = LocalDate.now().atEndOfTheDay().plusDays(1),
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { onboardingService.findByPerson(person.id) } returns onboardingOnShoppingPhase.success()
            coEvery { sessionNavigationService.getOnboardingPhase(person, onboardingOnShoppingPhase) } returns CONTRACT
            coEvery { memberService.findActiveOrPendingMembership(person.id) } returns NotFoundException("not found").failure()
            coEvery { personPreferencesService.findByPersonId(person.id) } returns NotFoundException("not found").failure()
            coEvery { bottomTabsService.get(person, null) } returns bottomTabs.success()
            coEvery { opportunityService.get(person.opportunityId!!) } returns opportunity.success()

            withFeatureFlag(FeatureNamespace.ALICE_APP, "gas_b2c_allowed_members", listOf("000000000")) {
                authenticatedAs(token, toTestPerson(person)) {
                    post(to = "/start_session", headers = headers) { response ->
                        assertThat(response).isSuccessfulJson()
                        val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                        coVerify(exactly = 1) { bottomTabsService.get(person, null) }
                        coVerifyOnce { opportunityService.get(any()) }
                        assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.CONTRACT_SIGNING)
                    }
                }
            }
        }

    @Test
    fun `#startSession should redirect to GAS phase when user hasn't finished registration process, is b2c has expired opportunity but is on allow list`() =
        runBlocking {
            val onboardingOnShoppingPhase = PersonOnboarding(
                finishedAt = null,
                currentPhase = SHOPPING,
                personId = person.id
            )

            val person = person.copy(
                productInfo = ProductInfo(
                    brand = Brand.ALICE,
                    primaryAttention = PrimaryAttentionType.ALICE,
                    tier = TierType.TIER_1,
                    coPayment = CoPaymentType.NONE,
                    healthcareModelType = HealthcareModelType.V3,
                    refund = RefundType.FULL,
                    productType = ProductType.B2C
                ),
                opportunityId = RangeUUID.generate()
            )

            val opportunity = Opportunity(
                id = person.opportunityId!!,
                simulationId = RangeUUID.generate(),
                productId = RangeUUID.generate(),
                prices = emptyList(),
                productPriceListingId = RangeUUID.generate(),
                expiresAt = LocalDate.now().atEndOfTheDay().plusDays(1),
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { onboardingService.findByPerson(person.id) } returns onboardingOnShoppingPhase.success()
            coEvery { sessionNavigationService.getOnboardingPhase(person, onboardingOnShoppingPhase) } returns CONTRACT
            coEvery { memberService.findActiveOrPendingMembership(person.id) } returns NotFoundException("not found").failure()
            coEvery { personPreferencesService.findByPersonId(person.id) } returns NotFoundException("not found").failure()
            coEvery { bottomTabsService.get(person, null) } returns bottomTabs.success()
            coEvery { opportunityService.get(person.opportunityId!!) } returns opportunity.success()

            withFeatureFlag(FeatureNamespace.ALICE_APP, "gas_b2c_allowed_members", listOf(person.nationalId)) {
                authenticatedAs(token, toTestPerson(person)) {
                    post(to = "/start_session", headers = headers) { response ->
                        assertThat(response).isSuccessfulJson()
                        val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                        coVerify(exactly = 1) { bottomTabsService.get(person, null) }
                        coVerifyOnce { opportunityService.get(any()) }
                        assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.CONTRACT_SIGNING)
                    }
                }
            }
        }

    @Test
    fun `#startSession should redirect to GAS phase when user hasn't finished registration process, is b2c has no opportunity but is on allow list`() =
        runBlocking {
            val onboardingOnShoppingPhase = PersonOnboarding(
                finishedAt = null,
                currentPhase = SHOPPING,
                personId = person.id
            )

            val person = person.copy(
                productInfo = ProductInfo(
                    brand = Brand.ALICE,
                    primaryAttention = PrimaryAttentionType.ALICE,
                    tier = TierType.TIER_1,
                    coPayment = CoPaymentType.NONE,
                    healthcareModelType = HealthcareModelType.V3,
                    refund = RefundType.FULL,
                    productType = ProductType.B2C
                ),
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { onboardingService.findByPerson(person.id) } returns onboardingOnShoppingPhase.success()
            coEvery { sessionNavigationService.getOnboardingPhase(person, onboardingOnShoppingPhase) } returns CONTRACT
            coEvery { memberService.findActiveOrPendingMembership(person.id) } returns NotFoundException("not found").failure()
            coEvery { personPreferencesService.findByPersonId(person.id) } returns NotFoundException("not found").failure()
            coEvery { bottomTabsService.get(person, null) } returns bottomTabs.success()

            withFeatureFlag(FeatureNamespace.ALICE_APP, "gas_b2c_allowed_members", listOf(person.nationalId)) {
                authenticatedAs(token, toTestPerson(person)) {
                    post(to = "/start_session", headers = headers) { response ->
                        assertThat(response).isSuccessfulJson()
                        val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                        coVerify(exactly = 1) { bottomTabsService.get(person, null) }
                        assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.CONTRACT_SIGNING)
                    }
                }
            }
        }

    @Test
    fun `#startSession should not redirect to GAS phase when user is b2c and opportunity is expired`() = runBlocking {
        val onboardingOnShoppingPhase = PersonOnboarding(
            finishedAt = null,
            currentPhase = SHOPPING,
            personId = person.id
        )

        val person = person.copy(
            productInfo = ProductInfo(
                brand = Brand.ALICE,
                primaryAttention = PrimaryAttentionType.ALICE,
                tier = TierType.TIER_1,
                coPayment = CoPaymentType.NONE,
                healthcareModelType = HealthcareModelType.V3,
                refund = RefundType.FULL,
                productType = ProductType.B2C,
            ),
            opportunityId = RangeUUID.generate()
        )

        val opportunity = Opportunity(
            id = person.opportunityId!!,
            simulationId = RangeUUID.generate(),
            productId = RangeUUID.generate(),
            prices = emptyList(),
            productPriceListingId = RangeUUID.generate(),
            expiresAt = LocalDate.now().atEndOfTheDay().minusDays(31),
        )


        coEvery { personService.get(person.id) } returns person.success()
        coEvery { onboardingService.findByPerson(person.id) } returns onboardingOnShoppingPhase.success()
        coEvery { sessionNavigationService.getOnboardingPhase(person, onboardingOnShoppingPhase) } returns CONTRACT
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns NotFoundException("not found").failure()
        coEvery { personPreferencesService.findByPersonId(person.id) } returns NotFoundException("not found").failure()
        coEvery { bottomTabsService.get(person, null) } returns bottomTabs.success()
        coEvery { opportunityService.get(person.opportunityId!!) } returns opportunity.success()

        withFeatureFlag(FeatureNamespace.ALICE_APP, "gas_b2c_allowed_members", listOf("000000000")) {
            authenticatedAs(token, toTestPerson(person)) {
                post(to = "/start_session", headers = headers) { response ->
                    assertThat(response).isUnauthorized()
                }
            }
        }
    }

    @Test
    fun `#startSession should redirect to home when user has finished registration process`() {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse = gson.fromJson(response.bodyAsText(), SessionResponseTestNavigation::class.java)
                assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.HOME)
                coVerify(exactly = 1) { bottomTabsService.get(person, member) }
            }
        }
    }


    @Test
    fun `#startSession should overdue and expire tasks when cache says so`() {
        val actionPlanTask = TestModelFactory.buildActionPlanTask()
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()
        coEvery {
            cache.get("member:${person.id}:check-tasks", Boolean::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> Boolean>(4).invoke()
            false
        }
        coEvery { actionPlanTaskService.markTasksAsOverdue(person.id) } returns listOf(actionPlanTask).success()
        coEvery { actionPlanTaskService.markTasksAsExpired(person.id) } returns listOf(actionPlanTask).success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse = gson.fromJson(response.bodyAsText(), SessionResponseTestNavigation::class.java)
                assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.HOME)
                coVerify(exactly = 1) { bottomTabsService.get(person, member) }
            }
        }
    }

    @Test
    fun `#startSession should expire only tasks when cache says so`() = runBlocking {
        withFeatureFlags(
            FeatureNamespace.HEALTH_PLAN to mapOf("should_use_new_health_plan_task_deadline_calculation_flow" to true),
            FeatureNamespace.ALICE_APP to mapOf("enable_update_expired_and_overdue_tasks" to true)
        ) {
            val actionPlanTask = TestModelFactory.buildActionPlanTask()
            coEvery { personService.get(person.id) } returns person.success()
            coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
            coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
            coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()
            coEvery {
                cache.get("member:${person.id}:check-tasks", Boolean::class, any(), any(), any())
            } coAnswers {
                arg<suspend () -> Boolean>(4).invoke()
                false
            }
            coEvery { actionPlanTaskService.markTasksAsExpired(person.id) } returns listOf(actionPlanTask).success()

            authenticatedAs(token, toTestPerson(person)) {
                post(to = "/start_session", headers = headers) { response ->
                    assertThat(response).isSuccessfulJson()
                    val sessionResponse =
                        gson.fromJson(response.bodyAsText(), SessionResponseTestNavigation::class.java)
                    assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.HOME)
                    coVerify(exactly = 1) { bottomTabsService.get(person, member) }
                    coVerify(exactly = 1) { actionPlanTaskService.markTasksAsExpired(person.id) }
                }
            }
        }
    }

    @Test
    fun `#startSession should overdue health forms when cache says so`() {
        val appContentScreenDetail = TestModelFactory.buildAppContentScreenDetail()
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()
        coEvery {
            cache.get("member:${person.id}:check-health-forms", Boolean::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> Boolean>(4).invoke()
            false
        }
        coEvery { appContentScreenDetailService.overdueAllHomeFormsFromPersonId(person.id) } returns listOf(
            appContentScreenDetail
        ).success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse = gson.fromJson(response.bodyAsText(), SessionResponseTestNavigation::class.java)
                assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.HOME)
                coVerify(exactly = 1) { bottomTabsService.get(person, member) }
            }
        }
    }

    @Test
    fun `#startSession should redirect to b2b terms when user is b2b and not accepted process data term`() {
        val memberB2B = TestModelFactory.buildMember(
            personId = person.id,
            activationDate = LocalDateTime.now(),
            status = MemberStatus.ACTIVE,
            productType = ProductType.B2B,
        )
        val beneficiary = TestModelFactory.buildBeneficiary(
            memberId = memberB2B.id
        )

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns memberB2B.success()
        coEvery {
            beneficiaryService.findByMemberId(memberB2B.id, BeneficiaryService.FindOptions(withOnboarding = true))
        } returns beneficiary.success()
        coEvery { beneficiaryService.findByMemberId(memberB2B.id) } returns beneficiary.success()
        coEvery { bottomTabsService.get(person, memberB2B) } returns bottomTabs.success()
        coEvery {
            memberContractTermService.findByMemberIdAndTermType(
                memberB2B.id,
                TermType.DATA_PROCESSING_TERMS
            )
        } returns emptyList<MemberContractTerm>().success()


        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse = gson.fromJson(response.bodyAsText(), SessionResponseTestNavigation::class.java)
                assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.B2B_TERMS)
                coVerify(exactly = 1) { beneficiaryService.findByMemberId(memberB2B.id) }
                coVerify(exactly = 1) {
                    beneficiaryService.findByMemberId(memberB2B.id, BeneficiaryService.FindOptions(withOnboarding = true))
                }
                coVerify(exactly = 1) {
                    memberContractTermService.findByMemberIdAndTermType(
                        memberB2B.id,
                        TermType.DATA_PROCESSING_TERMS
                    )
                }
                coVerify(exactly = 1) { bottomTabsService.get(person, memberB2B) }
            }
        }
    }

    @Test
    fun `#startSession should redirect to home when user is b2b and already accepted process data term`() {
        val memberB2B = TestModelFactory.buildMember(
            personId = person.id,
            activationDate = LocalDateTime.now(),
            status = MemberStatus.ACTIVE,
            productType = ProductType.B2B,
        )
        val beneficiary = TestModelFactory.buildBeneficiary(
            memberId = memberB2B.id
        )
        val memberContractTerm = TestModelFactory.buildMemberContractTerm(
            memberId = memberB2B.id
        )

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns memberB2B.success()
        coEvery {
            beneficiaryService.findByMemberId(memberB2B.id, BeneficiaryService.FindOptions(withOnboarding = true))
        } returns beneficiary.success()
        coEvery { beneficiaryService.findByMemberId(memberB2B.id) } returns beneficiary.success()
        coEvery { bottomTabsService.get(person, memberB2B) } returns bottomTabs.success()
        coEvery {
            memberContractTermService.findByMemberIdAndTermType(
                memberB2B.id,
                TermType.DATA_PROCESSING_TERMS
            )
        } returns listOf(memberContractTerm).success()


        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse = gson.fromJson(response.bodyAsText(), SessionResponseTestNavigation::class.java)
                assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.HOME)
                coVerify(exactly = 1) { bottomTabsService.get(person, memberB2B) }
                coVerify(exactly = 1) {
                    beneficiaryService.findByMemberId(memberB2B.id, BeneficiaryService.FindOptions(withOnboarding = true))
                }
                coVerify(exactly = 1) { beneficiaryService.findByMemberId(memberB2B.id) }
                coVerify(exactly = 1) {
                    memberContractTermService.findByMemberIdAndTermType(
                        memberB2B.id,
                        TermType.DATA_PROCESSING_TERMS
                    )
                }
            }
        }
    }

    @Test
    fun `#startSession should redirect to home when user has a b2b product but hasnt a beneficiary`() {
        val memberWithB2BProduct = TestModelFactory.buildMember(
            personId = person.id,
            activationDate = LocalDateTime.now(),
            status = MemberStatus.ACTIVE,
            productType = ProductType.B2B,
        )

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns memberWithB2BProduct.success()
        coEvery {
            beneficiaryService.findByMemberId(memberWithB2BProduct.id, BeneficiaryService.FindOptions(withOnboarding = true))
        } returns NotFoundException().failure()
        coEvery { beneficiaryService.findByMemberId(memberWithB2BProduct.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, memberWithB2BProduct) } returns bottomTabs.success()


        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse = gson.fromJson(response.bodyAsText(), SessionResponseTestNavigation::class.java)
                assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.HOME)

                coVerify(exactly = 1) { bottomTabsService.get(person, memberWithB2BProduct) }
                coVerify(exactly = 1) { beneficiaryService.findByMemberId(memberWithB2BProduct.id) }
                coVerify(exactly = 1) {
                    beneficiaryService.findByMemberId(memberWithB2BProduct.id, BeneficiaryService.FindOptions(withOnboarding = true))
                }
            }
        }
    }

    @Test
    fun `#startSession should redirect to home when user has active b2c member`() {

        val person = person.copy(
            productInfo = ProductInfo(
                brand = Brand.ALICE,
                primaryAttention = PrimaryAttentionType.ALICE,
                tier = TierType.TIER_1,
                coPayment = CoPaymentType.NONE,
                healthcareModelType = HealthcareModelType.V3,
                refund = RefundType.FULL,
                productType = ProductType.B2C
            )
        )

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { onboardingService.findByPerson(person.id) } returns NotFoundException().failure()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.HOME)
                coVerify(exactly = 1) { bottomTabsService.get(person, member) }
            }
        }
    }


    @Test
    fun `#startSession should redirect to home when user has active member`() {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { onboardingService.findByPerson(person.id) } returns NotFoundException().failure()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.HOME)
                coVerify(exactly = 1) { bottomTabsService.get(person, member) }
            }
        }
    }

    @Test
    fun `#startSession should redirect to data registration when user has no active member and no person onboarding`() {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { onboardingService.findByPerson(person.id) } returns NotFoundException().failure()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, null) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.DATA_REGISTRATION)
                coVerify(exactly = 1) { bottomTabsService.get(person, null) }
            }
        }
    }

    @Test
    fun `#startSession should redirect to home when user has no person onboarding`() {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { onboardingService.findByPerson(person.id) } returns NotFoundException().failure()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.HOME)
                coVerify(exactly = 1) { bottomTabsService.get(person, member) }
            }
        }
    }

    @Test
    fun `#startSession should redirect to home when user is elligible not eligible member, has no person onboarding`() {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { onboardingService.findByPerson(person.id) } returns NotFoundException().failure()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.HOME)
                coVerify(exactly = 1) { bottomTabsService.get(person, member) }
            }
        }
    }

    @Test
    fun `#startSession should create onboarding when lead is not member and does not has onboarding`() {
        val onboarding = TestModelFactory.buildPersonOnboarding()
        val completedPerson = TestModelFactory.buildPerson(
            personId = person.id,
            dateOfBirth = LocalDateTime.now(),
            phoneNumber = "***********"
        )

        coEvery { personService.get(person.id) } returns completedPerson.success()
        coEvery { onboardingService.findByPerson(person.id) } returns NotFoundException().failure()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns NotFoundException().failure()
        coEvery { sessionNavigationService.getOnboardingPhase(person, onboarding) } returns SHOPPING
        coEvery { bottomTabsService.get(completedPerson) } returns bottomTabs.success()

        val expectedNavitation = NavigationResponse(
            mobileRoute = MobileRouting.GAS_INIT,
        )

        authenticatedAs(token, toTestPerson(completedPerson)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                assertThat(sessionResponse.navigation).isEqualTo(expectedNavitation)
            }
        }
    }

    @Test
    fun `#startSession should return a list of links containing terms, faq and wait list url`() {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                val relations = sessionResponse.links.map { it.rel }

                coVerify(exactly = 1) { deviceService.upsertDevice(person.id, deviceId) }
                assertThat(relations).containsExactlyElementsOf(
                    listOf(
                        "terms_of_use",
                        "wait_list",
                        "signup",
                        "faq",
                        "landing_page",
                        "national_coverage",
                        "gas_help",
                        "phone_number",
                        "duquesa_phone_number",
                        "alice_phone_number",
                    )
                )
                coVerify(exactly = 1) { bottomTabsService.get(person, member) }
            }
        }
    }

    @Test
    fun `#startSession should return ok without header`() {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post("/start_session") { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                val expectedKeys = listOf(M.TITLE_OUVIDORIA, M.CONTENT_OUVIDORIA)
                val messageKeys = sessionResponse.messages.map { it.key }

                coVerify(exactly = 1) { bottomTabsService.get(person, member) }
                coVerify(exactly = 0) { deviceService.upsertDevice(person.id, deviceId) }
                assertThat(messageKeys).hasSameElementsAs(expectedKeys)
            }
        }
    }

    @Test
    fun `#startSession should return ok and even if when error on persist deviceId`() {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { deviceService.upsertDevice(person.id, deviceId) } returns Result.failure(Exception())
        coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post("/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()

                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                val expectedKeys = listOf(M.TITLE_OUVIDORIA, M.CONTENT_OUVIDORIA)
                val messageKeys = sessionResponse.messages.map { it.key }

                assertThat(messageKeys).hasSameElementsAs(expectedKeys)
                coVerify(exactly = 1) { bottomTabsService.get(person, member) }
            }
        }
    }

    @Test
    fun `#startSession should return SHOPPING navigation`() {
        val onboarding = TestModelFactory.buildPersonOnboarding(currentPhase = SHOPPING)
        val completedPerson = TestModelFactory.buildPerson(
            personId = person.id,
            dateOfBirth = LocalDateTime.now(),
            phoneNumber = "***********"
        )

        val expectedNavitation = NavigationResponse(
            mobileRoute = MobileRouting.SHOPPING,
            link = Links.NEW_SHOPPING_CART
        )

        coEvery { personService.get(person.id) } returns completedPerson.success()
        coEvery { onboardingService.findByPerson(completedPerson.id) } returns onboarding.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns NotFoundException("not found").failure()

        coEvery { sessionNavigationService.getOnboardingPhase(completedPerson, onboarding) } returns SHOPPING
        coEvery { personPreferencesService.findByPersonId(person.id) } returns NotFoundException("not found").failure()
        coEvery { bottomTabsService.get(completedPerson) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()

                assertThat(sessionResponse.navigation).isEqualTo(expectedNavitation)
                coVerify(exactly = 1) { bottomTabsService.get(completedPerson) }
            }
        }
    }

    @Test
    fun `#startSession should return DATA_REGISTRATION navigation when missing data and onboarding phase is SHOPPING`() {
        val onboarding = TestModelFactory.buildPersonOnboarding(currentPhase = SHOPPING)
        val expectedNavitation = PersonMissingDataNavigationResponse(
            mobileRoute = MobileRouting.DATA_REGISTRATION,
            link = null,
            navigation = null,
            properties = PersonMissingData(
                missingData = listOf("date_of_birth", "phone"),
                person = PersonData(
                    firstName = person.firstName,
                    lastName = person.lastName,
                    nickName = person.nickName,
                    email = person.email,
                    phone = person.phoneNumber,
                    dateOfBirth = person.dateOfBirth?.toString(),
                    nationalId = person.nationalId,
                    postalCode = person.mainAddress?.postalCode,
                )
            )
        )

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { onboardingService.findByPerson(person.id) } returns onboarding.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns NotFoundException("not found").failure()
        coEvery { sessionNavigationService.getOnboardingPhase(person, onboarding) } returns SHOPPING
        coEvery { personPreferencesService.findByPersonId(person.id) } returns NotFoundException("not found").failure()
        coEvery { bottomTabsService.get(person, null) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestMissingData = response.bodyAsJson()

                assertThat(sessionResponse.navigation).isEqualTo(expectedNavitation)
                coVerify(exactly = 1) { bottomTabsService.get(person, null) }
            }
        }
    }

    @Test
    fun `#startSession should get standard bottom tabs`() {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                assertThat(sessionResponse.bottomTabs).isEqualTo(bottomTabs)
                coVerify(exactly = 1) { bottomTabsService.get(person, member) }
            }
        }
    }

    @Test
    fun `#startSession should get league bottom tabs`() {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
        coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
        coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(to = "/start_session", headers = headers) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                assertThat(sessionResponse.bottomTabs).isEqualTo(bottomTabs)
                coVerify(exactly = 1) { bottomTabsService.get(person, member) }
            }
        }
    }

    @Test
    fun `#startSession should ignore overdue and expire tasks when domain throws error`() =
        runBlocking {

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { memberService.findActiveOrPendingMembership(person.id) } returns member.success()
            coEvery { beneficiaryService.findByMemberId(member.id) } returns NotFoundException().failure()
            coEvery { bottomTabsService.get(person, member) } returns bottomTabs.success()
            coEvery {
                cache.get(
                    "member:${person.id}:check-tasks",
                    Boolean::class,
                    any(),
                    any(),
                    any()
                )
            } coAnswers {
                arg<suspend () -> Boolean>(4).invoke()
            }
            coEvery { actionPlanTaskService.markTasksAsOverdue(person.id) } returns Exception("ex").failure()
            coEvery { actionPlanTaskService.markTasksAsExpired(person.id) } returns Exception("ex").failure()

            withFeatureFlag(FeatureNamespace.ALICE_APP, "enable_update_expired_and_overdue_tasks", true) {
                authenticatedAs(token, toTestPerson(person)) {
                    post(to = "/start_session", headers = headers) { response ->
                        assertThat(response).isSuccessfulJson()
                        val sessionResponse =
                            gson.fromJson(response.bodyAsText(), SessionResponseTestNavigation::class.java)
                        assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.HOME)

                        coVerifyOnce { bottomTabsService.get(any(), any()) }
                        coVerifyOnce { actionPlanTaskService.markTasksAsOverdue(any()) }
                        coVerifyOnce { actionPlanTaskService.markTasksAsExpired(any()) }
                    }
                }
            }
        }

    @Test
    fun `#start should not return the Activation Home navigation with empty regex term`() = runBlocking<Unit> {
        val pendingMember = member.copy(
            status = MemberStatus.PENDING,
            selectedProduct = TestModelFactory.buildMemberProduct(type = ProductType.B2B)
        )

        val beneficiary = TestModelFactory.buildBeneficiary(
            memberId = pendingMember.id,
            companyId = UUID.fromString("a47ac10b-58cc-4372-a567-0e02b2c3d479")
        )

        val smallCompany = TestModelFactory.buildCompany(beneficiary.companyId, companySize = CompanySize.SMALL)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { memberService.findActiveOrPendingMembership(person.id) } returns pendingMember.success()
        coEvery {
            beneficiaryService.findByMemberId(
                pendingMember.id,
                BeneficiaryService.FindOptions(withOnboarding = true)
            )
        } returns beneficiary.success()
        coEvery { bottomTabsService.get(person, pendingMember, SemanticVersion("4.19.1")) } returns bottomTabs.success()
        coEvery { personPreferencesService.findByPersonId(person.id) } returns NotFoundException("not found").failure()
        coEvery { companyService.get(beneficiary.companyId) } returns smallCompany.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(
                to = "/start_session",
                headers = (headers + mapOf(HttpHeaders.UserAgent to "iOS/4.19.1-19307 (iPhone14,5/15.6)"))) { response ->
                assertThat(response).isSuccessfulJson()
                val sessionResponse =
                    gson.fromJson(response.bodyAsText(), SessionResponseTestNavigation::class.java)

                assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.HOME)
                assertThat(sessionResponse.forceNavigation).isEqualTo(false)
            }
        }
    }

    @Test
    fun `#start should return the Activation Home navigation `() = runBlocking<Unit> {
        withFeatureFlags(
            FeatureNamespace.MEMBERSHIP to mapOf(
                "minimum_app_version_activation_home" to "4.20.1",
            ),
            FeatureNamespace.MEMBERSHIP to mapOf(
                "activation_company_regex_term" to "^[a-c].*",
            )
        ) {
            val pendingMember = member.copy(
                status = MemberStatus.PENDING,
                selectedProduct = TestModelFactory.buildMemberProduct(type = ProductType.B2B)
            )

            val beneficiary = TestModelFactory.buildBeneficiary(
                memberId = pendingMember.id,
                companyId = UUID.fromString("a47ac10b-58cc-4372-a567-0e02b2c3d479")
            )

            val smallCompany = TestModelFactory.buildCompany(beneficiary.companyId, companySize = CompanySize.SMALL)

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { memberService.findActiveOrPendingMembership(person.id) } returns pendingMember.success()
            coEvery { beneficiaryService.findByMemberId(
                pendingMember.id,
                BeneficiaryService.FindOptions(withOnboarding = true))
            } returns beneficiary.success()
            coEvery { bottomTabsService.get(person, pendingMember, SemanticVersion("4.20.1")) } returns bottomTabs.success()
            coEvery { companyService.get(beneficiary.companyId) } returns smallCompany.success()

            authenticatedAs(token, toTestPerson(person)) {
                post(
                    to = "/start_session",
                    headers = (headers + mapOf(HttpHeaders.UserAgent to "iOS/4.20.1-19307 (iPhone14,5/15.6)"))) { response ->
                    assertThat(response).isSuccessfulJson()
                    val sessionResponse =
                        gson.fromJson(response.bodyAsText(), SessionResponseTestNavigation::class.java)

                    val expectedParams = mapOf(
                        "action" to mapOf(
                            "method" to "GET",
                            "endpoint" to "http://localhost/beneficiaries/screen/activation_home",
                            "params" to mapOf("screen_id" to "ACTIVATION_HOME")
                        )
                    )

                    assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.CHESHIRE_SCREEN)
                    assertThat(sessionResponse.navigation?.properties).isEqualTo(expectedParams)
                    assertThat(sessionResponse.forceNavigation).isEqualTo(true)
                }
            }
        }
    }

    @Test
    fun `#startSession should redirect to B2bActivationModule when member not finished risk flow`() = runBlocking<Unit> {
        withFeatureFlags(
            FeatureNamespace.MEMBERSHIP to mapOf(
                "minimum_app_version_activation_home" to "4.20.0",
            ),
            FeatureNamespace.MEMBERSHIP to mapOf(
                "activation_company_regex_term" to "^[a-c].*",
            )
        ) {

            val appVersion = "4.20.0"

            val b2bMember = TestModelFactory.buildMember(
                personId = person.id,
                status = MemberStatus.ACTIVE,
                productType = ProductType.B2B
            )

            val beneficiary = TestModelFactory.buildBeneficiary(
                memberId = b2bMember.id,
                companyId = UUID.fromString("a47ac10b-58cc-4372-a567-0e02b2c3d479"),
                onboarding = TestModelFactory.buildBeneficiaryOnboarding(
                    flowType = BeneficiaryOnboardingFlowType.NO_RISK_FLOW,
                    phases = listOf(TestModelFactory.buildBeneficiaryOnboardingPhase(
                        phase = BeneficiaryOnboardingPhaseType.REGISTRATION
                    ))
                )
            )
            val smallCompany = TestModelFactory.buildCompany(beneficiary.companyId, companySize = CompanySize.SMALL)

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { onboardingService.findByPerson(person.id) } returns NotFoundException().failure()
            coEvery { memberService.findActiveOrPendingMembership(person.id) } returns b2bMember.success()
            coEvery { personPreferencesService.findByPersonId(person.id) } returns NotFoundException("not found").failure()
            coEvery { bottomTabsService.get(person, b2bMember, SemanticVersion(appVersion)) } returns bottomTabs.success()
            coEvery {
                beneficiaryService.findByMemberId(b2bMember.id, BeneficiaryService.FindOptions(withOnboarding = true))
            } returns beneficiary.success()
            coEvery { companyService.get(beneficiary.companyId) } returns smallCompany.success()

            authenticatedAs(token, toTestPerson(person)) {
                post(
                    to = "/start_session",
                    headers = (headers + mapOf(HttpHeaders.UserAgent to "iOS/$appVersion-19307 (iPhone14,5/15.6)"))) { response ->
                    assertThat(response).isSuccessfulJson()
                    val sessionResponse: SessionResponseTestNavigation = response.bodyAsJson()
                    coVerify(exactly = 1) { bottomTabsService.get(any(), any(), any()) }
                    coVerifyNone { opportunityService.get(any()) }
                    coVerifyOnce {beneficiaryService.findByMemberId(any(), any()) }
                    assertThat(sessionResponse.navigation?.mobileRoute).isEqualTo(MobileRouting.B2B_ACTIVATION)
                }
            }
        }
    }
}

data class SessionResponseTestNavigation(
    val id: String,
    val messages: List<AppMessage>,
    val links: List<Link>,
    val navigation: NavigationResponse? = null,
    val crmKey: String? = null,
    val bottomTabs: List<BottomTabsTransport>? = null,
    val forceNavigation: Boolean = false,
)

data class SessionResponseTestMissingData(
    val id: String,
    val messages: List<AppMessage>,
    val links: List<Link>,
    val navigation: PersonMissingDataNavigationResponse? = null,
    val crmKey: String? = null,
    val bottomTabs: List<BottomTabsTransport>? = null,
)
