package br.com.alice.member.api.controllers.appContent

import br.com.alice.app.content.client.AliceScreensService
import br.com.alice.app.content.client.AppContentABTestService
import br.com.alice.app.content.client.AppContentScreenDetailService
import br.com.alice.app.content.client.DuquesaScreensService
import br.com.alice.app.content.model.AliceScreensData
import br.com.alice.app.content.model.FiltersGetScreen
import br.com.alice.app.content.model.NPSJourneyTestVariant
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreenType
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.mobile.Platform
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.communication.crm.analytics.AnalyticsTrackerResult
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.DemandActionPlanStatus
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthFormAnswerSource
import br.com.alice.data.layer.models.HealthFormAnswerSourceType
import br.com.alice.data.layer.models.HealthFormQuestionType
import br.com.alice.data.layer.models.ScheduleAppointmentType
import br.com.alice.data.layer.models.ScreenDetailStatus
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.controllers.beneficiary.BeneficiaryActivationHomeScreenController
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.questionnaire.client.FormNavigationService
import br.com.alice.questionnaire.exceptions.InvalidQuestionIndexException
import br.com.alice.questionnaire.models.HealthFormActionsTransport
import br.com.alice.questionnaire.models.QuestionnaireQuestionInputResponse
import br.com.alice.questionnaire.models.QuestionnaireQuestionResponse
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.model.AppointmentScheduleWithStaff
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class ScreenControllerTest : RoutesTestHelper() {

    private val appContentScreenDetailService: AppContentScreenDetailService = mockk()
    private val formNavigationService: FormNavigationService = mockk()
    private val memberService: MemberService = mockk()
    private val aliceScreensService: AliceScreensService = mockk()
    private val duquesaScreensService: DuquesaScreensService = mockk()
    private val crmAnalyticsTracker: CrmAnalyticsTracker = mockk()
    private val personService: PersonService = mockk()
    private val appointmentScheduleService: AppointmentScheduleService = mockk()
    private val appContentABTestService: AppContentABTestService = mockk()
    private val beneficiaryActivationHomeScreenController: BeneficiaryActivationHomeScreenController = mockk()

    private val screenController = ScreenController(
        appContentScreenDetailService,
        formNavigationService,
        aliceScreensService,
        duquesaScreensService,
        memberService,
        crmAnalyticsTracker,
        personService,
        appointmentScheduleService,
        appContentABTestService,
        beneficiaryActivationHomeScreenController
    )

    private val person = TestModelFactory.buildPerson()
    private val memberAlice = TestModelFactory.buildMember(person.id, brand = Brand.ALICE)
    private val memberDuquesa = TestModelFactory.buildMember(person.id, brand = Brand.DUQUESA)
    private val token = person.id.toString()
    private val healthForm = TestModelFactory.buildHealthForm(key = "PRE_IMMERSION")

    private val appContentScreenDetail = TestModelFactory.buildAppContentScreenDetail(
        sectionContent = healthForm.key,
        personId = person.id,
        healthFormSource = HealthFormAnswerSource(
            type = HealthFormAnswerSourceType.INSTITUTION,
            id = "TESTADA"
        )
    )

    private val npsAppContentScreenDetail = appContentScreenDetail.copy(sectionContent = "NPS")
    private val npsHealthForm = healthForm.copy(key = "NPS")

    private val healthFormQuestion = TestModelFactory.buildHealthFormQuestion(
        formId = healthForm.id
    )

    private val questionnaireQuestionResponse = QuestionnaireQuestionResponse(
        id = RangeUUID.generate(),
        questionnaireId = RangeUUID.generate(),
        groupId = RangeUUID.generate(),
        question = "",
        details = null,
        progress = 1,
        input = QuestionnaireQuestionInputResponse(
            action = "a",
            displayAttributes = null,
            options = emptyList(),
            type = HealthFormQuestionType.NUMERIC,
        ),
    )

    private val appVersion = SemanticVersion("1.0.0")

    private val notificationNpsAppVersion = "4.20.0"
    private val journeyNpsAppVersion = "100.20.0"

    private val userAgent = "Android/{version}-0 (sweet/30)"
    private fun headersByVersion(version: String) = mapOf(
        "User-Agent" to userAgent.replace("{version}", version),
    )

    private val appointmentSchedule = AppointmentScheduleWithStaff(
        appointmentSchedule = TestModelFactory.buildAppointmentSchedule(),
        staff = TestModelFactory.buildStaff(),
    )

    private val appointmentSchedules = listOf(
        appointmentSchedule,
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { screenController }

        coEvery {
            personService.get(person.id)
        } returns person.success()

        coEvery {
            memberService.getCurrent(person.id)
        } returns memberAlice.success()

        coEvery {
            crmAnalyticsTracker.sendEvent(any(), any())
        } returns AnalyticsTrackerResult(true)

        coEvery { memberService.findActiveOrPendingMembership(any()) } returns NotFoundException().failure()
    }

    @Test
    fun `#getByScreenType should get member schedule when screen is appointment hub`() {
        val screenType = ScreenType.APPOINTMENT_HUB

        coEvery {
            appointmentScheduleService.findWithStaffBy(any())
        } returns listOf(appointmentSchedule).success()

        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            aliceScreensService.getOtherAliceScreens(
                AliceScreensData(
                    person.id,
                    screenType,
                    appVersion = appVersion,
                    nextAppointmentsSchedule = appointmentSchedules
                )
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerify { duquesaScreensService wasNot called }
        coVerify { appContentScreenDetailService wasNot called }
        coVerify { formNavigationService wasNot called }
        coVerify { formNavigationService wasNot called }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { aliceScreensService.getOtherAliceScreens(any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getByScreenType should return error when screen not found`() {
        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/invalid_screen") { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }
        }
    }

    @Test
    fun `#getByScreenType should return home screen as expected`() {
        val screenType = ScreenType.UNIFIED_HEALTH
        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            appContentScreenDetailService.getFirstActiveHealthFormByPersonId(person.id, any())
        } returns appContentScreenDetail.success()

        coEvery {
            formNavigationService.startForm(
                person.id,
                healthForm.key,
                appContentScreenDetail.healthFormSource
            )
        } returns HealthFormActionsTransport(currentQuestion = healthFormQuestion).success()

        coEvery {
            formNavigationService.getBaseQuestionResponse(
                personId = person.id,
                question = healthFormQuestion,
                source = appContentScreenDetail.healthFormSource,
                action = any(),
                appContentScreenDetail = appContentScreenDetail,
                shouldGenerateAnswerGroup = false
            )
        } returns questionnaireQuestionResponse.success()

        coEvery {
            aliceScreensService.getUnifiedHealthScreen(
                personId = person.id,
                question = null
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerify { duquesaScreensService wasNot called }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { appContentScreenDetailService.getFirstActiveHealthFormByPersonId(any(), any()) }
        coVerifyOnce { formNavigationService.startForm(any(), any(), any()) }
        coVerifyOnce {
            formNavigationService.getBaseQuestionResponse(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            )
        }
        coVerifyOnce { aliceScreensService.getUnifiedHealthScreen(any(), any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getByScreenType should return home screen throwing exception on questionnaire`() {
        val screenType = ScreenType.UNIFIED_HEALTH
        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            appContentScreenDetailService.getFirstActiveHealthFormByPersonId(person.id, any())
        } returns appContentScreenDetail.success()

        coEvery {
            appContentScreenDetailService.getNextPermanentHealthFormByPersonId(person.id, any())
        } returns appContentScreenDetail.success()

        coEvery {
            formNavigationService.startForm(
                person.id,
                healthForm.key,
                appContentScreenDetail.healthFormSource
            )
        } returns InvalidQuestionIndexException("").failure()

        coEvery {
            appContentScreenDetailService.markAsInactive(appContentScreenDetail)
        } returns appContentScreenDetail.copy(status = ScreenDetailStatus.INACTIVE).success()

        coEvery {
            aliceScreensService.getUnifiedHealthScreen(
                personId = person.id
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerify { duquesaScreensService wasNot called }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { appContentScreenDetailService.getFirstActiveHealthFormByPersonId(any(), any()) }
        coVerify(exactly = 2) { formNavigationService.startForm(any(), any(), any()) }
        coVerifyNone {
            formNavigationService.getBaseQuestionResponse(
                personId = person.id,
                question = healthFormQuestion,
                source = appContentScreenDetail.healthFormSource,
                action = any(),
                appContentScreenDetail = appContentScreenDetail,
                shouldGenerateAnswerGroup = false
            )
        }
        coVerifyOnce { aliceScreensService.getUnifiedHealthScreen(any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getByScreenType should return unified health screen without questionnaire when questionnaire is not NPS`() {
        val screenType = ScreenType.UNIFIED_HEALTH
        val expectedQuestion = null

        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            appContentScreenDetailService.getFirstActiveHealthFormByPersonId(person.id, any())
        } returns NotFoundException().failure()

        coEvery {
            appContentScreenDetailService.getNextPermanentHealthFormByPersonId(person.id, any())
        } returns appContentScreenDetail.success()

        coEvery {
            formNavigationService.startForm(
                person.id,
                healthForm.key,
                appContentScreenDetail.healthFormSource
            )
        } returns HealthFormActionsTransport(currentQuestion = healthFormQuestion).success()

        coEvery {
            formNavigationService.getBaseQuestionResponse(
                personId = person.id,
                question = healthFormQuestion,
                source = appContentScreenDetail.healthFormSource,
                action = any(),
                appContentScreenDetail = appContentScreenDetail,
                shouldGenerateAnswerGroup = false
            )
        } returns questionnaireQuestionResponse.success()

        coEvery {
            aliceScreensService.getUnifiedHealthScreen(
                personId = person.id,
                question = expectedQuestion
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerify { duquesaScreensService wasNot called }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { appContentScreenDetailService.getFirstActiveHealthFormByPersonId(any(), any()) }
        coVerifyOnce { appContentScreenDetailService.getNextPermanentHealthFormByPersonId(any(), any()) }
        coVerifyOnce { formNavigationService.startForm(any(), any(), any()) }
        coVerifyOnce {
            formNavigationService.getBaseQuestionResponse(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            )
        }
        coVerifyOnce { aliceScreensService.getUnifiedHealthScreen(any(), any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getByScreenType should return unified health screen with questionnaire when questionnaire is NPS and no ab test is enabled`() {
        val screenType = ScreenType.UNIFIED_HEALTH

        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            appContentScreenDetailService.getFirstActiveHealthFormByPersonId(person.id, any())
        } returns NotFoundException().failure()

        coEvery {
            appContentScreenDetailService.getNextPermanentHealthFormByPersonId(person.id, any())
        } returns npsAppContentScreenDetail.success()

        coEvery {
            formNavigationService.startForm(
                person.id,
                npsHealthForm.key,
                npsAppContentScreenDetail.healthFormSource
            )
        } returns HealthFormActionsTransport(currentQuestion = healthFormQuestion).success()

        coEvery {
            formNavigationService.getBaseQuestionResponse(
                personId = person.id,
                question = healthFormQuestion,
                source = npsAppContentScreenDetail.healthFormSource,
                action = any(),
                appContentScreenDetail = npsAppContentScreenDetail,
                shouldGenerateAnswerGroup = false
            )
        } returns questionnaireQuestionResponse.success()

        coEvery {
            aliceScreensService.getUnifiedHealthScreen(
                personId = person.id,
                question = questionnaireQuestionResponse
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerify { duquesaScreensService wasNot called }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { appContentScreenDetailService.getFirstActiveHealthFormByPersonId(any(), any()) }
        coVerifyOnce { appContentScreenDetailService.getNextPermanentHealthFormByPersonId(any(), any()) }
        coVerifyOnce { formNavigationService.startForm(any(), any(), any()) }
        coVerifyOnce {
            formNavigationService.getBaseQuestionResponse(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            )
        }
        coVerifyOnce { aliceScreensService.getUnifiedHealthScreen(any(), any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getByScreenType should return unified health screen with questionnaire when questionnaire is NPS and notification ab test is enabled with group_A`() =
        runBlocking {
            val screenType = ScreenType.UNIFIED_HEALTH

            val expectedResult = ScreensTransport(
                id = screenType.value,
                layout = ScreenLayout(type = "", body = listOf())
            )

            coEvery {
                appContentScreenDetailService.getFirstActiveHealthFormByPersonId(person.id, any())
            } returns NotFoundException().failure()

            coEvery {
                appContentScreenDetailService.getNextPermanentHealthFormByPersonId(person.id, any())
            } returns npsAppContentScreenDetail.success()

            coEvery {
                formNavigationService.startForm(
                    person.id,
                    npsHealthForm.key,
                    npsAppContentScreenDetail.healthFormSource
                )
            } returns HealthFormActionsTransport(currentQuestion = healthFormQuestion).success()

            coEvery {
                formNavigationService.getBaseQuestionResponse(
                    personId = person.id,
                    question = healthFormQuestion,
                    source = npsAppContentScreenDetail.healthFormSource,
                    action = any(),
                    appContentScreenDetail = npsAppContentScreenDetail,
                    shouldGenerateAnswerGroup = false
                )
            } returns questionnaireQuestionResponse.success()

            coEvery {
                aliceScreensService.getUnifiedHealthScreen(
                    personId = person.id,
                    question = questionnaireQuestionResponse
                )
            } returns expectedResult.success()

            withFeatureFlag(FeatureNamespace.ALICE_APP, "ab_nps_on_notifications_center", mapOf(0 to 1.0, 1 to 0.0)) {
                authenticatedAs(token, toTestPerson(person)) {
                    get("/app_content/screen/$screenType", headersByVersion(notificationNpsAppVersion)) { response ->
                        ResponseAssert.assertThat(response).isOKWithData(expectedResult)
                    }
                }
            }

            coVerify { duquesaScreensService wasNot called }
            coVerify { crmAnalyticsTracker wasNot called }

            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { appContentScreenDetailService.getFirstActiveHealthFormByPersonId(any(), any()) }
            coVerifyOnce { appContentScreenDetailService.getNextPermanentHealthFormByPersonId(any(), any()) }
            coVerifyOnce { formNavigationService.startForm(any(), any(), any()) }
            coVerifyOnce {
                formNavigationService.getBaseQuestionResponse(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
            coVerifyOnce { aliceScreensService.getUnifiedHealthScreen(any(), any()) }
            coVerifyOnce { personService.get(any()) }
        }

    @Test
    fun `#getByScreenType should return unified health screen without questionnaire when questionnaire is NPS and notification ab test is enabled with group_B`() =
        runBlocking {
            val screenType = ScreenType.UNIFIED_HEALTH

            val expectedResult = ScreensTransport(
                id = screenType.value,
                layout = ScreenLayout(type = "", body = listOf())
            )

            coEvery {
                personService.getNationalId(person.id)
            } returns person.nationalId.success()

            coEvery {
                memberService.getCurrent(person.id)
            } returns memberAlice.success()

            coEvery {
                appContentScreenDetailService.getFirstActiveHealthFormByPersonId(person.id, any())
            } returns NotFoundException().failure()

            coEvery {
                appContentScreenDetailService.getNextPermanentHealthFormByPersonId(person.id, any())
            } returns npsAppContentScreenDetail.success()

            coEvery {
                formNavigationService.startForm(
                    person.id,
                    npsHealthForm.key,
                    npsAppContentScreenDetail.healthFormSource
                )
            } returns HealthFormActionsTransport(currentQuestion = healthFormQuestion).success()

            coEvery {
                formNavigationService.getBaseQuestionResponse(
                    personId = person.id,
                    question = healthFormQuestion,
                    source = npsAppContentScreenDetail.healthFormSource,
                    action = any(),
                    appContentScreenDetail = npsAppContentScreenDetail,
                    shouldGenerateAnswerGroup = false
                )
            } returns questionnaireQuestionResponse.success()

            coEvery {
                aliceScreensService.getUnifiedHealthScreen(
                    personId = person.id,
                    question = null // no questionnaire
                )
            } returns expectedResult.success()

            withFeatureFlag(FeatureNamespace.ALICE_APP, "ab_nps_on_notifications_center", mapOf(0 to 0.0, 1 to 1.0)) {
                authenticatedAs(token, toTestPerson(person)) {
                    get("/app_content/screen/$screenType", headersByVersion(notificationNpsAppVersion)) { response ->
                        ResponseAssert.assertThat(response).isOKWithData(expectedResult)
                    }
                }
            }

            coVerify { duquesaScreensService wasNot called }
            coVerifyOnce { crmAnalyticsTracker.sendEvent(any(), any()) }
            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { personService.getNationalId(any()) }
            coVerifyOnce { appContentScreenDetailService.getFirstActiveHealthFormByPersonId(any(), any()) }
            coVerifyOnce { appContentScreenDetailService.getNextPermanentHealthFormByPersonId(any(), any()) }
            coVerifyOnce { formNavigationService.startForm(any(), any(), any()) }
            coVerifyOnce {
                formNavigationService.getBaseQuestionResponse(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
            coVerifyOnce { aliceScreensService.getUnifiedHealthScreen(any(), any()) }
            coVerifyOnce { personService.get(any()) }
        }

    @Test
    fun `#getByScreenType should return unified health screen with questionnaire when questionnaire is NPS and journey ab test is enabled with HOME`() =
        runBlocking {
            val screenType = ScreenType.UNIFIED_HEALTH

            val expectedResult = ScreensTransport(
                id = screenType.value,
                layout = ScreenLayout(type = "", body = listOf())
            )

            coEvery {
                memberService.getCurrent(person.id)
            } returns memberAlice.success()

            coEvery {
                appContentScreenDetailService.getFirstActiveHealthFormByPersonId(person.id, any())
            } returns NotFoundException().failure()

            coEvery {
                appContentScreenDetailService.getNextPermanentHealthFormByPersonId(person.id, any())
            } returns npsAppContentScreenDetail.success()

            coEvery {
                formNavigationService.startForm(
                    person.id,
                    npsHealthForm.key,
                    npsAppContentScreenDetail.healthFormSource
                )
            } returns HealthFormActionsTransport(currentQuestion = healthFormQuestion).success()

            coEvery {
                formNavigationService.getBaseQuestionResponse(
                    personId = person.id,
                    question = healthFormQuestion,
                    source = npsAppContentScreenDetail.healthFormSource,
                    action = any(),
                    appContentScreenDetail = npsAppContentScreenDetail,
                    shouldGenerateAnswerGroup = false
                )
            } returns questionnaireQuestionResponse.success()

            coEvery {
                aliceScreensService.getUnifiedHealthScreen(
                    personId = person.id,
                    question = questionnaireQuestionResponse
                )
            } returns expectedResult.success()

            coEvery {
                appContentABTestService.getNPSJourneyABTest(person.id)
            } returns NPSJourneyTestVariant.HOME.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/app_content/screen/$screenType", headersByVersion(journeyNpsAppVersion)) { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expectedResult)
                }
            }


            coVerify { duquesaScreensService wasNot called }
            coVerify { crmAnalyticsTracker wasNot called }
            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { appContentScreenDetailService.getFirstActiveHealthFormByPersonId(any(), any()) }
            coVerifyOnce { appContentScreenDetailService.getNextPermanentHealthFormByPersonId(any(), any()) }
            coVerifyOnce { formNavigationService.startForm(any(), any(), any()) }
            coVerifyOnce {
                formNavigationService.getBaseQuestionResponse(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
            coVerifyOnce { aliceScreensService.getUnifiedHealthScreen(any(), any()) }
            coVerifyOnce { personService.get(any()) }
        }

    @Test
    fun `#getByScreenType should return unified health screen without questionnaire when questionnaire is NPS and journey ab test is enabled with JOURNEY`() =
        runBlocking {
            val screenType = ScreenType.UNIFIED_HEALTH

            val expectedResult = ScreensTransport(
                id = screenType.value,
                layout = ScreenLayout(type = "", body = listOf())
            )

            coEvery {
                personService.getNationalId(person.id)
            } returns person.nationalId.success()

            coEvery {
                memberService.getCurrent(person.id)
            } returns memberAlice.success()

            coEvery {
                appContentScreenDetailService.getFirstActiveHealthFormByPersonId(person.id, any())
            } returns NotFoundException().failure()

            coEvery {
                appContentScreenDetailService.getNextPermanentHealthFormByPersonId(person.id, any())
            } returns npsAppContentScreenDetail.success()

            coEvery {
                formNavigationService.startForm(
                    person.id,
                    npsHealthForm.key,
                    npsAppContentScreenDetail.healthFormSource
                )
            } returns HealthFormActionsTransport(currentQuestion = healthFormQuestion).success()

            coEvery {
                formNavigationService.getBaseQuestionResponse(
                    personId = person.id,
                    question = healthFormQuestion,
                    source = npsAppContentScreenDetail.healthFormSource,
                    action = any(),
                    appContentScreenDetail = npsAppContentScreenDetail,
                    shouldGenerateAnswerGroup = false
                )
            } returns questionnaireQuestionResponse.success()

            coEvery {
                aliceScreensService.getUnifiedHealthScreen(
                    personId = person.id,
                    question = null // no questionnaire
                )
            } returns expectedResult.success()

            coEvery {
                appContentABTestService.getNPSJourneyABTest(person.id)
            } returns NPSJourneyTestVariant.JOURNEY.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/app_content/screen/$screenType", headersByVersion(journeyNpsAppVersion)) { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expectedResult)
                }
            }

            coVerify { duquesaScreensService wasNot called }

            coVerifyOnce { crmAnalyticsTracker.sendEvent(any(), any()) }
            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { personService.getNationalId(any()) }
            coVerifyOnce { appContentScreenDetailService.getFirstActiveHealthFormByPersonId(any(), any()) }
            coVerifyOnce { appContentScreenDetailService.getNextPermanentHealthFormByPersonId(any(), any()) }
            coVerifyOnce { formNavigationService.startForm(any(), any(), any()) }
            coVerifyOnce {
                formNavigationService.getBaseQuestionResponse(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
            coVerifyOnce { aliceScreensService.getUnifiedHealthScreen(any(), any()) }
            coVerifyOnce { personService.get(any()) }
        }

    @Test
    fun `#getByScreenType should return unified health screen without questionnaire when questionnaire is NPS and journey ab test is enabled with NOTIFICATION`() =
        runBlocking {
            val screenType = ScreenType.UNIFIED_HEALTH

            val expectedResult = ScreensTransport(
                id = screenType.value,
                layout = ScreenLayout(type = "", body = listOf())
            )

            coEvery {
                personService.getNationalId(person.id)
            } returns person.nationalId.success()

            coEvery {
                memberService.getCurrent(person.id)
            } returns memberAlice.success()

            coEvery {
                appContentScreenDetailService.getFirstActiveHealthFormByPersonId(person.id, any())
            } returns NotFoundException().failure()

            coEvery {
                appContentScreenDetailService.getNextPermanentHealthFormByPersonId(person.id, any())
            } returns npsAppContentScreenDetail.success()

            coEvery {
                formNavigationService.startForm(
                    person.id,
                    npsHealthForm.key,
                    npsAppContentScreenDetail.healthFormSource
                )
            } returns HealthFormActionsTransport(currentQuestion = healthFormQuestion).success()

            coEvery {
                formNavigationService.getBaseQuestionResponse(
                    personId = person.id,
                    question = healthFormQuestion,
                    source = npsAppContentScreenDetail.healthFormSource,
                    action = any(),
                    appContentScreenDetail = npsAppContentScreenDetail,
                    shouldGenerateAnswerGroup = false
                )
            } returns questionnaireQuestionResponse.success()

            coEvery {
                aliceScreensService.getUnifiedHealthScreen(
                    personId = person.id,
                    question = null // no questionnaire
                )
            } returns expectedResult.success()

            coEvery {
                appContentABTestService.getNPSJourneyABTest(person.id)
            } returns NPSJourneyTestVariant.NOTIFICATION.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/app_content/screen/$screenType", headersByVersion(journeyNpsAppVersion)) { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expectedResult)
                }
            }


            coVerify { duquesaScreensService wasNot called }

            coVerify(exactly = 2) { crmAnalyticsTracker.sendEvent(any(), any()) }
            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { personService.getNationalId(any()) }
            coVerifyOnce { appContentScreenDetailService.getFirstActiveHealthFormByPersonId(any(), any()) }
            coVerifyOnce { appContentScreenDetailService.getNextPermanentHealthFormByPersonId(any(), any()) }
            coVerifyOnce { formNavigationService.startForm(any(), any(), any()) }
            coVerifyOnce {
                formNavigationService.getBaseQuestionResponse(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            }
            coVerifyOnce { aliceScreensService.getUnifiedHealthScreen(any(), any()) }
            coVerifyOnce { personService.get(any()) }
        }

    @Test
    fun `#getByScreenType should return home screen as expected with no questionnaire`() {
        val screenType = ScreenType.UNIFIED_HEALTH
        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            appContentScreenDetailService.getFirstActiveHealthFormByPersonId(person.id, any())
        } returns NotFoundException().failure()

        coEvery {
            appContentScreenDetailService.getNextPermanentHealthFormByPersonId(person.id, any())
        } returns NotFoundException().failure()

        coEvery {
            aliceScreensService.getUnifiedHealthScreen(
                person.id
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerify { duquesaScreensService wasNot called }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { appContentScreenDetailService.getFirstActiveHealthFormByPersonId(any(), any()) }
        coVerifyOnce { appContentScreenDetailService.getNextPermanentHealthFormByPersonId(any(), any()) }
        coVerify { formNavigationService wasNot called }
        coVerifyOnce { aliceScreensService.getUnifiedHealthScreen(any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getByScreenType should return an error`() {
        val screenType = ScreenType.MAIN_MENU

        coEvery { memberService.getCurrent(person.id) } returns memberAlice.success()

        coEvery {
            aliceScreensService.getOtherAliceScreens(
                AliceScreensData(
                    person.id,
                    screenType,
                    appVersion = appVersion,
                )
            )
        } returns RuntimeException().failure()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType") { response ->
                ResponseAssert.assertThat(response).isInternalServerError()
            }
        }

        coVerify { duquesaScreensService wasNot called }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { aliceScreensService.getOtherAliceScreens(any()) }
    }

    @Test
    fun `#getByScreenType should return alice copay screen as expected`() {
        val screenType = ScreenType.COPAY_MENU
        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            aliceScreensService.getCopayScreens(
                person.id,
                screenType,
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerify { duquesaScreensService wasNot called }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerify { appContentScreenDetailService wasNot called }
        coVerify { formNavigationService wasNot called }
        coVerifyOnce { aliceScreensService.getCopayScreens(any(), any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getByScreenType should return alice ombudsman screen as expected`() {
        val screenType = ScreenType.OMBUDSMAN_MENU
        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            aliceScreensService.getOmbudsmanScreens(
                person.id,
                screenType,
                Platform.ANDROID
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerify { duquesaScreensService wasNot called }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerify { appContentScreenDetailService wasNot called }
        coVerify { formNavigationService wasNot called }
        coVerifyOnce { aliceScreensService.getOmbudsmanScreens(any(), any(), any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getByScreenType should return alice unified health screen as expected`() {
        val screenType = ScreenType.UNIFIED_HEALTH
        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            appContentScreenDetailService.getFirstActiveHealthFormByPersonId(person.id, any())
        } returns appContentScreenDetail.success()

        coEvery {
            formNavigationService.startForm(
                person.id,
                healthForm.key,
                appContentScreenDetail.healthFormSource
            )
        } returns HealthFormActionsTransport(currentQuestion = healthFormQuestion).success()

        coEvery {
            formNavigationService.getBaseQuestionResponse(
                personId = person.id,
                question = healthFormQuestion,
                source = appContentScreenDetail.healthFormSource,
                action = any(),
                appContentScreenDetail = appContentScreenDetail,
                shouldGenerateAnswerGroup = false
            )
        } returns questionnaireQuestionResponse.success()

        coEvery {
            aliceScreensService.getUnifiedHealthScreen(
                person.id,
                question = null,
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerify { duquesaScreensService wasNot called }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { appContentScreenDetailService.getFirstActiveHealthFormByPersonId(any(), any()) }
        coVerifyOnce { formNavigationService.startForm(any(), any(), any()) }
        coVerifyOnce {
            formNavigationService.getBaseQuestionResponse(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            )
        }
        coVerifyOnce { aliceScreensService.getUnifiedHealthScreen(any(), any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getByScreenType should return alice health all demands screen as expected`() {
        val screenType = ScreenType.HEALTH_ALL_DEMANDS
        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            aliceScreensService.getHealthAllDemandsScreen(
                person.id,
                null,
                SemanticVersion("1.0.0")
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerifyOnce { memberService.getCurrent(any()) }
        coVerify { appContentScreenDetailService wasNot called }
        coVerify { formNavigationService wasNot called }
        coVerify { duquesaScreensService wasNot called }
        coVerifyOnce { aliceScreensService.getHealthAllDemandsScreen(any(), any(), any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getByScreenType should return alice health all demands screen with filter as expected`() {
        val screenType = ScreenType.HEALTH_ALL_DEMANDS
        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            aliceScreensService.getHealthAllDemandsScreen(
                person.id,
                "TEST_REQUEST",
                SemanticVersion("1.0.0")
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType?type=${ActionPlanTaskType.TEST_REQUEST}")
            { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerifyOnce { memberService.getCurrent(any()) }
        coVerify { appContentScreenDetailService wasNot called }
        coVerify { formNavigationService wasNot called }
        coVerifyOnce { aliceScreensService.getHealthAllDemandsScreen(any(), any(), any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getByScreenType should return duquesa screen as expected`() {
        val screenType = ScreenType.DUQUESA_HOME
        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            memberService.getCurrent(person.id)
        } returns memberDuquesa.success()

        coEvery {
            duquesaScreensService.getHome(
                person.id,
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType")
            { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerifyOnce { memberService.getCurrent(any()) }
        coVerify { appContentScreenDetailService wasNot called }
        coVerify { formNavigationService wasNot called }
        coVerify { aliceScreensService wasNot called }
        coVerifyOnce { duquesaScreensService.getHome(any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getByScreenType should return duquesa screen as expected with isPregnant parameter`() {
        val screenType = ScreenType.DUQUESA_APPOINTMENT_SCHEDULE_TYPE_DOCTOR
        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            memberService.getCurrent(person.id)
        } returns memberDuquesa.success()

        coEvery {
            duquesaScreensService.getAppointmentScheduleTypeScreen(
                person.id,
                ScheduleAppointmentType.DOCTOR_FAMILY,
                FiltersGetScreen(true),
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType?isPregnant=true")
            { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerifyOnce { memberService.getCurrent(any()) }
        coVerify { appContentScreenDetailService wasNot called }
        coVerify { formNavigationService wasNot called }
        coVerify { aliceScreensService wasNot called }
        coVerifyOnce { duquesaScreensService.getAppointmentScheduleTypeScreen(any(), any(), any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getByScreenType should call getOtherAliceScreens with the provided selected_option param`() {
        val screenType = ScreenType.REFUND_HEALTH_EVENT_TYPE
        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            aliceScreensService.getOtherAliceScreens(
                AliceScreensData(
                    person.id,
                    screenType,
                    selectedOption = "test_option",
                    appVersion = appVersion,
                )
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType?selected_option=test_option")
            { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { aliceScreensService.getOtherAliceScreens(any()) }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getByScreenType should call getOtherAliceScreen with the provided task_statuses param`() {
        val screenType = ScreenType.REDESIGN_HEALTH_PLAN_HISTORY
        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            aliceScreensService.getOtherAliceScreens(
                AliceScreensData(
                    person.id,
                    screenType,
                    appVersion = appVersion,
                    taskStatuses = listOf(ActionPlanTaskStatus.DONE, ActionPlanTaskStatus.OVERDUE)
                )
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType?task_statuses=DONE&task_statuses=OVERDUE")
            { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce {
            aliceScreensService.getOtherAliceScreens(any())
        }
        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getByScreenType should call getOtherAliceScreen with the provided demand_statuses param`() {
        val screenType = ScreenType.REDESIGN_HEALTH_PLAN_DEMAND_DETAIL
        val expectedResult = ScreensTransport(
            id = screenType.value,
            layout = ScreenLayout(type = "", body = listOf())
        )

        coEvery {
            aliceScreensService.getOtherAliceScreens(
                AliceScreensData(
                    person.id,
                    screenType,
                    appVersion = appVersion,
                    demandStatuses = listOf(DemandActionPlanStatus.ACTIVE, DemandActionPlanStatus.DONE)
                )
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/app_content/screen/$screenType?demand_statuses=ACTIVE&demand_statuses=DONE")
            { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce {
            aliceScreensService.getOtherAliceScreens(any())
        }
        coVerifyOnce { personService.get(any()) }
    }
}
