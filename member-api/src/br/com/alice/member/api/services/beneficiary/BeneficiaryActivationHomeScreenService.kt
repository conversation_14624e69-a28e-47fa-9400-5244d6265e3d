package br.com.alice.member.api.services.beneficiary

import br.com.alice.app.content.model.*
import br.com.alice.app.content.model.section.ActionCardBadge
import br.com.alice.app.content.model.section.ActionCardSection
import br.com.alice.app.content.model.section.ActionableCustomContent
import br.com.alice.app.content.model.section.ActivationVideoCallSection
import br.com.alice.app.content.model.section.GridGutter
import br.com.alice.app.content.model.section.GridVariant
import br.com.alice.app.content.model.section.Staff
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.models.activation.ActivationItemTransport
import br.com.alice.member.api.models.activation.ActivationScreenTransport
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class BeneficiaryActivationHomeScreenService {
    private val activationHomeScreenId = "activation_home"
    private val activationHealthDeclarationsScreenId = "activation_health_declarations"
    private val activationCPTsScreenId = "activation_cpts"
    private val activationReviewTermsScreenId = "activation_review_terms"
    private val videocallOrientationsScreenId = "videocall_orientations"
    private val beneficiaryVideocallOrientationsScreenId = "beneficiary_videocall_orientations"
    private val dependentsVideocallOrientationsScreenId = "dependents_videocall_orientations"
    private val addedDependentInstructionsScreenId = "added_dependent_instructions"

    private val videocallInstructionsEndpoint = "${ServiceConfig.baseUrl}/beneficiaries/screen/videocall_orientations"
    private val beneficiaryVideocallInstructionsEndpoint = "${ServiceConfig.baseUrl}/beneficiaries/screen/videocall_orientations/beneficiary"
    private val dependentsVideocallInstructionsEndpoint = "${ServiceConfig.baseUrl}/beneficiaries/screen/videocall_orientations/dependents"
    private val addedDependentInstructionsEndpoint = "${ServiceConfig.baseUrl}/beneficiaries/screen/added_dependent_instructions"

    fun buildActivationHomeCards(transport: ActivationScreenTransport): ScreensTransport {
        val sections = listOf(
            ScreenModule.getGridModuleSection(
                children = listOf(buildTitle(transport), buildSubtitle()),
                gutter = GridGutter.GUTTER_2,
                variant = GridVariant.FULL_WIDTH
            ),
            buildAddedDependentCalloutIfNeeded(transport),
            buildHealthDeclarationPhase(transport),
            buildVideoCallPhase(transport),
            buildWaitingCPTsPhase(transport),
            buildCPTsConfirmationPhase(transport),
            buildReviewTermsPhase(transport)
        )
        return buildScreenStructure(
            id = activationHomeScreenId,
            title = null,
            sections = sections.filterNotNull(),
            footer = buildProceedFooterIfNeeded(transport),
            showLogoutButton = true
        )
    }

    private fun buildProceedFooterIfNeeded(transport: ActivationScreenTransport) : Section? {
        if (transport.allFinishedBeneficiaries) {
            return ScreenModule.getButtonFooter(
                "proceed_button",
                "Avançar",
                RemoteAction(
                    mobileRoute = ActionRouting.B2B_ACTIVATION,
                    params = mapOf("navigate_to_route_on_complete" to true)
                )
            )
        }

        return null
    }

    fun buildHealthDeclarationListScreen(transport: ActivationScreenTransport): ScreensTransport {
        val menuItems = transport.availableDependents.map {
            buildMenuSectionFromTransport(
                id = "health_declaration",
                menuItem = it,
                beneficiarySubtitle = "Sua Declaração de Saúde (DS)",
                dependentSubtitle = "DS de ${it.personFirstName}",
                dependentAdultPendingTitle =  "O dependente maior de idade deve preencher a DS em sua própria conta.",
                remoteAction = RemoteAction(
                    mobileRoute = ActionRouting.HEALTH_DECLARATION,
                    popToRouteOnComplete = activationHealthDeclarationsScreenId,
                    params = buildHealthDeclarationParameters(it),
                ),
            )
        }
        val sections = listOf(
            ScreenModule.getListMenuSectionWithBody("health_declaration_list", menuItems),
            buildTipCallout(),
        )
        return buildScreenStructure(activationHealthDeclarationsScreenId, "Declaração de Saúde", sections)
    }

    fun buildCPTsListScreen(transport: ActivationScreenTransport): ScreensTransport {
        val menuItems = transport.availableDependents.map {
            buildMenuSectionFromTransport(
                id = "cpt",
                menuItem = it,
                beneficiarySubtitle = "Sua Cobertura Parcial Temporária",
                dependentSubtitle = "CPT de ${it.personFirstName}",
                dependentAdultPendingTitle =  "O dependente maior de idade deve confirmar as CPTs em sua própria conta.",
                remoteAction = getCPTRemoteAction(it.personId, 0)
            )
        }

        val headerText = ScreenModule.getTextSectionWithContent(
            "title",
            "Confira abaixo os membros que receberam Cobertura Parcial Temporária:",
            SectionTextLayout.BODY_MEDIUM,
            Alignment.LEFT,
            SectionPadding.P2,
        )

        val sections = listOf(
            headerText,
            ScreenModule.getListMenuSectionWithBody("cpts_list", menuItems),
            buildTipCallout(),
        )
        return buildScreenStructure(activationCPTsScreenId, "Cobertura Parcial Temporária (CPTs)", sections)
    }

    fun buildReviewTermsListScreen(transport: ActivationScreenTransport): ScreensTransport {
        val menuItems = transport.availableDependents.map {
            buildMenuSectionFromTransport(
                id = "review_terms",
                menuItem = it,
                beneficiarySubtitle = "Seus termos de saúde",
                dependentSubtitle = "Termos de ${it.personFirstName}",
                dependentAdultPendingTitle =  "O dependente maior de idade deve revisar seus termos em sua própria conta.",
                remoteAction = getReviewTermsRemoteAction(it.personId, 0, activationReviewTermsScreenId)
            )
        }

        val sections = listOf(
            ScreenModule.getListMenuSectionWithBody("cpts_list", menuItems),
            buildTipCallout(),
        )
        return buildScreenStructure(activationCPTsScreenId, "Revisar e assinar termos de saúde", sections)
    }

    fun buildVideoCallInstructionsScreen(transport: ActivationScreenTransport) : ScreensTransport {
        val sections = if (transport.dependentsCount > 0) {
            listOf(
                ScreenModule.getTabBarSection(
                    variant = TabBarVariant.TEXTS,
                    items = listOf(
                        TabBarItem(
                            content = "Geral",
                            action = getAsyncRequestRemoteAction(beneficiaryVideocallInstructionsEndpoint)
                        ),
                        TabBarItem(
                            content = "Dependentes",
                            action = getAsyncRequestRemoteAction(dependentsVideocallInstructionsEndpoint)
                        ),
                    )
                )
            )
        } else {
            getBeneficiaryVideocallInstructionsSections()
        }

        return buildScreenStructure(
            id = videocallOrientationsScreenId,
            title = "Orientações para a videochamada",
            sections = sections
        )
    }

    fun buildBeneficiaryVideocallInstructionsScreen() : ScreensTransport {
        return buildScreenStructure(
            id = beneficiaryVideocallOrientationsScreenId,
            title = null,
            sections = getBeneficiaryVideocallInstructionsSections(),
            showAppbar = false
        )
    }

    fun buildDependentsVideocallInstructionsScreen() : ScreensTransport {
        val sections = listOf(
            buildTextSection("Videochamada com dependentes", SectionTextLayout.TITLE_MEDIUM_HIGHLIGHT),
            buildTextSection(
                "•  Os dependentes devem estar presentes e acordados.",
                SectionTextLayout.BODY_MEDIUM
            ),
            buildTextSection(
                "•  Menores de 14 anos precisam estar acompanhados por um responsável.",
                SectionTextLayout.BODY_MEDIUM
            ),
            buildTextSection(
                "•  O titular e os dependentes podem acessar a videochamada usando o mesmo link em dispositivos e locais diferentes.",
                SectionTextLayout.BODY_MEDIUM
            ),
            buildTextSection(
                "•  Dependentes, sejam menores ou maiores de idade, podem baixar o aplicativo da Alice, acessar a conta com seu próprio CPF e agendar a videochamada em outro horário diferente do titular.",
                SectionTextLayout.BODY_MEDIUM
            ),
        )

        return buildScreenStructure(
            id = dependentsVideocallOrientationsScreenId,
            title = null,
            sections = sections,
            showAppbar = false
        )
    }

    fun buildDelayedAddedDependentScreen(): ScreensTransport {
        val sections = listOf(
            buildTextSection("Novo dependente adicionado", SectionTextLayout.TITLE_SMALL),
            buildTextSection("Quando um novo dependente é adicionado após o início da ativação, é necessário concluir as tarefas dos novos dependentes em suas respectivas contas da Alice. Acesse o aplicativo com o CPF do dependente para finalizar as tarefas pendentes e ativar o plano.", SectionTextLayout.BODY_MEDIUM),
        )

        return buildScreenStructure(
            id = addedDependentInstructionsScreenId,
            title = null,
            sections = sections,
            showAppbar = false
        )
    }

    private fun getBeneficiaryVideocallInstructionsSections() : List<Section> =
        listOf(
            buildTextSection("Sobre a videochamada", SectionTextLayout.TITLE_MEDIUM_HIGHLIGHT),
            buildTextSection(
                "•  Uma enfermeira irá revisar com você, e com os dependentes, se houver, as informações fornecidas na declaração de saúde.",
                SectionTextLayout.BODY_MEDIUM
            ),
            buildTextSection("Acesso a videochamada", SectionTextLayout.TITLE_MEDIUM_HIGHLIGHT),
            buildTextSection(
                "•  O link da videochamada também será enviado para o e-mail que foi agendado.",
                SectionTextLayout.BODY_MEDIUM
            ),
            buildTextSection(
                "•  A tolerância para entrada na sala virtual é de 10 minutos. Após esse período, a sala não estará mais disponível, e será preciso  reagendar.",
                SectionTextLayout.BODY_MEDIUM
            ),
            buildTextSection("Outras orientações", SectionTextLayout.TITLE_MEDIUM_HIGHLIGHT),
            buildTextSection(
                "•  Escolha um local reservado, sem interferências, com roupas apropriadas e não esteja dirigindo.",
                SectionTextLayout.BODY_MEDIUM
            ),
            buildTextSection(
                "•  Para sua segurança, a conversa com a enfermagem será gravada. Você receberá um protocolo e poderá solicitar a gravação, caso necessário.",
                SectionTextLayout.BODY_MEDIUM
            )
        )

    private fun buildTextSection(text: String, layout: SectionTextLayout) : Section = ScreenModule.getTextSectionWithContent(
        "title",
        text,
        layout,
        Alignment.LEFT,
        if (layout == SectionTextLayout.TITLE_MEDIUM_HIGHLIGHT || layout == SectionTextLayout.TITLE_SMALL) SectionPadding.P3
        else SectionPadding.P1,
    )

    private fun buildMenuSectionFromTransport(
        id: String,
        menuItem: ActivationItemTransport,
        beneficiarySubtitle: String,
        dependentSubtitle: String,
        dependentAdultPendingTitle: String,
        remoteAction: RemoteAction,
    ) : Section {
        val subTitle = if (menuItem.personIsBeneficiary) {
            beneficiarySubtitle
        } else {
            dependentSubtitle
        }

        val title: String
        val icon: String

        if (menuItem.isPending ) {
            icon = "paper"
            title = if (!menuItem.isMinor && !menuItem.personIsBeneficiary) {
                dependentAdultPendingTitle
            } else {
                "Pendente"
            }
        } else {
            icon = "check_outlined"
            title = if (menuItem.personIsBeneficiary) {
                "Concluída"
            } else {
                "Concluída por seu dependente"
            }
        }

        return buildMenuSection(
            id,
            title = title,
            subTitle = subTitle,
            icon = icon,
            enabled = (menuItem.personIsBeneficiary || menuItem.isMinor) && menuItem.isPending,
            action = remoteAction,
            clickAffordance = menuItem.isPending
        )
    }

    private fun buildScreenStructure(
        id: String,
        title: String?,
        sections: List<Section>,
        footer: Section? = null,
        showLogoutButton: Boolean? = null,
        showAppbar: Boolean? = true
    ) = ScreensTransport(
        id,
        properties = ScreenProperties(safeArea = ScreenSafeArea(bottom = true)),
        layout = ScreenLayout(
            type = "single_column",
            appBar = takeIf {showAppbar == true}?.let {
                AppBar(
                    title = title,
                    back = "",
                    showLogo = true,
                    rightItems = takeIf {showLogoutButton == true}?.let {
                        listOf(AppBarItems.LOGOUT)
                    }
                )
            },
            body = sections,
            footer = footer
        ),
    )

    private fun buildTitle(transport: ActivationScreenTransport) = ScreenModule.getTextSectionWithContent(
        "title",
        "Falta pouco para começar sua jornada, ${transport.beneficiary.personFirstName}.",
        SectionTextLayout.TITLE_X_LARGE_HIGHLIGHT,
        Alignment.LEFT,
    )

    private fun buildSubtitle() = ScreenModule.getTextSectionWithContent(
        "subtitle",
        "Conclua as tarefas para ativar seu plano:",
        SectionTextLayout.BODY_MEDIUM,
        Alignment.LEFT,
        SectionPadding.P2,
    )

    private fun buildHealthDeclarationPhase(
        transport: ActivationScreenTransport,
    ): Section {
        val description = if (transport.dependentsCount > 0)
            "Para começar, precisamos entender o panorama geral da sua saúde e de seus dependentes."
        else
            "Para começar, precisamos entender o panorama geral da sua saúde."


        val badgeLabel = if (transport.dependentsCount > 0)
            "${transport.healthDeclarationItemsCompletedCount} de ${transport.dependentsCount + 1} concluídos"
        else
            "Pendente"

        val remoteAction = RemoteAction(
            mobileRoute = ActionRouting.CHESHIRE_SCREEN,
            params = mapOf(
                "action" to RemoteAction(
                    method = RemoteActionMethod.GET,
                    endpoint = "${ServiceConfig.baseUrl}/beneficiaries/screen/health_declarations",
                    params = mapOf(
                        "screen_id" to activationHealthDeclarationsScreenId
                    )
                )
            )
        )

        return buildActionCardConsideringOrder(
            id = "health_declaration",
            title = "Declaração de Saúde",
            activeDescription = description,
            activeBadgeLabel = badgeLabel,
            disabledDescription = null,
            actionLabel = "Responder questionário",
            remoteAction = remoteAction,
            phaseIndex = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION.order,
            previousPhaseIndex = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD.order,
            transport = transport,
        )
    }

    private fun buildVideoCallPhase(transport: ActivationScreenTransport): Section {
        if (transport.currentBeneficiaryPhaseIndex == BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT_SCHEDULED.order &&
            transport.beneficiary.videoCall?.appointment != null
        ) {
            val videoCallAppointment = transport.beneficiary.videoCall.appointment

            return buildActionCardActive(
                id = "videocall",
                title = "Videochamada com enfermagem",
                description = "Antes da videochamada, baixe o aplicativo Zoom na loja de aplicativo do seu celular e finalize o cadastro inserindo o ano de seu nascimento e e-mail.",
                badgeLabel = "Pendente",
                orientationsButton = getVideocallOrientationsButton(),
                customContent = ActivationVideoCallSection(
                    startTime = videoCallAppointment.startTime,
                    formattedStartTime = formatStartTime(videoCallAppointment.startTime),
                    instructions = "O link da será disponibilizado 30 minutos antes da videochamada.",
                    minimumMinutesToShowLocation = 30,
                    location = videoCallAppointment.location,
                    cancelUrl = videoCallAppointment.cancelUrl,
                    rescheduleUrl = videoCallAppointment.rescheduleUrl,
                    staff = videoCallAppointment.staff?.let {
                        Staff(
                            firstName = it.firstName,
                            profileImageUrl = it.profileImageUrl,
                            description = it.description
                        )
                    },
                ),
            )
        }

        val activeDescription: String
        val disabledDescription: String

        if (transport.dependentsCount > 0) {
            activeDescription = "Nosso time de enfermagem revisará as declarações de saúde com você e seus dependentes. Duração prevista: 60 min."
            disabledDescription = "Nosso time de enfermagem revisará as declarações de saúde com você e seus dependentes."
        } else {
            activeDescription = "Nossa enfermagem revisará a declaração de saúde com você. Duração prevista: 30 min."
            disabledDescription = "Nosso time de enfermagem revisará a declaração de saúde com você."
        }

        return buildActionCardConsideringOrder(
            id = "videocall",
            title = "Videochamada com enfermagem",
            activeDescription = activeDescription,
            activeBadgeLabel = "Pendente",
            disabledDescription = disabledDescription,
            actionLabel = "Agendar videochamada",
            remoteAction = transport.beneficiary.videoCall?.scheduleUrl?.let {
                RemoteAction(
                    mobileRoute = ActionRouting.WEBVIEW,
                    params = mapOf(
                        "link" to it,
                        "pop_on_complete" to true,
                        "token" to "true",
                        "feedback_message" to "Videochamada agendada"
                    )
                )
            },
            phaseIndex = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT.order,
            BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION.order,
            transport = transport,
            orientationsButton = getVideocallOrientationsButton()
        )
    }

    private fun buildWaitingCPTsPhase(transport: ActivationScreenTransport): Section {
        val currentPhaseIndex = transport.currentBeneficiaryPhaseIndex

        val previousPhaseOrder = if (currentPhaseIndex == BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT.order ||
            currentPhaseIndex == BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT_SCHEDULED.order) {
            currentPhaseIndex
        } else {
            BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT.order
        }

        return buildActionCardConsideringOrder(
            id = "waiting_cpts",
            title = "Análise de dados",
            activeDescription = "Estamos analisando os dados enviados. Por favor, aguarde a conclusão para prosseguir.",
            activeBadgeLabel = "Em andamento",
            disabledDescription = "Nesta etapa, analisaremos todas as informações enviadas.",
            actionLabel = null,
            remoteAction = null,
            phaseIndex = BeneficiaryOnboardingPhaseType.WAITING_CPTS_APPLICATION.order,
            previousPhaseIndex = previousPhaseOrder,
            transport = transport,
        )
    }

    private fun buildCPTsConfirmationPhase(transport: ActivationScreenTransport): Section {
        val description = if (transport.dependentsWithCPTs > 0)
            "Revise as restrições temporárias aplicadas com base nas declarações de saúde."
        else
            "Revise as restrições temporárias aplicadas com base nas respostas da sua declaração de saúde."

        val badgeLabel = if (transport.dependentsWithCPTs > 0)
            "${transport.cptsConfirmedItemsCount} de ${transport.beneficiariesWithCPTs} concluídos"
        else
            "Pendente"

        return buildActionCardConsideringOrder(
            id = "cpts_confirmation",
            title = "Cobertura Parcial Temporária",
            activeDescription = description,
            activeBadgeLabel = badgeLabel,
            disabledDescription = "Caso uma CPT seja aplicada, você receberá o termo para assinatura.",
            actionLabel = "Revisar",
            remoteAction= getCPTRemoteAction(transport.beneficiary.personId, transport.dependentsWithCPTs),
            phaseIndex = BeneficiaryOnboardingPhaseType.CPTS_CONFIRMATION.order,
            previousPhaseIndex = BeneficiaryOnboardingPhaseType.WAITING_CPTS_APPLICATION.order,
            transport = transport,
        )
    }

    private fun buildHealthDeclarationParameters(transport: ActivationItemTransport): Map<String, Any> {
        val parameters = mutableMapOf(
            "intro_only" to true,
            "person_id" to transport.personId,
            "beneficiary_national_id" to transport.beneficiaryNationalId,
            "is_minor" to transport.isMinor,
            "reduced_intro" to false,
            "show_help_button" to true,
        )

        if (transport.isMinor)
            parameters["minor_dependent_name"] = transport.personFirstName

        return parameters
    }

    private fun getVideocallOrientationsButton() =
        Button(
            id = "videocall_orientations_button",
            label = Label("Confira as orientações"),
            onTapAction = RemoteAction(
                mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                params = mapOf(
                    "action" to RemoteAction(
                        method = RemoteActionMethod.GET,
                        endpoint = videocallInstructionsEndpoint,
                        params = mapOf(
                            "screen_id" to videocallOrientationsScreenId
                        )
                    )
                )
            )
        )
    private fun getCPTRemoteAction(personId: String, dependentsCount: Int) =
        if (dependentsCount > 0)
            RemoteAction(
                mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                params = mapOf(
                    "action" to RemoteAction(
                        method = RemoteActionMethod.GET,
                        endpoint = "${ServiceConfig.baseUrl}/beneficiaries/screen/cpts",
                        params = mapOf(
                            "screen_id" to activationCPTsScreenId
                        )
                    )
                )
            )
        else {
            RemoteAction(
                mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                params = mapOf(
                    "action" to RemoteAction(
                        method = RemoteActionMethod.GET,
                        endpoint = "${ServiceConfig.baseUrl}/beneficiaries/$personId/screen/cpts_explanation",
                        params = mapOf(
                            "screen_id" to "cpts_explanation"
                        )
                    )
                )
            )
        }

    private fun getReviewTermsRemoteAction(personId: String, dependentsCount: Int, popToRoute: String) =
        if (dependentsCount > 0)
            RemoteAction(
                mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                params = mapOf(
                    "action" to RemoteAction(
                        method = RemoteActionMethod.GET,
                        endpoint = "${ServiceConfig.baseUrl}/beneficiaries/screen/review_terms",
                        params = mapOf(
                            "screen_id" to activationReviewTermsScreenId
                        )
                    )
                )
            )
        else {
            RemoteAction(
                mobileRoute = ActionRouting.REVIEW_TERMS,
                popToRouteOnComplete = popToRoute,
                params = mapOf(
                    "person_id" to personId,
                )
            )
        }

    private fun buildReviewTermsPhase(transport: ActivationScreenTransport): Section {
        val description = if (transport.dependentsCount > 0)
            "Chegou o momento de você entender um pouco mais sobre a Alice e formalizar as declarações de saúde."
        else
            "Chegou o momento de você entender um pouco mais sobre a Alice e formalizar sua declaração de saúde."

        val badgeLabel = if (transport.dependentsCount > 0)
            "${transport.reviewTermsItemsConfirmedCount} de ${transport.dependentsCount+1} concluídos"
        else
            "Pendente"

        return buildActionCardConsideringOrder(
            id = "review_terms",
            title = "Revisar e assinar os termos de saúde",
            activeDescription = description,
            activeBadgeLabel = badgeLabel,
            disabledDescription = "Chegou o momento de formalizar as informações das declarações de saúde.",
            actionLabel = "Revisar",
            remoteAction = getReviewTermsRemoteAction(transport.beneficiary.personId, transport.dependentsCount, activationHomeScreenId),
            phaseIndex = BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE.order,
            previousPhaseIndex = BeneficiaryOnboardingPhaseType.CPTS_CONFIRMATION.order,
            transport = transport,
        )
    }

    private fun buildActionCardConsideringOrder(
        id: String,
        title: String,
        activeDescription: String,
        activeBadgeLabel: String,
        disabledDescription: String?,
        actionLabel: String?,
        remoteAction: RemoteAction?,
        phaseIndex: Int,
        previousPhaseIndex: Int,
        transport: ActivationScreenTransport,
        orientationsButton: Button? = null,
    ): Section {

        val hasAdultsDependentsInPhases = transport.availableDependents.count { !it.personIsBeneficiary && !it.isMinor && it.currentPhaseIndex == phaseIndex } > 0
        val hasMinorDependentsInPhases = transport.availableDependents.count { !it.personIsBeneficiary && it.isMinor && it.currentPhaseIndex == phaseIndex } > 0
        val hasMinorDependentsInPreviousPhases = transport.availableDependents.count { !it.personIsBeneficiary && it.isMinor && it.currentPhaseIndex < phaseIndex } > 0
        val hasDependentsInPhases = hasMinorDependentsInPhases || hasAdultsDependentsInPhases

        // Incomplete phase
        if (transport.currentBeneficiaryPhaseIndex > phaseIndex && hasAdultsDependentsInPhases && !hasMinorDependentsInPhases) {
            return buildActionCardIncomplete(id, title, transport, phaseIndex)
        }

        //Next phase
        if (transport.currentBeneficiaryPhaseIndex < phaseIndex || hasMinorDependentsInPreviousPhases) {
            val description = getDescriptionForDisabledPhase(disabledDescription, transport, phaseIndex, previousPhaseIndex)
            return buildActionCardDisabled(id, title, description)
        }

        // Completed phase
        if (transport.currentBeneficiaryPhaseIndex > phaseIndex &&
            (transport.dependentsCount == 0 || !hasDependentsInPhases)
        )
            return buildActionCardCompleted(id, title)

        // Active phase
        return buildActionCardActive(
            id,
            title,
            activeDescription,
            activeBadgeLabel,
            actionLabel,
            remoteAction,
            null,
            orientationsButton
        )
    }

    private fun getDescriptionForDisabledPhase(
        description: String?,
        transport: ActivationScreenTransport,
        phaseIndex: Int,
        previousPhaseIndex: Int
    ) : String? {
        val hasMinorDependents = transport.availableDependents.count { it.isMinor } > 0
        val hasMinorDependentsInPreviousPhase = transport.availableDependents.count { it.isMinor && it.currentPhaseIndex == previousPhaseIndex } > 0

        // When have minor dependents in the previous phase we must show the description
        if (hasMinorDependents && hasMinorDependentsInPreviousPhase && transport.currentBeneficiaryPhaseIndex >= phaseIndex) {
            return description
        }
        // When have minor dependents, but they are not in previous phase we should not show the description
        else if (hasMinorDependents && !hasMinorDependentsInPreviousPhase && transport.currentBeneficiaryPhaseIndex == previousPhaseIndex) {
            return null
        }
        // When don't have minor dependents and the beneficiary is in the previous phase we must show the description
        else if (transport.currentBeneficiaryPhaseIndex == previousPhaseIndex) {
            return description
        }

        return null
    }

    private fun formatStartTime(startTime: String): String {
        val startLocalDateTime = LocalDateTime.parse(startTime, DateTimeFormatter.ISO_LOCAL_DATE_TIME).toSaoPauloTimeZone()
        val dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy 'às' HH'h'mm")
        return startLocalDateTime.format(dateFormatter)
    }

    private fun buildActionCardActive(
        id: String,
        title: String,
        description: String,
        badgeLabel: String,
        actionLabel: String? = null,
        remoteAction: RemoteAction? = null,
        customContent: ActionableCustomContent? = null,
        orientationsButton: Button? = null,
    ): Section {
        return Section(
            "${id}_action_card",
            SectionType.ACTION_CARD_SECTION,
            ActionCardSection.active(
                title = title,
                description = description,
                badge = ActionCardBadge(label = badgeLabel, icon = null),
                button = if (remoteAction != null && actionLabel != null) Button(
                    id = "${id}_button",
                    label = Label(actionLabel),
                    onTapAction = remoteAction,
                ) else null,
                padding = Padding(24.0, 12.0),
                customContent = customContent,
                orientationsButton = orientationsButton,
            ),
            minAppVersion = SectionType.ACTION_CARD_SECTION.minAppVersion!!
        )
    }

    private fun buildActionCardCompleted(
        id: String,
        title: String,
    ): Section {
        return Section(
            id = "${id}_action_card",
            type = SectionType.ACTION_CARD_SECTION,
            data = ActionCardSection.completed(
                title = title,
                icon = "check_filled",
                padding = Padding(24.0, 12.0)
            ),
            minAppVersion = SectionType.ACTION_CARD_SECTION.minAppVersion!!
        )
    }

    private fun buildActionCardDisabled(
        id: String,
        title: String,
        description: String?,
    ): Section {
        return Section(
            id = "${id}_action_card",
            type = SectionType.ACTION_CARD_SECTION,
            data = ActionCardSection.disabled(
                title = title,
                description = description,
                badge = ActionCardBadge(label = null, icon = "lock"),
                padding = Padding(24.0, 12.0)
            ),
            minAppVersion = SectionType.ACTION_CARD_SECTION.minAppVersion!!
        )
    }

    private fun buildActionCardIncomplete(
        id: String,
        title: String,
        transport: ActivationScreenTransport,
        phaseIndex: Int,
    ): Section {
        val adultsDependentsInPhase = transport.availableDependents.filter { !it.personIsBeneficiary && !it.isMinor && it.currentPhaseIndex == phaseIndex }
        val adultsNameJoined = adultsDependentsInPhase.joinToString(" e ") { it.personFirstName }

        val description = if (adultsDependentsInPhase.count() == 1) {
            "Aguardando $adultsNameJoined concluir a tarefa."
        } else {
            "Aguardando $adultsNameJoined concluírem a tarefa."
        }

        val dependentsWithCompletedPhase = transport.availableDependents.count { it.currentPhaseIndex > phaseIndex }

        return Section(
            id = "${id}_action_card",
            type = SectionType.ACTION_CARD_SECTION,
            data = ActionCardSection.incomplete(
                title = title,
                description = description,
                badge = ActionCardBadge(label = "$dependentsWithCompletedPhase de ${transport.availableDependents.count()} concluídos"),
                padding = Padding(24.0, 12.0)
            ),
            minAppVersion = SectionType.ACTION_CARD_SECTION.minAppVersion!!
        )
    }

    private fun buildMenuSection(
        id: String,
        title: String,
        subTitle: String,
        icon: String,
        enabled: Boolean,
        action: RemoteAction?,
        clickAffordance: Boolean,
    ) = ScreenModule.getMenuSection(
        sectionId = "${id}_menu",
        title = title,
        menuVariant = MenuVariant.INTERMEDIATE,
        label = subTitle,
        clickAffordance = clickAffordance,
        icon = icon,
        onTapAction = action,
        enabled = enabled,
    )

    private fun buildTipCallout() = ScreenModule.getCalloutSection(
        "tip_callout",
        "Dica",
        "Os dependentes podem acessar o app da Alice com seus próprios CPFs para completar suas tarefas.",
        CalloutVariant.INFORMATION
    )

    private fun getAsyncRequestRemoteAction(endpoint: String) = RemoteAction(
        method = RemoteActionMethod.GET,
        endpoint = endpoint
    )

    private fun buildAddedDependentCalloutIfNeeded(transport: ActivationScreenTransport) : Section? {
        if (transport.delayedAddedDependents.isEmpty()) return null

        val title: String
        val description: String

        if (transport.delayedAddedDependents.count() > 1) {
            title = "Novos dependentes adicionados ao plano"
            description = "Para concluir a ativação dos novos dependentes, acesse o aplicativo da Alice com o CPF do dependente."
        } else if (transport.delayedAddedDependents.first().isMinor) {
            val dependentName = transport.delayedAddedDependents.first().personFirstName
            title = "Novo dependente adicionado ao plano"
            description = "Para concluir a ativação de $dependentName, acesse o aplicativo da Alice com o CPF do dependente."
        } else {
            val dependentName = transport.delayedAddedDependents.first().personFirstName
            title = "Novo dependente adicionado ao plano"
            description = "$dependentName precisa acessar o aplicativo da Alice com seu próprio CPF para concluir suas tarefas e ativar o plano."
        }


        return ScreenModule.getCalloutSection(
            sectionId ="added_dependent_callout",
            title = title,
            body = description,
            variant = CalloutVariant.TUTORIAL,
            action = CalloutAction(
                label = "Saiba mais",
                onClickAction = RemoteAction(
                    mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                    params = mapOf(
                        "action" to mapOf(
                            "method" to "GET",
                            "endpoint" to addedDependentInstructionsEndpoint,
                            "params" to mapOf(
                                "screen_id" to addedDependentInstructionsScreenId
                            ),
                        ),
                    ),
                    transition = RemoteActionTransition.MODAL_WITH_HEADER
                ),
            ),
        )

    }
}
