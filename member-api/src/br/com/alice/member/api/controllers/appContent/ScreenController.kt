package br.com.alice.member.api.controllers.appContent

import br.com.alice.app.content.client.AliceScreensService
import br.com.alice.app.content.client.AppContentABTestService
import br.com.alice.app.content.client.AppContentScreenDetailService
import br.com.alice.app.content.client.DuquesaScreensService
import br.com.alice.app.content.model.AliceScreensData
import br.com.alice.app.content.model.FiltersGetScreen
import br.com.alice.app.content.model.ScreenType
import br.com.alice.common.Response
import br.com.alice.common.coFoldResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.logging.logger
import br.com.alice.common.mobile.Platform
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.communication.crm.analytics.AnalyticsEvent
import br.com.alice.communication.crm.analytics.AnalyticsEventName
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.data.layer.models.AppContentScreenDetail
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.DemandActionPlanStatus
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.ScheduleAppointmentType
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.controllers.beneficiary.BeneficiaryActivationHomeScreenController
import br.com.alice.member.api.converters.appContent.ScreenResponseConverter
import br.com.alice.member.api.models.Links
import br.com.alice.member.api.models.currentMemberAppVersion
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.questionnaire.client.FormNavigationService
import br.com.alice.questionnaire.exceptions.InvalidQuestionIndexException
import br.com.alice.questionnaire.models.QuestionnaireQuestionResponse
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.model.AppointmentScheduleWithStaff
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.Parameters
import io.opentelemetry.api.trace.Span
import kotlinx.coroutines.coroutineScope
import java.time.LocalDateTime
import java.util.UUID

class ScreenController(
    private val appContentScreenDetailService: AppContentScreenDetailService,
    private val formNavigationService: FormNavigationService,
    private val aliceScreensService: AliceScreensService,
    private val duquesaScreensService: DuquesaScreensService,
    private val memberService: MemberService,
    private val crmAnalyticsTracker: CrmAnalyticsTracker,
    private val personService: PersonService,
    private val appointmentScheduleService: AppointmentScheduleService,
    private val appContentABTestService: AppContentABTestService,
    private val beneficiaryActivationHomeScreenController: BeneficiaryActivationHomeScreenController
) : Controller() {
    private val groupA = "0"

    suspend fun getByScreenType(screenType: String, queryParams: Parameters) =
        handleScreenType(screenType, queryParams)

    private suspend fun handleScreenType(screenType: String, queryParams: Parameters): Response =
        span("handleScreenType") { span ->
            val personId = currentUid().toPersonId()
            val appVersion = currentMemberAppVersion().version
            val demandId = queryParams["demand_id"]?.toUUID()
            val taskIds = queryParams.getAll("task_ids")?.map { it.toUUID() }
            val filterByTaskType = queryParams["type"]
            val isPregnant = queryParams["isPregnant"]?.toBoolean()
            val selectedOption = queryParams["selected_option"]
            val taskStatuses = queryParams.getAll("task_statuses")?.map { ActionPlanTaskStatus.valueOf(it) }
            val demandStatuses = queryParams.getAll("demand_statuses")?.map {
                DemandActionPlanStatus.valueOf(it)
            } ?: listOf(DemandActionPlanStatus.ACTIVE)

            span.setScreenFilters(
                personId,
                screenType,
                appVersion,
                demandId,
                !taskIds.isNullOrEmpty(),
                filterByTaskType,
                isPregnant,
                selectedOption
            )
            val type = ScreenType.valueOf(screenType.uppercase())
            val questionnaireResponse =
                if (isQuestionnaireAllowedOnScreen(type)) getQuestionnaireQuestionResponseByPersonId(
                    personId,
                    appVersion
                )
                else null

            val nextAppointmentsSchedule = if (shouldShowNextAppointment(type)) getNextAppointments(personId)
            else null

            val person = personService.get(personId).getOrNull()
            val isInternal = person?.isInternal ?: false

            span.setAttribute("is_internal", isInternal.toString())

            val member = memberService.getCurrent(personId).getOrNull()
            span.setMemberInfo(member)

            val appPlatform = currentMemberAppVersion().platform
            val isDuquesaMember = isEnableDuquesaMember(person?.nationalId)

            span.setAttribute("is_duquesa_member", isDuquesaMember.toString())
            span.setAttribute("national_id", person?.nationalId.toString())

            val screenConfig = if (member != null && (member.isDuquesa && !isDuquesaMember)) {
                handleDuquesaScreens(personId, type, appVersion, FiltersGetScreen(isPregnant))
            } else {
                handleAliceScreens(
                    type = type,
                    personId = personId,
                    platform = appPlatform,
                    questionnaireQuestion = questionnaireResponse,
                    nextAppointmentsSchedule = nextAppointmentsSchedule,
                    filterByTaskType = filterByTaskType,
                    taskStatuses = taskStatuses,
                    demandStatuses = demandStatuses,
                    demandId = demandId,
                    selectedOption = selectedOption,
                    appVersion = appVersion,
                    isInternal = isInternal
                )
            }

            screenConfig.coFoldResponse({ result -> ScreenResponseConverter.convert(result) })
        }

    private suspend fun handleAliceScreens(
        type: ScreenType,
        personId: PersonId,
        platform: Platform,
        questionnaireQuestion: QuestionnaireQuestionResponse?,
        nextAppointmentsSchedule: List<AppointmentScheduleWithStaff>?,
        filterByTaskType: String?,
        taskStatuses: List<ActionPlanTaskStatus>?,
        demandStatuses: List<DemandActionPlanStatus>,
        demandId: UUID?,
        selectedOption: String?,
        appVersion: SemanticVersion,
        isInternal: Boolean
    ) = if (type.isCopayContext()) {
        aliceScreensService.getCopayScreens(personId, type)
    } else if (type.isOmbudsmanContext()) {
        aliceScreensService.getOmbudsmanScreens(personId, type, platform)
    } else {
        when (type) {
            ScreenType.UNIFIED_HEALTH -> {

                val activeOrPendingMemebership = memberService.findActiveOrPendingMembership(personId).getOrNullIfNotFound()

                if (activeOrPendingMemebership?.isPending == true && activeOrPendingMemebership.isB2BOrAdesao) {
                    beneficiaryActivationHomeScreenController.buildActivationHomeScreen().success()
                } else {
                    aliceScreensService.getUnifiedHealthScreen(personId, questionnaireQuestion)
                }
            }
            ScreenType.HEALTH_ALL_DEMANDS -> aliceScreensService.getHealthAllDemandsScreen(
                personId,
                filterByTaskType,
                appVersion
            )

            else -> aliceScreensService.getOtherAliceScreens(
                AliceScreensData(
                    personId = personId,
                    screenType = type,
                    demandId = demandId,
                    question = questionnaireQuestion,
                    nextAppointmentsSchedule = nextAppointmentsSchedule,
                    selectedOption = selectedOption,
                    appVersion = appVersion,
                    filterByTaskType = filterByTaskType,
                    taskStatuses = taskStatuses,
                    demandStatuses = demandStatuses,
                    isInternal = isInternal
                )
            )
        }
    }

    private suspend fun handleDuquesaScreens(
        personId: PersonId,
        screenType: ScreenType,
        appVersion: SemanticVersion,
        filters: FiltersGetScreen,
    ) =
        when (screenType) {
            ScreenType.DUQUESA_HOME -> duquesaScreensService.getHome(personId)
            ScreenType.DUQUESA_SCHEDULE_MENU -> duquesaScreensService.getScheduleMenu(personId)
            ScreenType.DUQUESA_SCHEDULE_OTHER_SPECIALTY_MODAL ->
                duquesaScreensService.getScheduleOtherSpecialtyModal(personId)

            ScreenType.DUQUESA_SCHEDULE_ALREADY_SCHEDULED_MODAL_DOCTOR ->

                duquesaScreensService.getScheduleAlreadyScheduledModal(
                    personId,
                    ScheduleAppointmentType.DOCTOR_FAMILY
                )

            ScreenType.DUQUESA_SCHEDULE_ALREADY_SCHEDULED_MODAL_NURSE ->
                duquesaScreensService.getScheduleAlreadyScheduledModal(
                    personId,
                    ScheduleAppointmentType.NURSE_FAMILY
                )

            ScreenType.DUQUESA_ACCREDITED_NETWORK_MENU ->
                duquesaScreensService.getAccreditedNetworkMenu(appVersion, personId)

            ScreenType.ACCREDITED_NETWORK_EMERGENCY_MODAL,
            ScreenType.ACCREDITED_NETWORK_EMERGENCY_MODAL_V2,
            ScreenType.ACCREDITED_NETWORK_HOSPITAL_MENU -> aliceScreensService.getOtherAliceScreens(
                AliceScreensData(
                    personId = personId,
                    screenType = screenType,
                    appVersion = appVersion,
                    filterByTaskType = null,
                )
            )


            ScreenType.DUQUESA_ACCREDITED_NETWORK_EMERGENCY_MODAL,
            ScreenType.ACCREDITED_NETWORK_EMERGENCY_MENU ->
                duquesaScreensService.getAccreditedNetworkEmergencyModal(personId)

            ScreenType.DUQUESA_APPOINTMENT_SCHEDULE_TYPE_DOCTOR ->
                duquesaScreensService.getAppointmentScheduleTypeScreen(
                    personId,
                    ScheduleAppointmentType.DOCTOR_FAMILY,
                    filters,
                )

            ScreenType.DUQUESA_APPOINTMENT_SCHEDULE_TYPE_NURSE ->
                duquesaScreensService.getAppointmentScheduleTypeScreen(
                    personId,
                    ScheduleAppointmentType.NURSE_FAMILY,
                    filters,
                )

            ScreenType.DUQUESA_SERVICE -> duquesaScreensService.getService(personId)
            ScreenType.DUQUESA_SERVICE_ASSISTANCE_MENU -> duquesaScreensService.getAssistanceMenu(personId)
            ScreenType.DUQUESA_SERVICE_ADMINISTRATIVE_MENU -> duquesaScreensService.getAdministrativeMenu(personId)
            ScreenType.DUQUESA_MAIN_MENU -> duquesaScreensService.getMainMenu(personId)
            ScreenType.DUQUESA_CONTRACT -> duquesaScreensService.getContractScreen(personId)
            ScreenType.DUQUESA_PLAN_DETAILS_MENU -> duquesaScreensService.getPlanDetailsMenu(personId)
            ScreenType.DUQUESA_REFERRALS -> duquesaScreensService.getReferrals(personId, appVersion)
            ScreenType.DUQUESA_REFERRAL_AVAILABLE -> duquesaScreensService.getReferralAvailable(personId)
            ScreenType.DUQUESA_REFERRAL_EXPIRED -> duquesaScreensService.getReferralExpired(personId)
            else -> Result.failure(IllegalArgumentException("$screenType does not exists for duquesa app"))
        }

    private fun isQuestionnaireAllowedOnScreen(type: ScreenType) =
        type in listOf(ScreenType.UNIFIED_HEALTH, ScreenType.REDESIGN_UNIFIED_HEALTH)

    private fun shouldShowNextAppointment(type: ScreenType) =
        type in listOf(ScreenType.APPOINTMENT_HUB, ScreenType.REDESIGN_UNIFIED_HEALTH)

    private fun shouldSendNPSNotification(personId: PersonId) = personId.let {
        val group = getNPSNotificationTestABDistribution(it)
        logger.info("NPS notification AB Test", "person_id" to it.toString(), "group" to group)

        // groupA is the control group (NPS at home)
        group != groupA
    }

    private fun getNPSNotificationTestABDistribution(personId: PersonId) =
        FeatureService.inDistribution(
            namespace = FeatureNamespace.ALICE_APP,
            key = "ab_nps_on_notifications_center",
            testValue = personId.toString(),
            defaultReturn = groupA
        )

    private suspend fun sendNPSNotificationAnalyticsEvent(nationalId: String) =
        sendAnalyticsEvent(nationalId, AnalyticsEventName.NPS_FORM)

    private suspend fun sendNPSJourneyAnalyticsEvent(nationalId: String) =
        sendAnalyticsEvent(nationalId, AnalyticsEventName.NPS_JOURNEY_ELIGIBLE)

    private suspend fun sendAnalyticsEvent(
        nationalId: String,
        eventName: AnalyticsEventName
    ) =
        AnalyticsEvent(
            name = eventName,
            timestamp = LocalDateTime.now(),
            properties = mapOf()
        ).let { event ->
            crmAnalyticsTracker.sendEvent(nationalId, event)
        }

    private fun isNPSQuestionnaire(questionnaireSectionContent: String) =
        questionnaireSectionContent == "NPS"


    private fun checkNPSJourneyVersion(appVersion: SemanticVersion) =
        FeatureService.get(
            namespace = FeatureNamespace.ALICE_APP,
            key = "nps_journey_version",
            defaultValue = "99.99.99"
        ).let { appVersion >= SemanticVersion(it) }

    private fun checkNPSNotificationVersion(appVersion: SemanticVersion) =
        FeatureService.get(
            namespace = FeatureNamespace.ALICE_APP,
            key = "nps_new_flow_version",
            defaultValue = "4.5.1"
        ).let { appVersion >= SemanticVersion(it) }

    private suspend fun getQuestionnaireQuestionResponseByPersonId(
        personId: PersonId,
        appVersion: SemanticVersion
    ): QuestionnaireQuestionResponse? =
        coroutineScope {

            // TODO: get QuestionnaireQuestionResponse only if appContentScreenDetail is a NPS Form to avoid a unnecessary call
            val detailToResponse =
                appContentScreenDetailService.getFirstActiveHealthFormByPersonId(personId).getOrNull()
                    ?.let { appContentScreenDetail ->
                        logger.info("ScreenController getFirstActiveHealthFormByPersonId has result", "id" to appContentScreenDetail.id)
                        getHealthForm(appContentScreenDetail, personId).getOrNull()
                            ?.let {
                                logger.info("ScreenController getHealthForm has result", "id" to it.id)
                                appContentScreenDetail to it
                            }
                    } ?: appContentScreenDetailService.getNextPermanentHealthFormByPersonId(personId).getOrNull()
                    ?.let { appContentScreenDetail ->
                        logger.info("ScreenController getNextPermanentHealthFormByPersonId has result", "id" to appContentScreenDetail.id)
                        getHealthForm(appContentScreenDetail, personId).getOrNull()
                            ?.let {
                                logger.info("ScreenController getHealthForm has result", "id" to it.id)
                                appContentScreenDetail to it
                            }
                    }

            detailToResponse?.let { (appContentScreenDetail, response) ->
                if (isNPSQuestionnaire(appContentScreenDetail.sectionContent) &&
                    shouldShowNPSQuestionnaire(personId, appVersion)
                ) {
                    logger.info("ScreenController should show NPS Questionnaire", "person_id" to personId.toString())
                    response
                } else {
                    logger.info("ScreenController should not show NPS", "person_id" to personId.toString())
                    null
                }
            }
        }

    private suspend fun shouldShowNPSQuestionnaire(personId: PersonId, appVersion: SemanticVersion): Boolean {
        return when {
            checkNPSJourneyVersion(appVersion) -> handleJourneyNPS(personId)
            checkNPSNotificationVersion(appVersion) -> handleNotificationNPS(personId)
            else -> {
                logger.info("App version is valid to home NPS", "person_id" to personId.toString())
                true
            }
        }
    }

    private suspend fun handleJourneyNPS(personId: PersonId): Boolean {
        logger.info("App version is valid to NPS journey", "person_id" to personId.toString())

        val variant = appContentABTestService.getNPSJourneyABTest(personId).getOrNull()
        logger.info("NPS journey AB Test", "person_id" to personId.toString(), "variant" to variant)

        if (variant?.isNPSJourneyEligible() == true) {
            personService.getNationalId(personId).map { nationalId ->
                sendNPSJourneyAnalyticsEvent(nationalId)

                if (variant.isNotificationEligible()) {
                    sendNPSNotificationAnalyticsEvent(nationalId)
                }
            }
        }

        return variant?.showHomeCard() ?: false
    }

    private suspend fun handleNotificationNPS(personId: PersonId): Boolean {
        logger.info("App version is valid to NPS notification", "person_id" to personId.toString())

        return if (shouldSendNPSNotification(personId)) {
            personService.getNationalId(personId).map { nationalId ->
                sendNPSNotificationAnalyticsEvent(nationalId)
            }
            false
        } else {
            true
        }
    }

    private suspend fun getNextAppointments(personId: PersonId): List<AppointmentScheduleWithStaff> =
        coroutineScope {
            appointmentScheduleService.findWithStaffBy(
                AppointmentScheduleFilter(
                    personId = personId,
                    status = listOf(AppointmentScheduleStatus.SCHEDULED),
                    endTimeGreater = LocalDateTime.now(),
                    sortOrder = SortOrder.Ascending
                )
            ).get().filter { appointmentSchedule ->
                appointmentSchedule.staff != null
            }.take(3)
        }

    private suspend fun getHealthForm(
        appContentScreenDetail: AppContentScreenDetail,
        personId: PersonId,
    ): Result<QuestionnaireQuestionResponse?, Throwable> = formNavigationService.startForm(
        personId = personId,
        formKey = appContentScreenDetail.sectionContent,
        source = appContentScreenDetail.healthFormSource
    ).flatMap { formActions ->
        val healthFormSource = appContentScreenDetail.healthFormSource

        formNavigationService.getBaseQuestionResponse(
            personId = personId,
            question = formActions.currentQuestion,
            source = appContentScreenDetail.healthFormSource,
            appContentScreenDetail = appContentScreenDetail,
            shouldGenerateAnswerGroup = false,
            action = Links.HealthForm.getQuestionLink(
                formActions.currentQuestion.healthFormId,
                formActions.currentQuestion.id,
                healthFormSource?.id,
                healthFormSource?.type,
                healthFormSource?.subtype
            ),
            backAction = formActions.previousQuestion?.let {
                Links.HealthForm.getPreviousQuestionLink(
                    formActions.previousQuestion!!.healthFormId,
                    formActions.previousQuestion!!.id,
                    healthFormSource?.id,
                    healthFormSource?.type,
                    healthFormSource?.subtype
                )
            }

        )
    }.flatMapError {
        if (it is InvalidQuestionIndexException) {
            logger.error(
                "ScreenController InvalidQuestionIndexException",
                "task_id" to appContentScreenDetail.id,
                "person_id" to appContentScreenDetail.personId,
                "screen_type" to appContentScreenDetail.sectionContent,
            )
            appContentScreenDetailService.markAsInactive(appContentScreenDetail)
        } else {
            logger.error(
                "ScreenController unknown Exception",
                "task_id" to appContentScreenDetail.id,
                "person_id" to appContentScreenDetail.personId,
                "screen_type" to appContentScreenDetail.sectionContent,
                "exception" to it
            )
            appContentScreenDetailService.markAsInactive(appContentScreenDetail)
        }
        NotFoundException().failure()
    }

    private fun Span.setScreenFilters(
        personId: PersonId,
        screenType: String,
        appVersion: SemanticVersion,
        demandId: UUID?,
        hasTaskIds: Boolean,
        filterByTaskType: String?,
        isPregnant: Boolean?,
        selectedOption: String?,
    ) {
        setAttribute("person_id", personId.toString())
        setAttribute("screen_type", screenType)
        setAttribute("demand_id", demandId.toString())
        setAttribute("has_task_ids", hasTaskIds.toString())
        setAttribute("filter_by_task_type", filterByTaskType.toString())
        setAttribute("is_pregnant", isPregnant.toString())
        setAttribute("app_version", appVersion.version)
        setAttribute("selected_option", selectedOption.toString())
    }

    private fun Span.setMemberInfo(
        member: Member?
    ) {
        setAttribute("member_id", member?.id.toString())
        setAttribute("member_is_duquesa", (member?.isDuquesa ?: false).toString())
    }
    private fun isEnableDuquesaMember(nationalId: String?): Boolean =
        isEnableAllDuquesaMember() || isAllowListDuquesaMember(nationalId)

    private fun isAllowListDuquesaMember(nationalId: String?) =
        nationalId?.let {
            FeatureService.inList(
                namespace = FeatureNamespace.ALICE_APP,
                key = "enable_duquesa_member",
                testValue = nationalId
            )
        } ?: false

    private fun isEnableAllDuquesaMember(): Boolean =
        FeatureService.get(
            namespace = FeatureNamespace.ALICE_APP,
            key = "enable_all_access_app_to_duquesa_member",
            defaultValue = false
        )
}
