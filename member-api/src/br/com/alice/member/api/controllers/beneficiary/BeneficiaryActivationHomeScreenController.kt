package br.com.alice.member.api.controllers.beneficiary

import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.BeneficiaryOnboarding
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.member.api.builders.HealthDeclarationAppointmentBuilder
import br.com.alice.member.api.models.BeneficiaryResponse
import br.com.alice.member.api.models.activation.ActivationItemTransport
import br.com.alice.member.api.models.activation.ActivationScreenTransport
import br.com.alice.member.api.models.activation.VideoCallTransport
import br.com.alice.member.api.services.beneficiary.BeneficiaryActivationHomeScreenService
import br.com.alice.member.api.usecases.GetBeneficiaryUseCase
import br.com.alice.membership.client.onboarding.HealthDeclarationAppointmentScheduler
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.client.PersonCalendlyService
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDateTime

class BeneficiaryActivationHomeScreenController(
    private val getBeneficiaryUseCase: GetBeneficiaryUseCase,
    private val screenService: BeneficiaryActivationHomeScreenService,
    private val healthDeclarationAppointmentScheduler: HealthDeclarationAppointmentScheduler,
    private val appointmentScheduleService: AppointmentScheduleService,
    private val personCalendlyService: PersonCalendlyService,
) : Controller() {
    suspend fun getActivationHomeScreen(): Response {
        val screenTransport = buildActivationHomeScreen()
        return screenTransport.toResponse()
    }

    suspend fun buildActivationHomeScreen(): ScreensTransport {
        val personId = currentUid().toPersonId()
        val beneficiaryInfo = getBeneficiaryUseCase.getBeneficiaryWithDependentsByPersonId(personId)
        return buildActivationHomeCards(beneficiaryInfo)
    }

    suspend fun getHealthDeclarationsScreen() : Response {
        val personId = currentUid().toPersonId()
        val beneficiaryInfo = getBeneficiaryUseCase.getBeneficiaryWithDependentsByPersonId(personId)

        val dependentsHealthDeclarations = beneficiaryInfo.dependentsResponse.map {
            buildDependentItemTransport(
                it,
                beneficiaryInfo,
                BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION
            )
        }

        val beneficiaryHealthDeclaration = buildBeneficiaryItemTransport(
            beneficiaryInfo,
            BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION
        )

        val healthDeclarationList = ActivationScreenTransport(
            listOf(beneficiaryHealthDeclaration) + dependentsHealthDeclarations
        )

        return screenService.buildHealthDeclarationListScreen(healthDeclarationList).toResponse()
    }

    suspend fun getCPTsScreen() : Response {
        val personId = currentUid().toPersonId()
        val beneficiaryInfo = getBeneficiaryUseCase.getBeneficiaryWithDependentsByPersonId(personId)

        val dependentsCPTs = beneficiaryInfo.dependentsResponse
            .filter { it.cptsCount > 0 }
            .map {
                buildDependentItemTransport(it, beneficiaryInfo, BeneficiaryOnboardingPhaseType.CPTS_CONFIRMATION)
            }

        val beneficiaryCPT = buildBeneficiaryItemTransport(
            beneficiaryInfo,
            BeneficiaryOnboardingPhaseType.CPTS_CONFIRMATION
        )

        val healthDeclarationList = ActivationScreenTransport(
            listOf(beneficiaryCPT) + dependentsCPTs
        )

        return screenService.buildCPTsListScreen(healthDeclarationList).toResponse()
    }

    suspend fun getReviewTermsScreen() : Response {
        val personId = currentUid().toPersonId()
        val beneficiaryInfo = getBeneficiaryUseCase.getBeneficiaryWithDependentsByPersonId(personId)

        val dependentsReviewTermsList = beneficiaryInfo.dependentsResponse.map {
            buildDependentItemTransport(
                it,
                beneficiaryInfo,
                BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE
            )
        }

        val beneficiaryReviewTerms = buildBeneficiaryItemTransport(
            beneficiaryInfo,
            BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE
        )

        val reviewTermsList = ActivationScreenTransport(
            listOf(beneficiaryReviewTerms) + dependentsReviewTermsList
        )

        return screenService.buildReviewTermsListScreen(reviewTermsList).toResponse()
    }

    fun getDelayedAddedDependentScreen() = screenService.buildDelayedAddedDependentScreen().toResponse()

    suspend fun getVideoCallOrientationsScreen() : Response {
        val personId = currentUid().toPersonId()
        val beneficiaryInfo = getBeneficiaryUseCase.getBeneficiaryWithDependentsByPersonId(personId)
        val transport = buildTransportFromBeneficiaryInfo(beneficiaryInfo)

        return screenService.buildVideoCallInstructionsScreen(transport).toResponse()
    }

    fun getBeneficiaryVideoCallOrientationsScreen() = screenService.buildBeneficiaryVideocallInstructionsScreen().toResponse()

    fun getDependentsVideoCallOrientationsScreen() = screenService.buildDependentsVideocallInstructionsScreen().toResponse()


    private fun buildDependentItemTransport(
        dependent: BeneficiaryResponse,
        beneficiaryInfo: GetBeneficiaryUseCase.BeneficiaryInfo,
        phase: BeneficiaryOnboardingPhaseType? = null,
        videoCall: VideoCallTransport? = null,
    ) = ActivationItemTransport(
        personId = dependent.personInfo.id.toString(),
        personFirstName = dependent.personInfo.firstName,
        isMinor = dependent.isMinor ?: false,
        personIsBeneficiary = false,
        isPending = (dependent.currentPhase?.order ?: 0) <= (phase?.order ?: 0),
        beneficiaryNationalId = beneficiaryInfo.person.nationalId,
        currentPhaseIndex = dependent.currentPhase?.order ?: 0,
        videoCall = videoCall,
        cptsCount = dependent.cptsCount,
        isDelayedActivation = isDelayedAddedDependent(beneficiaryInfo, dependent)
    )

    private fun buildBeneficiaryItemTransport(
        beneficiaryInfo: GetBeneficiaryUseCase.BeneficiaryInfo,
        phase: BeneficiaryOnboardingPhaseType? = null,
        videoCall: VideoCallTransport? = null,
    ) = ActivationItemTransport(
        personId = beneficiaryInfo.person.id.toString(),
        personFirstName = beneficiaryInfo.person.firstName,
        isMinor = beneficiaryInfo.isMinor ?: false,
        personIsBeneficiary = true,
        isPending = (beneficiaryInfo.currentPhase?.order ?: 0) <= (phase?.order ?: 0),
        beneficiaryNationalId = beneficiaryInfo.person.nationalId,
        currentPhaseIndex = beneficiaryInfo.currentPhase?.order ?: 0,
        videoCall = videoCall,
        cptsCount = beneficiaryInfo.cptsCount,
        isDelayedActivation = false,
    )

    private suspend fun buildActivationHomeCards(
        beneficiaryInfo: GetBeneficiaryUseCase.BeneficiaryInfo
    ) = buildTransportFromBeneficiaryInfo(beneficiaryInfo).let {
        screenService.buildActivationHomeCards(it)
    }

    private suspend fun buildTransportFromBeneficiaryInfo(beneficiaryInfo: GetBeneficiaryUseCase.BeneficiaryInfo) :
            ActivationScreenTransport {
        val dependentsItemsTransport = beneficiaryInfo.dependentsResponse.map {
            buildDependentItemTransport(
                it,
                beneficiaryInfo,
            )
        }

        val beneficiaryItemTransport = buildBeneficiaryItemTransport(
            beneficiaryInfo,
            videoCall = getVideoCallIfNeeded(beneficiaryInfo),
        )

        return ActivationScreenTransport(
            items = listOf(beneficiaryItemTransport) + dependentsItemsTransport,
        )
    }

    private fun isDelayedAddedDependent(beneficiaryInfo: GetBeneficiaryUseCase.BeneficiaryInfo, dependent: BeneficiaryResponse) =
        beneficiaryInfo.beneficiary.onboarding?.let { onboarding ->
            val healthDeclarationPhase = onboarding.phases.firstOrNull {
                it.phase == BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION
            }

            healthDeclarationPhase != null &&
                    dependent.firstPhaseTransactedAt != null &&
                    dependent.firstPhaseTransactedAt > healthDeclarationPhase.transactedAt
        } ?: false

    private suspend fun getVideoCallIfNeeded(beneficiaryInfo: GetBeneficiaryUseCase.BeneficiaryInfo) :
            VideoCallTransport? {

        return when(beneficiaryInfo.currentPhase) {
            BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT ->
                getVideoCallAppointment(beneficiaryInfo)
            BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT_SCHEDULED ->
                getVideoCallAppointmentScheduled(beneficiaryInfo)
            else -> null
        }
    }

    private suspend fun getVideoCallAppointment(beneficiaryInfo: GetBeneficiaryUseCase.BeneficiaryInfo) :
            VideoCallTransport {
        val isHighRisk = beneficiaryInfo.isHighRisk
        val scheduleUrl = healthDeclarationAppointmentScheduler
            .getBeneficiaryScheduleUrl(
                beneficiaryInfo.person.id,
                isHighRisk
            ).get()
        return VideoCallTransport(scheduleUrl = scheduleUrl)
    }

    private suspend fun getVideoCallAppointmentScheduled(beneficiaryInfo: GetBeneficiaryUseCase.BeneficiaryInfo) :
            VideoCallTransport = coroutineScope {
        val appointmentSchedulesDeferred = async {
            appointmentScheduleService.findWithStaffBy(
                AppointmentScheduleFilter(
                    personId = beneficiaryInfo.person.id,
                    status = listOf(AppointmentScheduleStatus.SCHEDULED),
                    endTimeGreater = LocalDateTime.MIN,
                    sortOrder = SortOrder.Ascending,
                    types = listOf(AppointmentScheduleType.HEALTH_DECLARATION)
                )
            )
        }

        val personCalendlyDeferred = async { personCalendlyService.getOrCreate(beneficiaryInfo.person.id) }

        val appointment = appointmentSchedulesDeferred.await().map {
            HealthDeclarationAppointmentBuilder.buildBeneficiaryHealthDeclarationAppointmentResponse(
                it,
                null,
                personCalendlyDeferred.await().get()
            )
        }.get()

        VideoCallTransport(appointment = appointment.schedule)
    }
}
