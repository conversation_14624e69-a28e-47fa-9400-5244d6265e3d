spaced_services := $(shell echo "$(services)" | tr ',' ' ')

db_start:
	@docker-compose run --service-ports --rm db_client sh -c "until pg_isready -h db_test -U postgres && pg_isready -h db_development -U postgres -p 5433; do sleep 1; done; echo db started"

db_create:
	@echo "creating databases.."
	@make db_start
	@docker-compose run --rm db_client sh -c "createdb -h db_test -U postgres token_test; createdb -h db_development -U postgres -p 5433 token;"
	@docker-compose run --rm db_client sh -c "createdb -h db_test -U postgres api_test; createdb -h db_development -p 5433 -U postgres main"
	@docker-compose run --rm db_client sh -c "createuser -s -h db_test -U postgres -d -r -e alice; createuser -s -h db_development -p 5433 -U postgres -d -r -e alice"
	@echo "test & development databases.. done."
	@echo "Run migration..."
	@make db_migration
	@echo "Migration done"

db_migration:
	@echo "Run databases migration.."
	@make db_start
	@docker-compose up flyway_main
	@docker-compose up flyway_token
	@echo "development databases migration.. done."

db_reset:
	@echo "cleaning data..."
	@docker-compose rm -s -f data-layer db_test db_development
	@docker volume ls -q | grep postgres-data | xargs docker volume rm
	@make db_create

db_seed:
	@echo "seeding.."
	@make db_start
	./gradlew :data-layer-core:databaseSeed

db_tests:
	@make db_start
	./gradlew clean :data-layer-core:test

cache_start:
	@echo "Starting cache service..."
	@docker-compose up --detach redis
	@echo "Cache service available at port 6379"

clean:
	./gradlew clean

tests:
	@make db_start
	./gradlew test --parallel --build-cache

run: # parameters: service = service name
	alice-cli dev generate-local-env;
	@make re_run service=$(service) withOPA=$(withOPA);

re_run: # parameters: service = service name
	./gradlew :$(service):jibDockerBuild;
	if [ "$(withOPA)" != "" ] && [ $(withOPA) == true ]; then \
		make opa; \
	fi;
	docker-compose up --detach $(service);

run_dev: # parameters: stage = [dev1|dev2], services = service names separated by commas, withKafka = should point to local kafka broker?
	alice-cli dev generate-local-env --stage="$(stage)" --services="$(services)" $(if $(withKafka),--withKafka="$(withKafka)") $(if $(withOPA),--withOPA="$(withOPA)");
	@for service in $(spaced_services); do \
		echo $$service; \
		make re_run service=$$service withOPA=$(withOPA); \
	done

run_dev_kafka: # parameters: stage = [dev1|dev2], services = service names separated by commas
	@make kafka;
	@make run_dev withKafka="true";

kafka:
	@echo "Provisioning broker"
	@docker-compose up -d zookeeper;
	@echo "Provisioning kafka"
	@docker-compose up -d kafka;
	@echo "Provisioning kafdrop UI"
	@docker-compose up -d kafdrop;

opa_test:
	@echo "Running OPA tests"
	@docker-compose run opa_auth test /policies/domains /policies/test --var-values

opa_test_single: # parameters: namespace = name of folder to run (with underscores)
	@echo "Running OPA tests for $(namespace)"
	@docker-compose run opa_auth test /policies/domains /policies/test -r $(namespace) --var-values

opa:
	@echo "Provisioning OPA"
	@docker-compose up -d opa_auth
	@echo "OPA available at port 8181"

db_run_with_opa:
	@make db_run withOPA=true

db_run:
	@make run service=data-layer withOPA=$(withOPA)

ehr_api_run:
	@make run service=ehr-api

einstein_bff_api_run:
	@make run service=einstein-bff-api

wanda_bff_api_run:
	@make run service=wanda-bff-api

wanda_domain_service_run:
	@make run service=wanda-domain-service

schedule_domain_service_run:
	@make run service=schedule-domain-service

ehr_domain_service_run:
	@make run service=ehr-domain-service

system_ops_bff_api_run:
	@make run service=system-ops-bff-api

fleury_integration_service_run:
	@make run service=fleury-integration-service

haoc_integration_service_run:
	@make run service=haoc-integration-service

einstein_integration_service_run:
	@make run service=einstein-integration-service

money_in_domain_run:
	@make run service=money-in-domain-service

healthcare_ops_api_run:
	@make run service=healthcare-ops-api

db_integration_service_run:
	@make run service=db-integration-service

feature_config_domain_service_run:
	@make run service=feature-config-domain-service

limbo_run:
	@make run service=limbo-api

# member-api
member_api_run:
	@make run service=member-api

member_api_integration_tests:
	./gradlew :member-api:integrationTests

member_with_deps:
	@for service in data-layer feature-config-domain-service membership-domain-service staff-domain-service member-api person-domain-service business-domain-service ehr-domain-service onboarding-domain-service; do \
  	    echo $$service; \
  	    ./gradlew :$$service:jibDockerBuild; \
  	    docker-compose run -d --service-ports --rm $$service; \
  	done

new_domain_service:
	@echo "creating new domain service..."
	cd project-templates/ && ./gradlew installTemplateNewDomainService
	@lazybones create new-domain-service 1.0-SNAPSHOT new-service

membership_domain_run:
	@make run service=membership-domain-service

file_vault_service_run:
	@make run service=file-vault-service

channel_domain_service_run:
	@make run service=channel-domain-service

run_ehr_with_deps:
	for service in data-layer feature-config-domain-service ehr-domain-service membership-domain-service channel-domain-service file-vault-service ehr-api; do \
	    echo $$service; \
	    ./gradlew :$$service:jibDockerBuild; \
	    docker-compose run -d --service-ports --rm $$service; \
	done

exec_indicator_domain_service_run:
	@make run service=exec-indicator-domain-service

exec_indicator_api_run:
	@make run service=exec-indicator-api

amas_api_run:
	@make run service=amas-bff-api

sonarqube:
	./gradlew build jacocoTestReport sonarqube

money_in_bff_api_domain_run:
	@make run service=money-in-bff-api

test_result_domain_service_run:
	@make run service=test-result-domain-service

member_wannabe_api_run:
	@make run service=member-wannabe-api

health_logic_domain_service_run:
	@make run service=health-logic-domain-service

health_logics_api_run:
	@make run service=health-logics-api

# staff scheduler
scheduler_bff_run:
	@make run service=scheduler-bff-api

channel_bff_api_run:
	@make run service=channel-bff-api

provider_domain_service_run:
	@make run service=provider-domain-service

hippocrates_service_run:
	@make run service=hippocrates-domain-service

run_ehr_and_aos:
	@make db_start
	for service in data-layer feature-config-domain-service membership-domain-service channel-domain-service file-vault-service ehr-domain-service ehr-api system-ops-bff-api; do \
	    docker-compose run -d --service-ports --rm $$service; \
	done

run_eita_with_deps:
	@make db_start
	for service in data-layer feature-config-domain-service membership-domain-service ehr-domain-service exec-indicator-domain-service exec-indicator-api staff-domain-service person-domain-service hippocrates-domain-service provider-domain-service; do \
  		echo $$service; \
		./gradlew :$$service:jibDockerBuild; \
		docker-compose run -d --service-ports --rm $$service; \
	done

run_amas:
	@make db_start
	for service in amas-domain-service; do \
        echo $$service; \
        ./gradlew :$$service:jibDockerBuild; \
        docker-compose run -d --service-ports --rm $$service; \
    done

run_eventinder:
	@make db_start
	for service in eventinder-domain-service; do \
        echo $$service; \
        ./gradlew :$$service:jibDockerBuild; \
        docker-compose run -d --service-ports --rm $$service; \
    done

fhir_domain_service_run:
	@make run service=fhir-domain-service

fhir_bff_api_run:
	@make run service=fhir-bff-api

fhir_with_deps:
	@for service in data-layer feature-config-domain-service membership-domain-service ehr-domain-service provider-domain-service limbo-api fhir-bff-api fhir-domain-service test-result-domain-service staff-domain-service; do \
  	    echo $$service; \
  	    ./gradlew :$$service:jibDockerBuild; \
  	    docker-compose run -d --service-ports --rm $$service; \
  	done

business_domain_service_run:
	@make run service=business-domain-service

business_risk_domain_service_run:
	@make run service=business-risk-domain-service

atlas_domain_service_run:
	@make run service=atlas-domain-service

bud_domain_service_run:
	@make run service=bud-domain-service

product_domain_service_run:
	@make run service=product-domain-service

onboarding_domain_service_run:
	@make run service=onboarding-domain-service

sherlock_domain_service_run:
	@make run service=sherlock-domain-service

sherlock_api_run:
	@make run service=sherlock-api

run_sherlock_with_deps:
	for service in data-layer sherlock-domain-service ehr-domain-service staff-domain-service file-vault-service sherlock-api; do \
	    echo $$service; \
	    ./gradlew :$$service:jibDockerBuild; \
	    docker-compose run -d --service-ports --rm $$service; \
	done

staff_domain_service_run:
	@make run service=staff-domain-service

questionnaire_domain_service_run:
	@make run service=questionnaire-domain-service

bottini_domain_run:
	@make run service=bottini-domain-service

person_domain_service_run:
	@make run service=person-domain-service

marauders_map_domain_service_run:
	@make run service=marauders-map-domain-service

event_api_run:
	@make run service=event-api

coverage_domain_service_run:
	@make run service=coverage-domain-service

sorting_hat_domain_service_run:
	@make run service=sorting-hat-domain-service

action_plan_domain_service_run:
	@make run service=action-plan-domain-service

app_content_domain_service_run:
	@make run service=app-content-domain-service

dragon_radar_domain_service_run:
	@make run service=dragon-radar-domain-service

dragon_radar_bff_api_run:
	@make run service=dragon-radar-bff-api

appointment_domain_service_run:
	@make run service=appointment-domain-service

health_plan_domain_service_run:
	@make run service=health-plan-domain-service

clinical_account_domain_service_run:
	@make run service=clinical-account-domain-service

member_onboarding_domain_service_run:
	@make run service=member-onboarding-domain-service

nullvs_integration_service_run:
	@make run service=nullvs-integration-service

itau_integration_service_run:
	@make run service=itau-integration-service

eita_external_api_run:
	@make run service=eita-external-api

duquesa_domain_service_run:
	@make run service=duquesa-domain-service

tiss_domain_service_run:
	@make run service=tiss-domain-service

akinator_domain_service_run:
	@make run service=akinator-domain-service

screening_domain_service_run:
	@make run service=screening-domain-service

refund_domain_service_run:
	@make run service=refund-domain-service

sales_channel_domain_service_run:
	@make run service=sales-channel-domain-service

backoffice_bff_api_run:
	@make run service=backoffice-bff-api

acquisition_domain_service_run:
	@make run service=acquisition-domain-service
