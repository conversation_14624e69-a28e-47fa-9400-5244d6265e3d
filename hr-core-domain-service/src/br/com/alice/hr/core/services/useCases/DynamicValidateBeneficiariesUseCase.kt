package br.com.alice.hr.core.services.useCases

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyProductPriceListingService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.common.core.extensions.isBeforeEq
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryContractType
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.HrMemberUploadTrackingStatus
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.Product
import br.com.alice.data.layer.services.HrMemberUploadTrackingDataService
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import br.com.alice.hr.core.model.BeneficiaryBatchValidationError
import br.com.alice.hr.core.model.BeneficiaryBatchValidationErrorItem
import br.com.alice.hr.core.util.RuleAndMessage
import br.com.alice.hr.core.util.ValidationCollector
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import java.util.Locale
import java.util.UUID

class DynamicValidateBeneficiariesUseCase(
    private val productService: ProductService,
    private val beneficiaryService: BeneficiaryService,
    private val personService: PersonService,
    private val companySubContractService: CompanySubContractService,
    private val companyProductPriceListingService: CompanyProductPriceListingService,
    private val hrMemberUploadTrackingDataService: HrMemberUploadTrackingDataService,
    private val memberService: MemberService,
): Spannable {
    companion object {
        private const val NATIONAL_ID = "nationalId"
        private const val SUBCONTRACT_TITLE = "subContractTitle"
        private const val PRODUCT_TITLE = "productTitle"
        private val TRACKING_STATUSES_SHOULD_RETURN_ERROR_VALIDATION = listOf(HrMemberUploadTrackingStatus.QUEUED, HrMemberUploadTrackingStatus.CREATED)
    }

    suspend fun run(
        company: Company,
        beneficiaryBatch: BeneficiaryBatchTransport,
        staticValidation: BeneficiaryBatchValidation = BeneficiaryBatchValidation(),
    ): Result<BeneficiaryBatchValidation, Throwable> = span("DynamicValidateBeneficiaries") {
        if (beneficiaryBatch.items.isEmpty()) {
            return@span BeneficiaryBatchValidation().success()
        }

        val errors = mutableListOf<BeneficiaryBatchValidationError>()
        val success = mutableListOf<UUID>()

        val beneficiaryIndexToProduct = mapBeneficiariesToProducts(beneficiaryBatch)
        val beneficiaryToSubContract = mapBeneficiariesToSubContract(beneficiaryBatch)

        beneficiaryBatch.items.forEach { b ->
            val productError = validateShouldCallValidationFor(PRODUCT_TITLE, b, staticValidation) { validateProductExists(b, beneficiaryIndexToProduct) }
            val nationalIdAlreadyExistsError = validateShouldCallValidationFor(NATIONAL_ID, b, staticValidation) { validateNationalIdAlreadyExists(b, company.id) }
            val subContractError = validateShouldCallValidationFor(SUBCONTRACT_TITLE, b, staticValidation) { validateSubContractExists(b, beneficiaryToSubContract) }
            val holderError = validateHolderDatesIfNecessary(beneficiaryBatch.items, b, company)
            val productIsAvailableError = if (productError == null && subContractError == null) {
                validateProductIsAvailable(b, beneficiaryToSubContract, beneficiaryIndexToProduct)
            } else null
            val productIsBlockedError = if (productError == null && subContractError == null && productIsAvailableError == null) {
                validateProductIsNotBlocked(b, beneficiaryToSubContract, beneficiaryIndexToProduct)
            } else null

            val beneficiaryErrors = listOfNotNull(nationalIdAlreadyExistsError, productError, subContractError, holderError, productIsAvailableError, productIsBlockedError)

            if (beneficiaryErrors.isNotEmpty()) {
                errors.addAll(beneficiaryErrors)
            } else {
                success.add(b.index)
            }
        }

        return@span BeneficiaryBatchValidation(
            productTitleOptions = beneficiaryIndexToProduct.values.mapNotNull { it?.title }.distinct().sorted(),
            subContractTitleOptions = beneficiaryToSubContract.values.mapNotNull { it?.title }.distinct().sorted(),
            success = success,
            errors = errors,
        ).success()
    }

    private suspend fun validateShouldCallValidationFor(
        field: String,
        b: BeneficiaryBatchItemTransport,
        staticValidation: BeneficiaryBatchValidation,
        validation: suspend () -> BeneficiaryBatchValidationError?,
    ): BeneficiaryBatchValidationError? =
        if (staticValidation.errors.any { it.index == b.index && it.error.any { it.field == field } }) {
            null
        } else validation()

    private suspend fun validateNationalIdAlreadyExists(
        b: BeneficiaryBatchItemTransport,
        companyId: UUID,
    ): BeneficiaryBatchValidationError? {
        val person = personService.findByNationalId(b.nationalId!!).getOrNullIfNotFound()
        val member = person?.let { memberService.getCurrent(it.id).getOrNullIfNotFound() }
        val memberIsNotNullAndIsFromOtherCompany = member != null && member.companyId != companyId

        return when {
            memberIsNotNullAndIsFromOtherCompany && member?.status == MemberStatus.ACTIVE -> {
                BeneficiaryBatchValidationError(
                    index = b.index,
                    error = listOf(
                        BeneficiaryBatchValidationErrorItem(
                            field = "nationalId",
                            message = "CPF já possui um plano ativo na Alice, fale com o time de apoio."
                        )
                    )
                )
            }
            memberIsNotNullAndIsFromOtherCompany && member?.status == MemberStatus.PENDING -> {
                BeneficiaryBatchValidationError(
                    index = b.index,
                    error = listOf(
                        BeneficiaryBatchValidationErrorItem(
                            field = "nationalId",
                            message = "CPF já possui um cadastro na Alice, fale com o time de apoio."
                        )
                    )
                )
            }
            else -> null
        }
    }

    private suspend fun validateProductIsNotBlocked(
        b: BeneficiaryBatchItemTransport,
        beneficiaryToSubContract: Map<UUID, CompanySubContract?>,
        beneficiaryIndexToProduct: Map<UUID, Product?>,
    ): BeneficiaryBatchValidationError? {
        val product = beneficiaryIndexToProduct[b.index] ?: return null
        val subContract = beneficiaryToSubContract[b.index] ?: return null

        val priceListing = companyProductPriceListingService.findCurrentBySubContractIdAndProductId(
            subContract.id,
            product.id
        ).getOrNullIfNotFound()

        return if (priceListing?.isBlockedForSale == true) {
            BeneficiaryBatchValidationError(
                index = b.index,
                error = listOf(
                    BeneficiaryBatchValidationErrorItem(
                        field = "productTitle",
                        message = "Produto inválido, use a lista da planilha modelo."
                    )
                )
            )
        } else null
    }

    private fun validateProductIsAvailable(b: BeneficiaryBatchItemTransport, beneficiaryToSubContract: Map<UUID, CompanySubContract?>, beneficiaryIndexToProduct: Map<UUID, Product?>): BeneficiaryBatchValidationError? {
        val product = beneficiaryIndexToProduct[b.index]
        val subContract = beneficiaryToSubContract[b.index]

        return if (subContract?.availableProducts == null || !subContract.availableProducts!!.contains(product?.id)) {
            BeneficiaryBatchValidationError(
                index = b.index,
                error = listOf(
                    BeneficiaryBatchValidationErrorItem(
                        field = "productTitle",
                        message = "Produto inválido, use a lista da planilha modelo.",
                    )
                )
            )
        } else null
    }

    private suspend fun mapBeneficiariesToSubContract(beneficiaryBatch: BeneficiaryBatchTransport): Map<UUID, CompanySubContract?> {
        val subContractTitles = beneficiaryBatch.items.mapNotNull { it.subContractTitle }.distinct()
        val companySubContracts = if (subContractTitles.isNotEmpty()) {
            companySubContractService.findByTitles(subContractTitles).getOrNullIfNotFound()
        } else emptyList()

        return beneficiaryBatch.items.associate {
            it.index to companySubContracts?.find { companySubContract ->
                companySubContract.title == it.subContractTitle
            }
        }
    }

    private suspend fun getAllProducts(items: List<BeneficiaryBatchItemTransport>): List<Product>? {
        val productTitles = items.mapNotNull { it.productTitle }.distinct()
        return productService.findActiveByTitles(productTitles).getOrNullIfNotFound()
    }

    private suspend fun mapBeneficiariesToProducts(beneficiaryBatch: BeneficiaryBatchTransport): Map<UUID, Product?> {
        val products = getAllProducts(beneficiaryBatch.items)
        val productMapByName = products?.associateBy { it.title }
        return beneficiaryBatch.items.associate { beneficiary ->
            beneficiary.index to productMapByName?.get(beneficiary.productTitle)
        }
    }

    private suspend fun validateHolderDatesIfNecessary(
        beneficiaryBatchItems: List<BeneficiaryBatchItemTransport>,
        beneficiaryBatchItem: BeneficiaryBatchItemTransport,
        company: Company,
    ): BeneficiaryBatchValidationError? {
        val validator = ValidationCollector(beneficiaryBatchItem.index)

        if (beneficiaryBatchItem.parentNationalId == null) return null

        val parentNationalId = beneficiaryBatchItem.parentNationalId!!.onlyNumbers()

        val person = personService.findByNationalId(parentNationalId).getOrNullIfNotFound()
        val beneficiary = person?.let { beneficiaryService.findByPersonId(it.id).getOrNullIfNotFound() }
        val holderBeneficiaryFromBatch = beneficiaryBatchItems.find { it.nationalId?.onlyNumbers() == parentNationalId && it.parentNationalId?.onlyNumbers() == null }

        return when {
            beneficiaryHolderIsInvalid(beneficiary, company, holderBeneficiaryFromBatch) -> validateBeneficiaryTrackingStatuses(
                parentNationalId,
                beneficiaryBatchItem.index,
            )
            beneficiary != null -> validateExistingBeneficiary(
                beneficiaryBatchItem = beneficiaryBatchItem,
                beneficiaryContractType = beneficiary.contractType,
                beneficiaryActivatedAt = beneficiary.activatedAt.toLocalDate(),
                beneficiaryHiredAt = beneficiary.hiredAt?.toLocalDate(),
                validator = validator,
                company = company,
            )
            holderBeneficiaryFromBatch != null -> validateExistingBeneficiary(
                beneficiaryBatchItem = beneficiaryBatchItem,
                beneficiaryContractType = BeneficiaryContractType.valueOf(holderBeneficiaryFromBatch.beneficiaryContractType!!),
                beneficiaryActivatedAt = holderBeneficiaryFromBatch.activatedAt!!.toNormalizedDate()?.toLocalDate(),
                beneficiaryHiredAt = holderBeneficiaryFromBatch.hiredAt!!.toNormalizedDate()?.toLocalDate(),
                validator = validator,
                company = company,
            )
            else -> validator.getErrors()
        }
    }

    private fun validateExistingBeneficiary(
        beneficiaryBatchItem: BeneficiaryBatchItemTransport,
        beneficiaryContractType: BeneficiaryContractType?,
        beneficiaryActivatedAt: LocalDate?,
        beneficiaryHiredAt: LocalDate?,
        validator: ValidationCollector,
        company: Company,
    ): BeneficiaryBatchValidationError? {
        val holderIsMissingRequiredInfo = validateHolderIsWithRequiredInfo(beneficiaryBatchItem.index, beneficiaryContractType, beneficiaryHiredAt)

        if (holderIsMissingRequiredInfo != null) {
            return holderIsMissingRequiredInfo
        }

        validator.check(
            beneficiaryBatchItem.activatedAt,
            "activatedAt",
            listOf(
                RuleAndMessage({
                    !beneficiaryHiredAt!!.isBeforeEq(it!!.toNormalizedDate()!!.toLocalDate())
                }, "Data de ativação deve ser igual ou posterior à contratação do titular."),
                RuleAndMessage({
                    !beneficiaryActivatedAt!!.isBeforeEq(it!!.toNormalizedDate()!!.toLocalDate())
                }, "Data de ativação do dependente deve ser igual ou posterior à data de ativação do titular."),
                RuleAndMessage({
                    !company.contractStartedAt!!.toLocalDate().isBeforeEq(it!!.toNormalizedDate()!!.toLocalDate())
                }, "Data de ativação deve ser após o início do contrato com a Alice."),
            )
        )

        return validator.getErrors()
    }

    private suspend fun validateBeneficiaryTrackingStatuses(
        parentNationalId: String,
        index: UUID,
    ): BeneficiaryBatchValidationError? {
        val memberTracking = hrMemberUploadTrackingDataService.findByMemberNationalId(parentNationalId).getOrNullIfNotFound()

        return if (memberTracking == null || memberTracking.status !in TRACKING_STATUSES_SHOULD_RETURN_ERROR_VALIDATION) {
            BeneficiaryBatchValidationError(
                index = index,
                error = listOf(
                    BeneficiaryBatchValidationErrorItem(
                        field = "parentNationalId",
                        message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                    )
                )
            )
        } else null
    }

    private fun validateHolderIsWithRequiredInfo(
        index: UUID,
        beneficiaryContractType: BeneficiaryContractType?,
        beneficiaryHiredAt: LocalDate?,
    ): BeneficiaryBatchValidationError? {
        val error = BeneficiaryBatchValidationError(
            index = index,
            error = listOf(
                BeneficiaryBatchValidationErrorItem(
                    field = "parentNationalId",
                    message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                )
            )
        )
        return when {
            beneficiaryContractType == null -> error
            beneficiaryHiredAt == null -> error
            else -> null
        }
    }

    private fun beneficiaryHolderIsInvalid(
        beneficiary: Beneficiary?,
        company: Company,
        holderBeneficiaryFromBatch: BeneficiaryBatchItemTransport?,
    ) = (beneficiary == null && holderBeneficiaryFromBatch == null) || (beneficiary != null && beneficiary.companyId != company.id)

    private fun validateProductExists(
        beneficiary: BeneficiaryBatchItemTransport,
        beneficiaryIndexToProduct: Map<UUID, Product?>,
    ): BeneficiaryBatchValidationError? {
        val validator = ValidationCollector(beneficiary.index)
        
        validator.check(
            beneficiaryIndexToProduct[beneficiary.index],
            "productTitle",
            listOf(
                RuleAndMessage({ it == null }, "Produto não encontrado.")
            )
        )
        
        return validator.getErrors()
    }

    private fun validateSubContractExists(
        beneficiary: BeneficiaryBatchItemTransport,
        beneficiaryToSubContract: Map<UUID, CompanySubContract?>,
    ): BeneficiaryBatchValidationError? {
        val validator = ValidationCollector(beneficiary.index)

        validator.check(
            beneficiaryToSubContract[beneficiary.index],
            "subContractTitle",
            listOf(
                RuleAndMessage({ it == null }, "Contrato para faturamento inválido, selecione uma das opções da lista.")
            )
        )

        return validator.getErrors()
    }

    private fun String.toNormalizedDate(): String? {
        val cleaned = this.replace("/", "-").trim()

        val formats = listOf(
            DateTimeFormatter.ofPattern("dd-MM-yyyy", Locale.getDefault()),
            DateTimeFormatter.ISO_LOCAL_DATE
        )

        for (format in formats) {
            try {
                val date = LocalDate.parse(cleaned, format)
                return date.format(DateTimeFormatter.ISO_LOCAL_DATE)
            } catch (e: DateTimeParseException) {
                continue
            }
        }

        return null
    }
}
