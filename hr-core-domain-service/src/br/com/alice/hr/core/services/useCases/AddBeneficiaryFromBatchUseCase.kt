package br.com.alice.hr.core.services.useCases

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.BeneficiaryService.CreateOptions
import br.com.alice.business.client.BeneficiaryService.FindOptions
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.model.BeneficiaryTransport
import br.com.alice.common.BeneficiaryType
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.logging.logger
import br.com.alice.common.models.Sex
import br.com.alice.common.models.State
import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryContractType
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.HrMemberUploadTracking
import br.com.alice.data.layer.models.HrMemberUploadTrackingStatus
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.Product
import br.com.alice.data.layer.services.HrMemberUploadTrackingDataService
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import br.com.alice.hr.core.model.BeneficiaryBatchValidationErrorItem
import br.com.alice.person.client.PersonService
import br.com.alice.product.client.ProductService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import java.util.Locale
import java.util.UUID

class AddBeneficiaryFromBatchUseCase(
    private val companyService: CompanyService,
    private val beneficiaryService: BeneficiaryService,
    private val companySubContractService: CompanySubContractService,
    private val personService: PersonService,
    private val productService: ProductService,
    private val hrMemberUploadTrackingDataService: HrMemberUploadTrackingDataService,
    private val dynamicValidateBeneficiariesUseCase: DynamicValidateBeneficiariesUseCase,
) : Spannable {

    companion object {
        val VALID_STATUS_TO_CREATE_BENEFICIARY = listOf(HrMemberUploadTrackingStatus.QUEUED)
        private const val CLT = "CLT"
        private const val PJ = "PJ"
        private val ONLY_ALPHANUMERIC_REGEX = "[^a-zA-Z0-9]".toRegex()
        val SEX_MAP = mapOf(
            "M" to Sex.MALE,
            "F" to Sex.FEMALE,
            "I" to Sex.INTERSEX,
        )
        private const val HOLDER = "Titular"
        private const val DEPENDENT = "Dependente"
        private val PARENT_TRACKING_STATUS_TO_CREATE_DEPENDENT = listOf(HrMemberUploadTrackingStatus.CREATED)
        private val BENEFICIARY_CREATE_OPTIONS = CreateOptions(ignoreMembershipValidation = true)
    }

    suspend fun run(companyId: UUID, beneficiaryItem: BeneficiaryBatchItemTransport, uploadId: UUID): Result<Beneficiary, Throwable> = span("AddBeneficiaryFromBatchUseCase") { span ->
        companyService.get(companyId).flatMap { company ->
            val beneficiaryBatch = beneficiaryItem.toBeneficiaryBatchTransport(uploadId)

            dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch).flatMap { validationResult ->
                getHrMemberUploadTracking(beneficiaryItem.nationalId!!, uploadId).flatMap { hrMemberUploadTracking ->
                    if (shouldCreateBeneficiary(validationResult, hrMemberUploadTracking)) {
                        createBeneficiary(beneficiaryItem, company)
                    } else {
                        fetchErrorsFromUniqueValidationResult(validationResult).forEach { errorItem ->
                            logger.error(
                                "Beneficiary creation failed",
                                "upload_id" to uploadId,
                                "company_id" to companyId,
                                "national_id" to beneficiaryItem.nationalId,
                                "errors" to "${errorItem.field}: ${errorItem.message}"
                            )
                        }

                        Result.failure(Throwable(beneficiaryItem.nationalId))
                    }
                }
            }
        }
    }

    private suspend fun createBeneficiary(beneficiaryItem: BeneficiaryBatchItemTransport, company: Company): Result<Beneficiary, Throwable> = span("createBeneficiary") { span ->
        try {
            companySubContractService.findByTitles(listOfNotNull(beneficiaryItem.subContractTitle)).flatMap { subContracts ->
                val subContract = subContracts.firstOrNull() ?: return@span Result.failure(Throwable("SubContract not found"))

                productService.findActiveByTitles(listOfNotNull(beneficiaryItem.productTitle)).flatMap { products ->
                    val product = products.firstOrNull() ?: return@span Result.failure(Throwable("Product not found"))
                    val address = buildAddress(beneficiaryItem)
                    val beneficiaryTransport = beneficiaryItem.toBeneficiaryTransport(company, subContract, address, product)

                    when(beneficiaryItem.ownership) {
                        HOLDER -> createBeneficiaryHolder(beneficiaryTransport, product)
                        DEPENDENT -> createBeneficiaryDependent(beneficiaryTransport, beneficiaryItem, product, company)
                        else -> Result.failure(Throwable("Invalid ownership: ${beneficiaryItem.ownership}"))
                    }
                }
            }
        } catch (e: Exception) {
            logger.error("Error creating beneficiary", "error" to e)
            Result.failure(e)
        }
    }

    private suspend fun createBeneficiaryHolder(beneficiaryTransport: BeneficiaryTransport, product: Product): Result<Beneficiary, Throwable> = span("createBeneficiaryHolder") { span ->
        beneficiaryService.createBeneficiary(beneficiaryTransport, product.id, BeneficiaryOnboardingFlowType.UNDEFINED, null, BENEFICIARY_CREATE_OPTIONS)
    }

    private suspend fun createBeneficiaryDependent(
        beneficiaryTransport: BeneficiaryTransport,
        beneficiaryItem: BeneficiaryBatchItemTransport,
        product: Product,
        company: Company
    ): Result<Beneficiary, Throwable> = span("createBeneficiaryDependent") { span ->
        findValidParentPerson(beneficiaryItem).flatMap { parentPerson ->
                beneficiaryService.findByCompanyIdAndPersonIds(company.id, listOf(parentPerson.id), FindOptions(withOnboarding = false)).flatMap { beneficiaries ->
                    val parentBeneficiary = beneficiaries.firstOrNull() ?: return@span Result.failure(Throwable("Parent beneficiary not found"))
                    val dependentTransport = beneficiaryTransport.toBeneficiaryDependentTransport(parentBeneficiary, beneficiaryItem)

                    beneficiaryService.createBeneficiary(dependentTransport, product.id, BeneficiaryOnboardingFlowType.UNDEFINED, null, BENEFICIARY_CREATE_OPTIONS)
                }
            }
        }

    private suspend fun findValidParentPerson(beneficiaryItem: BeneficiaryBatchItemTransport): Result<Person, Throwable> = span("findValidParentPerson") {
        personService.findByNationalId(beneficiaryItem.parentNationalId!!.onlyNumbers())
        .flatMap {
            if(it.isTest) {
                Result.failure(Throwable("Parent beneficiary is test"))
            } else {
                Result.success(it)
            }
        }
        .flatMapError {
            hrMemberUploadTrackingDataService.findLastByMemberNationalId(beneficiaryItem.parentNationalId!!.onlyNumbers()).flatMap { hrMemberUploadTracking ->
                if(hrMemberUploadTracking.status !in PARENT_TRACKING_STATUS_TO_CREATE_DEPENDENT) {
                    return@span Result.failure(Throwable("Parent beneficiary not found"))
                }

                Result.failure(Throwable("Parent beneficiary not created"))
            }
        }
    }

    private suspend fun getHrMemberUploadTracking(nationalId: String, uploadId: UUID): Result<HrMemberUploadTracking, Throwable> = span("getHrMemberUploadTracking") { span ->
        val sanitizedNationalId = nationalId.onlyNumbers()

        hrMemberUploadTrackingDataService.findByMemberNationalIdAndUploadId(sanitizedNationalId, uploadId)
    }

    private fun shouldCreateBeneficiary(validationResult: BeneficiaryBatchValidation, hrMemberUploadTracking: HrMemberUploadTracking): Boolean =
        fetchErrorsFromUniqueValidationResult(validationResult).isEmpty() 
        && VALID_STATUS_TO_CREATE_BENEFICIARY.contains(hrMemberUploadTracking.status)
    

    private fun fetchErrorsFromUniqueValidationResult(validationResult: BeneficiaryBatchValidation): List<BeneficiaryBatchValidationErrorItem> =
        validationResult.errors.firstOrNull()?.error ?: emptyList()

    private fun BeneficiaryBatchItemTransport.toBeneficiaryTransport(company: Company, subContract: CompanySubContract, address: Address, product: Product): BeneficiaryTransport =
        BeneficiaryTransport(
            companyId = company.id,
            companyCNPJ = company.cnpj,
            companySubContractId = subContract.id,
            type = defineBeneficiaryType(this),
            firstName = defineFirstName(this),
            lastName = defineLastName(this),
            nationalId = this.nationalId!!,
            email = this.email!!,
            sex = defineSex(this.sex!!),
            birthDate = convertyToLocalDateTime(this.dateOfBirth!!),
            phoneNumber = this.phoneNumber!!,
            mothersName = this.mothersName!!,
            activatedAt = convertyToLocalDateTime(this.activatedAt!!),
            address = address,
            initialProductId = product.id,
            contractType = defineBeneficiaryContractType(this),
            hiredAt = defineHiredAt(this),
            cnpj = defineCnpj(this),
            parentBeneficiary = null,
            parentBeneficiaryRelationType = null,
            parentBeneficiaryRelatedAt = null,
            flowType = BeneficiaryOnboardingFlowType.UNDEFINED, // DEFINE WHEN DEVELOP RISK FLOW
        )

    private fun BeneficiaryTransport.toBeneficiaryDependentTransport(parentBeneficiary: Beneficiary, beneficiaryItem: BeneficiaryBatchItemTransport): BeneficiaryTransport =
        this.copy(
            parentBeneficiary = parentBeneficiary.id,
            parentBeneficiaryRelationType = defineParentBeneficiaryRelationType(beneficiaryItem)
        )

    private fun defineFirstName(beneficiaryItem: BeneficiaryBatchItemTransport): String =
        beneficiaryItem.fullName!!.split(" ")[0]

    private fun defineLastName(beneficiaryItem: BeneficiaryBatchItemTransport): String =
        beneficiaryItem.fullName!!.split(" ").drop(1).joinToString(" ")

    private fun defineHiredAt(beneficiaryItem: BeneficiaryBatchItemTransport): LocalDateTime? =
        if(beneficiaryItem.ownership == HOLDER) {
            convertyToLocalDateTime(beneficiaryItem.hiredAt!!)
        } else {
            null
        }

    private fun defineBeneficiaryType(beneficiaryItem: BeneficiaryBatchItemTransport): BeneficiaryType =
        when (beneficiaryItem.ownership) {
            HOLDER -> BeneficiaryType.EMPLOYEE
            DEPENDENT -> BeneficiaryType.DEPENDENT
            else -> throw IllegalArgumentException("Invalid ownership: ${beneficiaryItem.ownership}")
        }

    private fun defineBeneficiaryContractType(beneficiaryItem: BeneficiaryBatchItemTransport): BeneficiaryContractType? =
        when (beneficiaryItem.beneficiaryContractType) {
            CLT -> BeneficiaryContractType.CLT
            PJ -> BeneficiaryContractType.PJ
            else -> null
        }
    
    private fun defineParentBeneficiaryRelationType(beneficiaryItem: BeneficiaryBatchItemTransport): ParentBeneficiaryRelationType? =
        with(beneficiaryItem.parentBeneficiaryRelationType!!.trim().lowercase()) {
            when {
                contains("filh") -> ParentBeneficiaryRelationType.CHILD
                contains("cônjuge") -> ParentBeneficiaryRelationType.SPOUSE
                contains("entead") -> ParentBeneficiaryRelationType.STEPCHILD
                contains("irmão(ã)") -> ParentBeneficiaryRelationType.BROTHER_SISTER
                contains("genro") -> ParentBeneficiaryRelationType.SON_DAUGHTER_IN_LAW
                contains("nora") -> ParentBeneficiaryRelationType.SON_DAUGHTER_IN_LAW
                contains("bisnet") -> ParentBeneficiaryRelationType.GREATGRANDCHILD
                contains("net") -> ParentBeneficiaryRelationType.GRANDCHILD
                contains("pai") -> ParentBeneficiaryRelationType.MOTHER_FATHER
                contains("mãe") -> ParentBeneficiaryRelationType.MOTHER_FATHER
                contains("sobrinh") -> ParentBeneficiaryRelationType.NIECE_NEPHEW
                contains("padrasto") -> ParentBeneficiaryRelationType.STEP_MOTHER_FATHER
                contains("madrasta") -> ParentBeneficiaryRelationType.STEP_MOTHER_FATHER
                contains("av") -> ParentBeneficiaryRelationType.GRANDMOTHER_GRANDFATHER

                else -> {
                    logger.error(
                        "unknown parent beneficiary relation type",
                        "relation_type" to this
                    )
                    null
                }
            }
        }

    private fun defineSex(sex: String): Sex =
        SEX_MAP[sex] ?: throw IllegalArgumentException("Invalid sex: $sex")

    private fun defineCnpj(beneficiaryItem: BeneficiaryBatchItemTransport): String? =
        if (beneficiaryItem.ownership == HOLDER && beneficiaryItem.beneficiaryContractType == PJ) {
            beneficiaryItem.cnpj!!.replace(ONLY_ALPHANUMERIC_REGEX, "")
        } else {
            null
        }

    private fun convertyToLocalDateTime(date: String): LocalDateTime {
        val dateFormatted = date.replace("/", "-").trim()

        val formats = listOf(
            DateTimeFormatter.ofPattern("dd-MM-yyyy", Locale.getDefault()),
            DateTimeFormatter.ISO_LOCAL_DATE
        )

        for (format in formats) {
            try {
                val dateResult = LocalDate.parse(dateFormatted, format)
                return dateResult.atStartOfDay()
            } catch (e: DateTimeParseException) {
                continue
            }
        }

        throw DateTimeParseException("Invalid date format", dateFormatted, 0)
    }

    private fun BeneficiaryBatchItemTransport.toBeneficiaryBatchTransport(uploadId: UUID): BeneficiaryBatchTransport =
        BeneficiaryBatchTransport(
            uploadId = uploadId,
            items = listOf(this)
        )

    private fun buildAddress(beneficiaryItem: BeneficiaryBatchItemTransport): Address =
        Address(
            postalCode = beneficiaryItem.addressPostalCode ?: "",
            street = beneficiaryItem.addressStreet ?: "",
            number = beneficiaryItem.addressNumber ?: "",
            complement = beneficiaryItem.addressComplement ?: "",
            neighbourhood = beneficiaryItem.addressNeighborhood ?: "",
            city = beneficiaryItem.addressCity ?: "",
            state = State.valueOf(beneficiaryItem.addressState!!)
        )
}
