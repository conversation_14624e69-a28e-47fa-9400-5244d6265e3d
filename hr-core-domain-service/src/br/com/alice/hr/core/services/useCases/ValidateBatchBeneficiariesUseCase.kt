package br.com.alice.hr.core.services.useCases

import br.com.alice.common.core.extensions.capitalizeEachWord
import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.Company
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import br.com.alice.hr.core.model.BeneficiaryBatchValidationError
import br.com.alice.hr.core.model.BeneficiaryBatchValidationErrorItem
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import java.text.Normalizer
import java.util.UUID
import java.util.regex.Pattern

class ValidateBatchBeneficiariesUseCase(
    private val staticValidateBeneficiariesUseCase: StaticValidateBeneficiariesUseCase,
    private val dynamicValidateBeneficiariesUseCase: DynamicValidateBeneficiariesUseCase,
    private val addressValidateBeneficiariesUseCase: AddressValidateBeneficiariesUseCase,
): Spannable {

    companion object {
        private const val DEPENDENT = "Dependente"
    }

    suspend fun run(
        beneficiaryBatch: BeneficiaryBatchTransport,
        company: Company,
        productTitles: List<String>,
        subContractTitles: List<String>,
        relationTypeOptions: List<String>,
    ): Result<ValidationAndBeneficiaries, Throwable> = span("ValidateBeneficiaries") {
        val success = mutableListOf<UUID>()
        val errors = mutableListOf<BeneficiaryBatchValidationError>()

        return@span staticValidateBeneficiariesUseCase.run(beneficiaryBatch).flatMap { staticValidationResult ->
            success.addAll(staticValidationResult.success)
            errors.addAll(staticValidationResult.errors)

            dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationResult).flatMap dynamicValidation@{ dynamicValidationResult ->
                success.addAll(dynamicValidationResult.success)
                errors.addAll(dynamicValidationResult.errors)

                addressValidateBeneficiariesUseCase.run(beneficiaryBatch).flatMap addressValidation@{ (addressValidationResult, beneficiaries) ->
                    success.addAll(addressValidationResult.success)
                    errors.addAll(addressValidationResult.errors)

                    validateDependentsWithFailedHolders(beneficiaryBatch, errors)

                    if (addressValidationResult.success.isEmpty()) {
                        return@addressValidation ValidationAndBeneficiaries(
                            validation = BeneficiaryBatchValidation(
                                productTitleOptions = productTitles,
                                subContractTitleOptions = subContractTitles,
                                parentBeneficiaryRelationOptions = relationTypeOptions,
                                success = filterSuccessWithoutErrors(success, errors),
                                errors = errors,
                            ),
                            beneficiaries = emptyList(),
                        ).success()
                    }

                    val validation = BeneficiaryBatchValidation(
                        productTitleOptions = productTitles,
                        subContractTitleOptions = subContractTitles,
                        parentBeneficiaryRelationOptions = relationTypeOptions,
                        success = filterSuccessWithoutErrors(success, errors),
                        errors = errors,
                    )

                    ValidationAndBeneficiaries(
                        validation = validation,
                        beneficiaries = filterBeneficiariesWithoutErrors(beneficiaries, errors),
                    ).success()
                }
            }
        }
    }

    private fun filterBeneficiariesWithoutErrors(
        beneficiaries: List<BeneficiaryBatchItemTransport>,
        errors: List<BeneficiaryBatchValidationError>,
    ): List<BeneficiaryBatchItemTransport> {
        val indexesWithErrors = errors.map { it.index }
        return beneficiaries.filter { it.index !in indexesWithErrors }.distinct()
    }

    private fun filterSuccessWithoutErrors(
        success: List<UUID>,
        errors: List<BeneficiaryBatchValidationError>,
    ): List<UUID> {
        val indexesWithErrors = errors.map { it.index }
        return success.filter { it !in indexesWithErrors }.distinct()
    }

    private fun validateDependentsWithFailedHolders(
        beneficiaryBatch: BeneficiaryBatchTransport,
        currentErrors: MutableList<BeneficiaryBatchValidationError>,
    ) {
        val errorsMap = currentErrors.associateBy { it.index }.toMutableMap()
        val nationalIdToIndexMap = beneficiaryBatch.items.associate {
            it.nationalId?.trim() to it.index
        }.filterKeys { it != null }

        beneficiaryBatch.items
            .filter { beneficiary ->
                val ownership = beneficiary.ownership?.trim()?.capitalizeEachWord()?.removeAccents()
                ownership == DEPENDENT && !beneficiary.parentNationalId.isNullOrBlank()
            }
            .forEach { dependent ->
                val parentNationalId = dependent.parentNationalId?.trim()
                val parentIndex = nationalIdToIndexMap[parentNationalId]

                val parentHasErrors = parentIndex != null && errorsMap.containsKey(parentIndex)

                if (parentHasErrors) {
                    val dependentValidationError = BeneficiaryBatchValidationErrorItem(
                        field = "parentNationalId",
                        message = "CPF de titular não cadastrado, complete o cadastro do titular para adicionar dependentes."
                    )

                    val existingError = errorsMap[dependent.index]
                    if (existingError != null) {
                        val updatedError = existingError.copy(
                            error = existingError.error + dependentValidationError
                        )
                        errorsMap[dependent.index] = updatedError

                        val errorIndex = currentErrors.indexOfFirst { it.index == dependent.index }
                        if (errorIndex != -1) {
                            currentErrors[errorIndex] = updatedError
                        }
                    } else {
                        val newError = BeneficiaryBatchValidationError(
                            index = dependent.index,
                            error = listOf(dependentValidationError)
                        )
                        errorsMap[dependent.index] = newError
                        currentErrors.add(newError)
                    }
                }
            }
    }

    private fun String.removeAccents(): String {
        val normalized = Normalizer.normalize(this, Normalizer.Form.NFD)
        val pattern = Pattern.compile("\\p{InCombiningDiacriticalMarks}+")
        return pattern.matcher(normalized).replaceAll("")
    }
}

data class ValidationAndBeneficiaries(
    val validation: BeneficiaryBatchValidation,
    val beneficiaries: List<BeneficiaryBatchItemTransport>,
)
