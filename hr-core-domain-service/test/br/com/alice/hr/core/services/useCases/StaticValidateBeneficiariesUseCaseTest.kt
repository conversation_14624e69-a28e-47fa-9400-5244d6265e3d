package br.com.alice.hr.core.services.useCases

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidationError
import br.com.alice.hr.core.model.BeneficiaryBatchValidationErrorItem
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalDate

class StaticValidateBeneficiariesUseCaseTest {
    private val staticValidateBeneficiariesUseCase = StaticValidateBeneficiariesUseCase()

    private val today = LocalDate.now().toBrazilianDateFormat()
    private val index1 = RangeUUID.generate()
    private val index2 = RangeUUID.generate()
    private val index3 = RangeUUID.generate()

    private val beneficiaryBatchItemTransportValid = BeneficiaryBatchItemTransport(
        index = index1,
        nationalId = "438.595.570-09",
        dateOfBirth = "01/01/1990",
        fullName = "Calango Zokas",
        sex = "M",
        cnpj = "75.006.845/0001-93",
        subContractTitle = "Matriz",
        phoneNumber = "1191234-5678",
        mothersName = "Jane Doe",
        email = "<EMAIL>",
        addressNumber = "222",
        addressPostalCode = "12345678",
        addressComplement = null,
        productTitle = "Plano de Saúde Bonzao",
        activatedAt = today,
        beneficiaryContractType = "CLT",
        hiredAt = "01/02/2023",
        parentNationalId = null,
        parentBeneficiaryRelationType = null,
        relationExceeds30Days = "nao",
        ownership = "Titular",
    )
    private val beneficiaryBatchTransport = BeneficiaryBatchTransport(
        items = emptyList(),
        uploadId = RangeUUID.generate(),
    )

    @Test
    fun `#run should return empty validation when batch is empty`() = runBlocking {
        val emptyBatch = beneficiaryBatchTransport.copy(items = emptyList())

        val result = staticValidateBeneficiariesUseCase
            .run(emptyBatch)
            .get()

        assert(result.success.isEmpty())
        assert(result.errors.isEmpty())
    }

    @Test
    fun `#run should return an error for duplicated beneficiaries`() = runBlocking {
        val duplicated = beneficiaryBatchTransport.copy(
            items = listOf(
                beneficiaryBatchItemTransportValid,
                beneficiaryBatchItemTransportValid.copy(index = index2),
            )
        )

        val result = staticValidateBeneficiariesUseCase
            .run(duplicated)
            .get()

        // Debug: Print actual result
        println("DEBUG: Success count: ${result.success.size}")
        println("DEBUG: Errors count: ${result.errors.size}")
        println("DEBUG: Success indexes: ${result.success}")
        result.errors.forEach { error ->
            println("DEBUG: Error for index ${error.index}: ${error.error}")
        }

        // Debug: Print input data
        println("DEBUG: Input beneficiaries:")
        duplicated.items.forEach { item ->
            println("DEBUG: - Index: ${item.index}, NationalId: '${item.nationalId}', NormalizedId: '${item.nationalId?.replace("[^0-9]".toRegex(), "")}'")
        }

        assert(result.success.isEmpty())
        assert(result.errors.size == 2)

        // Check that both indexes have duplication errors
        val errorsByIndex = result.errors.associateBy { it.index }
        assert(errorsByIndex.containsKey(index1))
        assert(errorsByIndex.containsKey(index2))

        // Check that both have the duplication error message
        assert(errorsByIndex[index1]!!.error.any { it.field == "nationalId" && it.message == "CPF duplicado na planilha." })
        assert(errorsByIndex[index2]!!.error.any { it.field == "nationalId" && it.message == "CPF duplicado na planilha." })
    }

    @Test
    fun `#run should return success for valid holder beneficiary`() = runBlocking {
        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid)
        )

        val result = staticValidateBeneficiariesUseCase
            .run(transport)
            .get()

        assert(result.success.size == 1)
        assert(result.success.contains(index1))
        assert(result.errors.isEmpty())
    }

    @Test
    fun `#run should return success for valid dependent beneficiary`() = runBlocking {
        val validDependent = BeneficiaryBatchItemTransport(
            index = index2,
            nationalId = "358.213.480-64",
            dateOfBirth = "01/01/2010",
            fullName = "Calanguinho Zokas",
            sex = "M",
            cnpj = null,
            subContractTitle = null,
            phoneNumber = "1191234-5678",
            mothersName = "Calanga Zokas",
            email = "<EMAIL>",
            addressNumber = "222",
            addressPostalCode = "12345678",
            addressComplement = null,
            productTitle = "Plano de Saúde Bonzao",
            activatedAt = today,
            beneficiaryContractType = null,
            hiredAt = null,
            parentNationalId = "438.595.570-09",
            parentBeneficiaryRelationType = "Filha ou filho",
            relationExceeds30Days = "Sim",
            ownership = "Dependente",
        )

        val transport = beneficiaryBatchTransport.copy(
            items = listOf(validDependent)
        )

        val result = staticValidateBeneficiariesUseCase
            .run(transport)
            .get()

        assert(result.success.size == 1)
        assert(result.success.contains(index2))
        assert(result.errors.isEmpty())
    }

    @Test
    fun `#run should return success for mixed valid holders and dependents`() = runBlocking {
        val validDependent = BeneficiaryBatchItemTransport(
            index = index2,
            nationalId = "358.213.480-64",
            dateOfBirth = "01/01/2010",
            fullName = "Calanguinho Zokas",
            sex = "M",
            cnpj = null,
            subContractTitle = null,
            phoneNumber = "1191234-5678",
            mothersName = "Calanga Zokas",
            email = "<EMAIL>",
            addressNumber = "222",
            addressPostalCode = "12345678",
            addressComplement = null,
            productTitle = "Plano de Saúde Bonzao",
            activatedAt = today,
            beneficiaryContractType = null,
            hiredAt = null,
            parentNationalId = "438.595.570-09",
            parentBeneficiaryRelationType = "Filha ou filho",
            relationExceeds30Days = "Sim",
            ownership = "Dependente",
        )

        val transport = beneficiaryBatchTransport.copy(
            items = listOf(beneficiaryBatchItemTransportValid, validDependent)
        )

        val result = staticValidateBeneficiariesUseCase
            .run(transport)
            .get()

        assert(result.success.size == 2)
        assert(result.success.contains(index1))
        assert(result.success.contains(index2))
        assert(result.errors.isEmpty())
    }

    @Nested
    inner class ValidateBasicInfoTests {
        @Nested
        inner class ValidateOwnershipTests {
            @Test
            fun `#run should return an error when ownership is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(ownership = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "ownership",
                                message = "Titulariedade obrigatória."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when ownership is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(ownership = "depedente"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "ownership",
                                message = "Titulariedade inválida, escreva somente Titular ou Dependente."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateFullNameTests {
            @Test
            fun `#run should return an error when name is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(fullName = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "fullName",
                                message = "Nome completo obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when name has less than 5 letters`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(fullName = "Zok"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "fullName",
                                message = "Nome completo deve ter ao menos 5 caracteres."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when name has no last name`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(fullName = "Calango"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "fullName",
                                message = "Insira o nome e sobrenome do beneficiário."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when name has repeated words`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(fullName = "João João Silva"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "fullName",
                                message = "Insira o nome e sobrenome do beneficiário."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateNationalIdTests {
            @Test
            fun `#run should return an error when national id is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(nationalId = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "nationalId",
                                message = "CPF obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when national id is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(nationalId = "12345678901"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "nationalId",
                                message = "CPF inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateSexTests {
            @Test
            fun `#run should return an error when sex is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(sex = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "sex",
                                message = "Sexo biológico obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when sex is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(sex = "homem"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "sex",
                                message = "Sexo biológico inválido, selecione uma das opções da lista."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateMothersNameTests {
            @Test
            fun `#run should return an error when mothers name is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(mothersName = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "mothersName",
                                message = "Nome completo da mãe obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when mothers name has less than 5 letters`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(mothersName = "Mae"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "mothersName",
                                message = "Insira o nome e sobrenome da mãe."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when mothers name has no last name`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(mothersName = "Maria"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "mothersName",
                                message = "Insira o nome e sobrenome da mãe."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidatePhoneNumberTests {
            @Test
            fun `#run should return an error when phone number is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(phoneNumber = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "phoneNumber",
                                message = "Telefone obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when phone number is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(phoneNumber = "1232322hj22"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "phoneNumber",
                                message = "Telefone inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when phone number has all same digits`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(phoneNumber = "1111111111"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "phoneNumber",
                                message = "Telefone inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return success when phone number has 10 digits`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(phoneNumber = "1191234567"),
                    )
                )

                val result = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(result.success.size == 1)
                assert(result.success.contains(index1))
                assert(result.errors.isEmpty())
            }

            @Test
            fun `#run should return success when phone number has 11 digits`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(phoneNumber = "11912345678"),
                    )
                )

                val result = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(result.success.size == 1)
                assert(result.success.contains(index1))
                assert(result.errors.isEmpty())
            }
        }

        @Nested
        inner class ValidateAddressPostalCodeTests {
            @Test
            fun `#run should return an error when address postal code is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(addressPostalCode = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "addressPostalCode",
                                message = "CEP obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when address postal code is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(addressPostalCode = "1232322hj22"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "addressPostalCode",
                                message = "CEP inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when address postal code has all same digits`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(addressPostalCode = "11111111"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "addressPostalCode",
                                message = "CEP inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateAddressNumberTests {
            @Test
            fun `#run should return an error when address number is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(addressNumber = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "addressNumber",
                                message = "Número do logradouro inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when address number is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(addressNumber = "sdadsa"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "addressNumber",
                                message = "Número do logradouro inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateProductTitleTests {
            @Test
            fun `#run should return an error when product title is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(productTitle = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "productTitle",
                                message = "Produto obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateBirthDateTests {
            @Test
            fun `#run should return an error when birth date is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(dateOfBirth = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "dateOfBirth",
                                message = "Data de nascimento obrigatória."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when birth date is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(dateOfBirth = "13/13/1212"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "dateOfBirth",
                                message = "Data de nascimento inválida."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when birth date is after today`() = runBlocking {
                val today = LocalDate.now()

                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(dateOfBirth = today.plusMonths(3).toString()),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "dateOfBirth",
                                message = "Data de nascimento inválida."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when birth date is before 1905`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(dateOfBirth = LocalDate.of(1904, 1, 1).toString()),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "dateOfBirth",
                                message = "Data de nascimento inválida."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateEmailTests {
            @Test
            fun `#run should return an error when email is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(email = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "email",
                                message = "E-mail obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when email has typo`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(email = "<EMAIL>"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "email",
                                message = "E-mail inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when email has invalid domain`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(email = "asasas@<EMAIL>"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "email",
                                message = "E-mail inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return success when email contains exclamation mark`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(email = "test!@gmail.com"),
                    )
                )

                val result = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(result.success.size == 1)
                assert(result.success.contains(index1))
                assert(result.errors.isEmpty())
            }
        }

        @Nested
        inner class ValidateActivatedAtTests {
            @Test
            fun `#run should return an error when activated at is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(activatedAt = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "activatedAt",
                                message = "Data de ativação obrigatória."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when activated at is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(activatedAt = "13/13/1212"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "activatedAt",
                                message = "Data de ativação inválida."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when activated at is before today`() = runBlocking {
                val today = LocalDate.now()

                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(activatedAt = today.minusDays(3).toString()),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "activatedAt",
                                message = "Data de ativação deve ser maior que a data atual."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }
    }

    @Nested
    inner class ValidateHolderFieldsTests {
        private val holderBeneficiaryBatchItemTransportValid = BeneficiaryBatchItemTransport(
            index = index1,
            nationalId = "438.595.570-09",
            dateOfBirth = "01/01/1990",
            fullName = "Calanga Zokas",
            sex = "F",
            cnpj = "75.006.845/0001-93",
            subContractTitle = "Matriz",
            phoneNumber = "1191234-5678",
            mothersName = "Jane Doe",
            email = "<EMAIL>",
            addressNumber = "222",
            addressPostalCode = "12345678",
            addressComplement = null,
            productTitle = "Plano de Saúde Bonzao",
            activatedAt = today,
            beneficiaryContractType = "CLT",
            hiredAt = "01/02/2023",
            parentNationalId = null,
            parentBeneficiaryRelationType = null,
            relationExceeds30Days = "nao",
            ownership = "Titular",
        )

        @Nested
        inner class ValidateSubContractTitleTests {
            @Test
            fun `#run should return an error when subContractTitle is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        holderBeneficiaryBatchItemTransportValid.copy(subContractTitle = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "subContractTitle",
                                message = "Contrato para faturamento obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when subContractTitle is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(subContractTitle = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "subContractTitle",
                                message = "Contrato para faturamento obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateBeneficiaryContractTypeTests {
            @Test
            fun `#run should return an error when beneficiaryContractType is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        holderBeneficiaryBatchItemTransportValid.copy(beneficiaryContractType = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "beneficiaryContractType",
                                message = "Regime de contratação obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when beneficiaryContractType is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(beneficiaryContractType = "inválido"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "beneficiaryContractType",
                                message = "Regime de contratação inválido, escolha CLT ou PJ."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateCnpjTests {
            @Test
            fun `#run should return an error when cnpj is missing when beneficiaryContractType is PJ`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        holderBeneficiaryBatchItemTransportValid.copy(
                            cnpj = null,
                            beneficiaryContractType = "PJ",
                        ),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "cnpj",
                                message = "CNPJ do titular obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when cnpj is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        beneficiaryBatchItemTransportValid.copy(
                            cnpj = "12345678901234",
                            beneficiaryContractType = "PJ"
                        ),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "cnpj",
                                message = "CNPJ do titular inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return success when cnpj is not provided and beneficiaryContractType is CLT`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        holderBeneficiaryBatchItemTransportValid.copy(
                            cnpj = null,
                            beneficiaryContractType = "CLT",
                        ),
                    )
                )

                val result = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(result.success.size == 1)
                assert(result.success.contains(index1))
                assert(result.errors.isEmpty())
            }

            @Test
            fun `#run should return success when cnpj is valid and beneficiaryContractType is PJ`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        holderBeneficiaryBatchItemTransportValid.copy(
                            cnpj = "75.006.845/0001-93",
                            beneficiaryContractType = "PJ",
                        ),
                    )
                )

                val result = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(result.success.size == 1)
                assert(result.success.contains(index1))
                assert(result.errors.isEmpty())
            }
        }

        @Nested
        inner class ValidateHiredAtTests {
            @Test
            fun `#run should return an error when hired at is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        holderBeneficiaryBatchItemTransportValid.copy(hiredAt = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "hiredAt",
                                message = "Data de admissão do titular obrigatória."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when hired at is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        holderBeneficiaryBatchItemTransportValid.copy(hiredAt = "13/13/1212"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index1,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "hiredAt",
                                message = "Data de admissão do titular com formato inválido, use DD/MM/AAAA."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }
    }

    @Nested
    inner class ValidateDependentFieldsTests {
        private val dependentBeneficiaryBatchItemTransportValid = BeneficiaryBatchItemTransport(
            index = index3,
            nationalId = "358.213.480-64",
            dateOfBirth = "01/01/2010",
            fullName = "Calanguinho Zokas",
            sex = "M",
            cnpj = null,
            subContractTitle = "Matriz",
            phoneNumber = "1191234-5678",
            mothersName = "Calanga Zokas",
            email = "<EMAIL>",
            addressNumber = "222",
            addressPostalCode = "12345678",
            addressComplement = null,
            productTitle = "Plano de Saúde Bonzao",
            activatedAt = today,
            beneficiaryContractType = "CLT",
            hiredAt = null,
            parentNationalId = "438.595.570-09",
            parentBeneficiaryRelationType = "Filha ou filho",
            relationExceeds30Days = "sim",
            ownership = "Dependente",
        )

        @Nested
        inner class ValidateRelationExceeds30DaysTests {
            @Test
            fun `#run should return an error when relationExceeds30Days is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        dependentBeneficiaryBatchItemTransportValid.copy(relationExceeds30Days = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index3,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "relationExceeds30Days",
                                message = "Vínculo com titular a mais de 30 dias obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when relationExceeds30Days is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        dependentBeneficiaryBatchItemTransportValid.copy(relationExceeds30Days = "dsffsdf"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index3,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "relationExceeds30Days",
                                message = "Vínculo com titular a mais de 30 dias inválido, selecione uma das opções da lista."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateParentBeneficiaryRelationTypeTests {
            @Test
            fun `#run should return an error when parentBeneficiaryRelationType is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        dependentBeneficiaryBatchItemTransportValid.copy(parentBeneficiaryRelationType = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index3,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "parentBeneficiaryRelationType",
                                message = "Relação com titular obrigatória."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when parentBeneficiaryRelationType is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        dependentBeneficiaryBatchItemTransportValid.copy(parentBeneficiaryRelationType = "Filho"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index3,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "parentBeneficiaryRelationType",
                                message = "Relação com titular inválida, selecione uma das opções da lista."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }

        @Nested
        inner class ValidateParentNationalIdTests {
            @Test
            fun `#run should return an error when parentNationalId is missing`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        dependentBeneficiaryBatchItemTransportValid.copy(parentNationalId = null),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index3,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "parentNationalId",
                                message = "CPF do titular obrigatório."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }

            @Test
            fun `#run should return an error when parentNationalId is invalid`() = runBlocking {
                val transport = beneficiaryBatchTransport.copy(
                    items = listOf(
                        dependentBeneficiaryBatchItemTransportValid.copy(parentNationalId = "123.456.789-00"),
                    )
                )
                val expectedError = listOf(
                    BeneficiaryBatchValidationError(
                        index = index3,
                        error = listOf(
                            BeneficiaryBatchValidationErrorItem(
                                field = "parentNationalId",
                                message = "CPF do titular inválido."
                            )
                        )
                    )
                )

                val resultError = staticValidateBeneficiariesUseCase
                    .run(transport)
                    .get()

                assert(resultError.success.isEmpty())
                assert(resultError.errors.size == expectedError.size)
                assert(resultError.errors == expectedError)
            }
        }
    }

    @Nested
    inner class DateNormalizationTests {
        @Test
        fun `#run should handle different valid date formats`() = runBlocking {
            val transport1 = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        dateOfBirth = "01/01/1990",
                        activatedAt = today
                    ),
                )
            )

            val result1 = staticValidateBeneficiariesUseCase
                .run(transport1)
                .get()

            assert(result1.success.size == 1)
            assert(result1.errors.isEmpty())
        }

        @Test
        fun `#run should handle invalid date formats that return null`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        dateOfBirth = "invalid-date",
                    ),
                )
            )
            val expectedError = listOf(
                BeneficiaryBatchValidationError(
                    index = index1,
                    error = listOf(
                        BeneficiaryBatchValidationErrorItem(
                            field = "dateOfBirth",
                            message = "Data de nascimento inválida."
                        )
                    )
                )
            )

            val result = staticValidateBeneficiariesUseCase
                .run(transport)
                .get()

            assert(result.success.isEmpty())
            assert(result.errors.size == expectedError.size)
            assert(result.errors == expectedError)
        }
    }

    @Nested
    inner class MultipleValidationErrorsTests {
        @Test
        fun `#run should return validation errors for single beneficiary with multiple invalid fields`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        fullName = null,
                    ),
                )
            )

            val result = staticValidateBeneficiariesUseCase
                .run(transport)
                .get()

            assert(result.success.isEmpty())
            assert(result.errors.size == 1)
            assert(result.errors[0].error.size >= 1) // Should have at least 1 validation error

            val errorFields = result.errors[0].error.map { it.field }
            assert(errorFields.contains("fullName"))
        }

        @Test
        fun `#run should handle mixed scenarios with valid and invalid beneficiaries`() = runBlocking {
            val invalidBeneficiary = beneficiaryBatchItemTransportValid.copy(
                index = index3,
                fullName = null,
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(invalidBeneficiary)
            )

            val result = staticValidateBeneficiariesUseCase
                .run(transport)
                .get()

            assert(result.success.isEmpty())
            assert(result.errors.size == 1)
            assert(result.errors[0].index == index3)
            assert(result.errors[0].error.isNotEmpty())
        }
    }

    @Nested
    inner class NullAndEmptyNationalIdTests {
        @Test
        fun `#run should not report duplication errors for multiple beneficiaries with null nationalId`() = runBlocking {
            val beneficiary1 = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                nationalId = null
            )
            val beneficiary2 = beneficiaryBatchItemTransportValid.copy(
                index = index2,
                nationalId = null
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiary1, beneficiary2)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            // Should have 0 success (both will fail basic validation for missing CPF)
            // Should have 2 errors for missing CPF, but NO duplication errors
            assert(result.success.isEmpty())
            assert(result.errors.size == 2)

            // Verify no duplication errors
            val hasNationalIdDuplicationError = result.errors.any { error ->
                error.error.any { it.field == "nationalId" && it.message == "CPF duplicado na planilha." }
            }
            assert(!hasNationalIdDuplicationError)

            // Verify both have missing CPF errors instead
            result.errors.forEach { error ->
                val hasMissingCpfError = error.error.any {
                    it.field == "nationalId" && it.message == "CPF obrigatório."
                }
                assert(hasMissingCpfError)
            }
        }

        @Test
        fun `#run should report missing errors for multiple beneficiaries with empty string nationalId`() = runBlocking {
            val beneficiary1 = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                nationalId = ""
            )
            val beneficiary2 = beneficiaryBatchItemTransportValid.copy(
                index = index2,
                nationalId = ""
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiary1, beneficiary2)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.isEmpty())
            assert(result.errors.size == 2)

            result.errors.forEach { error ->
                val hasMissingError = error.error.any {
                    it.field == "nationalId" && it.message == "CPF obrigatório."
                }
                assert(hasMissingError)
            }
        }

        @Test
        fun `#run should handle mixed null and empty string nationalIds correctly`() = runBlocking {
            val beneficiaryWithNull = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                nationalId = null
            )
            val beneficiaryWithEmpty = beneficiaryBatchItemTransportValid.copy(
                index = index2,
                nationalId = ""
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryWithNull, beneficiaryWithEmpty)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.isEmpty())
            assert(result.errors.size == 2)

            // Null nationalId should have missing CPF error, not duplication
            val nullError = result.errors.find { it.index == index1 }!!
            val hasNullMissingError = nullError.error.any {
                it.field == "nationalId" && it.message == "CPF obrigatório."
            }
            assert(hasNullMissingError)

            // Empty string should also have missing CPF error (not duplication since only one empty)
            val emptyError = result.errors.find { it.index == index2 }!!
            val hasEmptyMissingError = emptyError.error.any {
                it.field == "nationalId" && it.message == "CPF obrigatório."
            }
            assert(hasEmptyMissingError)

            // Verify no duplication errors for either
            val hasAnyDuplicationError = result.errors.any { error ->
                error.error.any { it.field == "nationalId" && it.message == "CPF duplicado na planilha." }
            }
            assert(!hasAnyDuplicationError)
        }

        @Test
        fun `#run should handle multiple whitespace-only nationalIds`() = runBlocking {
            val beneficiary1 = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                nationalId = "   "
            )
            val beneficiary2 = beneficiaryBatchItemTransportValid.copy(
                index = index2,
                nationalId = "\t\n "
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiary1, beneficiary2)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            // After onlyNumbers() processing, whitespace becomes empty string
            // Empty strings are treated as missing CPF, not duplicates
            assert(result.success.isEmpty())
            assert(result.errors.size == 2)

            // Verify both have missing CPF errors, not duplication errors
            result.errors.forEach { error ->
                val hasMissingCpfError = error.error.any {
                    it.field == "nationalId" && it.message == "CPF obrigatório."
                }
                assert(hasMissingCpfError)

                // Should NOT have duplication error
                val hasDuplicationError = error.error.any {
                    it.field == "nationalId" && it.message == "CPF duplicado na planilha."
                }
                assert(!hasDuplicationError)
            }
        }

        @Test
        fun `#run should handle null nationalIds mixed with actual duplicates`() = runBlocking {
            val validCpf = "438.595.570-09"

            val beneficiaryWithNull = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                nationalId = null
            )
            val beneficiaryDuplicate1 = beneficiaryBatchItemTransportValid.copy(
                index = index2,
                nationalId = validCpf
            )
            val beneficiaryDuplicate2 = beneficiaryBatchItemTransportValid.copy(
                index = index3,
                nationalId = validCpf
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryWithNull, beneficiaryDuplicate1, beneficiaryDuplicate2)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.isEmpty())
            assert(result.errors.size == 3)

            // Null should have missing CPF error
            val nullError = result.errors.find { it.index == index1 }!!
            val hasNullMissingError = nullError.error.any {
                it.field == "nationalId" && it.message == "CPF obrigatório."
            }
            assert(hasNullMissingError)

            // Duplicates should have duplication errors
            val duplicate1Error = result.errors.find { it.index == index2 }!!
            val duplicate2Error = result.errors.find { it.index == index3 }!!
            val hasDuplicate1Error = duplicate1Error.error.any {
                it.field == "nationalId" && it.message == "CPF duplicado na planilha."
            }
            val hasDuplicate2Error = duplicate2Error.error.any {
                it.field == "nationalId" && it.message == "CPF duplicado na planilha."
            }
            assert(hasDuplicate1Error)
            assert(hasDuplicate2Error)

            // Verify null error doesn't have duplication error
            val hasNullDuplicationError = nullError.error.any {
                it.field == "nationalId" && it.message == "CPF duplicado na planilha."
            }
            assert(!hasNullDuplicationError)
        }
    }

    @Nested
    inner class OwnershipProcessingEdgeCasesTests {
        @Test
        fun `#run should handle ownership with accents and different cases`() = runBlocking {
            val beneficiaryWithAccents = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                ownership = "titulár"
            )
            val beneficiaryUpperCase = beneficiaryBatchItemTransportValid.copy(
                index = index2,
                ownership = "DEPENDENTE",
                nationalId = "358.213.480-64",
                parentNationalId = "438.595.570-09",
                parentBeneficiaryRelationType = "Filha ou filho",
                relationExceeds30Days = "Sim",
                subContractTitle = null,
                beneficiaryContractType = null,
                hiredAt = null
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryWithAccents, beneficiaryUpperCase)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            // Both should be processed successfully despite accent and case differences
            assert(result.success.size == 2)
            assert(result.success.contains(index1))
            assert(result.success.contains(index2))
            assert(result.errors.isEmpty())
        }

        @Test
        fun `#run should handle ownership with extra spaces correctly`() = runBlocking {
            val beneficiaryWithSpaces = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                ownership = "  Titular  "
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryWithSpaces)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success == listOf(index1))
            assert(result.errors.isEmpty())
        }
    }

    @Nested
    inner class ValidationPriorityTests {
        @Test
        fun `#run should prioritize dependent validation over basic validation errors`() = runBlocking {
            val dependentWithBasicErrors = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                ownership = "Dependente",
                fullName = null,
                parentNationalId = null,
                parentBeneficiaryRelationType = "Filha ou filho",
                relationExceeds30Days = "Sim",
                subContractTitle = null,
                beneficiaryContractType = null,
                hiredAt = null
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(dependentWithBasicErrors)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.isEmpty())
            assert(result.errors.size == 1)

            val error = result.errors[0]
            val hasParentNationalIdError = error.error.any {
                it.field == "parentNationalId" && it.message == "CPF do titular obrigatório."
            }
            assert(hasParentNationalIdError)

            val hasFullNameError = error.error.any {
                it.field == "fullName" && it.message == "Nome completo obrigatório."
            }
            assert(hasFullNameError)
        }

        @Test
        fun `#run should prioritize holder validation over basic validation errors`() = runBlocking {
            val holderWithBasicErrors = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                ownership = "Titular",
                fullName = null,
                subContractTitle = null,
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(holderWithBasicErrors)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.isEmpty())
            assert(result.errors.size == 1)

            val error = result.errors[0]
            val hasSubContractTitleError = error.error.any {
                it.field == "subContractTitle" && it.message == "Contrato para faturamento obrigatório."
            }
            assert(hasSubContractTitleError)

            val hasFullNameError = error.error.any {
                it.field == "fullName" && it.message == "Nome completo obrigatório."
            }
            assert(hasFullNameError)
        }
    }

    @Nested
    inner class DateValidationBoundaryTests {
        @Test
        fun `#run should handle birth date exactly equal to minimum date boundary`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        dateOfBirth = "01/01/1905" // Exactly the minimum date
                    )
                )
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.size == 1)
            assert(result.success.contains(index1))
            assert(result.errors.isEmpty())
        }

        @Test
        fun `#run should handle birth date exactly equal to today`() = runBlocking {
            val today = LocalDate.now()
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        dateOfBirth = today.toString()
                    )
                )
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.isEmpty())
            assert(result.errors.size == 1)

            val error = result.errors[0]
            val hasBirthDateError = error.error.any {
                it.field == "dateOfBirth" && it.message == "Data de nascimento inválida."
            }
            assert(hasBirthDateError)
        }

        @Test
        fun `#run should handle activated date exactly equal to today`() = runBlocking {
            val today = LocalDate.now()
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        activatedAt = today.toString()
                    )
                )
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.size == 1)
            assert(result.success.contains(index1))
            assert(result.errors.isEmpty())
        }

        @Test
        fun `#run should handle date normalization with ISO format`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        dateOfBirth = "1990-01-01", // ISO format
                        activatedAt = LocalDate.now().plusDays(1).toString() // ISO format future date
                    )
                )
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.size == 1)
            assert(result.success.contains(index1))
            assert(result.errors.isEmpty())
        }

        @Test
        fun `#run should handle leap year dates correctly`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        dateOfBirth = "29/02/2000" // Leap year date
                    )
                )
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.size == 1)
            assert(result.success.contains(index1))
            assert(result.errors.isEmpty())
        }
    }

    @Nested
    inner class EmailValidationEdgeCasesTests {
        @Test
        fun `#run should handle email with multiple exclamation marks`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        email = "test!!multiple!@gmail.com"
                    )
                )
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.size == 1)
            assert(result.success.contains(index1))
            assert(result.errors.isEmpty())
        }

        @Test
        fun `#run should handle email with exclamation marks in different positions`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        email = "!test@gmail!.com!"
                    )
                )
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.size == 1)
            assert(result.success.contains(index1))
            assert(result.errors.isEmpty())
        }
    }

    @Nested
    inner class CnpjValidationEdgeCasesTests {
        @Test
        fun `#run should not validate CNPJ when contract type is CLT even if CNPJ is invalid`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        beneficiaryContractType = "CLT",
                        cnpj = "invalid-cnpj-123" // Invalid CNPJ but should be ignored for CLT
                    )
                )
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.size == 1)
            assert(result.success.contains(index1))
            assert(result.errors.isEmpty())
        }

        @Test
        fun `#run should validate CNPJ when contract type is PJ with valid CNPJ`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        beneficiaryContractType = "PJ",
                        cnpj = "75.006.845/0001-93" // Valid CNPJ
                    )
                )
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.size == 1)
            assert(result.success.contains(index1))
            assert(result.errors.isEmpty())
        }
    }

    @Nested
    inner class StringProcessingEdgeCasesTests {
        @Test
        fun `#run should handle fields with only whitespace`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        fullName = "   ", // Only whitespace
                        mothersName = "\t\n " // Different whitespace characters
                    )
                )
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.isEmpty())
            assert(result.errors.size == 1)

            val error = result.errors[0]
            // Should have errors for both fields since whitespace gets processed to empty
            val hasFullNameError = error.error.any {
                it.field == "fullName" && it.message == "Nome completo obrigatório."
            }
            val hasMothersNameError = error.error.any {
                it.field == "mothersName" && it.message == "Nome completo da mãe obrigatório."
            }
            assert(hasFullNameError)
            assert(hasMothersNameError)
        }

        @Test
        fun `#run should handle names with repeated words case insensitive`() = runBlocking {
            val transport = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        fullName = "João joão Silva" // Case insensitive repeated word
                    )
                )
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.isEmpty())
            assert(result.errors.size == 1)

            val error = result.errors[0]
            val hasRepeatedWordError = error.error.any {
                it.field == "fullName" && it.message == "Insira o nome e sobrenome do beneficiário."
            }
            assert(hasRepeatedWordError)
        }

        @Test
        fun `#run should handle phone number and postal code boundary cases`() = runBlocking {
            val transport1 = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        phoneNumber = "1234567890", // Exactly 10 digits
                        addressPostalCode = "12345678" // Exactly 8 digits
                    )
                )
            )

            val result1 = staticValidateBeneficiariesUseCase.run(transport1).get()

            assert(result1.success.size == 1)
            assert(result1.success.contains(index1))
            assert(result1.errors.isEmpty())

            val transport2 = beneficiaryBatchTransport.copy(
                items = listOf(
                    beneficiaryBatchItemTransportValid.copy(
                        index = index2,
                        phoneNumber = "12345678901", // Exactly 11 digits
                        addressPostalCode = "87654321" // Exactly 8 digits
                    )
                )
            )

            val result2 = staticValidateBeneficiariesUseCase.run(transport2).get()

            assert(result2.success.size == 1)
            assert(result2.success.contains(index2))
            assert(result2.errors.isEmpty())
        }
    }

    @Nested
    inner class ErrorAggregationTests {
        @Test
        fun `#run should handle duplicated beneficiaries with additional validation errors`() = runBlocking {
            val duplicatedCpf = "438.595.570-09"
            val beneficiary1 = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                nationalId = duplicatedCpf,
                fullName = null,
            )
            val beneficiary2 = beneficiaryBatchItemTransportValid.copy(
                index = index2,
                nationalId = duplicatedCpf,
                email = null,
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiary1, beneficiary2)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.isEmpty())
            assert(result.errors.size == 2)

            result.errors.forEach { error ->
                val hasDuplicationError = error.error.any {
                    it.field == "nationalId" && it.message == "CPF duplicado na planilha."
                }
                assert(hasDuplicationError)
            }

            val beneficiary1Error = result.errors.find { it.index == index1 }!!
            val hasFullNameError = beneficiary1Error.error.any { it.field == "fullName" }
            assert(hasFullNameError)

            val beneficiary2Error = result.errors.find { it.index == index2 }!!
            val hasEmailError = beneficiary2Error.error.any { it.field == "email" }
            assert(hasEmailError)
        }

        @Test
        fun `#run should aggregate multiple error types for same beneficiary index`() = runBlocking {
            val beneficiaryWithMultipleErrors = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                fullName = null,
                email = null,
                phoneNumber = null
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryWithMultipleErrors)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.isEmpty())
            assert(result.errors.size == 1)

            val error = result.errors[0]
            assert(error.error.size >= 3) // Should have at least 3 validation errors

            val errorFields = error.error.map { it.field }
            assert(errorFields.contains("fullName"))
            assert(errorFields.contains("email"))
            assert(errorFields.contains("phoneNumber"))
        }

        @Test
        fun `#run should return all validation errors simultaneously in batch validation without fail-fast behavior`() = runBlocking {
            // Setup a beneficiary with multiple validation errors across different validation categories
            val beneficiaryWithComprehensiveErrors = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                // Basic validation errors
                ownership = null,                           // Missing ownership
                fullName = "Jo",                           // Too short name (< 5 chars)
                nationalId = "12345678901",                // Invalid CPF
                sex = "invalid",                           // Invalid sex
                mothersName = "M",                         // Too short mother's name
                phoneNumber = "invalid-phone",             // Invalid phone
                addressPostalCode = "invalid-cep",         // Invalid postal code
                addressNumber = "invalid-number",          // Invalid address number
                productTitle = null,                       // Missing product title
                dateOfBirth = "32/13/2025",               // Invalid date format
                email = "invalid-email",                   // Invalid email
                activatedAt = "01/01/2020",               // Past activation date
                // Holder validation errors (since ownership will fail, these won't be validated, but let's set them up)
                subContractTitle = null,                   // Missing subcontract title
                beneficiaryContractType = "INVALID",       // Invalid contract type
                cnpj = "invalid-cnpj",                     // Invalid CNPJ
                hiredAt = "invalid-date"                   // Invalid hired date
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(beneficiaryWithComprehensiveErrors)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            // Verify no successful validations
            assert(result.success.isEmpty()) {
                "Expected no successful validations, but got: ${result.success}"
            }

            // Verify exactly one error entry (for the single beneficiary)
            assert(result.errors.size == 1) {
                "Expected exactly 1 error entry, but got: ${result.errors.size}"
            }

            val error = result.errors[0]
            assert(error.index == index1) {
                "Expected error index to be $index1, but got: ${error.index}"
            }

            // Verify multiple validation errors are collected (not fail-fast)
            assert(error.error.size >= 8) {
                "Expected at least 8 validation errors, but got: ${error.error.size}. Errors: ${error.error.map { "${it.field}: ${it.message}" }}"
            }

            // Verify specific expected validation errors are present
            val errorsByField = error.error.associateBy { it.field }

            // Basic validation errors that should be present
            assert(errorsByField.containsKey("ownership")) {
                "Expected ownership validation error. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField["ownership"]?.message == "Titulariedade obrigatória.") {
                "Expected ownership error message, but got: ${errorsByField["ownership"]?.message}"
            }

            assert(errorsByField.containsKey("fullName")) {
                "Expected fullName validation error. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField["fullName"]?.message == "Nome completo deve ter ao menos 5 caracteres.") {
                "Expected fullName error message, but got: ${errorsByField["fullName"]?.message}"
            }

            assert(errorsByField.containsKey("nationalId")) {
                "Expected nationalId validation error. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField["nationalId"]?.message == "CPF inválido.") {
                "Expected nationalId error message, but got: ${errorsByField["nationalId"]?.message}"
            }

            assert(errorsByField.containsKey("sex")) {
                "Expected sex validation error. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField["sex"]?.message == "Sexo biológico inválido, selecione uma das opções da lista.") {
                "Expected sex error message, but got: ${errorsByField["sex"]?.message}"
            }

            assert(errorsByField.containsKey("mothersName")) {
                "Expected mothersName validation error. Available fields: ${errorsByField.keys}"
            }

            assert(errorsByField.containsKey("phoneNumber")) {
                "Expected phoneNumber validation error. Available fields: ${errorsByField.keys}"
            }

            assert(errorsByField.containsKey("email")) {
                "Expected email validation error. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField["email"]?.message == "E-mail inválido.") {
                "Expected email error message, but got: ${errorsByField["email"]?.message}"
            }

            assert(errorsByField.containsKey("dateOfBirth")) {
                "Expected dateOfBirth validation error. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField["dateOfBirth"]?.message == "Data de nascimento inválida.") {
                "Expected dateOfBirth error message, but got: ${errorsByField["dateOfBirth"]?.message}"
            }

            // Verify that the validation collected multiple errors instead of stopping at the first one
            val distinctFields = error.error.map { it.field }.distinct()
            assert(distinctFields.size >= 8) {
                "Expected at least 8 distinct error fields, demonstrating batch validation. Got: $distinctFields"
            }

            // Log all errors for debugging
            println("DEBUG: All validation errors collected:")
            error.error.forEach { errorItem ->
                println("  - Field: ${errorItem.field}, Message: ${errorItem.message}")
            }
        }

        @Test
        fun `#run should collect validation errors from multiple validation phases simultaneously for holder beneficiary`() = runBlocking {
            // Setup a holder beneficiary with errors in basic validation AND holder-specific validation
            val holderWithMultipleValidationPhaseErrors = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                ownership = "Titular",                     // Valid ownership to trigger holder validation
                // Basic validation errors
                fullName = "Jo",                           // Too short (< 5 chars)
                nationalId = "11111111111",                // Invalid CPF
                email = "not-an-email",                    // Invalid email format
                dateOfBirth = "32/13/2025",               // Invalid date
                // Holder-specific validation errors
                subContractTitle = null,                   // Missing subcontract title
                beneficiaryContractType = "INVALID_TYPE",  // Invalid contract type
                cnpj = "12345678901234",                   // Invalid CNPJ for PJ (but this won't trigger since contract type is invalid)
                hiredAt = "invalid-date-format"            // Invalid hired date format
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(holderWithMultipleValidationPhaseErrors)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            // Verify no successful validations
            assert(result.success.isEmpty()) {
                "Expected no successful validations, but got: ${result.success}"
            }

            // Verify exactly one error entry
            assert(result.errors.size == 1) {
                "Expected exactly 1 error entry, but got: ${result.errors.size}"
            }

            val error = result.errors[0]
            assert(error.index == index1) {
                "Expected error index to be $index1, but got: ${error.index}"
            }

            // Verify multiple validation errors from different phases are collected
            assert(error.error.size >= 6) {
                "Expected at least 6 validation errors from multiple phases, but got: ${error.error.size}. Errors: ${error.error.map { "${it.field}: ${it.message}" }}"
            }

            val errorsByField = error.error.associateBy { it.field }

            // Verify basic validation errors are present
            assert(errorsByField.containsKey("fullName")) {
                "Expected basic validation error for fullName. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField.containsKey("nationalId")) {
                "Expected basic validation error for nationalId. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField.containsKey("email")) {
                "Expected basic validation error for email. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField.containsKey("dateOfBirth")) {
                "Expected basic validation error for dateOfBirth. Available fields: ${errorsByField.keys}"
            }

            // Verify holder-specific validation errors are present
            assert(errorsByField.containsKey("subContractTitle")) {
                "Expected holder validation error for subContractTitle. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField["subContractTitle"]?.message == "Contrato para faturamento obrigatório.") {
                "Expected subContractTitle error message, but got: ${errorsByField["subContractTitle"]?.message}"
            }

            assert(errorsByField.containsKey("beneficiaryContractType")) {
                "Expected holder validation error for beneficiaryContractType. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField["beneficiaryContractType"]?.message == "Regime de contratação inválido, escolha CLT ou PJ.") {
                "Expected beneficiaryContractType error message, but got: ${errorsByField["beneficiaryContractType"]?.message}"
            }

            assert(errorsByField.containsKey("hiredAt")) {
                "Expected holder validation error for hiredAt. Available fields: ${errorsByField.keys}"
            }

            // Verify that errors from both basic and holder validation phases are present
            val basicValidationFields = setOf("fullName", "nationalId", "email", "dateOfBirth")
            val holderValidationFields = setOf("subContractTitle", "beneficiaryContractType", "hiredAt")
            val errorFields = errorsByField.keys

            val hasBasicErrors = basicValidationFields.any { it in errorFields }
            val hasHolderErrors = holderValidationFields.any { it in errorFields }

            assert(hasBasicErrors) {
                "Expected at least one basic validation error. Error fields: $errorFields"
            }
            assert(hasHolderErrors) {
                "Expected at least one holder validation error. Error fields: $errorFields"
            }

            // Log all errors for debugging
            println("DEBUG: Validation errors from multiple phases:")
            error.error.forEach { errorItem ->
                val phase = when (errorItem.field) {
                    in basicValidationFields -> "BASIC"
                    in holderValidationFields -> "HOLDER"
                    else -> "OTHER"
                }
                println("  - [$phase] Field: ${errorItem.field}, Message: ${errorItem.message}")
            }
        }

        @Test
        fun `#run should collect validation errors from basic and dependent validation phases simultaneously for dependent beneficiary`() = runBlocking {
            // Setup a dependent beneficiary with errors in basic validation AND dependent-specific validation
            val dependentWithMultipleValidationPhaseErrors = beneficiaryBatchItemTransportValid.copy(
                index = index2,
                ownership = "Dependente",                  // Valid ownership to trigger dependent validation
                nationalId = "358.213.480-64",             // Valid but different CPF for dependent
                // Basic validation errors
                fullName = "X",                            // Too short (< 5 chars)
                sex = "INVALID_SEX",                       // Invalid sex
                email = "invalid.email.format",           // Invalid email
                mothersName = "M",                         // Too short mother's name
                phoneNumber = "123",                       // Invalid phone format
                // Dependent-specific validation errors
                parentNationalId = "invalid-cpf",          // Invalid parent CPF
                parentBeneficiaryRelationType = "Invalid Relation", // Invalid relation type
                relationExceeds30Days = "Maybe",           // Invalid yes/no value
                // These should be null for dependents (no validation errors expected)
                subContractTitle = null,
                beneficiaryContractType = null,
                hiredAt = null,
                cnpj = null
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(dependentWithMultipleValidationPhaseErrors)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            // Verify no successful validations
            assert(result.success.isEmpty()) {
                "Expected no successful validations, but got: ${result.success}"
            }

            // Verify exactly one error entry
            assert(result.errors.size == 1) {
                "Expected exactly 1 error entry, but got: ${result.errors.size}"
            }

            val error = result.errors[0]
            assert(error.index == index2) {
                "Expected error index to be $index2, but got: ${error.index}"
            }

            // Verify multiple validation errors from different phases are collected
            assert(error.error.size >= 7) {
                "Expected at least 7 validation errors from multiple phases, but got: ${error.error.size}. Errors: ${error.error.map { "${it.field}: ${it.message}" }}"
            }

            val errorsByField = error.error.associateBy { it.field }

            // Verify basic validation errors are present
            assert(errorsByField.containsKey("fullName")) {
                "Expected basic validation error for fullName. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField.containsKey("sex")) {
                "Expected basic validation error for sex. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField.containsKey("email")) {
                "Expected basic validation error for email. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField.containsKey("mothersName")) {
                "Expected basic validation error for mothersName. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField.containsKey("phoneNumber")) {
                "Expected basic validation error for phoneNumber. Available fields: ${errorsByField.keys}"
            }

            // Verify dependent-specific validation errors are present
            assert(errorsByField.containsKey("parentNationalId")) {
                "Expected dependent validation error for parentNationalId. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField["parentNationalId"]?.message == "CPF do titular obrigatório.") {
                "Expected parentNationalId error message, but got: ${errorsByField["parentNationalId"]?.message}"
            }

            assert(errorsByField.containsKey("parentBeneficiaryRelationType")) {
                "Expected dependent validation error for parentBeneficiaryRelationType. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField["parentBeneficiaryRelationType"]?.message == "Relação com titular inválida, selecione uma das opções da lista.") {
                "Expected parentBeneficiaryRelationType error message, but got: ${errorsByField["parentBeneficiaryRelationType"]?.message}"
            }

            assert(errorsByField.containsKey("relationExceeds30Days")) {
                "Expected dependent validation error for relationExceeds30Days. Available fields: ${errorsByField.keys}"
            }
            assert(errorsByField["relationExceeds30Days"]?.message == "Vínculo com titular a mais de 30 dias inválido, selecione uma das opções da lista.") {
                "Expected relationExceeds30Days error message, but got: ${errorsByField["relationExceeds30Days"]?.message}"
            }

            // Verify that errors from both basic and dependent validation phases are present
            val basicValidationFields = setOf("fullName", "sex", "email", "mothersName", "phoneNumber")
            val dependentValidationFields = setOf("parentNationalId", "parentBeneficiaryRelationType", "relationExceeds30Days")
            val errorFields = errorsByField.keys

            val hasBasicErrors = basicValidationFields.any { it in errorFields }
            val hasDependentErrors = dependentValidationFields.any { it in errorFields }

            assert(hasBasicErrors) {
                "Expected at least one basic validation error. Error fields: $errorFields"
            }
            assert(hasDependentErrors) {
                "Expected at least one dependent validation error. Error fields: $errorFields"
            }

            // Verify no holder-specific validation errors are present (since this is a dependent)
            val holderValidationFields = setOf("subContractTitle", "beneficiaryContractType", "hiredAt", "cnpj")
            val hasHolderErrors = holderValidationFields.any { it in errorFields }
            assert(!hasHolderErrors) {
                "Expected no holder validation errors for dependent beneficiary. Found holder fields: ${errorFields.intersect(holderValidationFields)}"
            }

            // Log all errors for debugging
            println("DEBUG: Validation errors from basic and dependent phases:")
            error.error.forEach { errorItem ->
                val phase = when (errorItem.field) {
                    in basicValidationFields -> "BASIC"
                    in dependentValidationFields -> "DEPENDENT"
                    else -> "OTHER"
                }
                println("  - [$phase] Field: ${errorItem.field}, Message: ${errorItem.message}")
            }
        }

        @Test
        fun `#run should collect all validation errors for multiple beneficiaries simultaneously demonstrating comprehensive batch validation`() = runBlocking {
            // Setup multiple beneficiaries each with different types of validation errors
            val holderWithErrors = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                ownership = "Titular",
                fullName = "Jo",                           // Too short
                nationalId = "11111111111",                // Invalid CPF
                subContractTitle = null,                   // Missing for holder
                beneficiaryContractType = "INVALID"        // Invalid type
            )

            val dependentWithErrors = beneficiaryBatchItemTransportValid.copy(
                index = index2,
                ownership = "Dependente",
                nationalId = "358.213.480-64",             // Different valid CPF
                fullName = "X",                            // Too short
                email = "not-email",                       // Invalid email
                parentNationalId = "invalid-cpf",          // Invalid parent CPF
                relationExceeds30Days = "Maybe",           // Invalid value
                subContractTitle = null,                   // Should be null for dependent
                beneficiaryContractType = null,            // Should be null for dependent
                hiredAt = null                             // Should be null for dependent
            )

            val anotherHolderWithErrors = beneficiaryBatchItemTransportValid.copy(
                index = index3,
                ownership = "Titular",
                nationalId = "123.456.789-00",             // Different invalid CPF
                sex = "INVALID",                           // Invalid sex
                email = null,                              // Missing email
                hiredAt = "invalid-date",                  // Invalid date format
                cnpj = "invalid-cnpj"                      // Invalid CNPJ (but won't be validated due to missing contract type)
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(holderWithErrors, dependentWithErrors, anotherHolderWithErrors)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.isEmpty()) {
                "Expected no successful validations, but got: ${result.success}"
            }

            // Verify exactly three error entries (one for each beneficiary)
            assert(result.errors.size == 3) {
                "Expected exactly 3 error entries, but got: ${result.errors.size}"
            }

            // Verify each beneficiary has multiple validation errors
            val errorsByIndex = result.errors.associateBy { it.index }

            // Verify holder 1 errors
            val holder1Error = errorsByIndex[index1]!!
            assert(holder1Error.error.size >= 4) {
                "Expected at least 4 errors for holder 1, but got: ${holder1Error.error.size}"
            }
            val holder1Fields = holder1Error.error.map { it.field }
            assert(holder1Fields.contains("fullName")) { "Expected fullName error for holder 1" }
            assert(holder1Fields.contains("nationalId")) { "Expected nationalId error for holder 1" }
            assert(holder1Fields.contains("subContractTitle")) { "Expected subContractTitle error for holder 1" }
            assert(holder1Fields.contains("beneficiaryContractType")) { "Expected beneficiaryContractType error for holder 1" }

            // Verify dependent errors
            val dependentError = errorsByIndex[index2]!!
            assert(dependentError.error.size >= 4) {
                "Expected at least 4 errors for dependent, but got: ${dependentError.error.size}"
            }
            val dependentFields = dependentError.error.map { it.field }
            assert(dependentFields.contains("fullName")) { "Expected fullName error for dependent" }
            assert(dependentFields.contains("email")) { "Expected email error for dependent" }
            assert(dependentFields.contains("parentNationalId")) { "Expected parentNationalId error for dependent" }
            assert(dependentFields.contains("relationExceeds30Days")) { "Expected relationExceeds30Days error for dependent" }

            // Verify holder 2 errors
            val holder2Error = errorsByIndex[index3]!!
            assert(holder2Error.error.size >= 3) {
                "Expected at least 3 errors for holder 2, but got: ${holder2Error.error.size}"
            }
            val holder2Fields = holder2Error.error.map { it.field }
            assert(holder2Fields.contains("nationalId")) { "Expected nationalId error for holder 2" }
            assert(holder2Fields.contains("sex")) { "Expected sex error for holder 2" }
            assert(holder2Fields.contains("email")) { "Expected email error for holder 2" }

            // Verify that all errors are collected simultaneously (batch validation behavior)
            val totalErrorCount = result.errors.sumOf { it.error.size }
            assert(totalErrorCount >= 11) {
                "Expected at least 11 total validation errors across all beneficiaries, demonstrating comprehensive batch validation. Got: $totalErrorCount"
            }

            // Log comprehensive results for debugging
            println("DEBUG: Comprehensive batch validation results:")
            println("  Total beneficiaries processed: ${transport.items.size}")
            println("  Total beneficiaries with errors: ${result.errors.size}")
            println("  Total validation errors collected: $totalErrorCount")

            result.errors.forEach { beneficiaryError ->
                println("  Beneficiary ${beneficiaryError.index}:")
                beneficiaryError.error.forEach { error ->
                    println("    - ${error.field}: ${error.message}")
                }
            }
        }
    }

    @Nested
    inner class EarlyReturnLogicTests {
        @Test
        fun `#run should return empty errors for dependent validation when ownership is Titular`() = runBlocking {
            val holderWithDependentFields = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                ownership = "Titular",
                parentNationalId = "invalid-cpf", // This should be ignored for holders
                parentBeneficiaryRelationType = null, // This should be ignored for holders
                relationExceeds30Days = null // This should be ignored for holders
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(holderWithDependentFields)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.size == 1)
            assert(result.success.contains(index1))
            assert(result.errors.isEmpty())
        }

        @Test
        fun `#run should return empty errors for holder validation when ownership is Dependente`() = runBlocking {
            val dependentWithHolderFields = beneficiaryBatchItemTransportValid.copy(
                index = index1,
                ownership = "Dependente",
                nationalId = "358.213.480-64",
                subContractTitle = null, // This should be ignored for dependents
                beneficiaryContractType = null, // This should be ignored for dependents
                hiredAt = null, // This should be ignored for dependents
                cnpj = "invalid-cnpj", // This should be ignored for dependents
                parentNationalId = "438.595.570-09",
                parentBeneficiaryRelationType = "Filha ou filho",
                relationExceeds30Days = "Sim"
            )

            val transport = beneficiaryBatchTransport.copy(
                items = listOf(dependentWithHolderFields)
            )

            val result = staticValidateBeneficiariesUseCase.run(transport).get()

            assert(result.success.size == 1)
            assert(result.success.contains(index1))
            assert(result.errors.isEmpty())
        }
    }
}
