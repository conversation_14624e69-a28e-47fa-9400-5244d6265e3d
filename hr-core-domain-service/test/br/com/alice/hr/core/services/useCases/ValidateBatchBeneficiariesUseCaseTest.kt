package br.com.alice.hr.core.services.useCases

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyAddress
import br.com.alice.hr.core.model.BeneficiaryBatchItemTransport
import br.com.alice.hr.core.model.BeneficiaryBatchTransport
import br.com.alice.hr.core.model.BeneficiaryBatchValidation
import br.com.alice.hr.core.model.BeneficiaryBatchValidationError
import br.com.alice.hr.core.model.BeneficiaryBatchValidationErrorItem
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class ValidateBatchBeneficiariesUseCaseTest {
    private val staticValidateBeneficiariesUseCase = mockk<StaticValidateBeneficiariesUseCase>()
    private val dynamicValidateBeneficiariesUseCase = mockk<DynamicValidateBeneficiariesUseCase>()
    private val addressValidateBeneficiariesUseCase = mockk<AddressValidateBeneficiariesUseCase>()

    private val validateBatchBeneficiariesUseCase = ValidateBatchBeneficiariesUseCase(
        staticValidateBeneficiariesUseCase,
        dynamicValidateBeneficiariesUseCase,
        addressValidateBeneficiariesUseCase
    )

    private val uploadId = RangeUUID.generate()
    private val companyId = RangeUUID.generate()
    private val index1 = RangeUUID.generate()
    private val index2 = RangeUUID.generate()
    private val today = LocalDate.now().toBrazilianDateFormat()

    private val company = Company(
        id = companyId,
        name = "Test Company",
        legalName = "Test Company LTDA",
        cnpj = "12345678000195",
        email = "<EMAIL>",
        phoneNumber = "11999999999",
        address = CompanyAddress(
            postalCode = "12345678",
            street = "Test Street",
            number = 123,
            city = "Test City",
            State = "SP"
        ),
        contractStartedAt = LocalDateTime.now().minusDays(30)
    )

    private val beneficiaryItem1 = BeneficiaryBatchItemTransport(
        index = index1,
        nationalId = "12345678901",
        fullName = "João Silva",
        email = "<EMAIL>",
        ownership = "Titular",
        activatedAt = today,
        beneficiaryContractType = "CLT",
        productTitle = "Plano Básico",
        subContractTitle = "Matriz",
        addressPostalCode = "12345678"
    )

    private val beneficiaryItem2 = BeneficiaryBatchItemTransport(
        index = index2,
        nationalId = "98765432109",
        fullName = "Maria Santos",
        email = "<EMAIL>",
        ownership = "Dependente",
        activatedAt = today,
        parentNationalId = "12345678901",
        parentBeneficiaryRelationType = "Cônjuge",
        productTitle = "Plano Básico",
        subContractTitle = "Matriz",
        addressPostalCode = "87654321"
    )

    private val beneficiaryBatch = BeneficiaryBatchTransport(
        uploadId = uploadId,
        items = listOf(beneficiaryItem1, beneficiaryItem2)
    )

    private val productTitles = listOf("Plano Básico", "Plano Premium")
    private val subContractTitles = listOf("Matriz", "Filial")
    private val relationTypeOptions = listOf("Cônjuge", "Filho(a)")

    private val staticValidationSuccess = BeneficiaryBatchValidation(
        success = listOf(index1, index2),
        errors = emptyList()
    )

    private val dynamicValidationSuccess = BeneficiaryBatchValidation(
        success = listOf(index1, index2),
        errors = emptyList()
    )

    private val addressValidationSuccess = BeneficiaryBatchValidation(
        success = listOf(index1, index2),
        errors = emptyList()
    )

    private val processedBeneficiaries = listOf(
        beneficiaryItem1.copy(addressStreet = "Rua A"),
        beneficiaryItem2.copy(addressStreet = "Rua B")
    )

    @Nested
    inner class SuccessfulValidation {
        @Test
        fun `should return success when all validations pass`() = runBlocking {
            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(listOf(index1, index2), result.validation.success)
            assertEquals(emptyList(), result.validation.errors)
            assertEquals(processedBeneficiaries, result.beneficiaries)
            assertEquals(productTitles, result.validation.productTitleOptions)
            assertEquals(subContractTitles, result.validation.subContractTitleOptions)
            assertEquals(relationTypeOptions, result.validation.parentBeneficiaryRelationOptions)
        }

        @Test
        fun `should accumulate success from all validation steps`() = runBlocking {
            val staticSuccess = BeneficiaryBatchValidation(success = listOf(index1), errors = emptyList())
            val dynamicSuccess = BeneficiaryBatchValidation(success = listOf(index2), errors = emptyList())
            val addressSuccess = BeneficiaryBatchValidation(success = listOf(index1, index2), errors = emptyList())

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticSuccess) } returns dynamicSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(listOf(index1, index2), result.validation.success)
            assertEquals(processedBeneficiaries, result.beneficiaries)
        }
    }

    @Nested
    inner class StaticValidationFailure {
        @Test
        fun `should accumulate errors when static validation has partial success`() = runBlocking {
            val staticError = BeneficiaryBatchValidationError(
                index = index2,
                error = listOf(BeneficiaryBatchValidationErrorItem("fullName", "Nome obrigatório"))
            )
            val staticValidationPartial = BeneficiaryBatchValidation(
                success = listOf(index1),
                errors = listOf(staticError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationPartial.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationPartial) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertTrue(result.validation.success.contains(index1))
            assertTrue(result.validation.errors.contains(staticError))
        }
    }

    @Nested
    inner class DynamicValidationFailure {
        @Test
        fun `should accumulate errors when dynamic validation has partial success`() = runBlocking {
            val dynamicError = BeneficiaryBatchValidationError(
                index = index2,
                error = listOf(BeneficiaryBatchValidationErrorItem("subContractTitle", "Subcontrato não encontrado"))
            )
            val dynamicValidationPartial = BeneficiaryBatchValidation(
                success = listOf(index1),
                errors = listOf(dynamicError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationSuccess) } returns dynamicValidationPartial.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertTrue(result.validation.success.contains(index1))
            assertTrue(result.validation.errors.contains(dynamicError))
        }
    }

    @Nested
    inner class AddressValidationFailure {
        @Test
        fun `should return early when address validation has no success`() = runBlocking {
            val addressError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("addressPostalCode", "CEP inválido"))
            )
            val addressValidationFailure = BeneficiaryBatchValidation(
                success = emptyList(),
                errors = listOf(addressError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationFailure to emptyList<BeneficiaryBatchItemTransport>()).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(emptyList(), result.validation.success)
            assertEquals(2, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(addressError))
            // Should also contain dependent validation error since index2 (dependent) has parent index1 (titular) with address errors
            assertTrue(result.validation.errors.any {
                it.index == index2 && it.error.any { error ->
                    error.field == "parentNationalId" && error.message.contains("CPF de titular não cadastrado")
                }
            })
            assertEquals(emptyList(), result.beneficiaries)
        }

        @Test
        fun `should accumulate errors when address validation has partial success`() = runBlocking {
            val addressError = BeneficiaryBatchValidationError(
                index = index2,
                error = listOf(BeneficiaryBatchValidationErrorItem("addressPostalCode", "CEP não encontrado"))
            )
            val addressValidationPartial = BeneficiaryBatchValidation(
                success = listOf(index1),
                errors = listOf(addressError)
            )
            val partialBeneficiaries = listOf(beneficiaryItem1.copy(addressStreet = "Rua A"))

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationPartial to partialBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertTrue(result.validation.success.contains(index1))
            assertTrue(result.validation.errors.contains(addressError))
            assertEquals(partialBeneficiaries, result.beneficiaries)
        }
    }

    @Nested
    inner class ErrorAccumulation {
        @Test
        fun `should accumulate errors from all validation steps`() = runBlocking {
            val staticError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("nationalId", "CPF inválido"))
            )
            val dynamicError = BeneficiaryBatchValidationError(
                index = index2,
                error = listOf(BeneficiaryBatchValidationErrorItem("productTitle", "Produto não encontrado"))
            )
            val addressError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("addressPostalCode", "CEP inválido"))
            )

            val staticValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index2),
                errors = listOf(staticError)
            )
            val dynamicValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index1),
                errors = listOf(dynamicError)
            )
            val addressValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index2),
                errors = listOf(addressError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationWithError.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationWithError) } returns dynamicValidationWithError.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationWithError to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(3, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(staticError))
            assertTrue(result.validation.errors.contains(addressError))

            // Verifica se o dependente (index2) tem o erro dinâmico + o erro de validação de dependente
            val dependentErrorWithValidation = result.validation.errors.find { it.index == index2 }
            assertNotNull(dependentErrorWithValidation)
            assertEquals(2, dependentErrorWithValidation!!.error.size)
            assertTrue(dependentErrorWithValidation.error.any { it.field == "productTitle" && it.message == "Produto não encontrado" })
            assertTrue(dependentErrorWithValidation.error.any {
                it.field == "parentNationalId" && it.message.contains("CPF de titular não cadastrado")
            })
        }

        @Test
        fun `should accumulate success and errors from mixed validation results`() = runBlocking {
            val staticError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("email", "Email inválido"))
            )
            val dynamicError = BeneficiaryBatchValidationError(
                index = index2,
                error = listOf(BeneficiaryBatchValidationErrorItem("activatedAt", "Data inválida"))
            )

            val staticValidationMixed = BeneficiaryBatchValidation(
                success = listOf(index2),
                errors = listOf(staticError)
            )
            val dynamicValidationMixed = BeneficiaryBatchValidation(
                success = listOf(index1),
                errors = listOf(dynamicError)
            )
            val addressValidationMixed = BeneficiaryBatchValidation(
                success = listOf(index1, index2),
                errors = emptyList()
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationMixed.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationMixed) } returns dynamicValidationMixed.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationMixed to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(emptyList(), result.validation.success)
            assertEquals(2, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(staticError))

            // Verifica se o dependente (index2) tem o erro dinâmico + o erro de validação de dependente
            val dependentErrorWithValidation = result.validation.errors.find { it.index == index2 }
            assertNotNull(dependentErrorWithValidation)
            assertEquals(2, dependentErrorWithValidation!!.error.size)
            assertTrue(dependentErrorWithValidation.error.any { it.field == "activatedAt" && it.message == "Data inválida" })
            assertTrue(dependentErrorWithValidation.error.any {
                it.field == "parentNationalId" && it.message.contains("CPF de titular não cadastrado")
            })
            assertEquals(emptyList(), result.beneficiaries)
        }
    }

    @Nested
    inner class EmptyBatch {
        @Test
        fun `should handle empty beneficiary batch`() = runBlocking {
            val emptyBatch = BeneficiaryBatchTransport(uploadId = uploadId, items = emptyList())
            val emptyValidation = BeneficiaryBatchValidation(success = emptyList(), errors = emptyList())

            coEvery { staticValidateBeneficiariesUseCase.run(emptyBatch) } returns emptyValidation.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, emptyBatch) } returns emptyValidation.success()
            coEvery { addressValidateBeneficiariesUseCase.run(emptyBatch) } returns (emptyValidation to emptyList<BeneficiaryBatchItemTransport>()).success()

            val result = validateBatchBeneficiariesUseCase.run(
                emptyBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(emptyList(), result.validation.success)
            assertEquals(emptyList(), result.validation.errors)
            assertEquals(emptyList(), result.beneficiaries)
        }
    }

    @Nested
    inner class DependentTitularValidation {
        @Test
        fun `should add error to dependent when titular has static validation errors`() = runBlocking {
            val staticError = BeneficiaryBatchValidationError(
                index = index1, // titular
                error = listOf(BeneficiaryBatchValidationErrorItem("nationalId", "CPF inválido"))
            )
            val staticValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index2), // dependent passes static validation
                errors = listOf(staticError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationWithError.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationWithError) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            // Should have the original static error plus the dependent validation error
            assertEquals(2, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(staticError))
            assertTrue(result.validation.errors.any {
                it.index == index2 && it.error.any { error ->
                    error.field == "parentNationalId" && error.message.contains("CPF de titular não cadastrado")
                }
            })
            assertEquals(emptyList(), result.validation.success)
        }

        @Test
        fun `should add error to dependent when titular has dynamic validation errors`() = runBlocking {
            val dynamicError = BeneficiaryBatchValidationError(
                index = index1, // titular
                error = listOf(BeneficiaryBatchValidationErrorItem("productTitle", "Produto não encontrado"))
            )
            val dynamicValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index2), // dependent passes dynamic validation
                errors = listOf(dynamicError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationSuccess) } returns dynamicValidationWithError.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            // Should have the original dynamic error plus the dependent validation error
            assertEquals(2, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(dynamicError))
            assertTrue(result.validation.errors.any {
                it.index == index2 && it.error.any { error ->
                    error.field == "parentNationalId" && error.message.contains("CPF de titular não cadastrado")
                }
            })
            assertEquals(emptyList(), result.validation.success)
        }

        @Test
        fun `should not add error to dependent when titular has no validation errors`() = runBlocking {
            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            // Should have no errors since both titular and dependent pass all validations
            assertEquals(emptyList(), result.validation.errors)
            assertEquals(listOf(index1, index2), result.validation.success)
        }

        @Test
        fun `should not add error to titular when dependent has validation errors`() = runBlocking {
            val staticError = BeneficiaryBatchValidationError(
                index = index2, // dependent has error
                error = listOf(BeneficiaryBatchValidationErrorItem("email", "Email inválido"))
            )
            val staticValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index1), // titular passes validation
                errors = listOf(staticError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationWithError.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationWithError) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            // Should only have the original dependent error, no additional errors
            assertEquals(1, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(staticError))
            assertEquals(listOf(index1), result.validation.success)
        }

        @Test
        fun `should handle multiple dependents with same titular having errors`() = runBlocking {
            val index3 = RangeUUID.generate()
            val index4 = RangeUUID.generate()

            val dependent1 = BeneficiaryBatchItemTransport(
                index = index3,
                nationalId = "11111111111",
                fullName = "Dependent 1",
                email = "<EMAIL>",
                ownership = "Dependente",
                parentNationalId = "12345678901",
                parentBeneficiaryRelationType = "Filho(a)",
                productTitle = "Plano Básico",
                addressPostalCode = "12345678"
            )

            val dependent2 = BeneficiaryBatchItemTransport(
                index = index4,
                nationalId = "22222222222",
                fullName = "Dependent 2",
                email = "<EMAIL>",
                ownership = "Dependente",
                parentNationalId = "12345678901",
                parentBeneficiaryRelationType = "Filho(a)",
                productTitle = "Plano Básico",
                addressPostalCode = "12345678"
            )

            val batchWithMultipleDependents = BeneficiaryBatchTransport(
                uploadId = uploadId,
                items = listOf(beneficiaryItem1, dependent1, dependent2)
            )

            val staticError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("nationalId", "CPF inválido"))
            )
            val staticValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index3, index4),
                errors = listOf(staticError)
            )

            val dynamicValidationForMultiple = BeneficiaryBatchValidation(
                success = listOf(index3, index4),
                errors = emptyList()
            )
            val addressValidationForMultiple = BeneficiaryBatchValidation(
                success = listOf(index3, index4),
                errors = emptyList()
            )

            coEvery { staticValidateBeneficiariesUseCase.run(batchWithMultipleDependents) } returns staticValidationWithError.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, batchWithMultipleDependents, staticValidationWithError) } returns dynamicValidationForMultiple.success()
            coEvery { addressValidateBeneficiariesUseCase.run(batchWithMultipleDependents) } returns (addressValidationForMultiple to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                batchWithMultipleDependents,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(3, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(staticError))
            assertTrue(result.validation.errors.any {
                it.index == index3 && it.error.any { error ->
                    error.field == "parentNationalId" && error.message.contains("CPF de titular não cadastrado")
                }
            })
            assertTrue(result.validation.errors.any {
                it.index == index4 && it.error.any { error ->
                    error.field == "parentNationalId" && error.message.contains("CPF de titular não cadastrado")
                }
            })
            assertEquals(emptyList(), result.validation.success)
        }

        @Test
        fun `should not add error to dependent when parentNationalId does not exist in batch`() = runBlocking {
            val dependentWithNonExistentParent = BeneficiaryBatchItemTransport(
                index = index2,
                nationalId = "98765432109",
                fullName = "Orphan Dependent",
                email = "<EMAIL>",
                ownership = "Dependente",
                parentNationalId = "99999999999",
                parentBeneficiaryRelationType = "Filho(a)",
                productTitle = "Plano Básico",
                addressPostalCode = "87654321"
            )

            val batchWithOrphanDependent = BeneficiaryBatchTransport(
                uploadId = uploadId,
                items = listOf(beneficiaryItem1, dependentWithNonExistentParent)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(batchWithOrphanDependent) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, batchWithOrphanDependent, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(batchWithOrphanDependent) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                batchWithOrphanDependent,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(emptyList(), result.validation.errors)
            assertEquals(listOf(index1, index2), result.validation.success)
        }

        @Test
        fun `should handle dependent with both static and dynamic validation errors on titular`() = runBlocking {
            val staticError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("nationalId", "CPF inválido"))
            )
            val dynamicError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("productTitle", "Produto não encontrado"))
            )

            val staticValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index2),
                errors = listOf(staticError)
            )
            val dynamicValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index2),
                errors = listOf(dynamicError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationWithError.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationWithError) } returns dynamicValidationWithError.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(3, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(staticError))
            assertTrue(result.validation.errors.contains(dynamicError))
            assertTrue(result.validation.errors.any {
                it.index == index2 && it.error.any { error ->
                    error.field == "parentNationalId" && error.message.contains("CPF de titular não cadastrado")
                }
            })
            assertEquals(emptyList(), result.validation.success)
        }

        @Test
        fun `should handle case where dependent has own validation errors and titular also has errors`() = runBlocking {
            val titularError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("nationalId", "CPF inválido"))
            )
            val dependentError = BeneficiaryBatchValidationError(
                index = index2,
                error = listOf(BeneficiaryBatchValidationErrorItem("email", "Email inválido"))
            )

            val staticValidationWithErrors = BeneficiaryBatchValidation(
                success = listOf(index2), // Dependente passa na validação estática
                errors = listOf(titularError, dependentError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationWithErrors.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationWithErrors) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(2, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(titularError))

            // Verifica se o dependente tem seus próprios erros + o erro de validação de dependente
            val dependentErrorWithValidation = result.validation.errors.find { it.index == index2 }
            assertNotNull(dependentErrorWithValidation)
            assertEquals(2, dependentErrorWithValidation!!.error.size)
            assertTrue(dependentErrorWithValidation.error.any { it.field == "email" && it.message == "Email inválido" })
            assertTrue(dependentErrorWithValidation.error.any {
                it.field == "parentNationalId" && it.message.contains("CPF de titular não cadastrado")
            })
            assertEquals(emptyList(), result.validation.success)
        }

        @Test
        fun `should add dependent validation error even when dependent already has multiple validation errors`() = runBlocking {
            val titularError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("nationalId", "CPF inválido"))
            )
            val dependentMultipleErrors = BeneficiaryBatchValidationError(
                index = index2,
                error = listOf(
                    BeneficiaryBatchValidationErrorItem("email", "Email inválido"),
                    BeneficiaryBatchValidationErrorItem("fullName", "Nome muito curto"),
                    BeneficiaryBatchValidationErrorItem("productTitle", "Produto não encontrado")
                )
            )

            val staticValidationWithErrors = BeneficiaryBatchValidation(
                success = listOf(index2), // Dependent passes static validation to allow flow to continue
                errors = listOf(titularError, dependentMultipleErrors)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationWithErrors.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationWithErrors) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(2, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(titularError))

            // Verifica se o dependente tem múltiplos erros próprios + o erro de validação de dependente
            val dependentErrorWithValidation = result.validation.errors.find { it.index == index2 }
            assertNotNull(dependentErrorWithValidation)
            assertEquals(4, dependentErrorWithValidation!!.error.size)
            assertTrue(dependentErrorWithValidation.error.any { it.field == "email" && it.message == "Email inválido" })
            assertTrue(dependentErrorWithValidation.error.any { it.field == "fullName" && it.message == "Nome muito curto" })
            assertTrue(dependentErrorWithValidation.error.any { it.field == "productTitle" && it.message == "Produto não encontrado" })
            assertTrue(dependentErrorWithValidation.error.any {
                it.field == "parentNationalId" && it.message.contains("CPF de titular não cadastrado")
            })

            assertEquals(emptyList(), result.validation.success)
        }

        @Test
        fun `should handle mixed ownership types with various validation states`() = runBlocking {
            val index3 = RangeUUID.generate()
            val index4 = RangeUUID.generate()

            val titular1 = beneficiaryItem1.copy(index = index1)
            val titular2 = BeneficiaryBatchItemTransport(
                index = index3,
                nationalId = "33333333333",
                fullName = "Titular 2",
                email = "<EMAIL>",
                ownership = "Titular",
                activatedAt = today,
                beneficiaryContractType = "CLT",
                productTitle = "Plano Básico",
                subContractTitle = "Matriz",
                addressPostalCode = "12345678"
            )
            val dependent1 = beneficiaryItem2.copy(index = index2)
            val dependent2 = BeneficiaryBatchItemTransport(
                index = index4,
                nationalId = "44444444444",
                fullName = "Dependent 2",
                email = "<EMAIL>",
                ownership = "Dependente",
                parentNationalId = "33333333333",
                parentBeneficiaryRelationType = "Cônjuge",
                productTitle = "Plano Básico",
                addressPostalCode = "87654321"
            )

            val mixedBatch = BeneficiaryBatchTransport(
                uploadId = uploadId,
                items = listOf(titular1, dependent1, titular2, dependent2)
            )

            val titular1Error = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("nationalId", "CPF inválido"))
            )

            val staticValidationMixed = BeneficiaryBatchValidation(
                success = listOf(index3, index4),
                errors = listOf(titular1Error)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(mixedBatch) } returns staticValidationMixed.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, mixedBatch, staticValidationMixed) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(mixedBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                mixedBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(2, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(titular1Error))
            assertTrue(result.validation.errors.any {
                it.index == index2 && it.error.any { error ->
                    error.field == "parentNationalId" && error.message.contains("CPF de titular não cadastrado")
                }
            })
            assertEquals(listOf(index3, index4), result.validation.success)
        }

        @Test
        fun `should handle dependent with null parentNationalId`() = runBlocking {
            val dependentWithNullParent = BeneficiaryBatchItemTransport(
                index = index2,
                nationalId = "98765432109",
                fullName = "Invalid Dependent",
                email = "<EMAIL>",
                ownership = "Dependente",
                parentNationalId = null,
                parentBeneficiaryRelationType = "Filho(a)",
                productTitle = "Plano Básico",
                addressPostalCode = "87654321"
            )

            val batchWithNullParent = BeneficiaryBatchTransport(
                uploadId = uploadId,
                items = listOf(beneficiaryItem1, dependentWithNullParent)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(batchWithNullParent) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, batchWithNullParent, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(batchWithNullParent) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                batchWithNullParent,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(emptyList(), result.validation.errors)
            assertEquals(listOf(index1, index2), result.validation.success)
        }

        @Test
        fun `should handle dependent with empty parentNationalId`() = runBlocking {
            val dependentWithEmptyParent = BeneficiaryBatchItemTransport(
                index = index2,
                nationalId = "98765432109",
                fullName = "Invalid Dependent",
                email = "<EMAIL>",
                ownership = "Dependente",
                parentNationalId = "",
                parentBeneficiaryRelationType = "Filho(a)",
                productTitle = "Plano Básico",
                addressPostalCode = "87654321"
            )

            val batchWithEmptyParent = BeneficiaryBatchTransport(
                uploadId = uploadId,
                items = listOf(beneficiaryItem1, dependentWithEmptyParent)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(batchWithEmptyParent) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, batchWithEmptyParent, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(batchWithEmptyParent) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                batchWithEmptyParent,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(emptyList(), result.validation.errors)
            assertEquals(listOf(index1, index2), result.validation.success)
        }

        @Test
        fun `should handle case with only titulars and no dependents`() = runBlocking {
            val titular2 = BeneficiaryBatchItemTransport(
                index = index2,
                nationalId = "98765432109",
                fullName = "Titular 2",
                email = "<EMAIL>",
                ownership = "Titular",
                activatedAt = today,
                beneficiaryContractType = "CLT",
                productTitle = "Plano Básico",
                subContractTitle = "Matriz",
                addressPostalCode = "87654321"
            )

            val titularsOnlyBatch = BeneficiaryBatchTransport(
                uploadId = uploadId,
                items = listOf(beneficiaryItem1, titular2)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(titularsOnlyBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, titularsOnlyBatch, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(titularsOnlyBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                titularsOnlyBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(emptyList(), result.validation.errors)
            assertEquals(listOf(index1, index2), result.validation.success)
        }

        @Test
        fun `should handle case with only dependents and no titulars`() = runBlocking {
            val dependent1 = BeneficiaryBatchItemTransport(
                index = index1,
                nationalId = "11111111111",
                fullName = "Dependent 1",
                email = "<EMAIL>",
                ownership = "Dependente",
                parentNationalId = "99999999999",
                parentBeneficiaryRelationType = "Filho(a)",
                productTitle = "Plano Básico",
                addressPostalCode = "12345678"
            )

            val dependent2 = BeneficiaryBatchItemTransport(
                index = index2,
                nationalId = "22222222222",
                fullName = "Dependent 2",
                email = "<EMAIL>",
                ownership = "Dependente",
                parentNationalId = "88888888888",
                parentBeneficiaryRelationType = "Cônjuge",
                productTitle = "Plano Básico",
                addressPostalCode = "87654321"
            )

            val dependentsOnlyBatch = BeneficiaryBatchTransport(
                uploadId = uploadId,
                items = listOf(dependent1, dependent2)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(dependentsOnlyBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, dependentsOnlyBatch, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(dependentsOnlyBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                dependentsOnlyBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(emptyList(), result.validation.errors)
            assertEquals(listOf(index1, index2), result.validation.success)
        }

        @Test
        fun `should handle dependent validation with address validation errors`() = runBlocking {
            val titularError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("nationalId", "CPF inválido"))
            )
            val addressError = BeneficiaryBatchValidationError(
                index = index2,
                error = listOf(BeneficiaryBatchValidationErrorItem("addressPostalCode", "CEP inválido"))
            )

            val staticValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index2),
                errors = listOf(titularError)
            )
            val addressValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index1),
                errors = listOf(addressError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationWithError.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationWithError) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationWithError to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(2, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(titularError))

            // Verifica se o dependente tem o erro de endereço + o erro de validação de dependente
            val dependentErrorWithValidation = result.validation.errors.find { it.index == index2 }
            assertNotNull(dependentErrorWithValidation)
            assertEquals(2, dependentErrorWithValidation!!.error.size)
            assertTrue(dependentErrorWithValidation.error.any { it.field == "addressPostalCode" && it.message == "CEP inválido" })
            assertTrue(dependentErrorWithValidation.error.any {
                it.field == "parentNationalId" && it.message.contains("CPF de titular não cadastrado")
            })
            assertEquals(emptyList(), result.validation.success)
        }

        @Test
        fun `should handle case where dependent validation error prevents address validation success`() = runBlocking {
            val titularError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("nationalId", "CPF inválido"))
            )

            val staticValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index2),
                errors = listOf(titularError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationWithError.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationWithError) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(2, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(titularError))
            assertTrue(result.validation.errors.any {
                it.index == index2 && it.error.any { error ->
                    error.field == "parentNationalId" && error.message.contains("CPF de titular não cadastrado")
                }
            })
            assertEquals(emptyList(), result.validation.success)
            assertEquals(emptyList(), result.beneficiaries)
        }

        @Test
        fun `should handle whitespace in parentNationalId matching`() = runBlocking {
            val dependentWithWhitespace = BeneficiaryBatchItemTransport(
                index = index2,
                nationalId = "98765432109",
                fullName = "Maria Santos",
                email = "<EMAIL>",
                ownership = "Dependente",
                parentNationalId = " 12345678901 ",
                parentBeneficiaryRelationType = "Cônjuge",
                productTitle = "Plano Básico",
                addressPostalCode = "87654321"
            )

            val batchWithWhitespace = BeneficiaryBatchTransport(
                uploadId = uploadId,
                items = listOf(beneficiaryItem1, dependentWithWhitespace)
            )

            val titularError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("nationalId", "CPF inválido"))
            )
            val staticValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index2),
                errors = listOf(titularError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(batchWithWhitespace) } returns staticValidationWithError.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, batchWithWhitespace, staticValidationWithError) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(batchWithWhitespace) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                batchWithWhitespace,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(2, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(titularError))
            assertTrue(result.validation.errors.any {
                it.index == index2 && it.error.any { error ->
                    error.field == "parentNationalId" && error.message.contains("CPF de titular não cadastrado")
                }
            })
        }

        @Test
        fun `should handle case sensitivity in ownership field`() = runBlocking {
            val dependentWithDifferentCase = BeneficiaryBatchItemTransport(
                index = index2,
                nationalId = "98765432109",
                fullName = "Maria Santos",
                email = "<EMAIL>",
                ownership = "DEPENDENTE",
                parentNationalId = "12345678901",
                parentBeneficiaryRelationType = "Cônjuge",
                productTitle = "Plano Básico",
                addressPostalCode = "87654321"
            )

            val batchWithDifferentCase = BeneficiaryBatchTransport(
                uploadId = uploadId,
                items = listOf(beneficiaryItem1, dependentWithDifferentCase)
            )

            val titularError = BeneficiaryBatchValidationError(
                index = index1,
                error = listOf(BeneficiaryBatchValidationErrorItem("nationalId", "CPF inválido"))
            )
            val staticValidationWithError = BeneficiaryBatchValidation(
                success = listOf(index2),
                errors = listOf(titularError)
            )

            coEvery { staticValidateBeneficiariesUseCase.run(batchWithDifferentCase) } returns staticValidationWithError.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, batchWithDifferentCase, staticValidationWithError) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(batchWithDifferentCase) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                batchWithDifferentCase,
                company,
                productTitles,
                subContractTitles,
                relationTypeOptions
            ).get()

            assertEquals(2, result.validation.errors.size)
            assertTrue(result.validation.errors.contains(titularError))
            assertTrue(result.validation.errors.any {
                it.index == index2 && it.error.any { error ->
                    error.field == "parentNationalId" && error.message.contains("CPF de titular não cadastrado")
                }
            })
        }
    }

    @Nested
    inner class OptionsValidation {
        @Test
        fun `should include provided options in validation result`() = runBlocking {
            val customProductTitles = listOf("Custom Plan A", "Custom Plan B")
            val customSubContractTitles = listOf("Branch 1", "Branch 2")
            val customRelationTypeOptions = listOf("Spouse", "Child", "Parent")

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                customProductTitles,
                customSubContractTitles,
                customRelationTypeOptions
            ).get()

            assertEquals(customProductTitles, result.validation.productTitleOptions)
            assertEquals(customSubContractTitles, result.validation.subContractTitleOptions)
            assertEquals(customRelationTypeOptions, result.validation.parentBeneficiaryRelationOptions)
        }

        @Test
        fun `should handle empty options lists`() = runBlocking {
            val emptyOptions = emptyList<String>()

            coEvery { staticValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns staticValidationSuccess.success()
            coEvery { dynamicValidateBeneficiariesUseCase.run(company, beneficiaryBatch, staticValidationSuccess) } returns dynamicValidationSuccess.success()
            coEvery { addressValidateBeneficiariesUseCase.run(beneficiaryBatch) } returns (addressValidationSuccess to processedBeneficiaries).success()

            val result = validateBatchBeneficiariesUseCase.run(
                beneficiaryBatch,
                company,
                emptyOptions,
                emptyOptions,
                emptyOptions
            ).get()

            assertEquals(emptyOptions, result.validation.productTitleOptions)
            assertEquals(emptyOptions, result.validation.subContractTitleOptions)
            assertEquals(emptyOptions, result.validation.parentBeneficiaryRelationOptions)
        }
    }
}
