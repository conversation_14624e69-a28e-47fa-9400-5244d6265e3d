package br.com.alice.nullvs.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.nullvs.SERVICE_NAME
import br.com.alice.nullvs.models.company.NullvsCompanyContractWebhookReceived

class NullvsCompanyContractWebhookReceivedEvent(
    request: NullvsCompanyContractWebhookReceived,
    inheritedAuthToken: String? = null
) :
    NotificationEvent<NullvsCompanyContractWebhookReceivedEvent.Payload>(
        producer = SERVICE_NAME,
        name = name,
        payload = Payload(request),
        inheritedAuthToken = inheritedAuthToken,
    ) {
    companion object {
        const val name = "nullvs-company-contract-webhook-received"
    }

    data class Payload(
        val response: NullvsCompanyContractWebhookReceived,
    )
}
