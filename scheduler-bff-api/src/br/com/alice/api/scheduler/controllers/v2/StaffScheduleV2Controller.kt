package br.com.alice.api.scheduler.controllers.v2

import br.com.alice.api.scheduler.ServiceConfig
import br.com.alice.api.scheduler.controllers.StaffController
import br.com.alice.api.scheduler.controllers.model.EditStaffScheduleRequest
import br.com.alice.api.scheduler.controllers.model.StaffScheduleResponse
import br.com.alice.api.scheduler.controllers.model.toProviderResponse
import br.com.alice.api.scheduler.converters.StaffScheduleRequestConverter
import br.com.alice.api.scheduler.converters.StaffScheduleResponseConverter
import br.com.alice.common.RangeUUID
import br.com.alice.common.Response
import br.com.alice.common.client.DataEventClient
import br.com.alice.common.client.DataEventPayload
import br.com.alice.common.client.DataEventType
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.StaffSchedule
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.schedule.client.StaffScheduleService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import io.ktor.http.HttpStatusCode
import java.time.LocalTime
import java.util.UUID

class StaffScheduleV2Controller(
    staffService: StaffService,
    private val staffScheduleService: StaffScheduleService,
    private val providerUnitService: ProviderUnitService,
    private val dataEventClient: DataEventClient,
) : StaffController(staffService) {

    suspend fun create(staffId: String, request: EditStaffScheduleRequest): Response {
        logger.info(
            "StaffScheduleController::create",
            "staff_id" to staffId,
            "request" to request,
            "requester" to currentStaffId()
        )

        if (isInvalidTimeRange(request.staffSchedule.startHour, request.staffSchedule.untilHour)) {
            val startHour = request.staffSchedule.startHour.toString()
            val untilHour = request.staffSchedule.untilHour.toString()
            return Response(
                status = HttpStatusCode.BadRequest,
                message = "The end time ($untilHour) of a staff schedule must be after the start time ($startHour)"
            )
        }

        val staffSchedule = StaffScheduleRequestConverter.convert(request.staffSchedule, staffId)

        return staffScheduleService.createV2(
            staffId.toUUID(),
            staffSchedule,
            request.selectedDate,
            request.selectedOption,
        ).map {
            val staffScheduleResponse = StaffScheduleResponseConverter.convert(it)
            val providerUnitMap = getProviderUnitName(listOf(staffScheduleResponse))
            staffScheduleResponse.toProviderResponse(providerUnitMap)
        }.foldResponse()
    }

    suspend fun update(id: String, request: EditStaffScheduleRequest): Response {
        logger.info(
            "StaffScheduleController::update",
            "staff_schedule_id" to id,
            "request" to request,
            "requester" to currentStaffId()
        )

        if (isInvalidTimeRange(request.staffSchedule.startHour, request.staffSchedule.untilHour)) {
            val startHour = request.staffSchedule.startHour.toString()
            val untilHour = request.staffSchedule.untilHour.toString()
            return Response(
                status = HttpStatusCode.BadRequest,
                message = "The end time ($untilHour) of a staff schedule must be after the start time ($startHour)"
            )
        }

        return staffScheduleService.getStaffScheduleById(id.toUUID())
            .flatMap {
                val staffSchedule = StaffScheduleRequestConverter.convert(request.staffSchedule, it.staffId.toString())
                sendEventIfCarveoutIsChanged(it, staffSchedule)
                staffScheduleService.updateV2(
                    it.copy(
                        startHour = staffSchedule.startHour,
                        untilHour = staffSchedule.untilHour,
                        carveOutHours = staffSchedule.carveOutHours,
                        providerUnitId = staffSchedule.providerUnitId
                    ),
                    request.selectedDate,
                    request.selectedOption,
                    currentStaffId()
                )
            }.map {
                val staffScheduleResponse = StaffScheduleResponseConverter.convert(it)
                val providerUnitMap = getProviderUnitName(listOf(staffScheduleResponse))
                staffScheduleResponse.toProviderResponse(providerUnitMap)
            }.foldResponse()
    }

    private fun isInvalidTimeRange(startHour: LocalTime, untilHour: LocalTime): Boolean {
        val startHourIsAfterUntilHour = startHour.isAfter(untilHour)
        val startAndUntilHoursAreEqual = startHour == untilHour
        val invalid = startHourIsAfterUntilHour || startAndUntilHoursAreEqual
        logger.info("StaffScheduleController::isInvalidTimeRange the end time ($untilHour) of a staff schedule must be after the start time ($startHour)")
        return invalid
    }

    private suspend fun getProviderUnitName(staffSchedules: List<StaffScheduleResponse>): Map<UUID, ProviderUnit> {
        val providerUnitIds = staffSchedules.mapNotNull { it.providerUnitId }.distinct()
        val providerUnitMap = takeIf { providerUnitIds.isNotEmpty() }?.let {
            providerUnitService.getByIds(providerUnitIds, false).get().associateBy { it.id }
        } ?: emptyMap()

        return providerUnitMap
    }

    private suspend fun sendEventIfCarveoutIsChanged(
        staffScheduleOld: StaffSchedule,
        staffScheduleEdited: StaffSchedule
    ) =
        takeIf { staffScheduleOld.carveOutHours != staffScheduleEdited.carveOutHours }?.let {
            dataEventClient.sendEventAsync(
                url = ServiceConfig.dataEventApiUrl,
                payload = DataEventPayload(
                    staffId = staffScheduleOld.staffId.toString(),
                    sessionId = RangeUUID.generate().toString(),
                    action = "update_carve_out",
                    namespace = "health.staff_schedule",
                    type = DataEventType.EVENT.description,
                    properties = mapOf(
                        "carve_out_enabled" to (staffScheduleEdited.carveOutHours != null).toString(),
                        "carve_out_hours" to staffScheduleEdited.carveOutHours.toString(),
                    ),
                    version = "1.0"
                )
            )
        }
}
