package br.com.alice.api.scheduler.routes

import br.com.alice.api.scheduler.controllers.v2.StaffScheduleV2Controller
import br.com.alice.common.coHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.v2Routes() {

    authenticate {
        val staffScheduleController by inject< StaffScheduleV2Controller>()

        route("/v2") {
            route("/staff/{staffId}") {
                post("/schedule") { coHandler("staffId", staffScheduleController::create) }
                put("/schedule/{id}") { coHandler("id", staffScheduleController::update) }
            }
        }
    }
}

