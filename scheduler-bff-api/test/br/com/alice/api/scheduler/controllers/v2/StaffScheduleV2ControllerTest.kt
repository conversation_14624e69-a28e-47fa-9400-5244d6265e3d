package br.com.alice.api.scheduler.controllers.v2

import br.com.alice.api.scheduler.ControllerTestHelper
import br.com.alice.api.scheduler.ServiceConfig
import br.com.alice.api.scheduler.controllers.model.EditStaffScheduleRequest
import br.com.alice.api.scheduler.controllers.model.StaffScheduleRequest
import br.com.alice.api.scheduler.controllers.model.toProviderResponse
import br.com.alice.api.scheduler.converters.StaffScheduleResponseConverter
import br.com.alice.common.RangeUUID
import br.com.alice.common.client.DataEventClient
import br.com.alice.common.client.DataEventPayload
import br.com.alice.common.client.DataEventType
import br.com.alice.common.convertTo
import br.com.alice.common.core.extensions.fromSaoPauloToUTCTimeZone
import br.com.alice.common.core.extensions.toLocalTime
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.models.Staff
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.schedule.client.StaffScheduleService
import br.com.alice.schedule.model.StaffScheduleEditOption
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.success
import io.ktor.util.date.WeekDay
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import java.time.LocalDate
import kotlin.test.BeforeTest
import kotlin.test.Test

class StaffScheduleV2ControllerTest : ControllerTestHelper() {

    private val token = RangeUUID.generate().toString()
    private val apiVersion = "v2"
    private val staffScheduleService: StaffScheduleService = mockk()
    private val staffService: StaffService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val dataEventClient: DataEventClient = mockk()

    private val startHour = "09:00".toLocalTime()
    private val untilHour = "12:00".toLocalTime()
    private val weekDay = WeekDay.valueOf("WEDNESDAY")
    private val providerUnitId = RangeUUID.generate()
    private val selectedDate = LocalDate.of(2025, 2, 5)

    private val providerUnit = TestModelFactory.buildProviderUnit(id = providerUnitId)
    private val staffForAuth = staff.convertTo(Staff::class)
    val staffSchedule = TestModelFactory.buildStaffSchedule(
        staffId = staff.id,
        carveOutHours = 24,
        alsoDigital = true,
        startHour = startHour.fromSaoPauloToUTCTimeZone(),
        untilHour = untilHour.fromSaoPauloToUTCTimeZone(),
        weekDay = weekDay
    )

    @BeforeTest
    override fun setup() {
        super.setup()

        this.module.single { staffScheduleService }
        this.module.single { staffService }
        this.module.single { providerUnitService }
        this.module.single { dataEventClient }
        this.module.single { StaffScheduleV2Controller(get(), get(), get(), get()) }
    }

    @Test
    fun `#create staff availability should return 200 OK with newly created staff schedule`() =
        mockRangeUuidAndDateTime { uuid, date ->
            val staffSchedule = staffSchedule.copy(createdAt = date, updatedAt = date, id = uuid)
            val request = EditStaffScheduleRequest(
                staffSchedule = StaffScheduleRequest(
                    startHour = startHour,
                    untilHour = untilHour,
                    weekDay = weekDay.toString(),
                    type = staffSchedule.type,
                    providerUnitId = null,
                    alsoDigital = true,
                    carveOutDays = 1,
                ),
                selectedDate = selectedDate,
                selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT
            )
            coEvery {
                staffScheduleService.createV2(
                    staff.id,
                    staffSchedule,
                    selectedDate,
                    StaffScheduleEditOption.THIS_SLOT_AND_NEXT
                )
            } returns staffSchedule.success()


            val expectedResponse = StaffScheduleResponseConverter.convert(staffSchedule)

            authenticatedAs(token, staffForAuth) {
                post("${apiVersion}/staff/${staff.id}/schedule", request) {
                    assertThat(it).isOKWithData(expectedResponse)
                }
            }
        }

    @Test
    fun `#create staff availability with provider should return 200 OK with newly created staff schedule`() = mockRangeUuidAndDateTime { uuid, date ->
        val staffSchedule = staffSchedule.copy(
            createdAt = date,
            updatedAt = date,
            id = uuid,
            providerUnitId = providerUnitId
        )
        val request = EditStaffScheduleRequest(
            staffSchedule = StaffScheduleRequest(
                startHour = startHour,
                untilHour = untilHour,
                weekDay = weekDay.toString(),
                type = staffSchedule.type,
                alsoDigital = true,
                carveOutDays = 1,
                providerUnitId = providerUnitId,
            ),
            selectedDate = selectedDate,
            selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT
        )
        coEvery {
            staffScheduleService.createV2(
                staff.id,
                staffSchedule,
                selectedDate,
                StaffScheduleEditOption.THIS_SLOT_AND_NEXT
            )
        } returns staffSchedule.success()

        coEvery {
            providerUnitService.getByIds(listOf(providerUnitId), false)
        } returns listOf(providerUnit).success()

        val expectedResponse = StaffScheduleResponseConverter.convert(staffSchedule)
            .toProviderResponse(mapOf(providerUnitId to providerUnit))

        authenticatedAs(token, staffForAuth) {
            post("${apiVersion}/staff/${staff.id}/schedule", request) {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#create staff availability should return 400 when time range is invalid`() = mockRangeUuidAndDateTime { uuid, date ->
        val request = EditStaffScheduleRequest(
            staffSchedule = StaffScheduleRequest(
                startHour = staffSchedule.untilHour.plusHours(1),
                untilHour = staffSchedule.untilHour,
                weekDay = weekDay.toString(),
                type = staffSchedule.type,
                alsoDigital = true,
                carveOutDays = 1,
                providerUnitId = providerUnitId,
            ),
            selectedDate = selectedDate,
            selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT
        )
        authenticatedAs(token, staffForAuth) {
            post("${apiVersion}/staff/${staff.id}/schedule", request) {
                assertThat(it).isBadRequest()
            }
        }

        coVerify { staffScheduleService wasNot called }
    }

    @Test
    fun `#update staffSchedule returns 200 OK with new data`() {
        val request = EditStaffScheduleRequest(
            staffSchedule = StaffScheduleRequest(
                startHour = staffSchedule.startHour.plusHours(1),
                untilHour = staffSchedule.untilHour,
                weekDay = staffSchedule.weekDay.toString(),
                providerUnitId = staffSchedule.providerUnitId,
                alsoDigital = staffSchedule.alsoDigital,
                carveOutDays = 1,
                id = staffSchedule.id,
            ),
            selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT,
            selectedDate = selectedDate
        )

        val updatedStaffSchedule = staffSchedule.copy(
            startHour = request.staffSchedule.startHour.fromSaoPauloToUTCTimeZone(),
            untilHour = request.staffSchedule.untilHour.fromSaoPauloToUTCTimeZone(),
            weekDay = staffSchedule.weekDay,
            carveOutHours = 24,
            providerUnitId = staffSchedule.providerUnitId
        )

        coEvery { staffScheduleService.getStaffScheduleById(updatedStaffSchedule.id) } returns staffSchedule.success()
        coEvery { staffScheduleService.updateV2(updatedStaffSchedule, selectedDate, selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT, staffForAuth.id) } returns updatedStaffSchedule.success()
        coEvery { providerUnitService.getByIds(listOf(providerUnitId), false) } returns listOf(providerUnit).success()

        val expectedResponse = StaffScheduleResponseConverter.convert(updatedStaffSchedule)
            .toProviderResponse(mapOf(providerUnitId to providerUnit))

        authenticatedAs(token, staffForAuth) {
            put("${apiVersion}/staff/${staff.id}/schedule/${staffSchedule.id}", request) {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#update staffSchedule returns 200 OK with new data updating carve out hours`() = mockRangeUuidAndDateTime { uuid, _ ->
        val token = RangeUUID.generate().toString()
        val staffSchedule = staffSchedule.copy(carveOutHours = null)

        val request = EditStaffScheduleRequest(
            staffSchedule = StaffScheduleRequest(
                startHour = staffSchedule.startHour.plusHours(1),
                untilHour = staffSchedule.untilHour,
                weekDay = staffSchedule.weekDay.toString(),
                providerUnitId = staffSchedule.providerUnitId,
                alsoDigital = staffSchedule.alsoDigital,
                carveOutDays = 1,
                id = staffSchedule.id,
            ),
            selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT,
            selectedDate = selectedDate
        )

        val updatedStaffSchedule = staffSchedule.copy(
            startHour = request.staffSchedule.startHour.fromSaoPauloToUTCTimeZone(),
            untilHour = request.staffSchedule.untilHour.fromSaoPauloToUTCTimeZone(),
            weekDay = staffSchedule.weekDay,
            carveOutHours = 24,
            providerUnitId = staffSchedule.providerUnitId
        )

        val eventPayload = DataEventPayload(
            staffId = staffSchedule.staffId.toString(),
            sessionId = uuid.toString(),
            action = "update_carve_out",
            namespace = "health.staff_schedule",
            type = DataEventType.EVENT.description,
            properties = mapOf(
                "carve_out_enabled" to true.toString(),
                "carve_out_hours" to updatedStaffSchedule.carveOutHours.toString(),
            ),
            version = "1.0"
        )

        coEvery { staffScheduleService.getStaffScheduleById(updatedStaffSchedule.id) } returns staffSchedule.success()
        coEvery { staffScheduleService.updateV2(updatedStaffSchedule, selectedDate, selectedOption = StaffScheduleEditOption.THIS_SLOT_AND_NEXT, staffForAuth.id) } returns updatedStaffSchedule.success()
        coEvery {
            dataEventClient.sendEventAsync(
                url = ServiceConfig.dataEventApiUrl,
                payload = eventPayload
            )
        } returns mockk()

        val expectedResponse = StaffScheduleResponseConverter.convert(updatedStaffSchedule)

        authenticatedAs(token, staffForAuth) {
            put("${apiVersion}/staff/${staff.id}/schedule/${staffSchedule.id}", request) {
                assertThat(it).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#update staffSchedule returns 400 when time range is invalid`() {
        val request = StaffScheduleRequest(
            startHour = staffSchedule.untilHour,
            untilHour = staffSchedule.untilHour,
            weekDay = staffSchedule.weekDay.toString(),
            providerUnitId = staffSchedule.providerUnitId,
            alsoDigital = staffSchedule.alsoDigital,
        )

        authenticatedAs(token, staffForAuth) {
            put("${apiVersion}/staff/${staff.id}/schedule/${staffSchedule.id}", request) {
                assertThat(it).isBadRequest()
            }
        }

        coVerify { staffScheduleService wasNot called }
    }

}

