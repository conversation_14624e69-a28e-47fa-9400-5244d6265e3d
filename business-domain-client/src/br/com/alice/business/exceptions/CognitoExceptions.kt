package br.com.alice.business.exceptions

import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.data.layer.models.Beneficiary

class ServerCouldNotProcessBeneficiaryCreationException(
    message: String,
    code: String = "server_could_not_process_beneficiary_creation",
    cause: Throwable? = null
) : InvalidArgumentException(message, code, cause) {
    constructor(
        successes: List<Pair<String, Beneficiary>>?,
        failures: List<Throwable>?
    ) : this(
        message = """
    Não foi possível cadastrar todos os beneficiários.
    Sucessos: ${successes?.size}, falhas: ${failures?.size}.
    Mensagens de erro: ${failures?.mapNotNull { it.message }?.joinToString("\n")}
    """.trimIndent()
    )
}