#!/usr/bin/env bash
#
# Called by "git commit" with no arguments.  The hook should
# exit with non-zero status after issuing an appropriate message if
# it wants to stop the commit.

# Redirect output to stderr.
exec 1>&2
set -euo pipefail

# --- OPA policy check and Gradle task for data.json ---
# Path to the policies directory (adjust if needed)
POLICIES_DIR="data-layer-core/resources/policies"
# Gradle task to run
GRADLE_TASK="generateOPAAggregates"
# Check for staged data.json changes inside the policies directory
if git diff --cached --name-only --diff-filter=ACM | grep -q "^${POLICIES_DIR}/.*/data.json$"; then
  echo "Detected changes to data.json in ${POLICIES_DIR}. Running ${GRADLE_TASK} task..."
  if ! ./gradlew "${GRADLE_TASK}"; then
    echo "Gradle task failed. Commit aborted."
    exit 1
  fi
  # re-add generated files if they are tracked
  git add -u "${POLICIES_DIR}"/*.json || true
fi
