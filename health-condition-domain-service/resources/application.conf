ktor {
    deployment {
        port = 8080
        port = ${?PORT}
    }
    application {
        modules = [br.com.alice.healthcondition.ApplicationKt.module]
    }
}

systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}

test {
    aws {
        social.messaging {
            region = "us-east-1"
            phone.id = "phone-number-id-40c3d924c8194a849090f595e86bd265"
        }
    }
}

development {
    aws {
        social.messaging {
            region = "us-east-1"
            region = ${?AWS_SOCIAL_MESSAGING_REGION}
            phone.id = "phone-number-id-40c3d924c8194a849090f595e86bd265"
            phone.id = ${?AWS_SOCIAL_MESSAGING_PHONE_ID}
        }
    }
}

production {
    aws {
        social.messaging {
            region = "us-east-1"
            region = ${?AWS_SOCIAL_MESSAGING_REGION}
            phone.id = "phone-number-id-40c3d924c8194a849090f595e86bd265"
            phone.id = ${?AWS_SOCIAL_MESSAGING_PHONE_ID}
        }
    }
}
