package br.com.alice.healthcondition.services.internal

import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.CreateChatRequest
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.MonitoringTriggerConfiguration
import br.com.alice.data.layer.models.MonitoringTriggerFeatureType
import br.com.alice.data.layer.models.MonitoringTriggerRecord
import br.com.alice.data.layer.models.MonitoringTriggerRecordActionExtraInfo
import br.com.alice.data.layer.models.MonitoringTriggerRecordActionType
import br.com.alice.data.layer.models.MonitoringTriggerRecordExtraData
import br.com.alice.data.layer.models.MonitoringTriggerRecordStatus.CONCLUDED
import br.com.alice.data.layer.models.MonitoringTriggerRecordStatus.PROCESSING
import br.com.alice.data.layer.services.NaiveTextualDeIdentificationService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordActionService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordService
import br.com.alice.person.client.PersonService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map

class CreateChannelMonitoringTriggerService(
    private val monitoringTriggerRecordService: MonitoringTriggerRecordService,
    private val channelService: ChannelService,
    private val monitoringTriggerRecordActionService: MonitoringTriggerRecordActionService,
    private val personService: PersonService,
    private val healthProfessionalService: HealthProfessionalService
) : Spannable {

    suspend fun create(triggerRecord: MonitoringTriggerRecord, triggerConfiguration: MonitoringTriggerConfiguration) =
        createChannel(triggerRecord, triggerConfiguration)
            .then { channel -> addStaffs(triggerRecord, channel) }
            .flatMap { channel -> updateTriggerRecord(triggerRecord, triggerConfiguration, channel) }
            .then { createMonitoringAction(it) }

    private suspend fun createChannel(
        triggerRecord: MonitoringTriggerRecord,
        triggerConfiguration: MonitoringTriggerConfiguration
    ) = channelService.addChatV2(
        CreateChatRequest(
            personId = triggerRecord.personId,
            origin = MONITORING_CHANNEL_ORIGIN,
            chatName = MONITORING_CHANNEL_NAME,
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.LONGITUDINAL,
        )
    ).flatMap { channelDocument ->
        channelService.sendTextMessage(
            chat = channelDocument,
            content = getChannelMessage(triggerConfiguration, triggerRecord),
            sendFromAlice = true
        ).map { channelDocument }
    }

    private suspend fun addStaffs(
        triggerRecord: MonitoringTriggerRecord,
        channel: ChannelDocument
    ) {
        triggerRecord.staffIds.map { it.toString() }
            .forEach { staffId ->
                channelService.addStaff(
                    channelId = channel.id(),
                    staffId = staffId,
                    requesterStaffId = staffId,
                )
            }
    }

    private suspend fun updateTriggerRecord(
        triggerRecord: MonitoringTriggerRecord,
        configuration: MonitoringTriggerConfiguration,
        channel: ChannelDocument
    ): Result<MonitoringTriggerRecord, Throwable> {
        val channelId = channel.id()

        val updated = triggerRecord.copy(
            status = if (isFinished(triggerRecord, configuration)) CONCLUDED else PROCESSING,
            currentStep = configuration.getNextStep(),
            extraData = triggerRecord.getExtraData(channelId)
        )

        return monitoringTriggerRecordService.update(updated)
    }

    private suspend fun createMonitoringAction(triggerRecord: MonitoringTriggerRecord) =
        monitoringTriggerRecordActionService.create(
            triggerRecord = triggerRecord,
            action = MonitoringTriggerRecordActionType.CHANNEL_CREATED,
            extraInfo = triggerRecord.extraData?.channelId?.let { channelId ->
                MonitoringTriggerRecordActionExtraInfo(channelId = channelId)
            }
        )

    private suspend fun getChannelMessage(
        configuration: MonitoringTriggerConfiguration,
        triggerRecord: MonitoringTriggerRecord
    ): String = span("getChannelMessage") { span ->
        span.setAttribute("trigger_record_id", triggerRecord.id.toString())
        span.setAttribute("configuration_id", configuration.id.toString())
        span.setAttribute("staff_ids", triggerRecord.staffIds.joinToString(","))
        span.setAttribute("has_message", configuration.additionalProperties?.channelInitialMessage != null)

        configuration.additionalProperties?.channelInitialMessage?.let { message ->
            personService.get(triggerRecord.personId)
                .flatMapPair {
                    if (triggerRecord.staffIds.isEmpty())
                        IllegalArgumentException("staff_ids_is_empty").failure()
                    else
                        healthProfessionalService.findByStaffId(triggerRecord.staffIds.first())
                }.map { (professional, person) ->
                    NaiveTextualDeIdentificationService.identify(message, person.toPersonPII())
                        .replace("@staffName", professional.name)
                }
                .thenError { span.setAttribute("error_message", it.message.orEmpty()) }
                .getOrElse { message }
        }.orEmpty()
    }

    private fun MonitoringTriggerConfiguration.getNextStep(): MonitoringTriggerFeatureType {
        val features = this.features.sortedBy { it.order }
        val currentStepIndex = features.indexOfFirst { it.feature == MonitoringTriggerFeatureType.CREATE_CHANNEL }
        val nextStepIndex = currentStepIndex + 1

        return takeIf { nextStepIndex < this.features.size }
            ?.let { features[nextStepIndex].feature }
            ?: features[currentStepIndex].feature
    }

    private fun isFinished(record: MonitoringTriggerRecord, configuration: MonitoringTriggerConfiguration): Boolean {
        val currentStep = record.currentStep
        val nextStep = configuration.getNextStep()

        return currentStep == nextStep
    }

    private fun MonitoringTriggerRecord.getExtraData(channelId: String) =
        this.extraData?.copy(channelId = channelId)
            ?: MonitoringTriggerRecordExtraData(channelId = channelId)

    private companion object {
        const val MONITORING_CHANNEL_NAME = "Monitoramento de Saúde"
        const val MONITORING_CHANNEL_ORIGIN = "monitoring"
    }
}
