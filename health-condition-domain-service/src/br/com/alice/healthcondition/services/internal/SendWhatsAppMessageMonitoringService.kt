package br.com.alice.healthcondition.services.internal

import br.com.alice.common.extensions.thenLogOnFailure
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.communication.whatsapp.aws.WhatsAppSocialMessagingService
import br.com.alice.communication.whatsapp.aws.transport.SocialMessagingRequestTransport
import br.com.alice.communication.whatsapp.transport.WhatsAppMessage
import br.com.alice.communication.whatsapp.transport.message.template.WhatsAppMessageTemplate
import br.com.alice.communication.whatsapp.transport.message.template.WhatsAppMessageTemplateComponent
import br.com.alice.communication.whatsapp.transport.message.template.WhatsAppMessageTemplateComponentParameter
import br.com.alice.data.layer.models.MonitoringTriggerConfiguration
import br.com.alice.data.layer.models.MonitoringTriggerRecord
import br.com.alice.data.layer.models.MonitoringTriggerWhatsAppTemplate
import br.com.alice.healthcondition.ServiceConfig
import br.com.alice.healthcondition.services.whatsapp.WhatsAppParameterLoader
import br.com.alice.healthcondition.services.whatsapp.model.AvailableEntity
import br.com.alice.healthcondition.services.whatsapp.model.EntityParameter
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class SendWhatsAppMessageMonitoringService(
    private val personService: PersonService,
    private val whatsAppParameterLoader: WhatsAppParameterLoader,
    private val whatsAppMessageService: WhatsAppSocialMessagingService,
) : Spannable {

    suspend fun sendMessage(triggerRecord: MonitoringTriggerRecord, triggerCfg: MonitoringTriggerConfiguration) =
        span("sendMessage") { span ->
            val whatsAppTemplate = triggerCfg.additionalProperties?.whatsAppTemplate

            span.setAttribute("monitoring_trigger_record_id", triggerRecord.id)
            span.setAttribute("trigger_id", triggerRecord.triggerId)
            span.setAttribute("person_id", triggerRecord.personId)
            span.setAttribute("whatsapp_template", whatsAppTemplate)

            val result = whatsAppTemplate?.let {
                triggerRecord.loadParameters(whatsAppTemplate)
                    ?.flatMap { parameters -> triggerRecord.buildSocialMessagingRequest(whatsAppTemplate, parameters) }
                    ?.flatMap { whatsAppMessageService.sendMessage(it) }
                    ?.flatMap { true.success() }
            } ?: let { false.success() }

            result.recordResult(span)
        }


    private suspend fun MonitoringTriggerRecord.buildSocialMessagingRequest(
        whatsAppTemplate: MonitoringTriggerWhatsAppTemplate,
        parameters: List<String>,
    ): Result<SocialMessagingRequestTransport, Throwable> {

        val channelId = extraData?.channelId
            ?: return RuntimeException("MemberHealthMonitoring with ID $id does not have a channelId.").failure()

        return personService.get(personId)
            .map { person ->
                val personPhoneNumber = person.phoneNumber
                    ?: return RuntimeException("Person with ID $personId does not have a phone number.").failure()

                SocialMessagingRequestTransport(
                    fromPhoneNumberId = ServiceConfig.Aws.SocialMessaging.phoneId,
                    whatsAppMessageRequest = WhatsAppMessage(
                        to = personPhoneNumber,
                        type = WhatsAppMessage.MessageType.TEMPLATE,
                        template = WhatsAppMessageTemplate(
                            name = whatsAppTemplate.name,
                            components = buildWhatsAppTemplateComponent(parameters, channelId),
                        )
                    ),
                )
            }
            .thenLogOnFailure("person_id" to personId) { "Error retrieving person to send WhatsApp message." }
    }

    private suspend fun MonitoringTriggerRecord.loadParameters(whatsAppTemplate: MonitoringTriggerWhatsAppTemplate) =
        whatsAppTemplate.variables
            ?.mapNotNull { variable ->
                when (variable.domain.uppercase()) {
                    "MEMBER" -> EntityParameter(
                        domain = AvailableEntity.PERSON,
                        attribute = variable.attribute,
                        id = personId.toString()
                    )

                    "STAFF" -> EntityParameter(
                        domain = AvailableEntity.STAFF,
                        attribute = variable.attribute,
                        id = staffIds.first().toString()
                    )

                    else -> {
                        logger.info("Unknown variable from: ${variable.domain} for attribute: ${variable.attribute}")
                        null
                    }
                }
            }?.let { whatsAppParameterLoader.retrieveParameters(it) }

    private fun buildWhatsAppTemplateComponent(parameters: List<String>, channelId: String) = listOf(
        WhatsAppMessageTemplateComponent(
            type = WhatsAppMessageTemplateComponent.Type.BODY,
            parameters = parameters.map { param ->
                WhatsAppMessageTemplateComponentParameter(
                    type = WhatsAppMessageTemplateComponentParameter.Type.TEXT,
                    text = param
                )
            }
        ),
        WhatsAppMessageTemplateComponent(
            type = WhatsAppMessageTemplateComponent.Type.BUTTON,
            subType = WhatsAppMessageTemplateComponent.SubType.URL,
            index = "0",
            parameters = listOf(
                WhatsAppMessageTemplateComponentParameter(
                    type = WhatsAppMessageTemplateComponentParameter.Type.TEXT,
                    text = channelId
                )
            )
        )
    )
}
