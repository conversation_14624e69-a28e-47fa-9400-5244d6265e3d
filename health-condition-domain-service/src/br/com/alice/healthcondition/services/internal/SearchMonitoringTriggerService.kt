package br.com.alice.healthcondition.services.internal

import br.com.alice.client.datagateway.DataGatewayClient
import br.com.alice.client.datagateway.DataGatewayFilter
import br.com.alice.client.datagateway.DataGatewayFilterOperator
import br.com.alice.client.datagateway.DataGatewayFilterParams
import br.com.alice.client.datagateway.DataGatewayResponse
import br.com.alice.client.datagateway.DataGatewaySort
import br.com.alice.client.datagateway.DataGatewaySortOrder
import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MonitoringTriggerRecord
import br.com.alice.data.layer.models.MonitoringTriggerRecordActionType
import br.com.alice.data.layer.models.MonitoringTriggerRecordStatus
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthcondition.event.ProcessMonitoringTriggerEvent
import br.com.alice.healthcondition.services.MonitoringTriggerConfigurationService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordActionService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordService
import br.com.alice.staff.client.HealthProfessionalFilters
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDate
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class SearchMonitoringTriggerService(
    private val dataGatewayClient: DataGatewayClient,
    private val personInternalReferenceService: PersonInternalReferenceService,
    private val kafkaProducerService: KafkaProducerService,
    private val monitoringTriggerConfigurationService: MonitoringTriggerConfigurationService,
    private val monitoringTriggerRecordService: MonitoringTriggerRecordService,
    private val healthProfessionalService: HealthProfessionalService,
    private val monitoringTriggerRecordActionService: MonitoringTriggerRecordActionService
) : Spannable {

    suspend fun process(triggerName: String, pageNumber: Long, pageSize: Long) =
        span("process") { span ->
            span.setAttribute("trigger_name", triggerName)
            span.setAttribute("page_number", pageNumber)
            span.setAttribute("page_size", pageSize)

            getPage(triggerName, pageNumber, pageSize)
                .then { response -> publishEventForNextPage(response, triggerName, pageSize) }
                .flatMap { (data) -> processPage(data, triggerName) }
        }

    private suspend fun getPage(triggerName: String, pageNumber: Long, pageSize: Long) =
        span("getPage") { span ->
            val triggerRefDay = getTriggerRefDayInitialFilterValue()

            span.setAttribute("trigger_name", triggerName)
            span.setAttribute("page_number", pageNumber)
            span.setAttribute("page_size", pageSize)
            span.setAttribute("trigger_ref_day", triggerRefDay)

            dataGatewayClient.search(
                tableName = DEFAULT_MONITORING_TABLE_NAME,
                params = DataGatewayFilterParams(
                    filters = listOfNotNull(
                        isTriggerActiveFilter,
                        triggerStatusFilter,
                        triggerRefDay.toTriggerRefDayFilter(),
                        triggerName.toTriggerNameFilter(),
                        getAllowedMemberInternalCodesFilter()
                    ),
                    sort = defaultSort,
                    pageSize = pageSize,
                    pageNumber = pageNumber
                ),
                response = OutputActiveTriggerResponse::class
            ).recordResult(span)
        }

    private suspend fun publishEventForNextPage(
        response: DataGatewayResponse<OutputActiveTriggerResponse>,
        triggerName: String,
        pageSize: Long
    ) = span("publishEventForNextPage") { span ->

        span.setAttribute("data_is_not_empty", response.data.isNotEmpty())
        span.setAttribute("has_next_page", response.meta.hasNextPage)
        span.setAttribute("current_page", response.meta.page)
        span.setAttribute("page_size", response.meta.pageSize)

        val shouldProcessNextPage = response.data.isNotEmpty() && response.meta.hasNextPage
        span.setAttribute("should_process_next_page", shouldProcessNextPage)

        if (shouldProcessNextPage)
            kafkaProducerService.produce(
                ProcessMonitoringTriggerEvent(
                    triggerName = triggerName,
                    pageNumber = response.meta.page + 1,
                    pageSize = pageSize
                )
            )
    }

    private suspend fun processPage(data: List<OutputActiveTriggerResponse>, triggerName: String) =
        span("processPage") { span ->
            span.setAttribute("data", data.size)

            filterExistentMonitoredTriggers(data)
                .flatMap { validTriggers ->
                    if (validTriggers.isEmpty())
                        emptyList<MonitoringTriggerRecord>().success()
                    else
                        saveTriggers(validTriggers, triggerName)
                }
                .recordResult(span)
        }

    private suspend fun filterExistentMonitoredTriggers(data: List<OutputActiveTriggerResponse>) =
        data.map { it.id }.distinct()
            .let { externalIds ->
                monitoringTriggerRecordService.findAllByExternalTriggerIds(externalIds)
                    .map { saved ->
                        val alreadySavedIds = saved.map { it.externalTriggerId }
                        data.filterNot { alreadySavedIds.contains(it.id) }
                    }
            }

    private suspend fun saveTriggers(data: List<OutputActiveTriggerResponse>, triggerName: String) =
        span("saveTriggers") { span ->
            coResultOf<List<MonitoringTriggerRecord>, Throwable> {
                coroutineScope {
                    val triggerConfigurationDef = getTriggerConfigurationDef(triggerName)
                    val internalReferencesDef = getPersonInternalReference(data)
                    val healthProfessionalsDef = getHealthProfessionalsDef(data)

                    val internalReferences = internalReferencesDef.await()
                    val triggerConfiguration = triggerConfigurationDef.await()
                    val healthProfessionals = healthProfessionalsDef.await()

                    span.setAttribute("data", data.size)
                    span.setAttribute("internal_references", internalReferences.size)
                    span.setAttribute("trigger_id", triggerConfiguration.id)
                    span.setAttribute("health_professionals", healthProfessionals.size)

                    val firstStep = triggerConfiguration.features.minByOrNull { it.order }
                        ?: throw IllegalArgumentException("trigger configuration has no features")

                    data.mapNotNull { activeTrigger ->
                        MonitoringTriggerRecord(
                            personId = internalReferences[activeTrigger.memberInternalCode]?.personId
                                ?: return@mapNotNull null,
                            triggerId = triggerConfiguration.id,
                            staffIds = listOfNotNull(healthProfessionals[activeTrigger.responsibleNurse]?.staffId),
                            externalTriggerId = activeTrigger.id,
                            identifiedAt = activeTrigger.triggerRefDay.atStartOfDay(),
                            status = MonitoringTriggerRecordStatus.CREATED,
                            currentStep = firstStep.feature,
                        )
                    }
                }
            }
                .flatMap { monitoringTriggerRecordService.addAll(it) }
                .then { monitoringList ->
                    monitoringTriggerRecordActionService.createAll(
                        records = monitoringList,
                        action = MonitoringTriggerRecordActionType.NEW_TRIGGER_IDENTIFIED
                    )
                }
                .recordResult(span)
        }

    private fun CoroutineScope.getHealthProfessionalsDef(data: List<OutputActiveTriggerResponse>) =
        async {
            data.mapNotNull { it.responsibleNurse }.distinct()
                .let { emails ->
                    healthProfessionalService.findBy(HealthProfessionalFilters(emails = emails))
                        .map { professionals -> professionals.associateBy { it.email } }
                        .get()
                }
        }

    private fun CoroutineScope.getTriggerConfigurationDef(triggerName: String) = async {
        monitoringTriggerConfigurationService.getByName(triggerName).get()
    }

    private fun CoroutineScope.getPersonInternalReference(data: List<OutputActiveTriggerResponse>) = async {
        data.map { it.memberInternalCode }
            .distinct()
            .let { codes ->
                personInternalReferenceService.getByInternalCodes(codes)
                    .map { references -> references.associateBy { it.internalCode } }
                    .get()
            }
    }

    private fun String.toTriggerNameFilter() =
        DataGatewayFilter(
            column = "trigger_name",
            operator = DataGatewayFilterOperator.EQUALS,
            value = this
        )

    private fun String.toTriggerRefDayFilter() =
        DataGatewayFilter(
            column = "trigger_ref_day",
            operator = DataGatewayFilterOperator.GREATER_THAN,
            value = this
        )

    private fun getTriggerRefDayInitialFilterValue() = FeatureService.get(
        namespace = FeatureNamespace.HEALTH_CONDITION,
        key = "monitoring_trigger_ref_day_initial_filter",
        defaultValue = "2025-06-01"
    )

    private fun getAllowedMemberInternalCodesFilter() = FeatureService.getList(
        namespace = FeatureNamespace.HEALTH_CONDITION,
        key = "allowed_monitoring_triggers_member_internal_codes",
        defaultValue = emptyList<String>()
    ).takeIf { it.isNotEmpty() }?.let {
        DataGatewayFilter(
            column = "member_internal_code",
            operator = DataGatewayFilterOperator.IN,
            value = it
        )
    }

    private companion object {
        const val DEFAULT_MONITORING_TABLE_NAME = "output_active_triggers"
        val isTriggerActiveFilter = DataGatewayFilter(
            column = "is_trigger_active",
            operator = DataGatewayFilterOperator.EQUALS,
            value = true
        )
        val triggerStatusFilter = DataGatewayFilter(
            column = "trigger_status",
            operator = DataGatewayFilterOperator.IN,
            value = listOf("Não analisado", "Reavaliação pendente")
        )
        val defaultSort = listOf(
            DataGatewaySort(
                column = "trigger_ref_day",
                order = DataGatewaySortOrder.ASC
            )
        )
    }
}

data class OutputActiveTriggerResponse(
    val id: String,
    val originTable: String,
    val triggerId: String,
    val triggerRefDay: LocalDate,
    val triggerOriginIdType: String,
    val triggerOriginId: String,
    val triggerOriginTable: String,
    val memberInternalCode: String,
    val triggerName: String,
    val triggerPriority: Int,
    val triggerDescription: String,
    val isTriggerActive: Boolean,
    val objectiveCodes: List<String>?,
    val responsibleNurse: String?,
    val triggerStatus: String,
    val lastInteractionCreatedAt: String?,
    val interactionUser: String?,
    val triggerNextScheduleDate: String?,
    val triggerDiscardReason: String?,
    val triggerValueDescription: String,
    val triggerValue: String
)
