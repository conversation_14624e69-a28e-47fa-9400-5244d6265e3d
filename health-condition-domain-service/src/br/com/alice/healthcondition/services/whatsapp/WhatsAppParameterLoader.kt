package br.com.alice.healthcondition.services.whatsapp

import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.thenLogOnFailure
import br.com.alice.healthcondition.services.whatsapp.model.EntityParameter

class WhatsAppParameterLoader(
    private val entitySearcherSelector: EntitySearcherSelector,
    private val entityAttributeResolverSelector: EntityAttributeResolverSelector,
) {

    suspend fun retrieveParameters(entityParameters: List<EntityParameter>) = coResultOf<List<String>, Throwable> {
        val idsWithEntities = entityParameters.map { it.id to entitySearcherSelector.getSearcher(it.domain) }
            .distinctBy { it.first }
            .map { (id, searcher) -> id to searcher.getById(id).get() }

        entityParameters.map { ep ->
            val entity = idsWithEntities.first { it.first == ep.id }.second
                ?: error("Entity not found for ID: ${ep.id}")

            entityAttributeResolverSelector.resolveAttribute(ep, entity).get()
        }
    }.thenLogOnFailure { "[ParameterLoader] Failed to load parameters. Aborting process." }
}
