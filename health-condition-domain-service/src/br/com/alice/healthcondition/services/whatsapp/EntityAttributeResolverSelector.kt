package br.com.alice.healthcondition.services.whatsapp

import br.com.alice.healthcondition.services.whatsapp.model.EntityParameter
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success

class EntityAttributeResolverSelector(
    private val attributeResolvers: Map<String, EntityAttributeResolver<out Any>>,
) {

    fun <M : Any> resolveAttribute(entityParameter: EntityParameter, entity: M): Result<String, Throwable> {
        val resolver = attributeResolvers[entityParameter.key]
            ?: return IllegalArgumentException("No resolver found for attribute: ${entityParameter.key}").failure()

        @Suppress("UNCHECKED_CAST")
        return (resolver as EntityAttributeResolver<M>).resolveAttribute(entity).success()
    }
}
