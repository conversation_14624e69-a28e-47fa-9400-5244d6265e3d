package br.com.alice.healthcondition.services

import br.com.alice.data.layer.models.MonitoringTriggerConfiguration
import br.com.alice.data.layer.services.MonitoringTriggerConfigurationDataService
import java.util.UUID

class MonitoringTriggerConfigurationService(
    private val monitoringTriggerConfigurationDataService: MonitoringTriggerConfigurationDataService
) {

    suspend fun create(configuration: MonitoringTriggerConfiguration) =
        monitoringTriggerConfigurationDataService.add(configuration)

    suspend fun update(configuration: MonitoringTriggerConfiguration) =
        monitoringTriggerConfigurationDataService.update(configuration)

    suspend fun getByName(name: String) =
        monitoringTriggerConfigurationDataService.findOne {
            where { this.name.eq(name) }
        }

    suspend fun get(id: UUID) =
        monitoringTriggerConfigurationDataService.get(id)

}
