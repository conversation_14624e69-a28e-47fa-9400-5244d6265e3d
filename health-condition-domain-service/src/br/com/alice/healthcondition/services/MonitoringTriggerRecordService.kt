package br.com.alice.healthcondition.services

import br.com.alice.data.layer.models.MonitoringTriggerRecord
import br.com.alice.data.layer.services.MonitoringTriggerRecordDataService
import java.util.UUID

class MonitoringTriggerRecordService(
    private val monitoringTriggerRecordDataService: MonitoringTriggerRecordDataService
) {

    suspend fun findById(id: UUID) =
        monitoringTriggerRecordDataService.get(id)

    suspend fun findAllByExternalTriggerIds(externalTriggerIds: List<String>) =
        monitoringTriggerRecordDataService.find {
            where { this.externalTriggerId.inList(externalTriggerIds) }
        }

    suspend fun findByChannelId(channelId: String) =
        monitoringTriggerRecordDataService.findOne {
            where { this.channelId.eq(channelId) }
        }

    suspend fun addAll(data: List<MonitoringTriggerRecord>) =
        monitoringTriggerRecordDataService.addList(data)

    suspend fun update(data: MonitoringTriggerRecord) =
        monitoringTriggerRecordDataService.update(data)
}
