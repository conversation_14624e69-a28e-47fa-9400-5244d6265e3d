package br.com.alice.healthcondition.consumers

import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.notifier.ChannelArchivedByInactivityEvent
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.observability.recordResult
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.MonitoringTriggerRecordActionExtraInfo
import br.com.alice.data.layer.models.MonitoringTriggerRecordActionType
import br.com.alice.healthcondition.services.MonitoringTriggerRecordActionService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success

class ArchivedChannelMonitoringTriggerConsumer(
    private val monitoringTriggerRecordService: MonitoringTriggerRecordService,
    private val monitoringTriggerRecordActionService: MonitoringTriggerRecordActionService
) : Consumer() {

    suspend fun handleOnArchivedChannel(event: ChannelArchivedByInactivityEvent) =
        span("handleOnArchivedChannel") { span ->
            withSubscribersEnvironment {
                val channel = event.payload.channel

                span.setAttribute("channel_id", channel.id())
                span.setAttribute("channel_kind", channel.kind.toString())
                span.setAttribute("channel_category", channel.category.toString())
                span.setAttribute("channel_sub_category", channel.subCategory.toString())

                if (channel.isValid())
                    monitoringTriggerRecordService.findByChannelId(channel.id())
                        .then { span.setAttribute("monitoring_trigger_record_id", it.id.toString()) }
                        .flatMap { record ->
                            monitoringTriggerRecordActionService.create(
                                triggerRecord = record,
                                action = MonitoringTriggerRecordActionType.CHANNEL_ARCHIVED_BY_INACTIVITY,
                                extraInfo = MonitoringTriggerRecordActionExtraInfo(channelId = channel.id())
                            )
                        }
                        .coFoldNotFound { false.success() }
                else
                    false.success()
            }.recordResult(span)
        }

    private fun ChannelDocument.isValid() =
        kind == ChannelKind.CHAT
                && category == ChannelCategory.ASSISTANCE
                && subCategory == ChannelSubCategory.LONGITUDINAL

}
