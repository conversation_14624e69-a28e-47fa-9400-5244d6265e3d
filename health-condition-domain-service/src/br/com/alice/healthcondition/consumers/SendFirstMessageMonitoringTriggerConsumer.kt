package br.com.alice.healthcondition.consumers

import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.MonitoringTriggerConfiguration
import br.com.alice.data.layer.models.MonitoringTriggerFeatureType
import br.com.alice.data.layer.models.MonitoringTriggerRecord
import br.com.alice.data.layer.models.MonitoringTriggerRecordAction
import br.com.alice.data.layer.models.MonitoringTriggerRecordActionType
import br.com.alice.data.layer.models.MonitoringTriggerRecordStatus.CONCLUDED
import br.com.alice.data.layer.models.MonitoringTriggerRecordStatus.PROCESSING
import br.com.alice.healthcondition.event.MonitoringTriggerRecordActionCreatedEvent
import br.com.alice.healthcondition.services.MonitoringTriggerConfigurationService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordActionService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordService
import br.com.alice.healthcondition.services.internal.SendWhatsAppMessageMonitoringService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class SendFirstMessageMonitoringTriggerConsumer(
    private val monitoringTriggerRecordService: MonitoringTriggerRecordService,
    private val monitoringTriggerConfigurationService: MonitoringTriggerConfigurationService,
    private val monitoringTriggerRecordActionService: MonitoringTriggerRecordActionService,
    private val sendWhatsAppMessageMonitoringService: SendWhatsAppMessageMonitoringService,
) : Consumer() {

    suspend fun handleMonitoringTriggerRecordActionCreated(event: MonitoringTriggerRecordActionCreatedEvent) =
        span("handleMonitoringTriggerRecordActionCreated") { span ->
            val monitoringAction = event.payload.monitoringAction
            span.setAttribute("monitoring_trigger_record_action_id", monitoringAction.id)
            span.setAttribute("monitoring_trigger_record_id", monitoringAction.monitoringTriggerRecordId)
            span.setAttribute("action", monitoringAction.action)

            withSubscribersEnvironment {
                if (event.isValid()) {
                    process(monitoringAction)
                    true.success()
                } else false.success()
            }
        }

    private suspend fun process(monitoringTriggerRecordAction: MonitoringTriggerRecordAction) =
        span("process") { span ->
            span.setAttribute("monitoring_trigger_record_action_id", monitoringTriggerRecordAction.id)
            span.setAttribute("monitoring_trigger_record_id", monitoringTriggerRecordAction.monitoringTriggerRecordId)
            span.setAttribute("action", monitoringTriggerRecordAction.action)
            span.setAttribute("trigger_id", monitoringTriggerRecordAction.triggerId)
            span.setAttribute("person_id", monitoringTriggerRecordAction.personId)

            monitoringTriggerRecordAction.findMonitoring()
                .flatMapPair { monitoringTriggerConfigurationService.get(monitoringTriggerRecordAction.triggerId) }
                .flatMap { (triggerConfiguration, triggerRecord) ->
                    val shouldProcess = shouldProcess(triggerRecord, triggerConfiguration)
                    span.setAttribute("should_process", shouldProcess)

                    if (shouldProcess) {
                        sendWhatsAppMessageMonitoringService.sendMessage(triggerRecord, triggerConfiguration)
                        finalize(triggerRecord, triggerConfiguration)
                        true.success()
                    } else false.success()
                }
        }

    private suspend fun finalize(
        triggerRecord: MonitoringTriggerRecord,
        triggerConfiguration: MonitoringTriggerConfiguration,
    ) {
        updateTriggerRecord(triggerRecord, triggerConfiguration)
            .map { createMonitoringAction(it) }
    }

    private fun MonitoringTriggerRecordActionCreatedEvent.isValid() =
        payload.monitoringAction.action == MonitoringTriggerRecordActionType.WHATSAPP_MESSAGE_SENT


    private suspend fun MonitoringTriggerRecordAction.findMonitoring() = monitoringTriggerRecordService.findById(id)

    private suspend fun shouldProcess(
        triggerRecord: MonitoringTriggerRecord,
        configuration: MonitoringTriggerConfiguration,
    ) = span("should_process") { span ->
        span.setAttribute("current_step", triggerRecord.currentStep)
        span.setAttribute("features", configuration.features.joinToString { it.feature.toString() })

        triggerRecord.currentStep == MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE &&
                configuration.features.any { it.feature == MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE }
    }

    private suspend fun updateTriggerRecord(
        triggerRecord: MonitoringTriggerRecord,
        configuration: MonitoringTriggerConfiguration,
    ): Result<MonitoringTriggerRecord, Throwable> {

        val updated = triggerRecord.copy(
            status = if (isFinished(triggerRecord, configuration)) CONCLUDED else PROCESSING,
            currentStep = configuration.getNextStep(),
        )

        return monitoringTriggerRecordService.update(updated)
    }

    private fun isFinished(
        record: MonitoringTriggerRecord,
        configuration: MonitoringTriggerConfiguration,
    ): Boolean {
        val currentStep = record.currentStep
        val nextStep = configuration.getNextStep()

        return currentStep == nextStep
    }

    private fun MonitoringTriggerConfiguration.getNextStep(): MonitoringTriggerFeatureType {
        val features = this.features.sortedBy { it.order }

        val currentStepIndex =
            features.indexOfFirst { it.feature == MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE }

        val nextStepIndex = currentStepIndex + 1

        return takeIf { nextStepIndex < this.features.size }
            ?.let { features[nextStepIndex].feature }
            ?: features[currentStepIndex].feature
    }

    private suspend fun createMonitoringAction(triggerRecord: MonitoringTriggerRecord) =
        monitoringTriggerRecordActionService.create(
            triggerRecord = triggerRecord,
            action = MonitoringTriggerRecordActionType.WHATSAPP_MESSAGE_SENT,
        )
}
