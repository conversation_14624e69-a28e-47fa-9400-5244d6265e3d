package br.com.alice.healthcondition

import br.com.alice.common.core.BaseConfig
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig


object ServiceConfig: BaseConfig(config = HoconApplicationConfig(ConfigFactory.load("application.conf"))) {

    object Aws {
        object SocialMessaging {
            val region = config("aws.social.messaging.region")
            val phoneId = config("aws.social.messaging.phone.id")
        }
    }
}
