package br.com.alice.healthcondition.controllers

import br.com.alice.common.Response
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.foldResponse
import br.com.alice.data.layer.models.MonitoringTriggerAdditionalProperties
import br.com.alice.data.layer.models.MonitoringTriggerConfiguration
import br.com.alice.data.layer.models.MonitoringTriggerFeature
import br.com.alice.healthcondition.services.MonitoringTriggerConfigurationService
import com.github.kittinunf.result.flatMap

class MonitoringTriggerConfigurationController(
    private val monitoringTriggerConfigurationService: MonitoringTriggerConfigurationService,
) : HealthConditionController() {

    suspend fun create(request: MonitoringTriggerConfigurationRequest): Response =
        withHealthConditionEnvironment {
            val configuration = MonitoringTriggerConfiguration(
                name = request.name,
                features = request.features,
                additionalProperties = request.additionalProperties
            )

            monitoringTriggerConfigurationService.create(configuration)
                .foldResponse()
        }

    suspend fun get(id: String): Response =
        monitoringTriggerConfigurationService.get(id.toUUID()).foldResponse()

    suspend fun update(id: String, request: MonitoringTriggerConfigurationRequest): Response =
        withHealthConditionEnvironment {
            monitoringTriggerConfigurationService.get(id.toUUID())
                .flatMap { saved ->
                    monitoringTriggerConfigurationService.update(
                        saved.copy(
                            name = request.name,
                            features = request.features,
                            additionalProperties = request.additionalProperties
                        )
                    )
                }.foldResponse()
        }
}

data class MonitoringTriggerConfigurationRequest(
    val name: String,
    val features: List<MonitoringTriggerFeature> = emptyList(),
    val additionalProperties: MonitoringTriggerAdditionalProperties? = null
)
