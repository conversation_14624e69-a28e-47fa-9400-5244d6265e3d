package br.com.alice.healthcondition.routes

import br.com.alice.appointment.event.AppointmentCaseRecordRequestedEvent
import br.com.alice.appointment.event.AppointmentCompletedEvent
import br.com.alice.channel.event.MergedChannelDemandEvent
import br.com.alice.channel.event.NewChannelDemandEvent
import br.com.alice.channel.notifier.ChannelArchivedByInactivityEvent
import br.com.alice.channel.notifier.ChannelUpsertedEvent
import br.com.alice.clinicalaccount.event.CreateCaseRecordFromDiseasesEvent
import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.internals.ConsumerJob
import br.com.alice.ehr.event.TertiaryIntentionTouchPointCreatedEvent
import br.com.alice.ehr.event.TertiaryIntentionTouchPointUpdatedEvent
import br.com.alice.healthcondition.consumers.AppointmentConsumer
import br.com.alice.healthcondition.consumers.AppointmentScheduleConsumer
import br.com.alice.healthcondition.consumers.ArchivedChannelMonitoringTriggerConsumer
import br.com.alice.healthcondition.consumers.BUDConsumer
import br.com.alice.healthcondition.consumers.BackfillConsumer
import br.com.alice.healthcondition.consumers.CaseRecordConsumer
import br.com.alice.healthcondition.consumers.ChannelConsumer
import br.com.alice.healthcondition.consumers.CounterReferralConsumer
import br.com.alice.healthcondition.consumers.CreateChannelMonitoringTriggerConsumer
import br.com.alice.healthcondition.consumers.HealthDeclarationFinishedConsumer
import br.com.alice.healthcondition.consumers.LowRiskMemberStratificationConsumer
import br.com.alice.healthcondition.consumers.MonitoringTriggerConsumer
import br.com.alice.healthcondition.consumers.OutcomeRecommendationActionEventConsumer
import br.com.alice.healthcondition.consumers.PersonHealthEventConsumer
import br.com.alice.healthcondition.consumers.SendFirstMessageMonitoringTriggerConsumer
import br.com.alice.healthcondition.consumers.TertiaryIntentionTouchPointConsumer
import br.com.alice.healthcondition.event.CaseRecordCreatedEvent
import br.com.alice.healthcondition.event.MonitoringTriggerRecordActionCreatedEvent
import br.com.alice.healthcondition.event.ProcessMonitoringTriggerEvent
import br.com.alice.healthlogic.event.BUDActionEvent
import br.com.alice.healthlogic.event.OutcomeRecommendationActionEvent
import br.com.alice.membership.model.events.HealthDeclarationFinishedEvent
import br.com.alice.person.model.events.LowRiskMemberStratificationEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import br.com.alice.secondary.attention.events.CounterReferralCreatedEvent
import br.com.alice.wanda.event.PersonHealthEventCreatedEvent

fun ConsumerJob.Configuration.kafkaRoutes() {

    val healthDeclarationFinishedConsumer by inject<HealthDeclarationFinishedConsumer>()
    consume(
        "health-declaration-create-case-records",
        HealthDeclarationFinishedEvent.name,
        healthDeclarationFinishedConsumer::createCaseRecords
    )

    val appointmentScheduleConsumer by inject<AppointmentScheduleConsumer>()
    consume(
        "health-declaration-add-case-responsible",
        AppointmentScheduleCreatedEvent.name,
        appointmentScheduleConsumer::handleHealthDeclarationSchedule
    )

    val caseRecordConsumer by inject<CaseRecordConsumer>()
    consume(
        "case-record-create-person-case",
        CaseRecordCreatedEvent.name,
        caseRecordConsumer::createPersonCase
    )

    val lowRiskMemberStratificationConsumer by inject<LowRiskMemberStratificationConsumer>()
    consume(
        handlerName = "create-demands-with-onboarding-questions",
        topicName = LowRiskMemberStratificationEvent.name,
        lowRiskMemberStratificationConsumer::createCaseRecordsWithOnboardingQuestions
    )

    val personHealthEventConsumer by inject<PersonHealthEventConsumer>()
    consume(
        "person-health-event-change-case-responsible",
        PersonHealthEventCreatedEvent.name,
        personHealthEventConsumer::changeCaseResponsible
    )

    val appointmentConsumer by inject<AppointmentConsumer>()
    consume(
        "create-case-records-by-appointment-completed",
        AppointmentCompletedEvent.name,
        appointmentConsumer::createCaseRecordsWhenCompleted
    )
    consume(
        "create-case-records-by-appointment-case-record-requested",
        AppointmentCaseRecordRequestedEvent.name,
        appointmentConsumer::createCaseRecordsWhenRequested
    )

    val channelConsumer by inject<ChannelConsumer>()
    consume(
        "add-channel-in-case-record",
        NewChannelDemandEvent.name,
        channelConsumer::addChannel
    )

    consume(
        "updated-channel-in-case-record",
        MergedChannelDemandEvent.name,
        channelConsumer::updatedChannel
    )

    consume(
        "remove-channel-archived-from-person-case",
        ChannelUpsertedEvent.name,
        channelConsumer::removeAssociation
    )

    val counterReferralConsumer by inject<CounterReferralConsumer>()
    consume(
        handlerName = "create-case-record-by-counter-referral",
        topicName = CounterReferralCreatedEvent.name,
        counterReferralConsumer::createCaseRecord
    )

    val outcomeRecommendationActionConsumer by inject<OutcomeRecommendationActionEventConsumer>()
    consume(
        handlerName = "outcome-recommendation-action-event-consumer",
        topicName = OutcomeRecommendationActionEvent.name,
        handler = outcomeRecommendationActionConsumer::handleRecommendationAction
    )

    val budConsumer by inject<BUDConsumer>()
    consume(
        handlerName = "add-new-case-record-from-service-script-action",
        topicName = BUDActionEvent.name,
        handler = budConsumer::addCaseRecord
    )

    val tertiaryIntentionTouchPointConsumer by inject<TertiaryIntentionTouchPointConsumer>()
    consume(
        handlerName = "create-case-record-from-tertiary-creation",
        topicName = TertiaryIntentionTouchPointCreatedEvent.name,
        handler = tertiaryIntentionTouchPointConsumer::createCaseRecord
    )
    consume(
        handlerName = "create-case-record-from-tertiary-update",
        topicName = TertiaryIntentionTouchPointUpdatedEvent.name,
        handler = tertiaryIntentionTouchPointConsumer::createCaseRecordFromUpdate
    )

    val backfillConsumer by inject<BackfillConsumer>()
    consume(
        handlerName = "backfill-case-record-created-from-disease",
        topicName = CreateCaseRecordFromDiseasesEvent.name,
        handler = backfillConsumer::createCaseRecordFromDiseases
    )

    val monitoringTriggerConsumer by inject<MonitoringTriggerConsumer>()
    consume(
        handlerName = "process-monitoring-trigger",
        topicName = ProcessMonitoringTriggerEvent.name,
        handler = monitoringTriggerConsumer::process
    )

    val createChannelMonitoringTriggerConsumer by inject<CreateChannelMonitoringTriggerConsumer>()
    consume(
        handlerName = "create-channel-for-monitoring-trigger-record",
        topicName = MonitoringTriggerRecordActionCreatedEvent.name,
        handler = createChannelMonitoringTriggerConsumer::handleMonitoringTriggerRecordActionCreated
    )

    val archivedChannelMonitoringTriggerConsumer by inject<ArchivedChannelMonitoringTriggerConsumer>()
    consume(
        handlerName = "create-monitoring-trigger-record-action-for-archived-channel",
        topicName = ChannelArchivedByInactivityEvent.name,
        handler = archivedChannelMonitoringTriggerConsumer::handleOnArchivedChannel
    )

    val sendFirstMessageMonitoringTriggerConsumer by inject<SendFirstMessageMonitoringTriggerConsumer>()
    consume(
        handlerName = "send-first-message-for-monitoring-trigger-record",
        topicName = MonitoringTriggerRecordActionCreatedEvent.name,
        handler = sendFirstMessageMonitoringTriggerConsumer::handleMonitoringTriggerRecordActionCreated
    )
}
