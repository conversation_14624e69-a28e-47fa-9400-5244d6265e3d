package br.com.alice.healthcondition.services.internal

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.communication.whatsapp.aws.WhatsAppSocialMessagingService
import br.com.alice.communication.whatsapp.aws.transport.SocialMessagingRequestTransport
import br.com.alice.communication.whatsapp.transport.WhatsAppMessage
import br.com.alice.communication.whatsapp.transport.message.template.WhatsAppMessageTemplate
import br.com.alice.communication.whatsapp.transport.message.template.WhatsAppMessageTemplateComponent
import br.com.alice.communication.whatsapp.transport.message.template.WhatsAppMessageTemplateComponentParameter
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MonitoringTriggerAdditionalProperties
import br.com.alice.data.layer.models.MonitoringTriggerConfiguration
import br.com.alice.data.layer.models.MonitoringTriggerFeature
import br.com.alice.data.layer.models.MonitoringTriggerFeatureType
import br.com.alice.data.layer.models.MonitoringTriggerRecord
import br.com.alice.data.layer.models.MonitoringTriggerRecordExtraData
import br.com.alice.data.layer.models.MonitoringTriggerRecordStatus
import br.com.alice.data.layer.models.MonitoringTriggerWhatsAppTemplate
import br.com.alice.data.layer.models.MonitoringTriggerWhatsAppTemplateVariable
import br.com.alice.healthcondition.services.whatsapp.WhatsAppParameterLoader
import br.com.alice.healthcondition.services.whatsapp.model.AvailableEntity
import br.com.alice.healthcondition.services.whatsapp.model.EntityParameter
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class SendWhatsAppMessageMonitoringServiceTest {

    private val personService = mockk<PersonService>()
    private val whatsAppParameterLoader = mockk<WhatsAppParameterLoader>()
    private val whatsappMessageService = mockk<WhatsAppSocialMessagingService>()
    private lateinit var sendWhatsAppMessageMonitoringService: SendWhatsAppMessageMonitoringService

    @BeforeTest
    fun setup() {
        sendWhatsAppMessageMonitoringService = SendWhatsAppMessageMonitoringService(
            personService = personService,
            whatsAppParameterLoader = whatsAppParameterLoader,
            whatsAppMessageService = whatsappMessageService
        )
    }

    @Test
    fun `given a message to be sent, when send message, then send to whatsapp`() {
        // given
        val triggerRecord = buildMonitoringTriggerRecord()
        val triggerConfiguration = buildMonitoringTriggerConfiguration()
        val personFound = TestModelFactory.buildPerson(phoneNumber = "+5511999999999")

        coEvery { personService.get(triggerRecord.personId) } returns personFound.success()

        coEvery {
            whatsAppParameterLoader.retrieveParameters(
                listOf(
                    EntityParameter(
                        domain = AvailableEntity.STAFF,
                        attribute = "name",
                        id = triggerRecord.staffIds.first().toString()
                    ), EntityParameter(
                        domain = AvailableEntity.PERSON,
                        attribute = "name",
                        id = triggerRecord.personId.toString()
                    )
                )
            )
        } returns listOf("Jhon Due", "Jane Smith").success()

        val socialMessagingRequest = SocialMessagingRequestTransport(
            fromPhoneNumberId = "phone-number-id-40c3d924c8194a849090f595e86bd265",
            whatsAppMessageRequest = WhatsAppMessage(
                to = personFound.phoneNumber!!,
                type = WhatsAppMessage.MessageType.TEMPLATE,
                template = WhatsAppMessageTemplate(
                    name = "coleta_pa",
                    components = listOf(
                        WhatsAppMessageTemplateComponent(
                            type = WhatsAppMessageTemplateComponent.Type.BODY,
                            parameters = listOf(
                                WhatsAppMessageTemplateComponentParameter(
                                    type = WhatsAppMessageTemplateComponentParameter.Type.TEXT,
                                    text = "Jhon Due"
                                ),
                                WhatsAppMessageTemplateComponentParameter(
                                    type = WhatsAppMessageTemplateComponentParameter.Type.TEXT,
                                    text = "Jane Smith"
                                )
                            )
                        ),
                        WhatsAppMessageTemplateComponent(
                            type = WhatsAppMessageTemplateComponent.Type.BUTTON,
                            subType = WhatsAppMessageTemplateComponent.SubType.URL,
                            index = "0",
                            parameters = listOf(
                                WhatsAppMessageTemplateComponentParameter(
                                    type = WhatsAppMessageTemplateComponentParameter.Type.TEXT,
                                    text = "channelId"
                                )
                            )
                        )
                    )
                )
            )
        )

        coEvery { whatsappMessageService.sendMessage(socialMessagingRequest) } returns Unit.success()

        //when
        val result = runBlocking {
            sendWhatsAppMessageMonitoringService.sendMessage(
                triggerRecord = triggerRecord,
                triggerCfg = triggerConfiguration
            )
        }

        assertThat(result).isSuccessWithData(true)
    }

    private fun buildMonitoringTriggerRecord() =
        MonitoringTriggerRecord(
            id = RangeUUID.generate(),
            personId = PersonId(),
            triggerId = RangeUUID.generate(),
            externalTriggerId = RangeUUID.generate().toString(),
            identifiedAt = LocalDateTime.now(),
            staffIds = listOf(RangeUUID.generate()),
            status = MonitoringTriggerRecordStatus.PROCESSING,
            currentStep = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
            extraData = MonitoringTriggerRecordExtraData(channelId = "channelId"),
        )

    private fun buildMonitoringTriggerConfiguration() =
        MonitoringTriggerConfiguration(
            id = RangeUUID.generate(),
            name = "coleta_pa",
            features = listOf(
                MonitoringTriggerFeature(MonitoringTriggerFeatureType.CREATE_CHANNEL, 0),
                MonitoringTriggerFeature(MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE, 1)
            ),
            additionalProperties = MonitoringTriggerAdditionalProperties(
                whatsAppTemplate = MonitoringTriggerWhatsAppTemplate(
                    name = "coleta_pa",
                    variables = listOf(
                        MonitoringTriggerWhatsAppTemplateVariable(
                            domain = "STAFF",
                            attribute = "name",
                        ),
                        MonitoringTriggerWhatsAppTemplateVariable(
                            domain = "MEMBER",
                            attribute = "name",
                        ),
                    )
                )
            )
        )
}
