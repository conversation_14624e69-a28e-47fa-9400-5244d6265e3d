package br.com.alice.healthcondition.services.internal

import br.com.alice.client.datagateway.DataGatewayClient
import br.com.alice.client.datagateway.DataGatewayFilter
import br.com.alice.client.datagateway.DataGatewayFilterOperator
import br.com.alice.client.datagateway.DataGatewayFilterParams
import br.com.alice.client.datagateway.DataGatewayResponse
import br.com.alice.client.datagateway.DataGatewayResponseMetadata
import br.com.alice.client.datagateway.DataGatewaySort
import br.com.alice.client.datagateway.DataGatewaySortOrder
import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MonitoringTriggerConfiguration
import br.com.alice.data.layer.models.MonitoringTriggerFeature
import br.com.alice.data.layer.models.MonitoringTriggerFeatureType
import br.com.alice.data.layer.models.MonitoringTriggerRecord
import br.com.alice.data.layer.models.MonitoringTriggerRecordAction
import br.com.alice.data.layer.models.MonitoringTriggerRecordActionType.NEW_TRIGGER_IDENTIFIED
import br.com.alice.data.layer.models.MonitoringTriggerRecordStatus
import br.com.alice.healthcondition.event.ProcessMonitoringTriggerEvent
import br.com.alice.healthcondition.services.MonitoringTriggerConfigurationService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordActionService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordService
import br.com.alice.staff.client.HealthProfessionalFilters
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import java.time.LocalDate
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

class SearchMonitoringTriggerServiceTest {

    private val dataGatewayClient: DataGatewayClient = mockk()
    private val personInternalReferenceService: PersonInternalReferenceService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val monitoringTriggerConfigurationService: MonitoringTriggerConfigurationService = mockk()
    private val monitoringTriggerRecordService: MonitoringTriggerRecordService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val monitoringTriggerRecordActionService: MonitoringTriggerRecordActionService = mockk()

    private val service = SearchMonitoringTriggerService(
        dataGatewayClient,
        personInternalReferenceService,
        kafkaProducerService,
        monitoringTriggerConfigurationService,
        monitoringTriggerRecordService,
        healthProfessionalService,
        monitoringTriggerRecordActionService
    )

    private val tableName = "output_active_triggers"
    private val triggerName = "someTriggerName"
    private val internalReference = TestModelFactory.buildPersonInternalReference()
    private val pageSize = 100L
    private val isTriggerActiveFilter = DataGatewayFilter(
        column = "is_trigger_active",
        operator = DataGatewayFilterOperator.EQUALS,
        value = true
    )
    private val triggerRefDay = "2025-07-01"
    private val triggerStatusFilter = DataGatewayFilter(
        column = "trigger_status",
        operator = DataGatewayFilterOperator.IN,
        value = listOf("Não analisado", "Reavaliação pendente")
    )
    private val defaultSort = listOf(
        DataGatewaySort(
            column = "trigger_ref_day",
            order = DataGatewaySortOrder.ASC
        )
    )
    private val healthProfessional = TestModelFactory.buildHealthProfessional()
    private val triggerConfiguration = MonitoringTriggerConfiguration(
        name = triggerName,
        features = listOf(
            MonitoringTriggerFeature(
                feature = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
                order = 0
            )
        )
    )
    private val outputActiveTrigger = OutputActiveTriggerResponse(
        id = "1c01874a4831d62891113c2cffe1cb4e",
        originTable = "test_result_triggers",
        triggerId = "1c01874a4831d62891113c2cffe1cb4e",
        triggerRefDay = LocalDate.of(2025, 6, 1),
        triggerOriginIdType = "monitoring_test_results_id",
        triggerOriginId = "f960c6fbd2dea38de3d519a84e61f1e0",
        triggerOriginTable = "monitoring_test_results",
        memberInternalCode = internalReference.internalCode,
        triggerName = "sem_coleta_pa",
        triggerPriority = 2,
        triggerDescription = "Sem coleta de PA",
        isTriggerActive = true,
        objectiveCodes = listOf("I10", "E66"),
        responsibleNurse = healthProfessional.email,
        triggerStatus = "Não analisado",
        lastInteractionCreatedAt = null,
        interactionUser = null,
        triggerNextScheduleDate = null,
        triggerDiscardReason = null,
        triggerValueDescription = "resultado do último exame localizado de PAS",
        triggerValue = "145"
    )
    private val datagatewayResponse = DataGatewayResponse(
        data = listOf(outputActiveTrigger),
        meta = DataGatewayResponseMetadata(
            total = 1234,
            page = 1,
            pageSize = 100,
            firstPage = 1,
            lastPage = 22,
            hasPreviousPage = false,
            hasNextPage = true,
            previousPage = null,
            nextPage = "http://data-gtw.datalake.alice.tools/output_active_triggers?page[number]=2&page[size]=100"
        )
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(
        dataGatewayClient,
        personInternalReferenceService,
        kafkaProducerService,
        monitoringTriggerConfigurationService,
        monitoringTriggerRecordService,
        healthProfessionalService,
        monitoringTriggerRecordActionService
    )

    @Test
    fun `#process should process first page successfully`() = mockRangeUuidAndDateTime { id, now ->
        withFeatureFlagMocks {
            val pageNumber = 1L
            val event = ProcessMonitoringTriggerEvent(
                triggerName = triggerName,
                pageNumber = pageNumber + 1,
                pageSize = pageSize
            )
            val externalIds = datagatewayResponse.data.map { it.id }

            val records = listOf(
                MonitoringTriggerRecord(
                    personId = internalReference.personId,
                    triggerId = triggerConfiguration.id,
                    staffIds = listOf(healthProfessional.staffId),
                    externalTriggerId = outputActiveTrigger.id,
                    identifiedAt = outputActiveTrigger.triggerRefDay.atStartOfDay(),
                    status = MonitoringTriggerRecordStatus.CREATED,
                    currentStep = triggerConfiguration.features.first().feature,
                )
            )
            val monitoringAction = listOf(
                MonitoringTriggerRecordAction(
                    personId = internalReference.personId,
                    triggerId = triggerConfiguration.id,
                    externalTriggerId = outputActiveTrigger.id,
                    monitoringTriggerRecordId = records.first().id,
                    action = NEW_TRIGGER_IDENTIFIED
                )
            )

            coEvery {
                dataGatewayClient.search(
                    tableName = tableName,
                    params = DataGatewayFilterParams(
                        filters = listOf(
                            isTriggerActiveFilter,
                            triggerStatusFilter,
                            DataGatewayFilter(
                                column = "trigger_ref_day",
                                operator = DataGatewayFilterOperator.GREATER_THAN,
                                value = triggerRefDay
                            ),
                            DataGatewayFilter(
                                column = "trigger_name",
                                operator = DataGatewayFilterOperator.EQUALS,
                                value = triggerName
                            )
                        ),
                        sort = defaultSort,
                        pageSize = pageSize,
                        pageNumber = pageNumber
                    ),
                    response = OutputActiveTriggerResponse::class
                )
            } returns datagatewayResponse.success()

            coEvery { kafkaProducerService.produce(event) } returns mockk()

            coEvery {
                monitoringTriggerRecordService.findAllByExternalTriggerIds(externalIds)
            } returns emptyList<MonitoringTriggerRecord>().success()

            coEvery {
                personInternalReferenceService.getByInternalCodes(listOf(internalReference.internalCode))
            } returns listOf(internalReference).success()
            coEvery {
                healthProfessionalService.findBy(HealthProfessionalFilters(emails = listOf(healthProfessional.email)))
            } returns listOf(healthProfessional).success()

            coEvery { monitoringTriggerConfigurationService.getByName(triggerName) } returns triggerConfiguration.success()
            coEvery { monitoringTriggerRecordService.addAll(records) } returns records.success()

            coEvery {
                monitoringTriggerRecordActionService.createAll(records, NEW_TRIGGER_IDENTIFIED)
            } returns monitoringAction.success()

            val result = service.process(triggerName, pageNumber, pageSize)

            ResultAssert.assertThat(result).isSuccessWithData(records)

            coVerifyOnce { dataGatewayClient.search<OutputActiveTriggerResponse>(any(), any(), any()) }
            coVerifyOnce { personInternalReferenceService.getByInternalCodes(any()) }
            coVerifyOnce { monitoringTriggerRecordService.findAllByExternalTriggerIds(any()) }
            coVerifyOnce { healthProfessionalService.findBy(any()) }
            coVerifyOnce { monitoringTriggerConfigurationService.getByName(any()) }
            coVerifyOnce { monitoringTriggerRecordService.addAll(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
            coVerifyOnce { monitoringTriggerRecordActionService.createAll(any(), any()) }
        }
    }

    @Test
    fun `#process should process first page successfully using internal codes filter`() =
        mockRangeUuidAndDateTime { id, now ->
            val internalCodes = listOf("NC1MOCK")
            withFeatureFlagMocks(internalCodes) {
                val pageNumber = 1L
                val event = ProcessMonitoringTriggerEvent(
                    triggerName = triggerName,
                    pageNumber = pageNumber + 1,
                    pageSize = pageSize
                )
                val externalIds = datagatewayResponse.data.map { it.id }

                val records = listOf(
                    MonitoringTriggerRecord(
                        personId = internalReference.personId,
                        triggerId = triggerConfiguration.id,
                        staffIds = listOf(healthProfessional.staffId),
                        externalTriggerId = outputActiveTrigger.id,
                        identifiedAt = outputActiveTrigger.triggerRefDay.atStartOfDay(),
                        status = MonitoringTriggerRecordStatus.CREATED,
                        currentStep = triggerConfiguration.features.first().feature,
                    )
                )
                val monitoringAction = listOf(
                    MonitoringTriggerRecordAction(
                        personId = internalReference.personId,
                        triggerId = triggerConfiguration.id,
                        externalTriggerId = outputActiveTrigger.id,
                        monitoringTriggerRecordId = records.first().id,
                        action = NEW_TRIGGER_IDENTIFIED
                    )
                )

                coEvery {
                    dataGatewayClient.search(
                        tableName = tableName,
                        params = DataGatewayFilterParams(
                            filters = listOf(
                                isTriggerActiveFilter,
                                triggerStatusFilter,
                                DataGatewayFilter(
                                    column = "trigger_ref_day",
                                    operator = DataGatewayFilterOperator.GREATER_THAN,
                                    value = triggerRefDay
                                ),
                                DataGatewayFilter(
                                    column = "trigger_name",
                                    operator = DataGatewayFilterOperator.EQUALS,
                                    value = triggerName
                                ),
                                DataGatewayFilter(
                                    column = "member_internal_code",
                                    operator = DataGatewayFilterOperator.IN,
                                    value = internalCodes
                                )
                            ),
                            sort = defaultSort,
                            pageSize = pageSize,
                            pageNumber = pageNumber
                        ),
                        response = OutputActiveTriggerResponse::class
                    )
                } returns datagatewayResponse.success()

                coEvery { kafkaProducerService.produce(event) } returns mockk()

                coEvery {
                    monitoringTriggerRecordService.findAllByExternalTriggerIds(externalIds)
                } returns emptyList<MonitoringTriggerRecord>().success()

                coEvery {
                    personInternalReferenceService.getByInternalCodes(listOf(internalReference.internalCode))
                } returns listOf(internalReference).success()
                coEvery {
                    healthProfessionalService.findBy(HealthProfessionalFilters(emails = listOf(healthProfessional.email)))
                } returns listOf(healthProfessional).success()

                coEvery { monitoringTriggerConfigurationService.getByName(triggerName) } returns triggerConfiguration.success()
                coEvery { monitoringTriggerRecordService.addAll(records) } returns records.success()

                coEvery {
                    monitoringTriggerRecordActionService.createAll(records, NEW_TRIGGER_IDENTIFIED)
                } returns monitoringAction.success()

                val result = service.process(triggerName, pageNumber, pageSize)

                ResultAssert.assertThat(result).isSuccessWithData(records)

                coVerifyOnce { dataGatewayClient.search<OutputActiveTriggerResponse>(any(), any(), any()) }
                coVerifyOnce { personInternalReferenceService.getByInternalCodes(any()) }
                coVerifyOnce { monitoringTriggerRecordService.findAllByExternalTriggerIds(any()) }
                coVerifyOnce { healthProfessionalService.findBy(any()) }
                coVerifyOnce { monitoringTriggerConfigurationService.getByName(any()) }
                coVerifyOnce { monitoringTriggerRecordService.addAll(any()) }
                coVerifyOnce { kafkaProducerService.produce(any()) }
                coVerifyOnce { monitoringTriggerRecordActionService.createAll(any(), any()) }
            }
        }

    @Test
    fun `#process should process first page successfully ignoring existent triggers`() =
        mockRangeUuidAndDateTime { id, now ->
            withFeatureFlagMocks {
                val pageNumber = 1L
                val event = ProcessMonitoringTriggerEvent(
                    triggerName = triggerName,
                    pageNumber = pageNumber + 1,
                    pageSize = pageSize
                )
                val existentData = outputActiveTrigger.copy(
                    id = "existent_id"
                )
                val datagatewayResponse = datagatewayResponse.copy(
                    data = listOf(
                        outputActiveTrigger,
                        existentData
                    )
                )
                val externalIds = datagatewayResponse.data.map { it.id }

                val existentTriggerRecord = MonitoringTriggerRecord(
                    personId = internalReference.personId,
                    triggerId = triggerConfiguration.id,
                    staffIds = listOf(healthProfessional.staffId),
                    externalTriggerId = existentData.id,
                    identifiedAt = existentData.triggerRefDay.atStartOfDay(),
                    status = MonitoringTriggerRecordStatus.CREATED,
                    currentStep = triggerConfiguration.features.first().feature,
                )

                val records = listOf(
                    MonitoringTriggerRecord(
                        personId = internalReference.personId,
                        triggerId = triggerConfiguration.id,
                        staffIds = listOf(healthProfessional.staffId),
                        externalTriggerId = outputActiveTrigger.id,
                        identifiedAt = outputActiveTrigger.triggerRefDay.atStartOfDay(),
                        status = MonitoringTriggerRecordStatus.CREATED,
                        currentStep = triggerConfiguration.features.first().feature,
                    )
                )
                val monitoringAction = listOf(
                    MonitoringTriggerRecordAction(
                        personId = internalReference.personId,
                        triggerId = triggerConfiguration.id,
                        externalTriggerId = outputActiveTrigger.id,
                        monitoringTriggerRecordId = records.first().id,
                        action = NEW_TRIGGER_IDENTIFIED
                    )
                )

                coEvery {
                    dataGatewayClient.search(
                        tableName = tableName,
                        params = DataGatewayFilterParams(
                            filters = listOf(
                                isTriggerActiveFilter,
                                triggerStatusFilter,
                                DataGatewayFilter(
                                    column = "trigger_ref_day",
                                    operator = DataGatewayFilterOperator.GREATER_THAN,
                                    value = triggerRefDay
                                ),
                                DataGatewayFilter(
                                    column = "trigger_name",
                                    operator = DataGatewayFilterOperator.EQUALS,
                                    value = triggerName
                                )
                            ),
                            sort = defaultSort,
                            pageSize = pageSize,
                            pageNumber = pageNumber
                        ),
                        response = OutputActiveTriggerResponse::class
                    )
                } returns datagatewayResponse.success()

                coEvery { kafkaProducerService.produce(event) } returns mockk()

                coEvery {
                    monitoringTriggerRecordService.findAllByExternalTriggerIds(externalIds)
                } returns listOf(existentTriggerRecord).success()

                coEvery {
                    personInternalReferenceService.getByInternalCodes(listOf(internalReference.internalCode))
                } returns listOf(internalReference).success()
                coEvery {
                    healthProfessionalService.findBy(HealthProfessionalFilters(emails = listOf(healthProfessional.email)))
                } returns listOf(healthProfessional).success()
                coEvery { monitoringTriggerConfigurationService.getByName(triggerName) } returns triggerConfiguration.success()

                coEvery { monitoringTriggerRecordService.addAll(records) } returns records.success()
                coEvery {
                    monitoringTriggerRecordActionService.createAll(records, NEW_TRIGGER_IDENTIFIED)
                } returns monitoringAction.success()

                val result = service.process(triggerName, pageNumber, pageSize)

                ResultAssert.assertThat(result).isSuccessWithData(records)

                coVerifyOnce { dataGatewayClient.search<OutputActiveTriggerResponse>(any(), any(), any()) }
                coVerifyOnce { personInternalReferenceService.getByInternalCodes(any()) }
                coVerifyOnce { monitoringTriggerRecordService.findAllByExternalTriggerIds(any()) }
                coVerifyOnce { healthProfessionalService.findBy(any()) }
                coVerifyOnce { monitoringTriggerConfigurationService.getByName(any()) }
                coVerifyOnce { monitoringTriggerRecordService.addAll(any()) }
                coVerifyOnce { monitoringTriggerRecordActionService.createAll(any(), any()) }
                coVerifyOnce { kafkaProducerService.produce(any()) }
            }
        }

    @Test
    fun `#process should do nothing when all triggers are saved already`() = runBlocking {
        withFeatureFlagMocks {
            val pageNumber = 1L
            val event = ProcessMonitoringTriggerEvent(
                triggerName = triggerName,
                pageNumber = pageNumber + 1,
                pageSize = pageSize
            )
            val externalIds = datagatewayResponse.data.map { it.id }

            val existentTriggerRecord = MonitoringTriggerRecord(
                personId = internalReference.personId,
                triggerId = triggerConfiguration.id,
                staffIds = listOf(healthProfessional.staffId),
                externalTriggerId = outputActiveTrigger.id,
                identifiedAt = outputActiveTrigger.triggerRefDay.atStartOfDay(),
                status = MonitoringTriggerRecordStatus.CREATED,
                currentStep = triggerConfiguration.features.first().feature,
            )

            coEvery {
                dataGatewayClient.search(
                    tableName = tableName,
                    params = DataGatewayFilterParams(
                        filters = listOf(
                            isTriggerActiveFilter,
                            triggerStatusFilter,
                            DataGatewayFilter(
                                column = "trigger_ref_day",
                                operator = DataGatewayFilterOperator.GREATER_THAN,
                                value = triggerRefDay
                            ),
                            DataGatewayFilter(
                                column = "trigger_name",
                                operator = DataGatewayFilterOperator.EQUALS,
                                value = triggerName
                            )
                        ),
                        sort = defaultSort,
                        pageSize = pageSize,
                        pageNumber = pageNumber
                    ),
                    response = OutputActiveTriggerResponse::class
                )
            } returns datagatewayResponse.success()

            coEvery { kafkaProducerService.produce(event) } returns mockk()

            coEvery {
                monitoringTriggerRecordService.findAllByExternalTriggerIds(externalIds)
            } returns listOf(existentTriggerRecord).success()

            val result = service.process(triggerName, pageNumber, pageSize)

            ResultAssert.assertThat(result).isSuccessWithData(emptyList())

            coVerifyOnce { dataGatewayClient.search<OutputActiveTriggerResponse>(any(), any(), any()) }
            coVerifyOnce { monitoringTriggerRecordService.findAllByExternalTriggerIds(any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
        }
    }

    @Test
    fun `#process should process first page and ignore next page successfully`() = mockRangeUuidAndDateTime { id, now ->
        withFeatureFlagMocks {
            val pageNumber = 1L
            val datagatewayResponse = datagatewayResponse.copy(
                meta = datagatewayResponse.meta.copy(
                    hasNextPage = false,
                    nextPage = null
                )
            )
            val externalIds = datagatewayResponse.data.map { it.id }

            val records = listOf(
                MonitoringTriggerRecord(
                    personId = internalReference.personId,
                    triggerId = triggerConfiguration.id,
                    staffIds = listOf(healthProfessional.staffId),
                    externalTriggerId = outputActiveTrigger.id,
                    identifiedAt = outputActiveTrigger.triggerRefDay.atStartOfDay(),
                    status = MonitoringTriggerRecordStatus.CREATED,
                    currentStep = triggerConfiguration.features.first().feature,
                )
            )
            val monitoringAction = listOf(
                MonitoringTriggerRecordAction(
                    personId = internalReference.personId,
                    triggerId = triggerConfiguration.id,
                    externalTriggerId = outputActiveTrigger.id,
                    monitoringTriggerRecordId = records.first().id,
                    action = NEW_TRIGGER_IDENTIFIED
                )
            )

            coEvery {
                dataGatewayClient.search(
                    tableName = tableName,
                    params = DataGatewayFilterParams(
                        filters = listOf(
                            isTriggerActiveFilter,
                            triggerStatusFilter,
                            DataGatewayFilter(
                                column = "trigger_ref_day",
                                operator = DataGatewayFilterOperator.GREATER_THAN,
                                value = triggerRefDay
                            ),
                            DataGatewayFilter(
                                column = "trigger_name",
                                operator = DataGatewayFilterOperator.EQUALS,
                                value = triggerName
                            )
                        ),
                        sort = defaultSort,
                        pageSize = pageSize,
                        pageNumber = pageNumber
                    ),
                    response = OutputActiveTriggerResponse::class
                )
            } returns datagatewayResponse.success()

            coEvery {
                monitoringTriggerRecordService.findAllByExternalTriggerIds(externalIds)
            } returns emptyList<MonitoringTriggerRecord>().success()

            coEvery {
                personInternalReferenceService.getByInternalCodes(listOf(internalReference.internalCode))
            } returns listOf(internalReference).success()
            coEvery {
                healthProfessionalService.findBy(HealthProfessionalFilters(emails = listOf(healthProfessional.email)))
            } returns listOf(healthProfessional).success()
            coEvery {
                monitoringTriggerConfigurationService.getByName(triggerName)
            } returns triggerConfiguration.success()

            coEvery {
                monitoringTriggerRecordService.addAll(records)
            } returns records.success()
            coEvery {
                monitoringTriggerRecordActionService.createAll(records, NEW_TRIGGER_IDENTIFIED)
            } returns monitoringAction.success()


            val result = service.process(triggerName, pageNumber, pageSize)

            ResultAssert.assertThat(result).isSuccessWithData(records)

            coVerifyOnce { dataGatewayClient.search<OutputActiveTriggerResponse>(any(), any(), any()) }
            coVerifyOnce { personInternalReferenceService.getByInternalCodes(any()) }
            coVerifyOnce { monitoringTriggerRecordService.findAllByExternalTriggerIds(any()) }
            coVerifyOnce { healthProfessionalService.findBy(any()) }
            coVerifyOnce { monitoringTriggerConfigurationService.getByName(any()) }
            coVerifyOnce { monitoringTriggerRecordService.addAll(any()) }
            coVerifyOnce { monitoringTriggerRecordActionService.createAll(any(), any()) }
            coVerify { kafkaProducerService wasNot called }
        }
    }

    @Test
    fun `#process should return error when trigger configuration is not found`() = runBlocking {
        withFeatureFlagMocks {
            val pageNumber = 1L
            val datagatewayResponse = datagatewayResponse.copy(
                meta = datagatewayResponse.meta.copy(
                    hasNextPage = false,
                    nextPage = null
                )
            )
            val externalIds = datagatewayResponse.data.map { it.id }

            coEvery {
                dataGatewayClient.search(
                    tableName = tableName,
                    params = DataGatewayFilterParams(
                        filters = listOf(
                            isTriggerActiveFilter,
                            triggerStatusFilter,
                            DataGatewayFilter(
                                column = "trigger_ref_day",
                                operator = DataGatewayFilterOperator.GREATER_THAN,
                                value = triggerRefDay
                            ),
                            DataGatewayFilter(
                                column = "trigger_name",
                                operator = DataGatewayFilterOperator.EQUALS,
                                value = triggerName
                            )
                        ),
                        sort = defaultSort,
                        pageSize = pageSize,
                        pageNumber = pageNumber
                    ),
                    response = OutputActiveTriggerResponse::class
                )
            } returns datagatewayResponse.success()

            coEvery {
                monitoringTriggerRecordService.findAllByExternalTriggerIds(externalIds)
            } returns emptyList<MonitoringTriggerRecord>().success()

            coEvery {
                monitoringTriggerConfigurationService.getByName(triggerName)
            } returns NotFoundException("trigger_configuration_not_found").failure()

            val result = service.process(triggerName, pageNumber, pageSize)

            ResultAssert.assertThat(result).isFailureOfType(NotFoundException::class)

            coVerifyOnce { dataGatewayClient.search<OutputActiveTriggerResponse>(any(), any(), any()) }
            coVerifyNone { personInternalReferenceService.getByInternalCodes(any()) }
            coVerifyOnce { monitoringTriggerRecordService.findAllByExternalTriggerIds(any()) }
            coVerifyNone { healthProfessionalService.findBy(any()) }
            coVerifyOnce { monitoringTriggerConfigurationService.getByName(any()) }
            coVerifyNone { monitoringTriggerRecordService.addAll(any()) }
            coVerify { kafkaProducerService wasNot called }
        }
    }

    @Test
    fun `#process should return error when trigger configuration has no features`() = runBlocking {
        withFeatureFlagMocks {
            val pageNumber = 1L
            val datagatewayResponse = datagatewayResponse.copy(
                meta = datagatewayResponse.meta.copy(
                    hasNextPage = false,
                    nextPage = null
                )
            )
            val externalIds = datagatewayResponse.data.map { it.id }
            val triggerConfiguration = triggerConfiguration.copy(features = emptyList())

            coEvery {
                dataGatewayClient.search(
                    tableName = tableName,
                    params = DataGatewayFilterParams(
                        filters = listOf(
                            isTriggerActiveFilter,
                            triggerStatusFilter,
                            DataGatewayFilter(
                                column = "trigger_ref_day",
                                operator = DataGatewayFilterOperator.GREATER_THAN,
                                value = triggerRefDay
                            ),
                            DataGatewayFilter(
                                column = "trigger_name",
                                operator = DataGatewayFilterOperator.EQUALS,
                                value = triggerName
                            )
                        ),
                        sort = defaultSort,
                        pageSize = pageSize,
                        pageNumber = pageNumber
                    ),
                    response = OutputActiveTriggerResponse::class
                )
            } returns datagatewayResponse.success()

            coEvery {
                monitoringTriggerRecordService.findAllByExternalTriggerIds(externalIds)
            } returns emptyList<MonitoringTriggerRecord>().success()
            coEvery {
                personInternalReferenceService.getByInternalCodes(listOf(internalReference.internalCode))
            } returns listOf(internalReference).success()
            coEvery {
                healthProfessionalService.findBy(HealthProfessionalFilters(emails = listOf(healthProfessional.email)))
            } returns listOf(healthProfessional).success()
            coEvery {
                monitoringTriggerConfigurationService.getByName(triggerName)
            } returns triggerConfiguration.success()

            val result = service.process(triggerName, pageNumber, pageSize)

            ResultAssert.assertThat(result).isFailureOfType(IllegalArgumentException::class)

            coVerifyOnce { dataGatewayClient.search<OutputActiveTriggerResponse>(any(), any(), any()) }
            coVerifyOnce { personInternalReferenceService.getByInternalCodes(any()) }
            coVerifyOnce { monitoringTriggerRecordService.findAllByExternalTriggerIds(any()) }
            coVerifyOnce { healthProfessionalService.findBy(any()) }
            coVerifyOnce { monitoringTriggerConfigurationService.getByName(any()) }
            coVerifyNone { monitoringTriggerRecordService.addAll(any()) }
            coVerify { kafkaProducerService wasNot called }
        }
    }

    @Test
    fun `#process should return error if data api response is an error`() = runBlocking {
        withFeatureFlagMocks {
            val pageNumber = 1L

            coEvery {
                dataGatewayClient.search(
                    tableName = tableName,
                    params = DataGatewayFilterParams(
                        filters = listOf(
                            isTriggerActiveFilter,
                            triggerStatusFilter,
                            DataGatewayFilter(
                                column = "trigger_ref_day",
                                operator = DataGatewayFilterOperator.GREATER_THAN,
                                value = triggerRefDay
                            ),
                            DataGatewayFilter(
                                column = "trigger_name",
                                operator = DataGatewayFilterOperator.EQUALS,
                                value = triggerName
                            )
                        ),
                        sort = defaultSort,
                        pageSize = pageSize,
                        pageNumber = pageNumber
                    ),
                    response = OutputActiveTriggerResponse::class
                )
            } returns RuntimeException("some_error").failure()

            coEvery {
                personInternalReferenceService.getByInternalCodes(listOf(internalReference.internalCode))
            } returns listOf(internalReference).success()

            val result = service.process(triggerName, pageNumber, pageSize)

            ResultAssert.assertThat(result).isFailureOfType(RuntimeException::class)

            coVerifyOnce { dataGatewayClient.search<OutputActiveTriggerResponse>(any(), any(), any()) }
        }
    }

    private suspend fun withFeatureFlagMocks(internalCodes: List<String> = emptyList(), func: suspend () -> Unit) =
        withFeatureFlags(
            FeatureNamespace.HEALTH_CONDITION to mapOf(
                "allowed_monitoring_triggers_member_internal_codes" to internalCodes,
                "monitoring_trigger_ref_day_initial_filter" to triggerRefDay
            )
        ) { func() }

}
