package br.com.alice.healthcondition.services.internal

import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChatMessageRequest
import br.com.alice.channel.models.CreateChatRequest
import br.com.alice.channel.models.MessageType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.MonitoringTriggerAdditionalProperties
import br.com.alice.data.layer.models.MonitoringTriggerConfiguration
import br.com.alice.data.layer.models.MonitoringTriggerFeature
import br.com.alice.data.layer.models.MonitoringTriggerFeatureType
import br.com.alice.data.layer.models.MonitoringTriggerRecord
import br.com.alice.data.layer.models.MonitoringTriggerRecordAction
import br.com.alice.data.layer.models.MonitoringTriggerRecordActionExtraInfo
import br.com.alice.data.layer.models.MonitoringTriggerRecordActionType
import br.com.alice.data.layer.models.MonitoringTriggerRecordActionType.NEW_TRIGGER_IDENTIFIED
import br.com.alice.data.layer.models.MonitoringTriggerRecordExtraData
import br.com.alice.data.layer.models.MonitoringTriggerRecordStatus
import br.com.alice.healthcondition.services.MonitoringTriggerRecordActionService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordService
import br.com.alice.person.client.PersonService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.coVerifyOrder
import io.mockk.confirmVerified
import io.mockk.mockk
import java.time.LocalDateTime
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class CreateChannelMonitoringTriggerServiceTest {

    private val monitoringTriggerRecordService: MonitoringTriggerRecordService = mockk()
    private val channelService: ChannelService = mockk()
    private val monitoringTriggerRecordActionService: MonitoringTriggerRecordActionService = mockk()
    private val personService: PersonService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()

    private val service = CreateChannelMonitoringTriggerService(
        monitoringTriggerRecordService,
        channelService,
        monitoringTriggerRecordActionService,
        personService,
        healthProfessionalService
    )

    private val monitoringChannelName = "Monitoramento de Saúde"
    private val monitoringChannelOrigin = "monitoring"
    private val configuration = MonitoringTriggerConfiguration(
        name = "trigger_name",
        features = listOf(
            MonitoringTriggerFeature(
                feature = MonitoringTriggerFeatureType.CREATE_CHANNEL,
                order = 0
            ),
            MonitoringTriggerFeature(
                feature = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
                order = 1
            )
        ),
        additionalProperties = MonitoringTriggerAdditionalProperties(
            channelInitialMessage = "Ola @firstname, me chamo @staffName. vamos falar sobre sua saude?"
        )
    )
    private val staffId = RangeUUID.generate()
    private val healthProfessional = TestModelFactory.buildHealthProfessional(staffId = staffId)
    private val person = TestModelFactory.buildPerson()
    private val channelInitialMessage =
        "Ola ${person.firstName}, me chamo ${healthProfessional.name}. vamos falar sobre sua saude?"
    private val triggerRecord = MonitoringTriggerRecord(
        personId = person.id,
        triggerId = configuration.id,
        staffIds = listOf(staffId),
        externalTriggerId = "external_trigger_id",
        identifiedAt = LocalDateTime.now(),
        status = MonitoringTriggerRecordStatus.CREATED,
        currentStep = MonitoringTriggerFeatureType.CREATE_CHANNEL,
    )
    private val monitoringAction = MonitoringTriggerRecordAction(
        personId = triggerRecord.personId,
        triggerId = configuration.id,
        externalTriggerId = triggerRecord.externalTriggerId,
        monitoringTriggerRecordId = triggerRecord.id,
        action = NEW_TRIGGER_IDENTIFIED
    )

    private val channelId = "channel-123"
    private val channelDocument = ChannelDocument(
        id = channelId,
        personId = triggerRecord.personId.toString(),
        channelPersonId = triggerRecord.personId.toString(),
        category = ChannelCategory.ASSISTANCE,
        subCategory = ChannelSubCategory.LONGITUDINAL,
    )

    @BeforeTest
    fun confirmMocks() {
        confirmVerified(
            monitoringTriggerRecordService,
            channelService,
            monitoringTriggerRecordActionService,
            personService,
            healthProfessionalService
        )
    }

    @Test
    fun `#create should create channel, add staffs, update monitoring and create action`() = runBlocking {
        val expectedChatRequest = CreateChatRequest(
            personId = triggerRecord.personId,
            chatName = monitoringChannelName,
            origin = monitoringChannelOrigin,
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.LONGITUDINAL
        )

        val updatedTriggerRecord = triggerRecord.copy(
            status = MonitoringTriggerRecordStatus.PROCESSING,
            currentStep = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
            extraData = MonitoringTriggerRecordExtraData(channelId = channelId)
        )

        coEvery { healthProfessionalService.findByStaffId(staffId) } returns healthProfessional.success()
        coEvery { personService.get(triggerRecord.personId) } returns person.success()
        coEvery { channelService.addChatV2(expectedChatRequest) } returns channelDocument.success()
        coEvery {
            channelService.sendTextMessage(channelDocument, channelInitialMessage, true)
        } returns "messageId".success()

        coEvery {
            channelService.addStaff(
                channelId = channelId,
                staffId = staffId.toString(),
                requesterStaffId = staffId.toString()
            )
        } returns channelId.success()

        coEvery { monitoringTriggerRecordService.update(updatedTriggerRecord) } returns updatedTriggerRecord.success()

        coEvery {
            monitoringTriggerRecordActionService.create(
                triggerRecord = updatedTriggerRecord,
                action = MonitoringTriggerRecordActionType.CHANNEL_CREATED,
                extraInfo = MonitoringTriggerRecordActionExtraInfo(channelId = channelId)
            )
        } returns monitoringAction.success()

        val result = service.create(triggerRecord, configuration)

        assertThat(result).isSuccessWithData(updatedTriggerRecord)

        coVerifyOnce { channelService.addChatV2(any()) }
        coVerifyOnce { channelService.addStaff(any(), any(), any()) }
        coVerifyOnce { channelService.sendTextMessage(any(), any(), any()) }
        coVerifyOnce { monitoringTriggerRecordService.update(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
        coVerifyOnce { personService.get(any()) }

        coVerifyOnce { monitoringTriggerRecordActionService.create(any(), any(), any()) }

        coVerifyOrder {
            channelService.addChatV2(any())
            channelService.addStaff(any(), any(), any())
            monitoringTriggerRecordService.update(any())
            monitoringTriggerRecordActionService.create(any(), any(), any())
        }
    }

    @Test
    fun `#create should mark monitoring as CONCLUDED when it's the last step`() = runBlocking {
        val chatRequest = CreateChatRequest(
            personId = triggerRecord.personId,
            chatName = monitoringChannelName,
            origin = monitoringChannelOrigin,
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.LONGITUDINAL
        )
        val configWithOnlyCreateChannel = configuration.copy(
            features = listOf(
                MonitoringTriggerFeature(
                    feature = MonitoringTriggerFeatureType.CREATE_CHANNEL,
                    order = 0
                )
            )
        )

        val updatedTriggerRecord = triggerRecord.copy(
            status = MonitoringTriggerRecordStatus.CONCLUDED,
            currentStep = MonitoringTriggerFeatureType.CREATE_CHANNEL,
            extraData = MonitoringTriggerRecordExtraData(channelId = channelId)
        )

        coEvery { healthProfessionalService.findByStaffId(staffId) } returns healthProfessional.success()
        coEvery { personService.get(triggerRecord.personId) } returns person.success()
        coEvery { channelService.addChatV2(chatRequest) } returns channelDocument.success()
        coEvery {
            channelService.sendTextMessage(channelDocument, channelInitialMessage, true)
        } returns "messageId".success()

        coEvery {
            channelService.addStaff(
                channelId = channelId,
                staffId = staffId.toString(),
                requesterStaffId = staffId.toString()
            )
        } returns channelId.success()

        coEvery { monitoringTriggerRecordService.update(updatedTriggerRecord) } returns updatedTriggerRecord.success()

        coEvery {
            monitoringTriggerRecordActionService.create(
                triggerRecord = updatedTriggerRecord,
                action = MonitoringTriggerRecordActionType.CHANNEL_CREATED,
                extraInfo = MonitoringTriggerRecordActionExtraInfo(channelId = channelId)
            )
        } returns monitoringAction.success()

        val result = service.create(triggerRecord, configWithOnlyCreateChannel)

        assertThat(result).isSuccessWithData(updatedTriggerRecord)

        coVerifyOnce { channelService.addChatV2(any()) }
        coVerifyOnce { channelService.addStaff(any(), any(), any()) }
        coVerifyOnce { channelService.sendTextMessage(any(), any(), any()) }
        coVerifyOnce { monitoringTriggerRecordService.update(any()) }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any()) }

        coVerifyOnce { monitoringTriggerRecordActionService.create(any(), any(), any()) }
    }

    @Test
    fun `#create should create channel, add staffs, update monitoring and create action even if build message fails`() =
        runBlocking {
            val expectedChatRequest = CreateChatRequest(
                personId = triggerRecord.personId,
                chatName = monitoringChannelName,
                origin = monitoringChannelOrigin,
                category = ChannelCategory.ASSISTANCE,
                subCategory = ChannelSubCategory.LONGITUDINAL
            )

            val updatedTriggerRecord = triggerRecord.copy(
                status = MonitoringTriggerRecordStatus.PROCESSING,
                currentStep = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
                extraData = MonitoringTriggerRecordExtraData(channelId = channelId)
            )

            coEvery { healthProfessionalService.findByStaffId(staffId) } returns NotFoundException("not_found").failure()
            coEvery { personService.get(triggerRecord.personId) } returns person.success()
            coEvery { channelService.addChatV2(expectedChatRequest) } returns channelDocument.success()
            coEvery {
                channelService.sendTextMessage(
                    channelDocument,
                    configuration.additionalProperties?.channelInitialMessage!!,
                    true
                )
            } returns "messageId".success()

            coEvery {
                channelService.addStaff(
                    channelId = channelId,
                    staffId = staffId.toString(),
                    requesterStaffId = staffId.toString()
                )
            } returns channelId.success()

            coEvery { monitoringTriggerRecordService.update(updatedTriggerRecord) } returns updatedTriggerRecord.success()

            coEvery {
                monitoringTriggerRecordActionService.create(
                    triggerRecord = updatedTriggerRecord,
                    action = MonitoringTriggerRecordActionType.CHANNEL_CREATED,
                    extraInfo = MonitoringTriggerRecordActionExtraInfo(channelId = channelId)
                )
            } returns monitoringAction.success()

            val result = service.create(triggerRecord, configuration)

            assertThat(result).isSuccessWithData(updatedTriggerRecord)

            coVerifyOnce { channelService.addChatV2(any()) }
            coVerifyOnce { channelService.addStaff(any(), any(), any()) }
            coVerifyOnce { channelService.sendTextMessage(any(), any(), any()) }
            coVerifyOnce { monitoringTriggerRecordService.update(any()) }
            coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
            coVerifyOnce { personService.get(any()) }

            coVerifyOnce { monitoringTriggerRecordActionService.create(any(), any(), any()) }

            coVerifyOrder {
                channelService.addChatV2(any())
                channelService.addStaff(any(), any(), any())
                monitoringTriggerRecordService.update(any())
                monitoringTriggerRecordActionService.create(any(), any(), any())
            }
        }

    @Test
    fun `#create should return error when channel creation fails`() = runBlocking {
        val expectedChatRequest = CreateChatRequest(
            personId = triggerRecord.personId,
            chatName = monitoringChannelName,
            origin = monitoringChannelOrigin,
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.LONGITUDINAL
        )

        coEvery { healthProfessionalService.findByStaffId(staffId) } returns healthProfessional.success()
        coEvery { personService.get(triggerRecord.personId) } returns person.success()
        coEvery { channelService.addChatV2(expectedChatRequest) } returns RuntimeException("some_error_occurred").failure()

        val result = service.create(triggerRecord, configuration)

        assertThat(result).isFailureOfType(RuntimeException::class)

        coVerifyOnce { channelService.addChatV2(any()) }
        coVerify { monitoringTriggerRecordService wasNot called }
        coVerify { monitoringTriggerRecordActionService wasNot called }
    }

    @Test
    fun `#create should return error when send text message fails`() = runBlocking {
        val expectedChatRequest = CreateChatRequest(
            personId = triggerRecord.personId,
            chatName = monitoringChannelName,
            origin = monitoringChannelOrigin,
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.LONGITUDINAL
        )

        coEvery { healthProfessionalService.findByStaffId(staffId) } returns healthProfessional.success()
        coEvery { personService.get(triggerRecord.personId) } returns person.success()
        coEvery { channelService.addChatV2(expectedChatRequest) } returns channelDocument.success()
        coEvery {
            channelService.sendTextMessage(channelDocument, channelInitialMessage, true)
        } returns RuntimeException("some_error_occurred").failure()

        val result = service.create(triggerRecord, configuration)

        assertThat(result).isFailureOfType(RuntimeException::class)

        coVerifyOnce { channelService.addChatV2(any()) }
        coVerifyOnce { channelService.sendTextMessage(any(), any(), any()) }
        coVerify { monitoringTriggerRecordService wasNot called }
        coVerify { monitoringTriggerRecordActionService wasNot called }
    }

}
