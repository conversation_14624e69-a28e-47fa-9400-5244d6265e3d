package br.com.alice.healthcondition.services.whatsapp

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class StaffEntitySearcherTest {

    private val staffService: StaffService = mockk<StaffService>()
    private val searcher = StaffEntitySearcher(staffService)

    @Test
    fun `given a StaffEntitySearcher, when searching by IDs, then it should return the correct staff members`() {
        // given
        val staff = TestModelFactory.buildStaff()

        coEvery { staffService.get(staff.id) } returns staff.success()

        // when
        val result = runBlocking { searcher.getById(staff.id.toString()) }

        // then
        assertThat(result).isSuccessWithData(staff)
        coVerifyOnce { staffService.get(staff.id) }
    }
}
