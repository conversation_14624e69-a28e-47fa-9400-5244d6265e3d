package br.com.alice.healthcondition.services.whatsapp

import br.com.alice.data.layer.helpers.TestModelFactory
import kotlin.test.Test
import kotlin.test.assertEquals

class StaffNameAttributeResolverTest {

    @Test
    fun `given a staff entity, when resolveAttribute is called, then it returns the full name`() {
        // given
        val staff = TestModelFactory.buildStaff(firstName = "John", lastName = "Doe")
        val resolver = StaffNameAttributeResolver()

        // when
        val name = resolver.resolveAttribute(staff)

        // then
        assertEquals("<PERSON>", name)
    }
}
