package br.com.alice.healthcondition.services.whatsapp

import br.com.alice.healthcondition.services.whatsapp.model.AvailableEntity
import com.github.kittinunf.result.success
import kotlin.test.Test
import kotlin.test.assertEquals

class EntitySearcherSelectorTest {

    private val personSearcher = EntitySearcher { ids -> (ids.map { "Person: $it" }).success() }

    private val entitySearcherSelector = EntitySearcherSelector(
        searchers = mapOf(AvailableEntity.PERSON to personSearcher)
    )

    @Test
    fun `given an available entity when getSearcher then returns the correct searcher`() {
        // given
        val entity = AvailableEntity.PERSON

        // when
        val searcher = entitySearcherSelector.getSearcher(entity)

        // then
        assertEquals(personSearcher, searcher)
    }

    @Test
    fun `given an unavailable entity when getSearcher then throws an exception`() {
        // given
        val entity = AvailableEntity.STAFF

        // when
        val exception = runCatching { entitySearcherSelector.getSearcher(entity) }.exceptionOrNull()

        // then
        assertEquals(IllegalArgumentException::class, exception!!::class)
        assertEquals("No searcher found for entity: $entity", exception.message)
    }

}
