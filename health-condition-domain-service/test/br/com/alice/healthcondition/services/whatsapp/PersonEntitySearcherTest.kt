package br.com.alice.healthcondition.services.whatsapp

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class PersonEntitySearcherTest {

    private val personService = mockk<PersonService>()
    private val searcher = PersonEntitySearcher(personService)

    @Test
    fun `given a PersonEntitySearcher, when searching by IDs, then it should return the correct persons`() {
        // given
        val person = TestModelFactory.buildPerson()

        coEvery { personService.get(person.id) } returns person.success()

        // when
        val result = runBlocking { searcher.getById(person.id.toString()) }

        // then
        assertThat(result).isSuccessWithData(person)
        coVerifyOnce { personService.get(person.id) }
    }
}
