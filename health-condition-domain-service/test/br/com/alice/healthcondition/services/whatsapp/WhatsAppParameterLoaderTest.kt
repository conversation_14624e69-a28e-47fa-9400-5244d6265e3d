package br.com.alice.healthcondition.services.whatsapp

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.Staff
import br.com.alice.healthcondition.services.whatsapp.model.AvailableEntity
import br.com.alice.healthcondition.services.whatsapp.model.EntityParameter
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class WhatsAppParameterLoaderTest {

    private val personEntitySearcher: EntitySearcher<Person> = mockk()
    private val staffEntitySearcher: EntitySearcher<Staff> = mockk()

    private val entitySearcherSelector = EntitySearcherSelector(
        mapOf(
            AvailableEntity.PERSON to personEntitySearcher,
            AvailableEntity.STAFF to staffEntitySearcher
        )
    )

    private val personNameAttributeResolver = PersonNameAttributeResolver()
    private val staffNameAttributeResolver = StaffNameAttributeResolver()
    private val firstNameAttributeResolver = EntityAttributeResolver<Staff> { it.firstName }

    private val entityAttributeResolverSelector = EntityAttributeResolverSelector(
        mapOf(
            "PERSON::name" to personNameAttributeResolver,
            "STAFF::name" to staffNameAttributeResolver,
            "STAFF::firstName" to firstNameAttributeResolver
        )
    )
    private val parameterLoader = WhatsAppParameterLoader(entitySearcherSelector, entityAttributeResolverSelector)

    @Test
    fun `given valid parameters, when retrieveParameters is called, then it should return the expected results`() {
        // given
        val staff = TestModelFactory.buildStaff(firstName = "John", lastName = "Doe")
        val person = TestModelFactory.buildPerson(firstName = "Jane", lastName = "Smith")

        val entityParameters = listOf(
            EntityParameter(domain = AvailableEntity.STAFF, attribute = "name", id = staff.id.toString()),
            EntityParameter(domain = AvailableEntity.PERSON, attribute = "name", id = person.id.toString()),
            EntityParameter(domain = AvailableEntity.STAFF, attribute = "firstName", id = staff.id.toString())
        )

        coEvery { staffEntitySearcher.getById(staff.id.toString()) } returns staff.success()
        coEvery { personEntitySearcher.getById(person.id.toString()) } returns person.success()

        // when
        val result = runBlocking { parameterLoader.retrieveParameters(entityParameters) }

        //then
        assertThat(result).isSuccessWithData(listOf("John Doe", "Jane Smith", "John"))

        coVerifyOnce { personEntitySearcher.getById(person.id.toString()) }
        coVerifyOnce { staffEntitySearcher.getById(staff.id.toString()) }
    }

    @Test
    fun `given an error retrieving entity, when retrieve parameters, then return failure`() {
        //given
        val entityParameters = listOf(EntityParameter(AvailableEntity.PERSON, "name", "12345"))

        coEvery { personEntitySearcher.getById(any()) } throws RuntimeException("Error retrieving person")

        // when
        val result = runBlocking { parameterLoader.retrieveParameters(entityParameters) }

        // then
        assertThat(result).isFailureOfType(RuntimeException::class)
    }
}
