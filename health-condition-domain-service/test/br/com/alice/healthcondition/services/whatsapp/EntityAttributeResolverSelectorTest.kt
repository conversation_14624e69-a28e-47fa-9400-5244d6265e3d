package br.com.alice.healthcondition.services.whatsapp

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.healthcondition.services.whatsapp.model.AvailableEntity
import br.com.alice.healthcondition.services.whatsapp.model.EntityParameter
import kotlin.test.Test

class EntityAttributeResolverSelectorTest {

    private val attributeResolvers = mapOf(
        "PERSON::name" to EntityAttributeResolver<String> { "resolvedValue" },
        "STAFF::crm" to EntityAttributeResolver { "anotherResolvedValue" }
    )

    private val entityAttributeResolverSelector = EntityAttributeResolverSelector(attributeResolvers)

    @Test
    fun `given a valid entity parameter when resolve attribute then returns the resolved attribute`() {
        // given
        val entityParameter = EntityParameter(AvailableEntity.PERSON, "name", "12345")

        // when
        val result = entityAttributeResolverSelector.resolveAttribute(entityParameter, "anyEntity")

        // then
        assertThat(result).isSuccess()
        assertThat(result).isSuccessWithData("resolvedValue")
    }

    @Test
    fun `given an invalid entity parameter when resolve attribute then returns an error`() {
        // given
        val entityParameter = EntityParameter(AvailableEntity.STAFF, "crma", "12345")

        // when
        val result = entityAttributeResolverSelector.resolveAttribute(entityParameter, "anyEntity")

        // then
        assertThat(result).isFailure()
        assertThat(result).isFailureOfType(IllegalArgumentException::class)
    }
}
