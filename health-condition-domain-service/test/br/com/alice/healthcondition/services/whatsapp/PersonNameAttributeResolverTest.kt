package br.com.alice.healthcondition.services.whatsapp

import br.com.alice.data.layer.helpers.TestModelFactory
import org.junit.jupiter.api.Assertions.assertEquals
import kotlin.test.Test

class PersonNameAttributeResolverTest {

    private val resolver = PersonNameAttributeResolver()

    @Test
    fun `given a PersonNameAttributeResolver, when resolving attribute, then it should return the full social name`() {
        // given
        val person = TestModelFactory.buildPerson(firstName = "John", lastName = "Doe")

        // when
        val result = resolver.resolveAttribute(person)

        // then
        assertEquals("<PERSON> Doe", result)
    }
}
