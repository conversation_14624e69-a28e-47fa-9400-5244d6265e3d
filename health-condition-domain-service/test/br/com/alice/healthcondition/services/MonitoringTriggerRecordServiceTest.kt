package br.com.alice.healthcondition.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.MonitoringTriggerFeatureType
import br.com.alice.data.layer.models.MonitoringTriggerRecord
import br.com.alice.data.layer.models.MonitoringTriggerRecordExtraData
import br.com.alice.data.layer.models.MonitoringTriggerRecordStatus
import br.com.alice.data.layer.services.MonitoringTriggerRecordDataService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDateTime
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class MonitoringTriggerRecordServiceTest {

    private val monitoringTriggerRecordDataService: MonitoringTriggerRecordDataService = mockk()

    private val service = MonitoringTriggerRecordService(
        monitoringTriggerRecordDataService
    )

    private val triggerRecord = MonitoringTriggerRecord(
        personId = PersonId(),
        triggerId = RangeUUID.generate(),
        externalTriggerId = "external_trigger_id",
        identifiedAt = LocalDateTime.now(),
        staffIds = listOf(RangeUUID.generate()),
        status = MonitoringTriggerRecordStatus.CREATED,
        currentStep = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
    )

    @Test
    fun `#findAllByExternalTriggerIds should get by external trigger ids successfully`() = runBlocking {
        coEvery {
            monitoringTriggerRecordDataService.find(queryEq {
                where { this.externalTriggerId.inList(listOf(triggerRecord.externalTriggerId)) }
            })
        } returns listOf(triggerRecord).success()

        val result = service.findAllByExternalTriggerIds(listOf(triggerRecord.externalTriggerId))

        assertThat(result).isSuccessWithData(listOf(triggerRecord))

        coVerifyOnce { monitoringTriggerRecordDataService.find(any()) }
    }

    @Test
    fun `#addAll should add all successfully`() = runBlocking {
        coEvery {
            monitoringTriggerRecordDataService.addList(listOf(triggerRecord))
        } returns listOf(triggerRecord).success()

        val result = service.addAll(listOf(triggerRecord))

        assertThat(result).isSuccessWithData(listOf(triggerRecord))

        coVerifyOnce { monitoringTriggerRecordDataService.addList(any()) }
    }

    @Test
    fun `#update should update successfully`() = runBlocking {

        coEvery {
            monitoringTriggerRecordDataService.update(triggerRecord)
        } returns triggerRecord.success()

        val result = service.update(triggerRecord)

        assertThat(result).isSuccessWithData(triggerRecord)

        coVerifyOnce { monitoringTriggerRecordDataService.update(any()) }
    }

    @Test
    fun `#findByChannelId should find by channel id successfully`() = runBlocking {
        val channelId = "channel_id"
        val triggerRecord = triggerRecord.copy(
            extraData = MonitoringTriggerRecordExtraData(channelId = channelId)
        )

        coEvery {
            monitoringTriggerRecordDataService.findOne(queryEq {
                where { this.channelId.eq(channelId) }
            })
        } returns triggerRecord.success()

        val result = service.findByChannelId(channelId)

        assertThat(result).isSuccessWithData(triggerRecord)

        coVerifyOnce { monitoringTriggerRecordDataService.findOne(any()) }
    }

    @Test
    fun `#findById should find by id successfully`() = runBlocking {
        coEvery {
            monitoringTriggerRecordDataService.get(triggerRecord.id)
        } returns triggerRecord.success()

        val result = service.findById(triggerRecord.id)

        assertThat(result).isSuccessWithData(triggerRecord)

        coVerifyOnce { monitoringTriggerRecordDataService.get(any()) }
    }

}
