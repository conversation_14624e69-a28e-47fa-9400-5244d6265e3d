package br.com.alice.healthcondition.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.MonitoringTriggerConfiguration
import br.com.alice.data.layer.models.MonitoringTriggerFeature
import br.com.alice.data.layer.models.MonitoringTriggerFeatureType
import br.com.alice.data.layer.services.MonitoringTriggerConfigurationDataService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class MonitoringTriggerConfigurationServiceTest {

    private val monitoringTriggerConfigurationDataService: MonitoringTriggerConfigurationDataService = mockk()

    private val service = MonitoringTriggerConfigurationService(
        monitoringTriggerConfigurationDataService
    )

    @Test
    fun `#getByName should get by name successfully`() = runBlocking {
        val triggerConfig = MonitoringTriggerConfiguration(
            name = "trigger_name",
            features = listOf(
                MonitoringTriggerFeature(
                    feature = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
                    order = 0
                )
            )
        )

        coEvery {
            monitoringTriggerConfigurationDataService.findOne(queryEq {
                where { this.name.eq(triggerConfig.name) }
            })
        } returns triggerConfig.success()

        val result = service.getByName(triggerConfig.name)

        assertThat(result).isSuccessWithData(triggerConfig)

        coVerifyOnce { monitoringTriggerConfigurationDataService.findOne(any()) }
    }

    @Test
    fun `#getByName should return error when is not found`() = runBlocking {
        val triggerName = "trigger_name"

        coEvery {
            monitoringTriggerConfigurationDataService.findOne(queryEq {
                where { this.name.eq(triggerName) }
            })
        } returns NotFoundException("not_found").failure()

        val result = service.getByName(triggerName)

        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { monitoringTriggerConfigurationDataService.findOne(any()) }
    }

    @Test
    fun `#get should get by id successfully`() = runBlocking {
        val triggerId = RangeUUID.generate()
        val triggerConfig = MonitoringTriggerConfiguration(
            id = triggerId,
            name = "trigger_name",
            features = listOf(
                MonitoringTriggerFeature(
                    feature = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
                    order = 0
                )
            )
        )

        coEvery {
            monitoringTriggerConfigurationDataService.get(triggerId)
        } returns triggerConfig.success()

        val result = service.get(triggerId)

        assertThat(result).isSuccessWithData(triggerConfig)

        coVerifyOnce { monitoringTriggerConfigurationDataService.get(any()) }
    }

    @Test
    fun `#get should return error when is not found`() = runBlocking {
        val triggerId = RangeUUID.generate()

        coEvery {
            monitoringTriggerConfigurationDataService.get(triggerId)
        } returns NotFoundException("trigger_not_found").failure()

        val result = service.get(triggerId)

        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { monitoringTriggerConfigurationDataService.get(any()) }
    }

    @Test
    fun `#create should create successfully`() = runBlocking {
        val triggerConfig = MonitoringTriggerConfiguration(
            name = "trigger_name",
            features = listOf(
                MonitoringTriggerFeature(
                    feature = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
                    order = 0
                )
            )
        )

        coEvery {
            monitoringTriggerConfigurationDataService.add(triggerConfig)
        } returns triggerConfig.success()

        val result = service.create(triggerConfig)

        assertThat(result).isSuccessWithData(triggerConfig)

        coVerifyOnce { monitoringTriggerConfigurationDataService.add(any()) }
    }

    @Test
    fun `#update should update successfully`() = runBlocking {
        val triggerConfig = MonitoringTriggerConfiguration(
            name = "trigger_name",
            features = listOf(
                MonitoringTriggerFeature(
                    feature = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
                    order = 0
                )
            )
        )

        coEvery {
            monitoringTriggerConfigurationDataService.update(triggerConfig)
        } returns triggerConfig.success()

        val result = service.update(triggerConfig)

        assertThat(result).isSuccessWithData(triggerConfig)

        coVerifyOnce { monitoringTriggerConfigurationDataService.update(any()) }
    }
}
