package br.com.alice.healthcondition.controller

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.data.layer.models.MonitoringTriggerAdditionalProperties
import br.com.alice.data.layer.models.MonitoringTriggerConfiguration
import br.com.alice.data.layer.models.MonitoringTriggerFeature
import br.com.alice.data.layer.models.MonitoringTriggerFeatureType
import br.com.alice.healthcondition.controllers.MonitoringTriggerConfigurationController
import br.com.alice.healthcondition.controllers.MonitoringTriggerConfigurationRequest
import br.com.alice.healthcondition.services.MonitoringTriggerConfigurationService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class MonitoringTriggerConfigurationControllerTest : RoutesTestHelper() {

    private val monitoringTriggerConfigurationService: MonitoringTriggerConfigurationService = mockk()

    private val controller = MonitoringTriggerConfigurationController(
        monitoringTriggerConfigurationService
    )

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { controller }
    }

    @Test
    fun `#create should create a monitoring trigger configuration`() = runBlocking {
        mockRangeUuidAndDateTime { _, _ ->
            val request = MonitoringTriggerConfigurationRequest(
                name = "trigger_name",
                features = listOf(
                    MonitoringTriggerFeature(
                        feature = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
                        order = 0
                    )
                ),
                additionalProperties = MonitoringTriggerAdditionalProperties(
                    channelInitialMessage = "channel_initial_message"
                )
            )

            val expected = MonitoringTriggerConfiguration(
                name = request.name,
                features = request.features.map { MonitoringTriggerFeature(it.feature, it.order) },
                additionalProperties = request.additionalProperties
            )

            coEvery { monitoringTriggerConfigurationService.create(expected) } returns expected.success()

            post("/internal/monitoring_trigger_configuration", request) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expected)

                coVerifyOnce { monitoringTriggerConfigurationService.create(any()) }
            }
        }
    }

    @Test
    fun `#get should get a monitoring trigger configuration`() = runBlocking {
        val id = RangeUUID.generate()
        val expected = MonitoringTriggerConfiguration(
            name = "trigger_name",
            features = listOf(
                MonitoringTriggerFeature(
                    feature = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
                    order = 0
                )
            ),
            additionalProperties = MonitoringTriggerAdditionalProperties(
                channelInitialMessage = "channel_initial_message"
            )
        )

        coEvery { monitoringTriggerConfigurationService.get(id) } returns expected.success()

        get("/internal/monitoring_trigger_configuration/$id") { response ->
            ResponseAssert.assertThat(response).isOKWithData(expected)

            coVerifyOnce { monitoringTriggerConfigurationService.get(any()) }
        }
    }

    @Test
    fun `#update should update a monitoring trigger configuration`() = runBlocking {
        mockRangeUuidAndDateTime { id, _ ->
            val request = MonitoringTriggerConfigurationRequest(
                name = "trigger_name",
                features = listOf(
                    MonitoringTriggerFeature(
                        feature = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
                        order = 0
                    )
                ),
                additionalProperties = MonitoringTriggerAdditionalProperties(
                    channelInitialMessage = "channel_initial_message"
                )
            )

            val current = MonitoringTriggerConfiguration(
                id = id,
                name = "trigger_name",
                features = listOf(
                    MonitoringTriggerFeature(
                        feature = MonitoringTriggerFeatureType.CREATE_CHANNEL,
                        order = 0
                    )
                )
            )

            val updated = MonitoringTriggerConfiguration(
                name = request.name,
                features = request.features.map { MonitoringTriggerFeature(it.feature, it.order) },
                additionalProperties = request.additionalProperties
            )

            coEvery { monitoringTriggerConfigurationService.get(id) } returns current.success()
            coEvery { monitoringTriggerConfigurationService.update(updated) } returns updated.success()

            put("/internal/monitoring_trigger_configuration/$id", request) { response ->
                ResponseAssert.assertThat(response).isOKWithData(updated)

                coVerifyOnce { monitoringTriggerConfigurationService.get(any()) }
                coVerifyOnce { monitoringTriggerConfigurationService.update(any()) }
            }
        }
    }

}
