package br.com.alice.healthcondition.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.MonitoringTriggerAdditionalProperties
import br.com.alice.data.layer.models.MonitoringTriggerConfiguration
import br.com.alice.data.layer.models.MonitoringTriggerFeature
import br.com.alice.data.layer.models.MonitoringTriggerFeatureType
import br.com.alice.data.layer.models.MonitoringTriggerRecord
import br.com.alice.data.layer.models.MonitoringTriggerRecordAction
import br.com.alice.data.layer.models.MonitoringTriggerRecordActionType
import br.com.alice.data.layer.models.MonitoringTriggerRecordExtraData
import br.com.alice.data.layer.models.MonitoringTriggerRecordStatus
import br.com.alice.data.layer.models.MonitoringTriggerWhatsAppTemplate
import br.com.alice.data.layer.models.MonitoringTriggerWhatsAppTemplateVariable
import br.com.alice.healthcondition.event.MonitoringTriggerRecordActionCreatedEvent
import br.com.alice.healthcondition.services.MonitoringTriggerConfigurationService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordActionService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordService
import br.com.alice.healthcondition.services.internal.SendWhatsAppMessageMonitoringService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class SendFirstMessageMonitoringTriggerConsumerTest : ConsumerTest() {

    private val mockMonitoringTriggerRecordService = mockk<MonitoringTriggerRecordService>()
    private val mockMonitoringTriggerConfigurationService = mockk<MonitoringTriggerConfigurationService>()
    private val mockMonitoringTriggerRecordActionService = mockk<MonitoringTriggerRecordActionService>()
    private val mockSendWhatsAppMessageMonitoringService = mockk<SendWhatsAppMessageMonitoringService>()
    private lateinit var consumer: SendFirstMessageMonitoringTriggerConsumer

    @BeforeTest
    fun setup() {
        consumer = SendFirstMessageMonitoringTriggerConsumer(
            monitoringTriggerRecordService = mockMonitoringTriggerRecordService,
            monitoringTriggerConfigurationService = mockMonitoringTriggerConfigurationService,
            monitoringTriggerRecordActionService = mockMonitoringTriggerRecordActionService,
            sendWhatsAppMessageMonitoringService = mockSendWhatsAppMessageMonitoringService
        )
    }

    @Test
    fun `given an invalid event, when handleMonitoringTriggerRecordActionCreated is called, then it should return false`() {
        // given
        val invalidEvent = MonitoringTriggerRecordActionCreatedEvent(
            monitoringAction = MonitoringTriggerRecordAction(
                personId = PersonId(),
                triggerId = RangeUUID.generate(),
                externalTriggerId = RangeUUID.generate().toString(),
                monitoringTriggerRecordId = RangeUUID.generate(),
                action = MonitoringTriggerRecordActionType.NEW_TRIGGER_IDENTIFIED
            )
        )

        // when
        val result = runBlocking { consumer.handleMonitoringTriggerRecordActionCreated(invalidEvent) }

        // then
        assertThat(result).isSuccessWithData(false)
        coVerifyNone {
            mockMonitoringTriggerRecordService.findById(any())
            mockMonitoringTriggerConfigurationService.get(any())
            mockSendWhatsAppMessageMonitoringService.sendMessage(any(), any())
            mockMonitoringTriggerRecordActionService.create(any(), any())
        }
    }

    @Test
    fun `given a valid event, when handleMonitoringTriggerRecordActionCreated is called, then it should process the event`() {
        // given
        val monitoringAction = MonitoringTriggerRecordAction(
            personId = PersonId(),
            triggerId = RangeUUID.generate(),
            externalTriggerId = RangeUUID.generate().toString(),
            monitoringTriggerRecordId = RangeUUID.generate(),
            action = MonitoringTriggerRecordActionType.WHATSAPP_MESSAGE_SENT
        )

        val triggerRecord = buildMonitoringTriggerRecord(monitoringAction)
        val event = MonitoringTriggerRecordActionCreatedEvent(monitoringAction)
        val triggerConfiguration = buildMonitoringTriggerConfiguration(triggerRecord)

        coEvery {
            mockMonitoringTriggerRecordService.findById(monitoringAction.id)
        } returns triggerRecord.success()

        coEvery {
            mockMonitoringTriggerConfigurationService.get(monitoringAction.triggerId)
        } returns triggerConfiguration.success()

        coEvery {
            mockSendWhatsAppMessageMonitoringService.sendMessage(triggerRecord, triggerConfiguration)
        } returns true.success()

        coEvery {
            mockMonitoringTriggerRecordService.update(
                data = triggerRecord.copy(
                    currentStep = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
                    status = MonitoringTriggerRecordStatus.CONCLUDED
                )
            )
        } returns triggerRecord.success()

        coEvery {
            mockMonitoringTriggerRecordActionService.create(triggerRecord, monitoringAction.action)
        } returns monitoringAction.success()

        // when
        val result = runBlocking { consumer.handleMonitoringTriggerRecordActionCreated(event) }

        // then
        assertThat(result).isSuccessWithData(true)
        coVerifyOnce { mockMonitoringTriggerRecordService.findById(monitoringAction.id) }
        coVerifyOnce { mockMonitoringTriggerConfigurationService.get(monitoringAction.triggerId) }
        coVerifyOnce { mockSendWhatsAppMessageMonitoringService.sendMessage(triggerRecord, triggerConfiguration) }
        coVerifyOnce { mockMonitoringTriggerRecordActionService.create(triggerRecord, monitoringAction.action) }
    }

    private fun buildMonitoringTriggerRecord(recordAction: MonitoringTriggerRecordAction) =
        MonitoringTriggerRecord(
            id = recordAction.id,
            personId = recordAction.personId,
            triggerId = recordAction.triggerId,
            externalTriggerId = recordAction.externalTriggerId,
            identifiedAt = LocalDateTime.now(),
            staffIds = listOf(RangeUUID.generate(), RangeUUID.generate()),
            status = MonitoringTriggerRecordStatus.PROCESSING,
            currentStep = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
            extraData = MonitoringTriggerRecordExtraData(channelId = "channelId"),
        )

    private fun buildMonitoringTriggerConfiguration(triggerRecord: MonitoringTriggerRecord) =
        MonitoringTriggerConfiguration(
            id = triggerRecord.triggerId,
            name = "coleta_pa",
            features = listOf(
                MonitoringTriggerFeature(MonitoringTriggerFeatureType.CREATE_CHANNEL, 0),
                MonitoringTriggerFeature(MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE, 1)
            ),
            additionalProperties = MonitoringTriggerAdditionalProperties(
                whatsAppTemplate = MonitoringTriggerWhatsAppTemplate(
                    name = "coleta_pa",
                    variables = listOf(
                        MonitoringTriggerWhatsAppTemplateVariable(
                            domain = "STAFF",
                            attribute = "name",
                        ),
                        MonitoringTriggerWhatsAppTemplateVariable(
                            domain = "PERSON",
                            attribute = "name",
                        ),
                    )
                )
            )
        )
}
