package br.com.alice.healthcondition.consumers

import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.notifier.ChannelArchivedByInactivityEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.extensions.toPersonId
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.MonitoringTriggerFeatureType
import br.com.alice.data.layer.models.MonitoringTriggerRecord
import br.com.alice.data.layer.models.MonitoringTriggerRecordAction
import br.com.alice.data.layer.models.MonitoringTriggerRecordActionExtraInfo
import br.com.alice.data.layer.models.MonitoringTriggerRecordActionType
import br.com.alice.data.layer.models.MonitoringTriggerRecordExtraData
import br.com.alice.data.layer.models.MonitoringTriggerRecordStatus
import br.com.alice.healthcondition.services.MonitoringTriggerRecordActionService
import br.com.alice.healthcondition.services.MonitoringTriggerRecordService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import java.time.LocalDateTime
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class ArchivedChannelMonitoringTriggerConsumerTest : ConsumerTest() {

    private val monitoringTriggerRecordService: MonitoringTriggerRecordService = mockk()
    private val monitoringTriggerRecordActionService: MonitoringTriggerRecordActionService = mockk()

    private val consumer = ArchivedChannelMonitoringTriggerConsumer(
        monitoringTriggerRecordService,
        monitoringTriggerRecordActionService
    )

    private val channel = ChannelDocument(
        id = "channelId",
        channelPersonId = "channelPersonId",
        personId = PersonId().toString(),
        kind = ChannelKind.CHAT,
        category = ChannelCategory.ASSISTANCE,
        subCategory = ChannelSubCategory.LONGITUDINAL
    )
    private val triggerRecord = MonitoringTriggerRecord(
        personId = PersonId(),
        triggerId = RangeUUID.generate(),
        staffIds = listOf(RangeUUID.generate()),
        externalTriggerId = "external_trigger_id",
        identifiedAt = LocalDateTime.now(),
        status = MonitoringTriggerRecordStatus.CONCLUDED,
        currentStep = MonitoringTriggerFeatureType.SEND_WHATSAPP_MESSAGE,
        extraData = MonitoringTriggerRecordExtraData(channelId = channel.id())
    )
    private val triggerRecordAction = MonitoringTriggerRecordAction(
        personId = triggerRecord.personId,
        triggerId = triggerRecord.triggerId,
        externalTriggerId = triggerRecord.externalTriggerId,
        monitoringTriggerRecordId = triggerRecord.id,
        action = MonitoringTriggerRecordActionType.CHANNEL_ARCHIVED_BY_INACTIVITY,
        extraInfo = MonitoringTriggerRecordActionExtraInfo(channelId = channel.id())
    )

    @BeforeTest
    fun confirm() = confirmVerified(
        monitoringTriggerRecordService,
        monitoringTriggerRecordActionService
    )

    @Test
    fun `#handleOnArchivedChannel should create action when trigger record is found`() = runBlocking {
        val event = ChannelArchivedByInactivityEvent(
            channel.id(),
            channel.personId.toPersonId(),
            channel
        )
        coEvery { monitoringTriggerRecordService.findByChannelId(channel.id()) } returns triggerRecord.success()
        coEvery {
            monitoringTriggerRecordActionService.create(
                triggerRecord,
                MonitoringTriggerRecordActionType.CHANNEL_ARCHIVED_BY_INACTIVITY,
                MonitoringTriggerRecordActionExtraInfo(channelId = channel.id())
            )
        } returns triggerRecordAction.success()

        val result = consumer.handleOnArchivedChannel(event)

        assertThat(result).isSuccessWithData(triggerRecordAction)

        coVerifyOnce { monitoringTriggerRecordService.findByChannelId(any()) }
        coVerifyOnce { monitoringTriggerRecordActionService.create(any(), any(), any()) }
    }

    @Test
    fun `#handleOnArchivedChannel should not process when trigger record is not found`() = runBlocking {
        val event = ChannelArchivedByInactivityEvent(
            channel.id(),
            channel.personId.toPersonId(),
            channel
        )
        coEvery { monitoringTriggerRecordService.findByChannelId(channel.id()) } returns NotFoundException().failure()

        val result = consumer.handleOnArchivedChannel(event)

        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { monitoringTriggerRecordService.findByChannelId(any()) }
        coVerifyNone { monitoringTriggerRecordActionService.create(any(), any(), any()) }
    }

    @Test
    fun `#handleOnArchivedChannel should not process when channel is invalid`() = runBlocking {
        val channel = channel.copy(subCategory = ChannelSubCategory.SCREENING)
        val event = ChannelArchivedByInactivityEvent(
            channel.id(),
            channel.personId.toPersonId(),
            channel
        )

        val result = consumer.handleOnArchivedChannel(event)

        assertThat(result).isSuccessWithData(false)

        coVerifyNone { monitoringTriggerRecordService.findByChannelId(any()) }
        coVerifyNone { monitoringTriggerRecordActionService.create(any(), any(), any()) }
    }
}
