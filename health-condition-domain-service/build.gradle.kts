plugins {
    kotlin
    application
    id("com.github.johnrengelman.shadow")
    id("com.google.cloud.tools.jib")
    id("org.sonarqube")
}

group = "br.com.alice.health-condition-domain-service"
version = aliceHealthConditionDomainServiceVersion

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    main {
        kotlin.sourceDirs = files("src", "${layout.buildDirectory}/generated/source/kotlin")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:health-condition-domain-service")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

tasks {
    shadowJar {
        isZip64 = true
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":common-kafka"))
    implementation(project(":common-service"))
    implementation(project(":common-clients"))
    implementation(project(":communication"))
    implementation(project(":channel-domain-client"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:product-domain-service-data-package"))
	implementation(project(":data-packages:person-domain-service-data-package"))
	implementation(project(":data-packages:channel-domain-service-data-package"))
	implementation(project(":data-packages:secondary-attention-domain-service-data-package"))
	implementation(project(":data-packages:appointment-domain-service-data-package"))
	implementation(project(":data-packages:questionnaire-domain-service-data-package"))
	implementation(project(":data-packages:schedule-domain-service-data-package"))
	implementation(project(":data-packages:wanda-domain-service-data-package"))
	implementation(project(":data-packages:health-condition-domain-service-data-package"))
    implementation(project(":data-packages:health-logic-domain-service-data-package"))
    implementation(project(":data-packages:clinical-account-domain-service-data-package"))
    implementation(project(":data-packages:staff-domain-service-data-package"))
    implementation(project(":data-packages:health-plan-domain-service-data-package"))
    implementation(project(":data-packages:ehr-domain-service-data-package"))
    implementation(project(":data-packages:dragon-radar-domain-service-data-package"))
    implementation(project(":data-packages:bud-domain-service-data-package"))
    implementation(project(":appointment-domain-client"))
    implementation(project(":feature-config-domain-client"))
    implementation(project(":health-condition-domain-client"))
    implementation(project(":membership-domain-client"))
    implementation(project(":schedule-domain-client"))
    implementation(project(":person-domain-client"))
    implementation(project(":questionnaire-domain-client"))
    implementation(project(":wanda-domain-client"))
    implementation(project(":health-plan-domain-client"))
    implementation(project(":staff-domain-client"))
    implementation(project(":secondary-attention-domain-client"))
    implementation(project(":clinical-account-domain-client"))

    implementation(platform("software.amazon.awssdk:bom:$awsSdkVersion"))
    implementation("software.amazon.awssdk:socialmessaging")

    ktor2Dependencies()

    testImplementation(project(":data-layer-common-tests"))
    testImplementation(project(":common-tests"))
    test2Dependencies()
    testImplementation("org.apache.commons:commons-csv:$commonsCsvVersion")

}
