package br.com.alice.schedule.model.googlecalendar

import br.com.alice.common.core.extensions.toCustomFormat
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.core.extensions.toWeekDayFullDateFormat
import java.time.LocalDateTime

data class GoogleCalendarEventDescription(
    val memberName: String,
    val eventName: String,
    val date: LocalDateTime,
    val title: String = "<b>Confirmação de agendamento</b><br><br>",
    val subTitle: String = "Oi $memberName. Tudo bem?<br>Separamos as informações mais importantes para o seu atendimento<br><br>",
    val details: String = "<b>$eventName</b><br>" +
            "${date.toSaoPauloTimeZone().toWeekDayFullDateFormat()} - Às ${date.toSaoPauloTimeZone().toCustomFormat("HH:mm")}<br><br><br>",
    val zoomLink: String,
    val locationDetails: String = "Consulta digital - via zoom<br>" +
            "Você pode acessar do seu computador, tablet ou smartphone.<br>" +
            "<a href=\"$zoomLink\">$zoomLink</a><br><br><br>",
    val additionalInformation: String = "<b>Como funcionam os agendamentos na Alice</b><br><br><br>" +
            "<b>Atrasos</b><br><br>" +
            "Após o início da consulta vamos te esperar por 10 minutos. Se você achar que vai atrasar um pouquinho mais, " +
            "é só avisar a gente no Alice Agora, ok?<br><br><br>" +
            "<b>Alterar o dia ou horário do agendamento</b><br><br>" +
            "Para fazer alterações, acesse a Agenda no menu do app. Depois toque no ícone com os três pontinhos " +
            "da consulta e escolha entre Reagendar ou Cancelar agendamento.",
    val fullDescription: String = title + subTitle + details + locationDetails + additionalInformation
)
