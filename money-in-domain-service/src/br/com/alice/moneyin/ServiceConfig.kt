package br.com.alice.moneyin

import br.com.alice.common.core.RunningMode
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotConfiguration
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object ServiceConfig {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    fun environment(): RunningMode {
        val environmentAsString = config.property("systemEnv").getString()
        return RunningMode.valueOf(environmentAsString.uppercase())
    }

    object Crm {
        fun hubspotConfig(): HubspotConfiguration {
            val apiBaseUrl = config.property("${environment().value}.crm.hubspot.baseUrl").getString()
            val apiKey = config.property("${environment().value}.crm.hubspot.apiKey").getString()
            val accessToken = config.property("${environment().value}.crm.hubspot.accessToken").getString()
            val dealPipeline = config.property("${environment().value}.crm.hubspot.dealPipeline").getString()
            val dealStage = config.property("${environment().value}.crm.hubspot.dealStage").getString()

            return HubspotConfiguration(apiKey, apiBaseUrl, accessToken, dealPipeline, dealStage)
        }
    }

    object Payment {
        fun invoiceUrl() = config.property("${environment().value}.payment.invoiceUrl").getString()

        object Iugu {
            val apiKey = config.property("${environment().value}.payment.iugu.apiKey").getString()
            val baseUrl = config.property("${environment().value}.payment.iugu.baseUrl").getString()
        }
    }

    object Mailer {
        val defaultSenderName = config.property("${environment().value}.mailer.senderName").getString()
        val defaultSenderEmail = config.property("${environment().value}.mailer.senderEmail").getString()

        val defaultB2BSenderName = config.property("${environment().value}.mailer.b2BSenderName").getString()
        val defaultB2BSenderEmail = config.property("${environment().value}.mailer.b2BSenderEmail").getString()

        val bccRecipientEmail = config.property("${environment().value}.mailer.bccRecipientEmail").getString()

        val pinPointCampaignId = config.property("${environment().value}.mailer.pinPoint.campaignId").getString()
    }

    object Auth {
        fun user() = config.property("${environment().value}.auth.user").getString()
        fun pass() = config.property("${environment().value}.auth.pass").getString()
    }

    val isProduction get() = environment().value == "prod"

}
