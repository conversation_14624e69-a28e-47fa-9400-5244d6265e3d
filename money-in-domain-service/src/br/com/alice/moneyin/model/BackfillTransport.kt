package br.com.alice.moneyin.model

import br.com.alice.data.layer.models.DestinationAccount
import br.com.alice.data.layer.models.InvoiceItemType
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class MemberInvoiceRequest(val values: List<UUID>)

data class InvoicePaymentSendEmailRequest(val invoicePaymentId: UUID, val billingAccountablePartyId: UUID?)

data class UpdateEmailRequest(
    val values: List<Value>
) {
    data class Value(
        val national_id: String,
        val email: String,
    )
}

data class UpdateMemberInvoiceItemRequest(
    val ids: List<UUID>,
    val oldInvoiceType: InvoiceItemType,
    val oldNotes: String,
    val newInvoiceType: InvoiceItemType,
    val newNotes: String? = null,
)

data class BindCompanyAndSubcontractToMemberInvoiceGroupRequest(
    val list: List<Payload>
) {
    data class Payload(
        val memberInvoiceGroupId: UUID,
        val companyId: UUID,
        val subcontractId: UUID
    )
}

data class CancelExpiredInvoicePaymentsRequest(
    val startDate: LocalDate,
    val endDate: LocalDate,
    val limit: Int,
    val offset: Int
)

data class IntRangeRequest(
    val start: Int,
    val end: Int
)

data class IdsRequest(
    val ids: List<UUID>,
)

data class AssociateMemberInvoicesInMemberInvoiceGroupRequest(
    val memberInvoiceGroupId: UUID,
    val memberInvoiceIds: List<UUID>?
)

data class RecalculateInvoiceBreakdownRequest(
    val ids: List<UUID>,
    val type: List<InvoiceItemType>
)

data class ChangeMemberInvoiceGroupOwnerRequest(
    val items: Map<UUID, Item>
) {
    data class Item(
        val companyId: UUID,
        val subcontractId: UUID,
    )
}


data class CreateInvoicePixDetail(
    val paymentId: UUID,
    val paymentUrl: String?,
    val dueDate: LocalDateTime?,
    val paymentCode: String?
)

data class RequestReissueInvoicePaymentInBatch(
    val dueDate: LocalDate,
    val paymentIds: List<UUID>,
    val sendEmail: Boolean = false,
)

data class RequestUpdateInvoicePaymentAmount(
    val page: Int = 0,
    val limit: Int = 15
)

data class InternalFeatureResponse(
    val successCount: Int = 0,
    val errorsCount: Int = 0,
    val message: String? = null,
    val additionalInfo: Map<String, Any?>? = null
)

data class UpdateMemberInvoiceStatusRequest(
    val ids: List<UUID>,
    val sendEvent: Boolean,
    val status: InvoiceStatus
)

data class UpdateMemberInvoiceGroupStatusRequest(
    val ids: List<UUID>,
    val sendEvent: Boolean,
    val status: MemberInvoiceGroupStatus
)

data class UpdateInvoicePaymentBankAccountInfoRequest(
    val ids: List<UUID>,
    val destinationAccount: DestinationAccount
)

data class SendTemplateToEmail(
    val email: String,
    val template: String,
    val params: Map<String, String>
)



