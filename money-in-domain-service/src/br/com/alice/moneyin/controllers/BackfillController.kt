package br.com.alice.moneyin.controllers

import br.com.alice.common.PaymentMethod
import br.com.alice.common.Response
import br.com.alice.common.asyncLayer
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.pmapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.toResponse
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.communication.email.model.EmailAttachment
import br.com.alice.data.layer.MONEY_IN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.BolepixPaymentDetail
import br.com.alice.data.layer.models.BoletoPaymentDetail
import br.com.alice.data.layer.models.CancellationReason
import br.com.alice.data.layer.models.InvoiceItemOperation
import br.com.alice.data.layer.models.InvoiceItemType
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.InvoicePaymentOrigin
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.MemberInvoiceGroup
import br.com.alice.data.layer.models.MemberInvoiceGroupStatus
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.models.PixPaymentDetail
import br.com.alice.moneyin.builder.InvoicePaymentBuilder
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.InvoicePaymentService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.communication.InvoiceMailer
import br.com.alice.moneyin.converters.InvoiceItemListConverter.toInvoiceBreakdown
import br.com.alice.moneyin.model.AssociateMemberInvoicesInMemberInvoiceGroupRequest
import br.com.alice.moneyin.model.BindCompanyAndSubcontractToMemberInvoiceGroupRequest
import br.com.alice.moneyin.model.ChangeMemberInvoiceGroupOwnerRequest
import br.com.alice.moneyin.model.CreateInvoicePixDetail
import br.com.alice.moneyin.model.IdsRequest
import br.com.alice.moneyin.model.InternalFeatureResponse
import br.com.alice.moneyin.model.InvoicePaymentSendEmailRequest
import br.com.alice.moneyin.model.MemberInvoiceRequest
import br.com.alice.moneyin.model.RecalculateInvoiceBreakdownRequest
import br.com.alice.moneyin.model.RequestReissueInvoicePaymentInBatch
import br.com.alice.moneyin.model.SendTemplateToEmail
import br.com.alice.moneyin.model.UpdateEmailRequest
import br.com.alice.moneyin.model.UpdateInvoicePaymentBankAccountInfoRequest
import br.com.alice.moneyin.model.UpdateMemberInvoiceGroupStatusRequest
import br.com.alice.moneyin.model.UpdateMemberInvoiceItemRequest
import br.com.alice.moneyin.model.UpdateMemberInvoiceStatusRequest
import br.com.alice.moneyin.services.MoneyInResourceSignTokenServiceImpl
import br.com.alice.moneyin.services.PaymentDetailService
import br.com.alice.moneyin.services.internal.AcquirerOrchestratorService
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.lift
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.math.BigDecimal
import java.time.LocalDate
import java.util.Base64
import java.util.concurrent.atomic.AtomicInteger

class BackfillController(
    private val billingAccountablePartyService: BillingAccountablePartyService,
    private val invoicesService: InvoicesService,
    private val memberInvoiceGroupService: MemberInvoiceGroupService,
    private val invoicePaymentService: InvoicePaymentService,
    private val invoiceMailer: InvoiceMailer,
    private val paymentDetailService: PaymentDetailService,
    private val memberService: MemberService,
    private val moneyInResourceSignTokenServiceImpl: MoneyInResourceSignTokenServiceImpl,
    private val acquirerOrchestratorService: AcquirerOrchestratorService
) : Controller() {

    private val attachment = readResource("example-file.pdf")
    private suspend fun withBackfillEnvironment(func: suspend () -> Response) =
        asyncLayer {
            withRootServicePolicy(MONEY_IN_ROOT_SERVICE_NAME) {
                withUnauthenticatedTokenWithKey(MONEY_IN_ROOT_SERVICE_NAME) {
                    func.invoke()
                }
            }
        }

    private fun encodeBase64(data: String): String {
        return Base64.getEncoder().encodeToString(data.toByteArray())
    }

    suspend fun expireMoneyInInvoicePaymentLink(request: IdsRequest) =
        request.ids.pmap {
            moneyInResourceSignTokenServiceImpl.softDeleteSignToken(it)
        }.lift().toResponse()

    suspend fun backfillStatusMig(request: IdsRequest): Response {
        return memberInvoiceGroupService.getByIds(request.ids)
            .mapEach { mig ->
                val invoicePayments = invoicePaymentService.getByInvoiceGroupIds(listOf(mig.id)).get()
                if (invoicePayments.size == 1) {
                    val invoicePayment = invoicePayments.first()
                    if (invoicePayment.status == InvoicePaymentStatus.APPROVED) {
                        memberInvoiceGroupService.update(
                            mig.copy(status = MemberInvoiceGroupStatus.PAID)
                        ).get()
                    }
                }
            }.toResponse()
    }

    suspend fun cancelMemberInvoice(request: MemberInvoiceRequest): Response =
        withBackfillEnvironment {
            logger.info(
                "Starting backfill - BackFillController::cancelMemberInvoice",
                "values" to request.values,
            )

            request.values.pmap { invoiceId ->
                invoicesService.cancel(
                    invoiceId,
                    CancellationReason.PAYMENT_PROCESSOR_CANCELED,
                    forceCancellation = true
                )
                    .fold({ invoiceId to "" }, { invoiceId to it.message!! })
            }.toResponse()
        }

    suspend fun backfillTriggerMemberInvoiceGroupUpdate(request: AssociateMemberInvoicesInMemberInvoiceGroupRequest): Response =
        withBackfillEnvironment {
            logger.info(
                "Starting backfill - BackFillController::backfillTriggerMemberInvoiceGroupUpdate",
            )
            memberInvoiceGroupService.get(request.memberInvoiceGroupId)
                .flatMap {
                    memberInvoiceGroupService.update(
                        it.copy(
                            memberInvoiceIds = request.memberInvoiceIds ?: it.memberInvoiceIds
                        )
                    )
                }.toResponse()
        }

    suspend fun updateBillingAccountablePartyEmailByNationalId(request: UpdateEmailRequest): Response =
        withBackfillEnvironment {
            logger.info(
                "Starting backfill - BackFillController::updateBillingAccountablePartyEmailByNationalId",
                "values" to request.values.map { it.national_id }
            )

            val valuesGrouped = request.values.associate { it.national_id to it.email }
            val nationalIds = valuesGrouped.keys.toList()
            billingAccountablePartyService.findByNationalIds(nationalIds)
                .then {
                    logger.info(
                        "Billing accountable found",
                        "ids" to it.map { billingAccountableParty -> billingAccountableParty.id },
                        "size" to it.size,
                    )
                }
                .pmapEach { billingAccountableParty ->
                    val oldEmail = billingAccountableParty.email

                    billingAccountablePartyService.update(
                        billingAccountableParty.copy(email = valuesGrouped.getValue(billingAccountableParty.nationalId))
                    ).map {
                        logger.info(
                            "updated successfully",
                            "id" to it.id,
                            "national_id" to it.nationalId,
                            "old_email" to encodeBase64(oldEmail),
                            "new_email" to encodeBase64(it.email),
                        )
                        it.nationalId to ""
                    }.flatMapError { Pair(billingAccountableParty.nationalId, it.message!!).success() }
                        .get()
                }.foldResponse()
        }

    suspend fun cancelMemberInvoiceGroup(request: MemberInvoiceRequest): Response =
        withBackfillEnvironment {
            logger.info(
                "Starting backfill - BackFillController::cancelMemberInvoiceGroup",
                "values" to request.values,
            )

            request.values.pmap { invoiceGroupId ->
                memberInvoiceGroupService.cancelById(invoiceGroupId)
                    .fold({ invoiceGroupId to "" }, { invoiceGroupId to it.message!! })
            }.toResponse()
        }

    suspend fun resendInvoiceEmail(request: InvoicePaymentSendEmailRequest): Response =
        withBackfillEnvironment {
            invoicePaymentService.get(request.invoicePaymentId, true)
                .flatMap { invoiceMailer.send(it, it.reason!!, request.billingAccountablePartyId) }
                .toResponse()
        }

    suspend fun updateMemberInvoiceItemList(request: UpdateMemberInvoiceItemRequest): Response =
        withBackfillEnvironment {
            logger.info(
                "Starting backfill - BackFillController::updateMemberInvoiceItemList",
                "ids" to request.ids.map { it }
            )

            invoicesService.findInvoicesByIds(request.ids)
                .pmapEach { memberInvoice ->
                    val items = memberInvoice.invoiceItems?.map { item ->
                        if (item.type == request.oldInvoiceType && item.notes == request.oldNotes) {
                            item.copy(
                                type = request.newInvoiceType,
                                notes = request.newNotes ?: item.notes,
                            )
                        } else {
                            item
                        }
                    }

                    val invoiceBreakdown = items?.toInvoiceBreakdown()?.get()

                    invoicesService.update(
                        memberInvoice.copy(
                            invoiceItems = items,
                            invoiceBreakdown = invoiceBreakdown
                        )
                    )
                }.foldResponse()
        }

    suspend fun bindCompanySubcontractToMemberInvoiceGroup(request: BindCompanyAndSubcontractToMemberInvoiceGroupRequest): Response =
        withBackfillEnvironment {
            logger.info(
                "Starting backfill - BackFillController::bindCompanySubcontractToMemberInvoiceGroup",
                "list" to request.list.map { it }
            )

            val memberInvoiceGroupIds = request.list.map { it.memberInvoiceGroupId }
            val dataMapped = request.list.associateBy { it.memberInvoiceGroupId }

            memberInvoiceGroupService.getByIds(memberInvoiceGroupIds)
                .pmapEach {
                    val data = dataMapped[it.id]!!

                    val memberInvoiceGroup =
                        it.copy(companyId = data.companyId, companySubcontractId = data.subcontractId)

                    memberInvoiceGroupService.update(memberInvoiceGroup)
                }.foldResponse()
        }

    suspend fun recalculateInvoiceBreakdown(request: RecalculateInvoiceBreakdownRequest): Response =
        withBackfillEnvironment {
            logger.info(
                "Starting backfill - BackFillController::recalculateInvoiceBreakdown",
                "member_invoice_ids" to request.ids,
            )
            invoicesService.findInvoicesByIds(request.ids).then { memberInvoices ->

                memberInvoices.forEach { memberInvoice ->
                    var breakdown = memberInvoice.invoiceBreakdown

                    breakdown?.let { it ->
                        it.invoiceItems?.forEach { item ->

                            val value = calculateValue(item.absoluteValue!!, item.operation)

                            request.type.forEach { type ->
                                if (type == item.type && item.type == InvoiceItemType.OPERATIONAL_ADJUSTMENT) {
                                    breakdown = breakdown!!.copy(
                                        operationalAdjustment = breakdown!!.operationalAdjustment!!.plus(value),
                                        discount = breakdown!!.discount!!.plus(if (item.operation == InvoiceItemOperation.DISCOUNT) value else BigDecimal.ZERO),
                                        addition = breakdown!!.addition!!.plus(if (item.operation == InvoiceItemOperation.CHARGE) value else BigDecimal.ZERO),
                                    )
                                }
                            }

                            breakdown = breakdown!!.copy(totalAmount = breakdown!!.totalAmount.plus(value))

                        }

                    }

                    val updatedMemberInvoice =
                        memberInvoice.copy(invoiceBreakdown = breakdown, totalAmount = breakdown!!.totalAmount)
                    invoicesService.update(updatedMemberInvoice)

                    logger.info(
                        "BackFillController::updatedMemberInvoice",
                        "member_invoice_id" to updatedMemberInvoice.id,
                        "total_amount" to updatedMemberInvoice.totalAmount,
                    )

                }

            }

            logger.info(
                "Ending backfill - BackFillController::recalculateInvoiceBreakdown",
                "member_invoice_ids" to request.ids,
            )

            true.toResponse()
        }

    private fun calculateValue(value: BigDecimal, operation: InvoiceItemOperation) =
        if (operation == InvoiceItemOperation.DISCOUNT)
            value.negate()
        else value

    suspend fun changeMemberInvoiceGroupOwner(request: ChangeMemberInvoiceGroupOwnerRequest) = withBackfillEnvironment {
        logger.info(
            "Starting backfill - BackFillController::moveMemberInvoiceGroupOwner",
            "member_invoice_ids" to request.items,
        )

        val migIds = request.items.keys.toList()
        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errors = mutableMapOf<String, String>()

        memberInvoiceGroupService.getByIds(migIds).pmapEach { mig ->
            val newOwner = request.items.getValue(mig.id)

            memberInvoiceGroupService.update(
                mig.copy(
                    companySubcontractId = newOwner.subcontractId,
                    companyId = newOwner.companyId,
                )
            ).then {
                successCount.incrementAndGet()
            }.thenError {
                logger.error("Error updating mig", it)
                errors[mig.id.toString()] = it.message ?: "ERROR"
                errorsCount.incrementAndGet()
            }
        }.foldResponse()
    }

    suspend fun createInvoicePixDetails(request: CreateInvoicePixDetail) = withBackfillEnvironment {
        logger.info(
            "Starting backfill - BackFillController::createInvoicePixDetails",
            "payment_id" to request.paymentId,
        )

        paymentDetailService.createPaymentDetail(
            PixPaymentDetail(
                paymentId = request.paymentId,
                paymentUrl = request.paymentUrl,
                paymentCode = request.paymentCode,
                dueDate = request.dueDate,
            )
        ).then {
            true.success()
        }.thenError {
            logger.error("Error createInvoicePixDetails", it)
        }.foldResponse()
    }

    suspend fun fillNewFieldsPaymentDetail(request: IdsRequest): Response = withBackfillEnvironment {
        logger.info("Starting backfill - BackFillController::updatePaymentDetail")

        request.ids.pmap { ids ->
            invoicePaymentService.get(ids, withPaymentDetails = true)
                .flatMapPair { acquirerOrchestratorService.get(it) }
                .flatMap { (acquirerResponse, invoicePayment) ->
                    val paymentDetail = invoicePayment.paymentDetail!!
                    val updatedPaymentDetail = when (paymentDetail) {
                        is PixPaymentDetail -> {
                            paymentDetail.copy(
                                qrCode = acquirerResponse.pix?.qrCodeBase64,
                                copyAndPaste = acquirerResponse.pix?.qrCodeBase64
                            )
                        }

                        is BoletoPaymentDetail -> {
                            paymentDetail.copy(
                                barcode = acquirerResponse.bankSlip?.barcodeData,
                                digitableLine = acquirerResponse.bankSlip?.digitableLine
                            )
                        }

                        is BolepixPaymentDetail -> {
                            paymentDetail.copy(
                                pixQrCode = acquirerResponse.pix?.qrCodeBase64,
                                pixCopyAndPaste = acquirerResponse.pix?.copyAndPaste,
                                bankSlipBarcodeData = acquirerResponse.bankSlip?.barcodeData,
                                bankSlipDigitableLine = acquirerResponse.bankSlip?.digitableLine
                            )
                        }

                        else -> paymentDetail
                    }
                    paymentDetailService.updatePaymentDetail(updatedPaymentDetail)
                }
        }.lift().foldResponse()
    }

    suspend fun sendTemplateToEmail(request: SendTemplateToEmail) = withBackfillEnvironment {

        val recipientParams = request.email.split("@")

        logger.info(
            "Starting backfill - BackFillController::sendTemplateToEmail",
            "params" to request.params, "recipient_owner" to recipientParams.first(),
            "recipient_domain" to recipientParams.last(), "template" to request.template,
        )

        val attachments =
            listOf(EmailAttachment(fileName = "example-file.pdf", content = attachment, type = "application/pdf"))

        invoiceMailer.sendTemplateToEmail(
            email = request.email,
            template = request.template,
            params = request.params,
            attachments = attachments
        )
            .toResponse()
    }

    suspend fun reissueInvoicePaymentInBatch(request: RequestReissueInvoicePaymentInBatch) = withBackfillEnvironment {
        logger.info(
            "Starting backfill - BackFillController::reissueInvoicePaymentInBatch",
            "payment_id" to request.paymentIds,
            "due_date" to request.dueDate,
            "send_email" to request.sendEmail,
        )

        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errors = mutableMapOf<String, String>()

        request.paymentIds.pmap { paymentId ->
            invoicePaymentService.cancel(paymentId, CancellationReason.OVERDUE)
                .map { payment ->
                    if (payment.invoiceGroupId != null) {
                        createPaymentFromInvoiceGroup(payment, request.dueDate, request.sendEmail)
                    } else {
                        createPaymentFromMember(payment, request.dueDate, request.sendEmail)
                    }
                }
                .then {
                    successCount.incrementAndGet()
                }.thenError {
                    logger.error("Error updating mig", it)
                    errors[paymentId.toString()] = it.message ?: "ERROR"
                    errorsCount.incrementAndGet()
                }.fold({ paymentId to "" }, { paymentId to (it.message ?: "error") })
        }.toResponse()
    }

    suspend fun removeNullDiscountValueFromMemberInvoiceGroups(request: BackfillWithLimit) = withBackfillEnvironment {
        logger.info("Starting backfill - BackFillController::removeNullValuesFromMemberInvoiceGroup")

        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errors = mutableMapOf<String, String>()

        memberInvoiceGroupService.findWithDiscountNull(request.limit).pmapEach { memberInvoiceGroup ->
            memberInvoiceGroupService.update(memberInvoiceGroup.copy(discount = BigDecimal.ZERO)).then {
                successCount.incrementAndGet()
            }.thenError {
                logger.error("Error updating memberInvoiceGroup", "member_invoice_group_id" to memberInvoiceGroup.id)

                errors[memberInvoiceGroup.id.toString()] = it.message ?: "ERROR"
                errorsCount.incrementAndGet()
            }
        }

        Response(
            HttpStatusCode.OK,
            BackfillResponse(
                successCount = successCount.get(),
                errorsCount = errorsCount.get(),
                additionalInfo = mapOf("errors" to errors)
            )
        )
    }

    private suspend fun createPaymentFromInvoiceGroup(
        payment: InvoicePayment,
        dueDate: LocalDate,
        shouldSendEmail: Boolean
    ) =
        coroutineScope {
            logger.info("reissueInvoicePaymentInBatch::createPaymentFromInvoiceGroup")

            val memberInvoicesDef =
                async {
                    invoicesService.listByMemberInvoiceGroupId(payment.invoiceGroupId!!).map {
                        it.ifEmpty {
                            logger.info(
                                "Finding member invoices by member_invoice_ids from InvoicePayment",
                            )
                            invoicesService.findInvoicesByIds(payment.memberInvoiceIds).get()
                        }
                    }.get()
                }

            val billingAccountablePartyDef =
                async { billingAccountablePartyService.get(payment.billingAccountablePartyId!!).get() }
            val memberInvoiceGroupDef =
                async { memberInvoiceGroupService.get(payment.invoiceGroupId!!).get() }

            val memberInvoices = memberInvoicesDef.await()
            val billingAccountableParty = billingAccountablePartyDef.await()
            val memberInvoiceGroup = memberInvoiceGroupDef.await()

            val method =
                if (payment.reason == PaymentReason.B2B_REGULAR_PAYMENT || payment.reason == PaymentReason.B2B_FIRST_PAYMENT) PaymentMethod.BOLEPIX else PaymentMethod.BOLETO

            invoicePaymentService.createInvoicePaymentForMemberInvoices(
                InvoicePaymentService.CreateInvoicePaymentForMemberInvoicesPayload(
                    method,
                    memberInvoices,
                    dueDate = dueDate.atEndOfTheDay(),
                    billingAccountableParty,
                    payment.reason!!,
                    memberInvoiceGroup.copy(type = null),
                    null,
                    InvoicePaymentOrigin.ISSUED_BY_BACKFILL,
                    paymentUrl = null,
                    externalId = null,
                    sendEmail = shouldSendEmail,
                )
            )
        }

    private suspend fun createPaymentFromMember(
        canceledPayment: InvoicePayment,
        dueDate: LocalDate,
        shouldSendEmail: Boolean
    ) =
        coroutineScope {
            logger.info("reissueInvoicePaymentInBatch::createPaymentFromMember")

            val memberInvoicesDef =
                async { invoicesService.findInvoicesByIds(canceledPayment.memberInvoiceIds).get() }

            val billingAccountablePartyDef =
                async { billingAccountablePartyService.get(canceledPayment.billingAccountablePartyId!!).get() }

            val memberInvoices = memberInvoicesDef.await()
            val billingAccountableParty = billingAccountablePartyDef.await()
            val member = memberService.get(memberInvoices.first().memberId).get()

            val method =
                if (canceledPayment.reason == PaymentReason.B2B_REGULAR_PAYMENT || canceledPayment.reason == PaymentReason.B2B_FIRST_PAYMENT) PaymentMethod.BOLEPIX else PaymentMethod.BOLETO

            val payment = InvoicePaymentBuilder.buildPendingInvoicePaymentWithDetails(
                method,
                memberInvoices,
                dueDate.atEndOfTheDay(),
                canceledPayment.reason ?: PaymentReason.REGULAR_PAYMENT,
                billingAccountableParty,
                canceledPayment.amount,
                InvoicePaymentOrigin.ISSUED_BY_BACKFILL,
                null,
            ).copy(sendEmail = shouldSendEmail)

            invoicePaymentService.createInvoicePayment(
                payment,
                member
            )
        }

    suspend fun updateMemberInvoiceStatus(request: UpdateMemberInvoiceStatusRequest): Response {
        logger.info(
            "Starting backfill - BackFillController::updateMemberInvoiceStatus",
            "ids" to request.ids,
            "status" to request.status,
        )

        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errors = mutableMapOf<String, String>()

        invoicesService.findInvoicesByIds(request.ids).pmapEach { memberInvoice ->
            if (request.sendEvent) {
                when (request.status) {
                    InvoiceStatus.CANCELED -> invoicesService.cancel(memberInvoice.id, CancellationReason.INVALID, true)
                    InvoiceStatus.PAID -> invoicesService.markAsPaid(memberInvoice.id)
                    else -> invoicesService.update(
                        memberInvoice.copy(status = request.status)
                    )
                }
            } else {
                invoicesService.update(
                    memberInvoice.copy(status = request.status)
                )
            }.then {
                successCount.incrementAndGet()
            }.thenError {
                logger.error("Error updating memberInvoice", it)
                errors[memberInvoice.id.toString()] = it.message ?: "ERROR"
                errorsCount.incrementAndGet()
            }
        }.getOrNull()

        return Response(
            HttpStatusCode.OK,
            InternalFeatureResponse(
                successCount = successCount.get(),
                errorsCount = errorsCount.get(),
                additionalInfo = mapOf("errors" to errors)
            )
        )
    }

    suspend fun updateInvoicePaymentDestinationAccount(request: UpdateInvoicePaymentBankAccountInfoRequest) = withBackfillEnvironment {
        invoicePaymentService.listInvoicePaymentsById(request.ids)
            .pmapEach { invoicePaymentService.update(it.copy(destinationAccount = request.destinationAccount)) }
            .foldResponse()
    }

    suspend fun updateMemberInvoiceGroupStatus(request: UpdateMemberInvoiceGroupStatusRequest): Response {
        logger.info(
            "Starting backfill - BackFillController::updateMemberInvoiceGroupStatus",
            "ids" to request.ids,
            "status" to request.status,
        )

        val successCount = AtomicInteger(0)
        val errorsCount = AtomicInteger(0)
        val errors = mutableMapOf<String, String>()

        memberInvoiceGroupService.getByIds(request.ids).pmapEach { memberInvoiceGroup ->
            if (request.sendEvent) {
                when (request.status) {
                    MemberInvoiceGroupStatus.CANCELED -> memberInvoiceGroupService.cancelById(memberInvoiceGroup.id)
                    MemberInvoiceGroupStatus.PAID -> markMemberInvoiceGroupAsPaid(memberInvoiceGroup)
                    MemberInvoiceGroupStatus.PROCESSED -> memberInvoiceGroupService.markAsProcessed(memberInvoiceGroup)
                    else -> memberInvoiceGroupService.update(
                        memberInvoiceGroup.copy(status = request.status)
                    )
                }
            } else {
                memberInvoiceGroupService.update(
                    memberInvoiceGroup.copy(status = request.status)
                )
            }.then {
                successCount.incrementAndGet()
            }.thenError {
                logger.error("Error updating memberInvoiceGroup", it)
                errors[memberInvoiceGroup.id.toString()] = it.message ?: "ERROR"
                errorsCount.incrementAndGet()
            }
        }.getOrNull()

        return Response(
            HttpStatusCode.OK,
            InternalFeatureResponse(
                successCount = successCount.get(),
                errorsCount = errorsCount.get(),
                additionalInfo = mapOf("errors" to errors)
            )
        )
    }

    private suspend fun markMemberInvoiceGroupAsPaid(memberInvoiceGroup: MemberInvoiceGroup) =
        invoicePaymentService.getLastByInvoiceGroupId(memberInvoiceGroup.id, false)
            .flatMap { invoicePayment ->
                memberInvoiceGroupService.markAsPaid(memberInvoiceGroup, invoicePayment)
            }

    private fun readResource(resource: String) = javaClass.classLoader.getResource(resource)!!.readBytes()
}

data class BackfillResponse(
    val successCount: Int,
    val errorsCount: Int,
    val additionalInfo: Map<String, Any?>? = null,
)

data class BackfillWithLimit(
    val limit: Int = 100
)
