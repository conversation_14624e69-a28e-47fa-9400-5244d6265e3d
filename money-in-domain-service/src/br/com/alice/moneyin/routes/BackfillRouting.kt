package br.com.alice.moneyin.routes

import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.moneyin.controllers.BackfillController
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Routing.backfillRouting() {
    val backFillController by inject<BackfillController>()

    authenticate {
        route("backfill") {
            post("/update_billing_accountable_party_id_by_national_id") { coHandler(backFillController::updateBillingAccountablePartyEmailByNationalId) }
            post("/cancel_member_invoice") { coHandler(backFillController::cancelMemberInvoice) }
            post("/triger_member_invoice_group_update") { coHandler(backFillController::backfillTriggerMemberInvoiceGroupUpdate) }
            post("/cancel_member_invoice_group") { coHandler(backFillController::cancelMemberInvoiceGroup) }
            post("/update_member_invoice_items") { coHandler(backFillController::updateMemberInvoiceItemList) }
            post("/bind_company_subcontract_to_member_invoice_group") { coHandler(backFillController::bindCompanySubcontractToMemberInvoiceGroup) }
            post("/soft_delete_money_in_sign_token") { coHandler(backFillController::expireMoneyInInvoicePaymentLink) }
            route("member_invoice") {
                post("/recalculate_invoice_breakdown") { coHandler(backFillController::recalculateInvoiceBreakdown) }
                post("update_status") { coHandler(backFillController::updateMemberInvoiceStatus) }
            }

            route("member_invoice_group") {
                post("/backfill_status") { coHandler(backFillController::backfillStatusMig) }
                post("/change_owner") { coHandler(backFillController::changeMemberInvoiceGroupOwner) }
                post("update_status") { coHandler(backFillController::updateMemberInvoiceGroupStatus) }
                post("/remove_null_discount_value") { coHandler(backFillController::removeNullDiscountValueFromMemberInvoiceGroups) }
            }

            route("invoice_payment") {
                post("/update_destination_account") { coHandler(backFillController::updateInvoicePaymentDestinationAccount) }
                post("/create_pix_invoice_details") { coHandler(backFillController::createInvoicePixDetails) }
                post("/reissue_in_batch") { coHandler(backFillController::reissueInvoicePaymentInBatch) }
                post("/resend_invoice_email") { coHandler(backFillController::resendInvoiceEmail) }
                post("/fill_new_fields_for_details") { coHandler(backFillController::fillNewFieldsPaymentDetail) }
            }

            route("email") {
                post("/send_template_to_email") { coHandler(backFillController::sendTemplateToEmail) }
            }


        }
    }

}
