package br.com.alice.moneyin.communication

import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.core.extensions.toCustomFormat
import br.com.alice.common.core.extensions.toMoneyString
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDate
import br.com.alice.common.helpers.returns
import br.com.alice.communication.email.EmailSender
import br.com.alice.communication.email.model.EmailAddress
import br.com.alice.communication.email.model.EmailAttachment
import br.com.alice.communication.email.model.EmailReceipt
import br.com.alice.communication.email.model.SendEmailRequest
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BoletoPaymentDetail
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.moneyin.ServiceConfig
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.InvoiceLiquidationService
import br.com.alice.moneyin.client.InvoicePdfService
import br.com.alice.moneyin.client.MemberInvoiceGroupService
import br.com.alice.moneyin.client.PortalUrlGeneratorService
import br.com.alice.moneyin.communication.InvoiceMailer.Companion.BOLEPIX_DEFAULTING_INVOICE_TEMPLATE
import br.com.alice.moneyin.communication.InvoiceMailer.Companion.BOLEPIX_RECURRING_INVOICE_TEMPLATE
import br.com.alice.moneyin.model.InvalidPaymentMethodAndReasonException
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.format.TextStyle
import java.util.Locale
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class InvoiceMailerTest {

    @AfterTest
    fun clear() = clearAllMocks()

    private val sender: EmailSender = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val memberInvoiceGroupService: MemberInvoiceGroupService = mockk()
    private val invoiceLiquidationService: InvoiceLiquidationService = mockk()
    private val invoicePdfService: InvoicePdfService = mockk()
    private val portalUrlGeneratorService: PortalUrlGeneratorService = mockk()

    private val invoiceMailer = InvoiceMailer(
        sender,
        billingAccountablePartyService,
        memberInvoiceGroupService,
        invoiceLiquidationService,
        invoicePdfService,
        portalUrlGeneratorService,
    )

    private val paymentUrl = "portalUrl"

    private val bccRecipientEmail = "<EMAIL>"
    private val bccRecipients = listOf(EmailAddress(bccRecipientEmail, bccRecipientEmail))

    @Test
    fun `#send should use boleto first invoice template when payment reason is FIRST_PAYMENT`() = runBlocking {
        val invoicePaymentWithoutDetail = TestModelFactory.buildInvoicePayment()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val boletoDetail = TestModelFactory.buildBoletoPaymentDetail(invoicePaymentWithoutDetail.id)
        val invoicePayment = invoicePaymentWithoutDetail.withPaymentDetail(boletoDetail)
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(id = invoicePayment.invoiceGroupId!!)

        coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl
        coEvery { billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) } returns billingAccountableParty
        coEvery { memberInvoiceGroupService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroup

        val sendEmailRequest = SendEmailRequest(
            from = EmailAddress(ServiceConfig.Mailer.defaultSenderName, ServiceConfig.Mailer.defaultSenderEmail),
            to = listOf(EmailAddress(billingAccountableParty.fullName, billingAccountableParty.email)),
            bcc = bccRecipients,
            campaignId = ServiceConfig.Mailer.pinPointCampaignId,
            templateName = InvoiceMailer.BOLETO_FIRST_INVOICE_TEMPLATE,
            replaceVariables = mapOf(
                "Attributes.nickname" to billingAccountableParty.fullName,
                "Attributes.boleto" to paymentUrl,
                "Attributes.digitableLine" to (invoicePayment.paymentDetail as BoletoPaymentDetail).digitableLine.orEmpty(),
                "Attributes.dueDate" to (invoicePayment.paymentDetail as BoletoPaymentDetail).dueDate.toBrazilianDateFormat(),
                "Attributes.referenceDate" to memberInvoiceGroup.referenceDate.toCustomFormat("yyyy-MM-01"),
                "Attributes.month" to memberInvoiceGroup.referenceDate.month.getDisplayName(
                    TextStyle.FULL,
                    Locale("pt", "BR")
                ),
                "Attributes.year" to memberInvoiceGroup.referenceDate.year.toString(),
                "Attributes.amount" to invoicePayment.amount.toMoneyString(),
                "Attributes.boletoPaymentUrl" to paymentUrl,
                "Attributes.invoiceDetailsB2bXlsxFileDownload" to ""
            ),
        )

        val emailReceipt = EmailReceipt(id = "emailReceiptId")
        coEvery { sender.send(sendEmailRequest) } returns emailReceipt

        val result = invoiceMailer.send(invoicePayment, PaymentReason.FIRST_PAYMENT)
        assertThat(result).isSuccessWithData(emailReceipt)

        coVerify(exactly = 1) { sender.send(sendEmailRequest) }
    }

    @Test
    fun `#send should use invoice template with member invoice when the invoice group id is null`() = runBlocking {
        val invoicePaymentWithoutDetail = TestModelFactory.buildInvoicePayment(invoiceGroupId = null)
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val boletoDetail = TestModelFactory.buildBoletoPaymentDetail(invoicePaymentWithoutDetail.id)
        val invoicePayment = invoicePaymentWithoutDetail.withPaymentDetail(boletoDetail)
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()

        coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl
        coEvery { billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) } returns billingAccountableParty
        coEvery { memberInvoiceGroupService.getByMemberInvoices(invoicePayment.memberInvoiceId!!) } returns memberInvoiceGroup

        val sendEmailRequest = SendEmailRequest(
            from = EmailAddress(ServiceConfig.Mailer.defaultSenderName, ServiceConfig.Mailer.defaultSenderEmail),
            to = listOf(EmailAddress(billingAccountableParty.fullName, billingAccountableParty.email)),
            bcc = bccRecipients,
            campaignId = ServiceConfig.Mailer.pinPointCampaignId,
            templateName = InvoiceMailer.BOLETO_FIRST_INVOICE_TEMPLATE,
            replaceVariables = mapOf(
                "Attributes.nickname" to billingAccountableParty.fullName,
                "Attributes.boleto" to paymentUrl,
                "Attributes.digitableLine" to (invoicePayment.paymentDetail as BoletoPaymentDetail).digitableLine.orEmpty(),
                "Attributes.dueDate" to (invoicePayment.paymentDetail as BoletoPaymentDetail).dueDate.toBrazilianDateFormat(),
                "Attributes.referenceDate" to memberInvoiceGroup.referenceDate.toCustomFormat("yyyy-MM-01"),
                "Attributes.month" to memberInvoiceGroup.referenceDate.month.getDisplayName(
                    TextStyle.FULL,
                    Locale("pt", "BR")
                ),
                "Attributes.year" to memberInvoiceGroup.referenceDate.year.toString(),
                "Attributes.amount" to invoicePayment.amount.toMoneyString(),
                "Attributes.boletoPaymentUrl" to paymentUrl,
                "Attributes.invoiceDetailsB2bXlsxFileDownload" to ""
            ),
        )

        val emailReceipt = EmailReceipt(id = "emailReceiptId")
        coEvery { sender.send(sendEmailRequest) } returns emailReceipt

        val result = invoiceMailer.send(invoicePayment, PaymentReason.FIRST_PAYMENT)
        assertThat(result).isSuccessWithData(emailReceipt)

        coVerify(exactly = 1) { sender.send(sendEmailRequest) }
    }

    @Test
    fun `#send should use invoice template with member invoice when the invoice group id is null and the member invoice ids is an empty list`() =
        runBlocking {
            val invoicePaymentWithoutDetail =
                TestModelFactory.buildInvoicePayment(
                    invoiceGroupId = null,
                    memberInvoiceIds = emptyList()
                )
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val boletoDetail = TestModelFactory.buildBoletoPaymentDetail(invoicePaymentWithoutDetail.id)
            val invoicePayment = invoicePaymentWithoutDetail.withPaymentDetail(boletoDetail)
            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup()

            coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl
            coEvery { billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) } returns billingAccountableParty

            val sendEmailRequest = SendEmailRequest(
                from = EmailAddress(ServiceConfig.Mailer.defaultSenderName, ServiceConfig.Mailer.defaultSenderEmail),
                to = listOf(EmailAddress(billingAccountableParty.fullName, billingAccountableParty.email)),
                bcc = bccRecipients,
                campaignId = ServiceConfig.Mailer.pinPointCampaignId,
                templateName = InvoiceMailer.BOLETO_FIRST_INVOICE_TEMPLATE,
                replaceVariables = mapOf(
                    "Attributes.nickname" to billingAccountableParty.fullName,
                    "Attributes.boleto" to paymentUrl,
                    "Attributes.digitableLine" to (invoicePayment.paymentDetail as BoletoPaymentDetail).digitableLine.orEmpty(),
                    "Attributes.dueDate" to (invoicePayment.paymentDetail as BoletoPaymentDetail).dueDate.toBrazilianDateFormat(),
                    "Attributes.referenceDate" to invoicePaymentWithoutDetail.createdAt.toCustomFormat("yyyy-MM-01"),
                    "Attributes.month" to invoicePaymentWithoutDetail.createdAt.month.getDisplayName(
                        TextStyle.FULL,
                        Locale("pt", "BR")
                    ),
                    "Attributes.year" to memberInvoiceGroup.referenceDate.year.toString(),
                    "Attributes.amount" to invoicePayment.amount.toMoneyString(),
                    "Attributes.boletoPaymentUrl" to paymentUrl,
                    "Attributes.invoiceDetailsB2bXlsxFileDownload" to ""
                ),
            )

            val emailReceipt = EmailReceipt(id = "emailReceiptId")
            coEvery { sender.send(sendEmailRequest) } returns emailReceipt

            val result = invoiceMailer.send(invoicePayment, PaymentReason.FIRST_PAYMENT)
            assertThat(result).isSuccessWithData(emailReceipt)

            coVerify(exactly = 1) { sender.send(sendEmailRequest) }
            coVerifyNone {
                memberInvoiceGroupService.getByMemberInvoices(any())
            }
        }

    @Test
    fun `#send should use boleto recurring invoice template when payment reason is REGULAR_PAYMENT`() =
        runBlocking {
            val invoicePaymentWithoutDetail = TestModelFactory.buildInvoicePayment()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val boletoDetail = TestModelFactory.buildBoletoPaymentDetail(invoicePaymentWithoutDetail.id)
            val invoicePayment = invoicePaymentWithoutDetail.withPaymentDetail(boletoDetail)
            val memberInvoiceGroup =
                TestModelFactory.buildMemberInvoiceGroup(id = invoicePayment.invoiceGroupId!!)

            coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl
            coEvery { billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) } returns billingAccountableParty
            coEvery { memberInvoiceGroupService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroup

            val sendEmailRequest = SendEmailRequest(
                from = EmailAddress(
                    ServiceConfig.Mailer.defaultSenderName,
                    ServiceConfig.Mailer.defaultSenderEmail
                ),
                to = listOf(EmailAddress(billingAccountableParty.fullName, billingAccountableParty.email)),
                bcc = bccRecipients,
                campaignId = ServiceConfig.Mailer.pinPointCampaignId,
                templateName = InvoiceMailer.BOLETO_RECURRING_INVOICE_TEMPLATE,
                replaceVariables = mapOf(
                    "Attributes.nickname" to billingAccountableParty.fullName,
                    "Attributes.boleto" to paymentUrl,
                    "Attributes.digitableLine" to (invoicePayment.paymentDetail as BoletoPaymentDetail).digitableLine.orEmpty(),
                    "Attributes.dueDate" to (invoicePayment.paymentDetail as BoletoPaymentDetail).dueDate.toBrazilianDateFormat(),
                    "Attributes.referenceDate" to memberInvoiceGroup.referenceDate.toCustomFormat("yyyy-MM-01"),
                    "Attributes.month" to memberInvoiceGroup.referenceDate.month.getDisplayName(
                        TextStyle.FULL,
                        Locale("pt", "BR")
                    ),
                    "Attributes.year" to memberInvoiceGroup.referenceDate.year.toString(),
                    "Attributes.amount" to invoicePayment.amount.toMoneyString(),
                    "Attributes.boletoPaymentUrl" to paymentUrl,
                    "Attributes.invoiceDetailsB2bXlsxFileDownload" to ""
                ),
            )

            val emailReceipt = EmailReceipt(id = "emailReceiptId")
            coEvery { sender.send(sendEmailRequest) } returns emailReceipt

            val result = invoiceMailer.send(invoicePayment, PaymentReason.REGULAR_PAYMENT)
            assertThat(result).isSuccessWithData(emailReceipt)

            coVerify(exactly = 1) { sender.send(sendEmailRequest) }
        }

    companion object {
        @JvmStatic
        fun sendEmailDependingOnTemplateAndReason() = listOf(
            arrayOf(BOLEPIX_RECURRING_INVOICE_TEMPLATE, PaymentReason.REGULAR_PAYMENT),
            arrayOf(BOLEPIX_DEFAULTING_INVOICE_TEMPLATE, PaymentReason.OVERDUE_PAYMENT),
        )
    }

    @ParameterizedTest(name = "should use bolepix {0} template when payment reason is {1}")
    @MethodSource("sendEmailDependingOnTemplateAndReason")
    fun `#send should use bolepix templates depending on the payment reason`(
        template: String,
        reason: PaymentReason
    ) = runBlocking {
        val invoicePaymentWithoutDetail = TestModelFactory.buildInvoicePayment(method = PaymentMethod.BOLEPIX)
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val bolepixDetail = TestModelFactory.buildBolepixPaymentDetail(invoicePaymentWithoutDetail.id)
        val invoicePayment = invoicePaymentWithoutDetail.withPaymentDetail(bolepixDetail)

        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(id = invoicePayment.invoiceGroupId!!)

        coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl
        coEvery { billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) } returns billingAccountableParty
        coEvery { memberInvoiceGroupService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroup

        val sendEmailRequest = SendEmailRequest(
            from = EmailAddress(ServiceConfig.Mailer.defaultSenderName, ServiceConfig.Mailer.defaultSenderEmail),
            to = listOf(EmailAddress(billingAccountableParty.fullName, billingAccountableParty.email)),
            bcc = bccRecipients,
            campaignId = ServiceConfig.Mailer.pinPointCampaignId,
            templateName = template,
            replaceVariables = mapOf(
                "Attributes.nickname" to billingAccountableParty.fullName,
                "Attributes.boleto" to paymentUrl,
                "Attributes.paymentCodePix" to bolepixDetail.pixCopyAndPaste.orEmpty(),
                "Attributes.pixPaymentUrl" to paymentUrl,
                "Attributes.barcodeBoleto" to bolepixDetail.bankSlipDigitableLine.orEmpty(),
                "Attributes.boletoPaymentUrl" to paymentUrl,
                "Attributes.dueDate" to bolepixDetail.dueDate!!.toBrazilianDateFormat(),
                "Attributes.amount" to invoicePayment.amount.toMoneyString(),
                "Attributes.month" to memberInvoiceGroup.referenceDate.month.getDisplayName(
                    TextStyle.FULL,
                    Locale("pt", "BR")
                ),
                "Attributes.year" to memberInvoiceGroup.referenceDate.year.toString(),
                "Attributes.invoiceDetailsB2bXlsxFileDownload" to ""
            ),
        )

        val emailReceipt = EmailReceipt(id = "emailReceiptId")
        coEvery { sender.send(sendEmailRequest) } returns emailReceipt

        val result = invoiceMailer.send(invoicePayment, reason)
        assertThat(result).isSuccessWithData(emailReceipt)

        coVerify(exactly = 1) { sender.send(sendEmailRequest) }
    }

    @Test
    fun `#send should use boleto invoice template when payment reason is OVERDUE_PAYMENT`() = runBlocking {
        val invoicePaymentWithoutDetail = TestModelFactory.buildInvoicePayment()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val boletoDetail = TestModelFactory.buildBoletoPaymentDetail(invoicePaymentWithoutDetail.id)
        val invoicePayment = invoicePaymentWithoutDetail.withPaymentDetail(boletoDetail)
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(id = invoicePayment.invoiceGroupId!!)

        coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl
        coEvery { billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) } returns billingAccountableParty
        coEvery { memberInvoiceGroupService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroup

        val sendEmailRequest = SendEmailRequest(
            from = EmailAddress(ServiceConfig.Mailer.defaultSenderName, ServiceConfig.Mailer.defaultSenderEmail),
            to = listOf(EmailAddress(billingAccountableParty.fullName, billingAccountableParty.email)),
            bcc = bccRecipients,
            campaignId = ServiceConfig.Mailer.pinPointCampaignId,
            templateName = InvoiceMailer.BOLETO_DEFAULTING_INVOICE_TEMPLATE,
            replaceVariables = mapOf(
                "Attributes.nickname" to billingAccountableParty.fullName,
                "Attributes.boleto" to paymentUrl,
                "Attributes.digitableLine" to (invoicePayment.paymentDetail as BoletoPaymentDetail).digitableLine.orEmpty(),
                "Attributes.dueDate" to (invoicePayment.paymentDetail as BoletoPaymentDetail).dueDate.toBrazilianDateFormat(),
                "Attributes.referenceDate" to memberInvoiceGroup.referenceDate.toCustomFormat("yyyy-MM-01"),
                "Attributes.month" to memberInvoiceGroup.referenceDate.month.getDisplayName(
                    TextStyle.FULL,
                    Locale("pt", "BR")
                ),
                "Attributes.year" to memberInvoiceGroup.referenceDate.year.toString(),
                "Attributes.amount" to invoicePayment.amount.toMoneyString(),
                "Attributes.boletoPaymentUrl" to paymentUrl,
                "Attributes.invoiceDetailsB2bXlsxFileDownload" to ""
            ),
        )

        val emailReceipt = EmailReceipt(id = "emailReceiptId")
        coEvery { sender.send(sendEmailRequest) } returns emailReceipt

        val result = invoiceMailer.send(invoicePayment, PaymentReason.OVERDUE_PAYMENT)
        assertThat(result).isSuccessWithData(emailReceipt)

        coVerify(exactly = 1) { sender.send(sendEmailRequest) }
    }

    @Test
    fun `#send should use pix first invoice template when payment reason is FIRST_PAYMENT`() = runBlocking {
        val invoicePaymentWithoutDetail = TestModelFactory.buildInvoicePayment(method = PaymentMethod.PIX)
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val pixDetail = TestModelFactory.buildPixPaymentDetail(invoicePaymentWithoutDetail.id)
        val invoicePayment = invoicePaymentWithoutDetail.withPaymentDetail(pixDetail)
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(id = invoicePayment.invoiceGroupId!!)

        coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl
        coEvery { billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) } returns billingAccountableParty
        coEvery { memberInvoiceGroupService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroup

        val sendEmailRequest = SendEmailRequest(
            from = EmailAddress(ServiceConfig.Mailer.defaultSenderName, ServiceConfig.Mailer.defaultSenderEmail),
            to = listOf(EmailAddress(billingAccountableParty.fullName, billingAccountableParty.email)),
            bcc = bccRecipients,
            campaignId = ServiceConfig.Mailer.pinPointCampaignId,
            templateName = InvoiceMailer.PIX_FIRST_INVOICE_TEMPLATE,
            replaceVariables = mapOf(
                "Attributes.nickname" to billingAccountableParty.fullName,
                "Attributes.boleto" to paymentUrl,
                "Attributes.barcode" to pixDetail.copyAndPaste.orEmpty(),
                "Attributes.invoiceDetailsB2bXlsxFileDownload" to "",
                "Attributes.month" to memberInvoiceGroup.referenceDate.month.getDisplayName(
                    TextStyle.FULL,
                    Locale("pt", "BR")
                ),
                "Attributes.year" to memberInvoiceGroup.referenceDate.year.toString()
            ),
        )

        val emailReceipt = EmailReceipt(id = "emailReceiptId")
        coEvery { sender.send(sendEmailRequest) } returns emailReceipt

        val result = invoiceMailer.send(invoicePayment, PaymentReason.FIRST_PAYMENT)
        assertThat(result).isSuccessWithData(emailReceipt)

        coVerify(exactly = 1) { sender.send(sendEmailRequest) }
    }

    @Test
    fun `#send should use boleto b2b recurring invoice template when payment reason is B2B_REGULAR_PAYMENT`() =
        runBlocking {
            val invoicePaymentWithoutDetail = TestModelFactory.buildInvoicePayment(
                method = PaymentMethod.BOLETO,
                reason = PaymentReason.B2B_REGULAR_PAYMENT
            )
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val boletoDetail = TestModelFactory.buildBoletoPaymentDetail(invoicePaymentWithoutDetail.id)
            val invoicePayment = invoicePaymentWithoutDetail.withPaymentDetail(boletoDetail)
            val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(id = invoicePayment.invoiceGroupId!!)

            coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl
            coEvery { billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) } returns billingAccountableParty
            coEvery { memberInvoiceGroupService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroup

            val sendEmailRequest = SendEmailRequest(
                from = EmailAddress(
                    ServiceConfig.Mailer.defaultB2BSenderName,
                    ServiceConfig.Mailer.defaultB2BSenderEmail
                ),
                to = listOf(EmailAddress(billingAccountableParty.fullName, billingAccountableParty.email)),
                bcc = bccRecipients,
                campaignId = ServiceConfig.Mailer.pinPointCampaignId,
                templateName = InvoiceMailer.B2B_BOLETO_RECURRING_INVOICE_TEMPLATE,
                replaceVariables = mapOf(
                    "Attributes.nickname" to billingAccountableParty.fullName,
                    "Attributes.boleto" to paymentUrl,
                    "Attributes.digitableLine" to (invoicePayment.paymentDetail as BoletoPaymentDetail).digitableLine.orEmpty(),
                    "Attributes.dueDate" to (invoicePayment.paymentDetail as BoletoPaymentDetail).dueDate.toBrazilianDateFormat(),
                    "Attributes.referenceDate" to memberInvoiceGroup.referenceDate.toCustomFormat("yyyy-MM-01"),
                    "Attributes.month" to memberInvoiceGroup.referenceDate.month.getDisplayName(
                        TextStyle.FULL,
                        Locale("pt", "BR")
                    ),
                    "Attributes.year" to memberInvoiceGroup.referenceDate.year.toString(),
                    "Attributes.amount" to invoicePayment.amount.toMoneyString(),
                    "Attributes.boletoPaymentUrl" to paymentUrl,
                    "Attributes.invoiceDetailsB2bXlsxFileDownload" to "${InvoiceMailer.INVOICE_DETAILS_B2B_XLSX_FILE_DOWNLOAD}/${memberInvoiceGroup.id}",
                ),
            )

            val emailReceipt = EmailReceipt(id = "emailReceiptId")
            coEvery { sender.send(sendEmailRequest) } returns emailReceipt

            val result = invoiceMailer.send(invoicePayment, PaymentReason.B2B_REGULAR_PAYMENT)
            assertThat(result).isSuccessWithData(emailReceipt)

            coVerify(exactly = 1) { sender.send(sendEmailRequest) }
        }

    @Test
    fun `#send should use boleto b2b first invoice template when payment reason is B2B_FIRST_PAYMENT`() = runBlocking {
        val invoicePaymentWithoutDetail = TestModelFactory.buildInvoicePayment(
            method = PaymentMethod.BOLETO,
            reason = PaymentReason.B2B_FIRST_PAYMENT,
        )
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val boletoDetail = TestModelFactory.buildBoletoPaymentDetail(invoicePaymentWithoutDetail.id)
        val invoicePayment = invoicePaymentWithoutDetail.withPaymentDetail(boletoDetail)
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(id = invoicePayment.invoiceGroupId!!)

        coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl
        coEvery { billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) } returns billingAccountableParty
        coEvery { memberInvoiceGroupService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroup

        val sendEmailRequest = SendEmailRequest(
            from = EmailAddress(ServiceConfig.Mailer.defaultB2BSenderName, ServiceConfig.Mailer.defaultB2BSenderEmail),
            to = listOf(EmailAddress(billingAccountableParty.fullName, billingAccountableParty.email)),
            bcc = bccRecipients,
            campaignId = ServiceConfig.Mailer.pinPointCampaignId,
            templateName = InvoiceMailer.B2B_BOLETO_RECURRING_INVOICE_TEMPLATE,
            replaceVariables = mapOf(
                "Attributes.nickname" to billingAccountableParty.fullName,
                "Attributes.boleto" to paymentUrl,
                "Attributes.digitableLine" to (invoicePayment.paymentDetail as BoletoPaymentDetail).digitableLine.orEmpty(),
                "Attributes.dueDate" to (invoicePayment.paymentDetail as BoletoPaymentDetail).dueDate.toBrazilianDateFormat(),
                "Attributes.referenceDate" to memberInvoiceGroup.referenceDate.toCustomFormat("yyyy-MM-01"),
                "Attributes.month" to memberInvoiceGroup.referenceDate.month.getDisplayName(
                    TextStyle.FULL,
                    Locale("pt", "BR")
                ),
                "Attributes.year" to memberInvoiceGroup.referenceDate.year.toString(),
                "Attributes.amount" to invoicePayment.amount.toMoneyString(),
                "Attributes.boletoPaymentUrl" to paymentUrl,
                "Attributes.invoiceDetailsB2bXlsxFileDownload" to "${InvoiceMailer.INVOICE_DETAILS_B2B_XLSX_FILE_DOWNLOAD}/${memberInvoiceGroup.id}",
            ),
        )

        val emailReceipt = EmailReceipt(id = "emailReceiptId")
        coEvery { sender.send(sendEmailRequest) } returns emailReceipt

        val result = invoiceMailer.send(invoicePayment, PaymentReason.B2B_REGULAR_PAYMENT)
        assertThat(result).isSuccessWithData(emailReceipt)

        coVerify(exactly = 1) { sender.send(sendEmailRequest) }
    }

    @Test
    fun `#send should use pix recurring invoice template when payment reason is REGULAR_PAYMENT`() = runBlocking {
        val invoicePaymentWithoutDetail = TestModelFactory.buildInvoicePayment(method = PaymentMethod.PIX)
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val pixDetail = TestModelFactory.buildPixPaymentDetail(invoicePaymentWithoutDetail.id)
        val invoicePayment = invoicePaymentWithoutDetail.withPaymentDetail(pixDetail)
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(id = invoicePayment.invoiceGroupId!!)

        coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl
        coEvery { billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) } returns billingAccountableParty
        coEvery { memberInvoiceGroupService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroup

        val sendEmailRequest = SendEmailRequest(
            from = EmailAddress(ServiceConfig.Mailer.defaultSenderName, ServiceConfig.Mailer.defaultSenderEmail),
            to = listOf(EmailAddress(billingAccountableParty.fullName, billingAccountableParty.email)),
            bcc = bccRecipients,
            campaignId = ServiceConfig.Mailer.pinPointCampaignId,
            templateName = InvoiceMailer.PIX_RECURRING_INVOICE_TEMPLATE,
            replaceVariables = mapOf(
                "Attributes.nickname" to billingAccountableParty.fullName,
                "Attributes.boleto" to paymentUrl,
                "Attributes.barcode" to pixDetail.copyAndPaste.orEmpty(),
                "Attributes.invoiceDetailsB2bXlsxFileDownload" to "",
                "Attributes.month" to memberInvoiceGroup.referenceDate.month.getDisplayName(
                    TextStyle.FULL,
                    Locale("pt", "BR")
                ),
                "Attributes.year" to memberInvoiceGroup.referenceDate.year.toString(),
            ),
        )

        val emailReceipt = EmailReceipt(id = "emailReceiptId")
        coEvery { sender.send(sendEmailRequest) } returns emailReceipt

        val result = invoiceMailer.send(invoicePayment, PaymentReason.REGULAR_PAYMENT)
        assertThat(result).isSuccessWithData(emailReceipt)

        coVerify(exactly = 1) { sender.send(sendEmailRequest) }
    }

    @Test
    fun `#send should use invoice template when payment reason is B2B liquidation`() = runBlocking {
        mockLocalDate { now ->
            val invoiceLiquidationId = RangeUUID.generate()
            val invoicePaymentWithoutDetail = TestModelFactory.buildInvoicePayment(
                method = PaymentMethod.BOLEPIX,
                reason = PaymentReason.B2B_LIQUIDATION,
                invoiceLiquidationId = invoiceLiquidationId,
                invoiceGroupId = null,
                memberInvoiceIds = emptyList(),
            )
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val bolepixDetail = TestModelFactory.buildBolepixPaymentDetail(invoicePaymentWithoutDetail.id)
            val invoicePayment = invoicePaymentWithoutDetail.withPaymentDetail(bolepixDetail)

            val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(
                dueDate = now,
                installment = 1,
                totalInstallments = 2,
            )

            val invoiceBytes = byteArrayOf(1, 2, 3, 4, 5)

            coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl
            coEvery { billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) } returns billingAccountableParty
            coEvery { invoiceLiquidationService.get(invoiceLiquidationId) } returns invoiceLiquidation
            coEvery {
                invoicePdfService.generateInvoice(
                    invoicePaymentId = invoicePayment.id,
                    true
                )
            } returns invoiceBytes

            val sendEmailRequest = SendEmailRequest(
                from = EmailAddress(ServiceConfig.Mailer.defaultSenderName, ServiceConfig.Mailer.defaultSenderEmail),
                to = listOf(EmailAddress(billingAccountableParty.fullName, billingAccountableParty.email)),
                bcc = bccRecipients,
                campaignId = ServiceConfig.Mailer.pinPointCampaignId,
                templateName = InvoiceMailer.B2B_LIQUIDATION_TEMPLATE,
                replaceVariables = mapOf(
                    "Attributes.boleto" to paymentUrl,
                    "Attributes.invoiceDetailsB2bXlsxFileDownload" to "",
                    "Attributes.month" to invoicePayment.createdAt.month.getDisplayName(
                        TextStyle.FULL,
                        Locale("pt", "BR")
                    ),
                    "Attributes.year" to invoicePayment.createdAt.year.toString(),
                    "Attributes.nickname" to billingAccountableParty.fullName,
                    "Attributes.paymentCodePix" to bolepixDetail.pixCopyAndPaste.orEmpty(),
                    "Attributes.pixPaymentUrl" to paymentUrl,
                    "Attributes.barcodeBoleto" to bolepixDetail.bankSlipDigitableLine.orEmpty(),
                    "Attributes.boletoPaymentUrl" to paymentUrl,
                    "Attributes.dueDate" to bolepixDetail.dueDate!!.toBrazilianDateFormat(),
                    "Attributes.amount" to invoicePayment.amount.toMoneyString(),
                    "Attributes.currentParcel" to "1",
                    "Attributes.totalParcel" to "2",
                ),
                attachments = listOf(EmailAttachment("fatura.pdf", invoiceBytes, "application/pdf")),
            )

            val emailReceipt = EmailReceipt(id = "emailReceiptId")
            coEvery { sender.send(sendEmailRequest) } returns emailReceipt

            val result = invoiceMailer.send(invoicePayment, PaymentReason.B2B_LIQUIDATION)
            assertThat(result).isSuccessWithData(emailReceipt)

            coVerify(exactly = 1) { sender.send(sendEmailRequest) }
        }
    }

    @Test
    fun `#send should use invoice template when payment reason is B2C liquidation`() = runBlocking {
        mockLocalDate { now ->
            val invoiceLiquidationId = RangeUUID.generate()
            val invoicePaymentWithoutDetail = TestModelFactory.buildInvoicePayment(
                method = PaymentMethod.BOLETO,
                reason = PaymentReason.B2C_LIQUIDATION,
                invoiceLiquidationId = invoiceLiquidationId,
                invoiceGroupId = null,
                memberInvoiceIds = emptyList(),
            )
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val boletoDetail = TestModelFactory.buildBoletoPaymentDetail(invoicePaymentWithoutDetail.id)
            val invoicePayment = invoicePaymentWithoutDetail.withPaymentDetail(boletoDetail)

            val invoiceLiquidation = TestModelFactory.buildInvoiceLiquidation(
                dueDate = now,
                installment = 1,
                totalInstallments = 2,
            )

            val invoiceBytes = byteArrayOf(1, 2, 3, 4, 5)

            coEvery { portalUrlGeneratorService.mountPortalUrl(invoicePayment.id) } returns paymentUrl
            coEvery { billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) } returns billingAccountableParty
            coEvery { invoiceLiquidationService.get(invoiceLiquidationId) } returns invoiceLiquidation
            coEvery {
                invoicePdfService.generateInvoice(
                    invoicePaymentId = invoicePayment.id,
                    true
                )
            } returns invoiceBytes

            val sendEmailRequest = SendEmailRequest(
                from = EmailAddress(ServiceConfig.Mailer.defaultSenderName, ServiceConfig.Mailer.defaultSenderEmail),
                to = listOf(EmailAddress(billingAccountableParty.fullName, billingAccountableParty.email)),
                bcc = bccRecipients,
                campaignId = ServiceConfig.Mailer.pinPointCampaignId,
                templateName = InvoiceMailer.B2C_LIQUIDATION_TEMPLATE,
                replaceVariables = mapOf(
                    "Attributes.boleto" to paymentUrl,
                    "Attributes.invoiceDetailsB2bXlsxFileDownload" to "",
                    "Attributes.month" to invoiceLiquidation.dueDate.month.getDisplayName(
                        TextStyle.FULL,
                        Locale("pt", "BR")
                    ),
                    "Attributes.year" to invoicePayment.createdAt.year.toString(),
                    "Attributes.nickname" to billingAccountableParty.fullName,
                    "Attributes.digitableLine" to (invoicePayment.paymentDetail as BoletoPaymentDetail).digitableLine.orEmpty(),
                    "Attributes.dueDate" to (invoicePayment.paymentDetail as BoletoPaymentDetail).dueDate.toBrazilianDateFormat(),
                    "Attributes.referenceDate" to invoiceLiquidation.dueDate.toCustomFormat("yyyy-MM-01"),
                    "Attributes.amount" to invoicePayment.amount.toMoneyString(),
                    "Attributes.boletoPaymentUrl" to paymentUrl,
                    "Attributes.currentParcel" to "1",
                    "Attributes.totalParcel" to "2",
                ),
                attachments = listOf(EmailAttachment("fatura.pdf", invoiceBytes, "application/pdf")),
            )

            val emailReceipt = EmailReceipt(id = "emailReceiptId")
            coEvery { sender.send(sendEmailRequest) } returns emailReceipt

            val result = invoiceMailer.send(invoicePayment, PaymentReason.B2C_LIQUIDATION)
            assertThat(result).isSuccessWithData(emailReceipt)

            coVerify(exactly = 1) { sender.send(sendEmailRequest) }
        }
    }

    @Test
    fun `#send should return error if payment method is PIX and payment reason is OVERDUE_PAYMENT`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment(method = PaymentMethod.PIX)
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        coEvery { billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) } returns billingAccountableParty

        val result = invoiceMailer.send(invoicePayment, PaymentReason.OVERDUE_PAYMENT)

        assertThat(result).isFailureOfType(InvalidPaymentMethodAndReasonException::class)
    }

    @Test
    fun `#send should return error if payment method is SIMPLE_CREDIT_CARD`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment(method = PaymentMethod.SIMPLE_CREDIT_CARD)
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()

        coEvery { billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) } returns billingAccountableParty

        val result = invoiceMailer.send(invoicePayment, PaymentReason.OVERDUE_PAYMENT)

        assertThat(result).isFailureOfType(InvalidPaymentMethodAndReasonException::class)
    }

    @Test
    fun `#sendTemplateToEmail should send email to user using template successfully`() = runBlocking {

        val email = "<EMAIL>"
        val template = "template"
        val params = mapOf(
            "Attributes.nickname" to "nickname",
            "Attributes.boleto" to "boleto",
            "Attributes.barcode" to "barcode",
            "Attributes.invoiceDetailsB2bXlsxFileDownload" to "",
            "Attributes.month" to "month",
            "Attributes.year" to 2000.toString()
        )

        val emailReceipt = EmailReceipt(id = "emailReceiptId")
        coEvery { sender.send(any()) } returns emailReceipt

        val result = invoiceMailer.sendTemplateToEmail(email, template, params)
        assertThat(result).isSuccessWithData(emailReceipt)

        coVerify(exactly = 1) { sender.send(any()) }
    }

    @Test
    fun `#sendInvoicePaidEmail should return error if billingAccountableParty is empty`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment(billingAccountablePartyId = null)

        val result = invoiceMailer.sendInvoicePaidEmail(invoicePayment)

        assertThat(result).isFailureOfType(InvalidArgumentException::class)
    }

    @Test
    fun `#sendInvoicePaidEmail should succeed`() = runBlocking {
        val invoicePayment = TestModelFactory.buildInvoicePayment(
            billingAccountablePartyId = RangeUUID.generate(),
            invoiceGroupId = RangeUUID.generate(),
        )
        val billingAccountableParty =
            TestModelFactory.buildBillingAccountableParty()
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(id = invoicePayment.invoiceGroupId!!)

        coEvery { billingAccountablePartyService.get(invoicePayment.billingAccountablePartyId!!) } returns billingAccountableParty
        coEvery { memberInvoiceGroupService.get(invoicePayment.invoiceGroupId!!) } returns memberInvoiceGroup
        coEvery {
            sender.send(
                SendEmailRequest(
                    from = EmailAddress(
                        ServiceConfig.Mailer.defaultB2BSenderName,
                        ServiceConfig.Mailer.defaultB2BSenderEmail
                    ),
                    to = listOf(
                        EmailAddress(
                            billingAccountableParty.fullNameConsideringType,
                            billingAccountableParty.email
                        )
                    ),
                    bcc = bccRecipients,
                    templateName = "Comprovante_pagamento_mensalidade",
                    campaignId = ServiceConfig.Mailer.pinPointCampaignId,
                    replaceVariables = mapOf(
                        "Attributes.amount" to (invoicePayment.amountPaid ?: invoicePayment.amount).toMoneyString(),
                        "Attributes.month" to memberInvoiceGroup.referenceDate.toBrazilianDateFormat(),
                        "Attributes.nickname" to billingAccountableParty.firstName,
                    ),
                )
            )
        } returns EmailReceipt("123")

        val result = invoiceMailer.sendInvoicePaidEmail(invoicePayment)
        assertThat(result).isSuccessWithData(EmailReceipt("123"))

        coVerifyOnce { billingAccountablePartyService.get(any()) }
        coVerifyOnce { memberInvoiceGroupService.get(any()) }
        coVerifyOnce { sender.send(any()) }
    }
    
}
