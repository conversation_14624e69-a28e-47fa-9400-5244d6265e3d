package br.com.alice.business.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CassiMemberInfo
import br.com.alice.business.client.CompanyService
import br.com.alice.business.events.CognitoCompanyUpsertedEvent
import br.com.alice.business.exceptions.ServerCouldNotProcessBeneficiaryCreationException
import br.com.alice.business.metrics.BeneficiaryMetric.BeneficiaryCreationFlow.COGNITO
import br.com.alice.business.metrics.BeneficiaryMetric.beneficiaryCreatedIncrement
import br.com.alice.business.model.BeneficiaryTransport
import br.com.alice.business.model.CognitoCompanyMemberRequest
import br.com.alice.business.model.CognitoCompanyRequest
import br.com.alice.business.model.sanitize
import br.com.alice.business.model.toBeneficiaryTransport
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.asMap
import br.com.alice.common.core.extensions.isNotNullOrBlank
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.coroutine.pmapNotNull
import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.common.serialization.gson
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyProductType
import br.com.alice.data.layer.models.CompanySizeConfiguration
import br.com.alice.data.layer.models.ConfigurationVersion
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Member
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.MembershipAlreadyActiveMismatchException
import br.com.alice.person.client.PendingMembershipMismatchException
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.lift
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class BeneficiaryConsumer(
    private val beneficiaryService: BeneficiaryService,
    private val personService: PersonService,
    private val memberService: MemberService,
    private val companyService: CompanyService
) : Consumer() {

    suspend fun handleCognitoCompanyUpserted(event: CognitoCompanyUpsertedEvent) =
        withSubscribersEnvironment {
            coroutineScope {
                val cognitoCompanyRequest = event.payload.cognitoCompanyRequest
                val company = event.payload.company
                val companyProductType = event.payload.companyProductType
                val beneficiariesRequest = cognitoCompanyRequest.members?.sanitize()
                val broker = cognitoCompanyRequest.broker

                logger.info(
                    "BeneficiaryConsumer::handleCognitoCompanyUpserted",
                    "company_id" to company.id,
                    "company_product_type" to companyProductType,
                    "broker" to broker,
                    "beneficiaries_count" to (beneficiariesRequest?.size ?: 0)
                )

                if (beneficiariesRequest.isNullOrEmpty()) {
                    logger.info(
                        "BeneficiaryConsumer::handleCognitoCompanyUpserted empty beneficiaries",
                        "company_id" to company.id,
                        "company_cnpj" to company.cnpj
                    )
                    return@coroutineScope true.success()
                }

                val metadata = getMetadata(cognitoCompanyRequest)

                val nationalIds = beneficiariesRequest.map { it.nationalId }
                val accountableNationalIds = beneficiariesRequest.mapNotNull {
                    if (it.nationalId != it.accountableNationalId) it.accountableNationalId
                    else null
                }
                val distinctNationalIds = (nationalIds + accountableNationalIds).distinct()

                findPersonsAndMembersByNationalIds(distinctNationalIds)
                    .map { (members, people) ->
                        val membersPersonIds = members.filter { it.isB2C }.map { it.personId }
                        val personsWithActiveB2CMembership = people.filter { membersPersonIds.contains(it.id) }
                        val peopleMap = people.associateBy { it.nationalId }

                        if (personsWithActiveB2CMembership.isNotNullOrEmpty()) {
                            logger.error(
                                "BeneficiaryConsumer::handleCognitoCompanyUpserted found persons with active B2C membership",
                                "person_ids" to personsWithActiveB2CMembership.map { it.id },
                                "company_id" to company.id,
                                "company_cnpj" to company.cnpj
                            )
                            return@coroutineScope false.success()
                        }

                        val holderBeneficiariesToCreate =
                            buildHolderBeneficiaries(beneficiariesRequest, company, companyProductType, broker)

                        val holderCreatedBeneficiaries = createBeneficiaries(holderBeneficiariesToCreate, metadata)
                            .flatMapError {
                                if (it is ServerCouldNotProcessBeneficiaryCreationException) {
                                    logger.warn(
                                        "BeneficiaryConsumer::handleCognitoCompanyUpserted error while creating holder beneficiaries",
                                        "error_message" to it.message,
                                        "company_cnpj" to company.cnpj
                                    )
                                    return@coroutineScope true.success()
                                }
                                else it.failure()
                            }.get()

                        val parentBeneficiariesMap = holderCreatedBeneficiaries.associate { it.first to it.second }

                        val notCreatedNowNationalIds =
                            accountableNationalIds.filter { it !in parentBeneficiariesMap.keys }

                        val parentBeneficiariesWithNationalId = if (notCreatedNowNationalIds.isNotEmpty()) {
                            notCreatedNowNationalIds.pmap {
                                Pair(it, beneficiaryService.findByPersonId(peopleMap.getValue(it).id).get())
                            }.associate { it.first to it.second } + parentBeneficiariesMap
                        } else parentBeneficiariesMap


                        val dependentBeneficiariesToCreate =
                            buildDependentBeneficiaries(
                                beneficiariesRequest,
                                company,
                                companyProductType,
                                broker,
                                parentBeneficiariesWithNationalId
                            )

                        val dependentBeneficiaries = createBeneficiaries(dependentBeneficiariesToCreate, metadata)
                            .flatMapError {
                                if (it is ServerCouldNotProcessBeneficiaryCreationException) {
                                    logger.error(
                                        "BeneficiaryConsumer::handleCognitoCompanyUpserted error while creating dependent beneficiaries",
                                        "error_message" to it.message,
                                        "company_cnpj" to company.cnpj
                                    )
                                    return@coroutineScope true.success()
                                }
                                else it.failure()
                            }.get()

                        val upsertedBeneficiaries =
                            (holderCreatedBeneficiaries + dependentBeneficiaries).distinctBy { it.second.id }
                        logger.info(
                            "BeneficiaryConsumer::handleCognitoCompanyUpserted finish",
                            "created/updated" to upsertedBeneficiaries.size
                        )

                        return@coroutineScope true.success()
                    }
            }
        }

    private suspend fun buildHolderBeneficiaries(
        beneficiariesRequest: List<CognitoCompanyMemberRequest>,
        company: Company,
        companyProductType: CompanyProductType?,
        broker: String?
    ): List<BeneficiaryTransport> {
        val holderBeneficiariesRequest = beneficiariesRequest.filter { it.dependencyType?.lowercase() == "titular" }
        logger.info(
            "BeneficiaryConsumer::buildHolderBeneficiaries",
            "holder_beneficiaries" to holderBeneficiariesRequest.map { it.fullName }
        )
        return buildBeneficiaries(
            beneficiariesRequest = holderBeneficiariesRequest,
            company = company,
            companyProductType = companyProductType,
            broker = broker
        )
    }

    private suspend fun buildDependentBeneficiaries(
        beneficiariesRequest: List<CognitoCompanyMemberRequest>,
        company: Company,
        companyProductType: CompanyProductType?,
        broker: String?,
        parentBeneficiariesWithNationalId: Map<String, Beneficiary> = emptyMap()
    ): List<BeneficiaryTransport> {
        val dependentBeneficiariesRequest =
            beneficiariesRequest.filter { it.dependencyType?.lowercase() == "dependente" }

        logger.info(
            "BeneficiaryConsumer::buildDependentBeneficiaries",
            "dependent_beneficiaries" to dependentBeneficiariesRequest.map { it.fullName },
            "parent_beneficiary_pairs" to parentBeneficiariesWithNationalId,
        )
        if (parentBeneficiariesWithNationalId.isEmpty()) return emptyList()

        return buildBeneficiaries(
            beneficiariesRequest = dependentBeneficiariesRequest,
            company = company,
            companyProductType = companyProductType,
            broker = broker,
            beneficiariesWithNationalId = parentBeneficiariesWithNationalId
        )
    }

    private suspend fun buildBeneficiaries(
        beneficiariesRequest: List<CognitoCompanyMemberRequest>?,
        company: Company,
        companyProductType: CompanyProductType?,
        broker: String?,
        beneficiariesWithNationalId: Map<String, Beneficiary> = emptyMap()
    ): List<BeneficiaryTransport> {
        if (beneficiariesRequest.isNullOrEmpty()) return emptyList()

        val membershipsRelationship = getMembershipRelationship()

        return beneficiariesRequest
            .pmapNotNull { beneficiaryRequest ->
                val beneficiaryAccountableId = beneficiaryRequest.accountableNationalId?.onlyNumbers()
                val parentBeneficiary = with(beneficiaryAccountableId) {
                    when {
                        this == null -> null
                        else -> {
                            beneficiariesWithNationalId[this] ?: run {
                                logger.error(
                                    "BeneficiaryConsumer::buildBeneficiaries not found beneficiary for given accountable national id",
                                    "beneficiary_accountable_national_id" to this,
                                    "beneficiary_national_id" to beneficiaryRequest.nationalId
                                )
                                return@pmapNotNull null
                            }
                        }
                    }
                }

                logger.info(
                    "BeneficiaryConsumer::handleCognitoCompanyUpserted beneficiary and person request",
                    "beneficiary" to beneficiaryRequest.fullName,
                    "company_id" to company.id,
                    "broker" to broker,
                    "parent_beneficiary_id" to parentBeneficiary?.id,
                    "beneficiaries_with_national_id" to beneficiariesWithNationalId,
                    "parent_type" to beneficiaryRequest.parentType
                )

                val beneficiaryAndPersonRequest = beneficiaryRequest.toBeneficiaryAndPersonRequest(
                    company = company,
                    companyProductType = companyProductType,
                    broker = broker,
                    parentBeneficiaryId = parentBeneficiary?.id
                ).copy(initialProductId = getChosenPlan(beneficiaryRequest, membershipsRelationship))

                beneficiaryAndPersonRequest
                    .toBeneficiaryTransport()
                    .let { beneficiaryTransport ->
                        logger.info(
                            "BeneficiaryConsumer::handleCognitoCompanyUpserted beneficiary transport",
                            "beneficiary_transport" to beneficiaryTransport.toString()
                        )
                        beneficiaryTransport
                    }
            }
    }

    private fun getMembershipRelationship() =
        FeatureService.get(
            namespace = FeatureNamespace.BUSINESS,
            key = "commercial-plans-memberships-relationship",
            defaultValue = ""
        ).let {
            try {
                gson.fromJson<Map<String, Map<String, String>>>(it, Map::class.java)
            } catch (exception: Exception) {
                logger.error(
                    "BeneficiaryConsumer::getChosenPlan error while getting feature flag",
                    "key" to "commercial-plans-memberships-relationship",
                    "error_message" to exception.message
                )
                null
            }
        }

    private fun getChosenPlan(
        request: CognitoCompanyMemberRequest,
        membershipsRelationship: Map<String, Map<String, String>>?
    ): UUID? {
        logger.debug(
            "BeneficiaryConsumer::getChosenPlan",
            "membership_relationship_map" to membershipsRelationship
        )

        return membershipsRelationship?.let {
            val chosenAlicePlan = request.chosenAlicePlan?.trim()?.lowercase()
                ?: request.chosenMembershipOneLifeOrMEI?.trim()?.lowercase()
                ?: request.chosenMembershipMoreThanTwoLifes?.trim()?.lowercase()

            val copay = request.copay?.trim()?.lowercase() ?: chosenAlicePlan?.let {
                if (it.contains("copart"))
                    "com copart"
                else
                    "sem copart"
            }

            when {
                chosenAlicePlan.isNotNullOrBlank() -> {
                    val copayPlan = if (copay?.contains("com copart") == true) "with_copay" else "without_copay"

                    val aliceMembershipMap = membershipsRelationship[chosenAlicePlan]?.asMap()
                    aliceMembershipMap?.let {
                        try {
                            it[copayPlan].toString().toUUID()
                        } catch (exception: Exception) {
                            logger.error(
                                "BeneficiaryConsumer::getChosenPlan error while converting product id from feature flag",
                                "membership_map" to it,
                                "copay_plan" to copayPlan,
                                "error_message" to exception.message
                            )
                            null
                        }
                    }
                }

                else -> null
            }
        }
    }

    private suspend fun findPersonsAndMembersByNationalIds(nationalIds: List<String>) =
        personService.findByNationalIds(nationalIds)
            .flatMapPair { persons ->
                val personIds = persons.map { it.id }
                if (personIds.isNotEmpty()) {
                    memberService.findActiveMembersByPersonIds(personIds)
                } else emptyList<Member>().success()
            }

    private suspend fun createBeneficiaries(
        beneficiariesToCreate: List<BeneficiaryTransport>,
        metadata: Map<String, String>
    ) = coroutineScope {
        beneficiariesToCreate.pmapNotNull { beneficiaryTransport ->
            createBeneficiary(
                beneficiaryTransport = beneficiaryTransport,
                productId = beneficiaryTransport.initialProductId,
                flowType = beneficiaryTransport.flowType,
                cassiMemberInfo = null,
                metadata = metadata.plus("nationalId" to beneficiaryTransport.nationalId),
            )?.coFoldDuplicated {
                logger.info(
                    "BeneficiaryConsumer::createBeneficiaries trying to get beneficiary that already exists",
                    "first_name" to beneficiaryTransport.firstName
                )
                personService.findByNationalId(beneficiaryTransport.nationalId)
                    .map {
                        val benefDef = async { beneficiaryService.findByPersonId(it.id).get() }
                        val companyDef = async { companyService.findByCnpj(beneficiaryTransport.companyCNPJ!!).get() }

                        companyDef.await() to benefDef.await()
                    }
                    .flatMap { (company, beneficiary) ->
                        if (company.id == beneficiary.companyId) beneficiary.success()
                        else DuplicatedItemException("O beneficiário já está cadastrado no CNPJ de outra empresa.").failure()
                    }
            }
                ?.thenError {
                    logger.error(
                        "BeneficiaryConsumer::createBeneficiaries something wrong happened when it tried to create the beneficiary.",
                        "beneficiary_name" to "${beneficiaryTransport.firstName} ${beneficiaryTransport.lastName}",
                        "beneficiary_national_id" to beneficiaryTransport.nationalId,
                        "company_id" to beneficiaryTransport.companyId,
                        "company_cnpj" to beneficiaryTransport.companyCNPJ,
                        "error_message" to it.message,
                    )
                }
                ?.map { beneficiaryTransport.nationalId to it }
        }.lift { successes, failures ->
            if (failures.isNotEmpty()) {
                ServerCouldNotProcessBeneficiaryCreationException(successes, failures).failure()
            }
            else
                successes.success()
        }
    }

    private suspend fun createBeneficiary(
        beneficiaryTransport: BeneficiaryTransport,
        productId: UUID?,
        flowType: BeneficiaryOnboardingFlowType?,
        cassiMemberInfo: CassiMemberInfo?,
        metadata: Map<String, String>? = null,
    ): Result<Beneficiary, Throwable>? {
        val beneficiaryType = if (beneficiaryTransport.parentBeneficiary == null) "holder" else "dependent"
        logger.info(
            "BeneficiaryConsumer::createBeneficiary adding $beneficiaryType",
            "first_name" to beneficiaryTransport.firstName
        )
        return beneficiaryService.createBeneficiary(
            beneficiaryTransport = beneficiaryTransport,
            initialProductId = productId,
            flowType = flowType ?: BeneficiaryOnboardingFlowType.UNDEFINED,
            cassiMemberInfo = cassiMemberInfo,
            createOptions = BeneficiaryService.CreateOptions(
                ignoreHiredAtValidation = true,
                shouldValidateAdditionalInfo = true,
                ignoreMembershipValidation = true
            ),
            metadata = metadata,
        ).fold(
            {
                beneficiaryCreatedIncrement(it.id, COGNITO)
                it.success()
            },
            { throwable -> handleBeneficiaryCreationError(throwable, beneficiaryTransport) }
        )
    }

    private fun handleBeneficiaryCreationError(
        throwable: Throwable,
        beneficiaryTransport: BeneficiaryTransport
    ): Result.Failure<Throwable>? {
        var errorMessage = "unhandled error"
        return with(throwable) {
            when (this) {
                is MembershipAlreadyActiveMismatchException -> null
                is InvalidArgumentException -> {
                    errorMessage = "CPF do beneficiário é inválido"
                    null
                }

                is PendingMembershipMismatchException -> {
                    errorMessage = "O beneficiário possui uma membership pendente em um produto diferente do selecionado no COGNITOt"
                    null
                }

                else -> throwable.failure()
            }.let {
                logBeneficiaryCreationError(
                    message = errorMessage,
                    beneficiaryTransport = beneficiaryTransport,
                    throwable = throwable
                )
                it
            }
        }
    }

    private fun shouldUseCompanyConfigurationStable() = FeatureService.get(
        namespace = FeatureNamespace.BUSINESS,
        key = "company_configuration_product_current_configuration_version",
        defaultValue = ConfigurationVersion.STABLE.toString()
    ).let { ConfigurationVersion.valueOf(it) } == ConfigurationVersion.STABLE

    private fun getMetadata(
        cognitoCompanyRequest: CognitoCompanyRequest
    ): Map<String, String> {
        val rangeOfBeneficiaries = cognitoCompanyRequest.rangeOfBeneficiaries
        val beneficiariesCountAtDayZero = cognitoCompanyRequest.beneficiariesCountAtDayZero?.toIntOrNull()
        val broker = cognitoCompanyRequest.broker
        val sizeConfiguration = rangeOfBeneficiaries?.let { CompanySizeConfiguration.getFromRangeOfBeneficiaries(it) }
            ?: beneficiariesCountAtDayZero?.let { CompanySizeConfiguration.getFromBeneficiariesCountAtDayZero(it, shouldUseCompanyConfigurationStable()) }

        val size = rangeOfBeneficiaries ?: beneficiariesCountAtDayZero?.toString()

        val channel = when {
            broker != null -> "3p"
            sizeConfiguration == CompanySizeConfiguration.M -> "MLA"
            else -> "Small Biz"
        }

        logger.info(
            "BeneficiaryConsumer::getMetadata",
            "size" to size,
            "configuration_size" to sizeConfiguration,
            "broker" to broker
        )

        return mapOf(
            "canal" to channel,
            "corretora" to broker,
            "empresaVidas" to size,
        ).filterNotNullValues()
    }

    private fun logBeneficiaryCreationError(
        message: String,
        beneficiaryTransport: BeneficiaryTransport,
        throwable: Throwable
    ) = logger.error(
        "BeneficiaryConsumer::createBeneficiary $message",
        "beneficiary_national_id" to beneficiaryTransport.nationalId,
        "company_id" to beneficiaryTransport.companyId,
        "company_cnpj" to beneficiaryTransport.companyCNPJ,
        "error_message" to throwable.message
    )

    @Suppress("UNCHECKED_CAST")
    private fun <K, V> Map<K, V?>.filterNotNullValues(): Map<K, V> =
        filterValues { it != null } as Map<K, V>

}
