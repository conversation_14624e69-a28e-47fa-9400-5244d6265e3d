package br.com.alice.business.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.events.CognitoCompanyUpsertedEvent
import br.com.alice.business.metrics.BeneficiaryMetric
import br.com.alice.business.model.CognitoCompanyMemberRequest
import br.com.alice.business.model.CognitoCompanyRequest
import br.com.alice.business.model.sanitize
import br.com.alice.business.model.toBeneficiaryTransport
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.helpers.verifyNone
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.CompanyProductType
import br.com.alice.data.layer.models.ConfigurationVersion
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.PriceAdjustmentType
import br.com.alice.data.layer.models.ProductType
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.MembershipAlreadyActiveMismatchException
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class BeneficiaryConsumerTest : ConsumerTest() {

    private val beneficiaryService: BeneficiaryService = mockk()
    private val personService: PersonService = mockk()
    private val memberService: MemberService = mockk()
    private val companyService: CompanyService = mockk()
    private val consumer = BeneficiaryConsumer(beneficiaryService, personService, memberService, companyService)

    private val members = listOf(
        CognitoCompanyMemberRequest(
            fullName = "MARIA DA SILVA",
            nationalId = "747.390.010-20",
            email = "<EMAIL>",
            phone = "11-*********",
            biologicalSex = "Masculino",
            dateOfBirth = "2000-01-01",
            mothersName = "Alice",
            chosenAlicePlan = "Equilíbrio Enfermaria Mais",
            copay = "SeM COPArticipaÇÃo",
            streetAddressState = "SP",
            dependencyType = "Titular",
            contractModel = "CLT",
            cnpj = "08.337.157/0001-19"
        ),
        CognitoCompanyMemberRequest(
            fullName = "JOSE PINHEIRO",
            nationalId = "524.809.180-25",
            email = null,
            accountableEmail = "<EMAIL>",
            phone = "11-*********",
            biologicalSex = "Masculino",
            dateOfBirth = "2000-01-01",
            mothersName = "Alice",
            chosenMembershipMoreThanTwoLifes = "Alice Conforto 210",
            streetAddressState = "SP",
            dependencyType = "Dependente",
            accountableNationalId = "747.390.010-20",
            parentType = "filho"
        )
    )
    private val cognitoCompanyRequest = CognitoCompanyRequest(
        id = "2",
        name = "Acme",
        legalName = "Acme Company",
        cnpj = "08.337.157/0001-19",
        email = "<EMAIL>",
        phoneNumber = "11-*********",
        rangeOfBeneficiaries = "2 a 5",
        contractType = "Livre adesão",
        contractStartedAt = "2023-10-01",
        beneficiariesCountAtDayZero = "10",
        microOrIndividualCompany = "MEI",
        billingAccountablePartyEmail = "<EMAIL>",
        billingAccountablePartyPhoneNumber = "11-*********",
        members = members
    )
    private val availableProductIds = listOf(
        RangeUUID.generate(),
        RangeUUID.generate()
    )
    private val defaultProductId = availableProductIds.firstOrNull() ?: RangeUUID.generate()
    private val company = TestModelFactory.buildCompany(
        name = cognitoCompanyRequest.name,
        legalName = cognitoCompanyRequest.legalName,
        cnpj = cognitoCompanyRequest.cnpj,
        email = cognitoCompanyRequest.email!!,
        phoneNumber = cognitoCompanyRequest.phoneNumber.orEmpty(),
        availableProducts = emptyList(),
        defaultProductId = defaultProductId,
        contractStartedAt = LocalDateTime.of(2023, 10, 1, 0, 0),
        beneficiariesCountAtDayZero = 10,
        defaultFlowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW,
        priceAdjustmentType = PriceAdjustmentType.POOL_SMALL_COMPANY
    )
    private val companyProductConfiguration = TestModelFactory.buildCompanyProductConfiguration(
        companyProductType = CompanyProductType.MICRO_2_5_OPTIONAL,
        availableProductIds = availableProductIds
    )
    private val event = CognitoCompanyUpsertedEvent(
        cognitoCompanyRequest = cognitoCompanyRequest,
        company = company,
        companyProductType = companyProductConfiguration.companyProductType
    )
    private val commercialPlansMembershipsRelationship =
        "{\n" +
                "  \"equilíbrio enfermaria\": {\n" +
                "    \"with_copay\": \"5b5b93ac-03ba-4b2f-86ed-b22620922270\",\n" +
                "    \"without_copay\": \"5b5b93ac-03ba-4b2f-86ed-b22620922271\"\n" +
                "  },\n" +
                "  \"equilíbrio enfermaria mais\": {\n" +
                "    \"with_copay\": \"5b5b93ac-03ba-4b2f-86ed-b22620922272\",\n" +
                "    \"without_copay\": \"5b5b93ac-03ba-4b2f-86ed-b22620922273\"\n" +
                "  },\n" +
                "  \"equilíbrio (copart.)\": {\n" +
                "    \"with_copay\": \"5b5b93ac-03ba-4b2f-86ed-b22620922272\",\n" +
                "    \"without_copay\": \"5b5b93ac-03ba-4b2f-86ed-b22620922273\"\n" +
                "  },\n" +
                "  \"equilíbrio apartamento\": {\n" +
                "    \"with_copay\": \"5b5b93ac-03ba-4b2f-86ed-b22620922274\",\n" +
                "    \"without_copay\": \"5b5b93ac-03ba-4b2f-86ed-b22620922275\"\n" +
                "  },\n" +
                "  \"equilíbrio apartamento mais\": {\n" +
                "    \"with_copay\": \"5b5b93ac-03ba-4b2f-86ed-b22620922276\",\n" +
                "    \"without_copay\": \"5b5b93ac-03ba-4b2f-86ed-b22620922277\"\n" +
                "  },\n" +
                "  \"alice exclusivo 111\": {\n" +
                "    \"with_copay\": \"5b5b93ac-03ba-4b2f-86ed-b22620922278\",\n" +
                "    \"without_copay\": \"5b5b93ac-03ba-4b2f-86ed-b22620922278\"\n" +
                "  },\n" +
                "  \"alice conforto 210\": {\n" +
                "    \"with_copay\": \"5b5b93ac-03ba-4b2f-86ed-b22620922279\",\n" +
                "    \"without_copay\": \"5b5b93ac-03ba-4b2f-86ed-b22620922279\"\n" +
                "  }\n" +
                "}"
    private val person = TestModelFactory.buildPerson(nationalId = "74739001020")
    private val beneficiary = TestModelFactory.buildBeneficiary(personId = person.id, companyId = company.id)
    private val beneficiariesRequest = cognitoCompanyRequest.members!!.sanitize()
    private val productId = "5b5b93ac-03ba-4b2f-86ed-b22620922273".toUUID()
    private val anotherProductId = "5b5b93ac-03ba-4b2f-86ed-b22620922279".toUUID()
    private val firstBeneficiaryTransport = buildBeneficiaryTransport(
        beneficiariesRequest.first(),
        productId
    )
    private val lastBeneficiaryTransport = buildBeneficiaryTransport(
        beneficiariesRequest.last(),
        anotherProductId,
        parentBeneficiaryId = beneficiary.id
    )
    private val member = TestModelFactory.buildMember(
        personId = person.id,
        status = MemberStatus.ACTIVE,
        productType = ProductType.B2B
    )
    private val beneficiaryCreateOptions = BeneficiaryService.CreateOptions(
        changeProduct = false,
        ignoreMembershipValidation = true,
        ignoreHiredAtValidation = true,
        shouldValidateAdditionalInfo = true
    )

    @BeforeTest
    fun setup() {
        super.before()

        mockkObject(BeneficiaryMetric)

        coEvery { beneficiaryService.findByPersonIds(listOf(person.id)) } returns listOf(beneficiary)
        coEvery {
            beneficiaryService.createBeneficiary(
                beneficiaryTransport = firstBeneficiaryTransport,
                initialProductId = "5b5b93ac-03ba-4b2f-86ed-b22620922273".toUUID(),
                flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW,
                cassiMemberInfo = null,
                createOptions = beneficiaryCreateOptions,
                metadata = mapOf(
                    "canal" to "Small Biz",
                    "empresaVidas" to "2 a 5",
                    "nationalId" to firstBeneficiaryTransport.nationalId,
                )
            )
        } returns beneficiary

        coEvery {
            beneficiaryService.createBeneficiary(
                beneficiaryTransport = lastBeneficiaryTransport,
                initialProductId = "5b5b93ac-03ba-4b2f-86ed-b22620922279".toUUID(),
                flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW,
                cassiMemberInfo = null,
                createOptions = beneficiaryCreateOptions,
                metadata = mapOf(
                    "canal" to "Small Biz",
                    "empresaVidas" to "2 a 5",
                    "nationalId" to lastBeneficiaryTransport.nationalId,
                )
            )
        } returns beneficiary
    }

    @Test
    fun `#handleCognitoCompanyUpserted should create beneficiaries with right information`() = runBlocking {
        val membersNationalIds = cognitoCompanyRequest.members?.map { it.nationalId } ?: emptyList()
        val holdersNationalIds =
            cognitoCompanyRequest.members?.mapNotNull { it.accountableNationalId } ?: emptyList()
        val nationalIds = (membersNationalIds + holdersNationalIds).distinct().map { it.onlyNumbers() }
        coEvery { personService.findByNationalIds(nationalIds) } returns emptyList()
        coEvery { memberService.findActiveMembersByPersonIds(listOf(person.id)) } returns listOf(member)
        coEvery { beneficiaryService.createBeneficiary(any(), any(), any(), any()) } returns beneficiary

        withFeatureFlag(
            FeatureNamespace.BUSINESS,
            "commercial-plans-memberships-relationship",
            commercialPlansMembershipsRelationship
        ) {
            val result = consumer.handleCognitoCompanyUpserted(event)
            assertThat(result).isSuccessWithData(true)
        }

        verify(exactly = 2) {
            BeneficiaryMetric.beneficiaryCreatedIncrement(
                any(),
                BeneficiaryMetric.BeneficiaryCreationFlow.COGNITO
            )
        }
        coVerifyOnce { personService.findByNationalIds(nationalIds) }
    }

    @Test
    fun `#handleCognitoCompanyUpserted should create beneficiaries with right information but it should not create some beneficiary when something is wrong`() =
        mockkObject(logger) {
            runBlocking {
                val membersNationalIds = cognitoCompanyRequest.members?.map { it.nationalId } ?: emptyList()
                val holdersNationalIds =
                    cognitoCompanyRequest.members?.mapNotNull { it.accountableNationalId } ?: emptyList()
                val nationalIds = (membersNationalIds + holdersNationalIds).distinct().map { it.onlyNumbers() }
                val errorMessage = """
                    Não foi possível cadastrar todos os beneficiários.
                    Sucessos: 0, falhas: 1.
                    Mensagens de erro: Sou algum erro qualquer
                """.trimIndent()

                coEvery { personService.findByNationalIds(nationalIds) } returns emptyList()

                coEvery {
                    beneficiaryService.createBeneficiary(
                        beneficiaryTransport = lastBeneficiaryTransport,
                        initialProductId = "5b5b93ac-03ba-4b2f-86ed-b22620922279".toUUID(),
                        flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW,
                        cassiMemberInfo = null,
                        createOptions = beneficiaryCreateOptions,
                        metadata = mapOf(
                            "canal" to "Small Biz",
                            "empresaVidas" to "2 a 5",
                            "nationalId" to lastBeneficiaryTransport.nationalId,
                        )
                    )
                } returns Exception("Sou algum erro qualquer")

                withFeatureFlag(
                    FeatureNamespace.BUSINESS,
                    "commercial-plans-memberships-relationship",
                    commercialPlansMembershipsRelationship
                ) {
                    val result = consumer.handleCognitoCompanyUpserted(event)
                    assertThat(result).isSuccessWithData(true)
                }

                coVerifyOnce {
                    BeneficiaryMetric.beneficiaryCreatedIncrement(
                        any(),
                        BeneficiaryMetric.BeneficiaryCreationFlow.COGNITO
                    )
                }
                coVerifyOnce { personService.findByNationalIds(nationalIds) }
                coVerifyOnce { logger.error(
                    "BeneficiaryConsumer::handleCognitoCompanyUpserted error while creating dependent beneficiaries",
                    "error_message" to errorMessage,
                    "company_cnpj" to company.cnpj
                ) }
            }
        }

    @Test
    fun `#handleCognitoCompanyUpserted should create beneficiaries with right information when there is a parent outside request`() =
        runBlocking {
            val testPayload = event.payload.copy(
                cognitoCompanyRequest = event.payload.cognitoCompanyRequest.copy(
                    members = listOf(
                        CognitoCompanyMemberRequest(
                            fullName = "MARIA DA SILVA",
                            nationalId = "747.390.010-20",
                            email = "<EMAIL>",
                            phone = "11-*********",
                            biologicalSex = "Masculino",
                            dateOfBirth = "2000-01-01",
                            mothersName = "Alice",
                            chosenAlicePlan = "Equilíbrio Enfermaria Mais",
                            copay = "SeM COPArticipaÇÃo",
                            streetAddressState = "SP",
                            dependencyType = "Dependente",
                            contractModel = "CLT",
                            cnpj = "08.337.157/0001-19",
                            accountableNationalId = "***********"
                        )
                    )
                )
            )
            val testEvent = CognitoCompanyUpsertedEvent(
                cognitoCompanyRequest = testPayload.cognitoCompanyRequest,
                company = testPayload.company,
                companyProductType = testPayload.companyProductType
            )
            val parentPerson = TestModelFactory.buildPerson(nationalId = "***********")
            val beneficiaryParent =
                TestModelFactory.buildBeneficiary(personId = parentPerson.id, companyId = company.id)

            val membersNationalIds = testPayload.cognitoCompanyRequest.members?.map { it.nationalId } ?: emptyList()
            val holdersNationalIds =
                testPayload.cognitoCompanyRequest.members?.mapNotNull { it.accountableNationalId } ?: emptyList()
            val nationalIds = (membersNationalIds + holdersNationalIds).distinct().map { it.onlyNumbers() }

            coEvery { personService.findByNationalIds(nationalIds) } returns listOf(parentPerson)
            coEvery { memberService.findActiveMembersByPersonIds(listOf(parentPerson.id)) } returns listOf(member)
            coEvery {
                beneficiaryService.createBeneficiary(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            } returns beneficiary
            coEvery { beneficiaryService.findByPersonId(parentPerson.id) } returns beneficiaryParent

            val result = consumer.handleCognitoCompanyUpserted(testEvent)
            assertThat(result).isSuccessWithData(true)

            verify(exactly = 1) {
                BeneficiaryMetric.beneficiaryCreatedIncrement(
                    any(),
                    BeneficiaryMetric.BeneficiaryCreationFlow.COGNITO
                )
            }
            coVerifyOnce { personService.findByNationalIds(nationalIds) }
        }

    @Test
    fun `#handleCognitoCompanyUpserted should create beneficiaries with right information metadata`() =
        runBlocking {
            withFeatureFlag(
                FeatureNamespace.BUSINESS,
                "company_configuration_product_current_configuration_version",
                ConfigurationVersion.TRANSITORY.toString()
            ) {

                val testPayload = event.payload.copy(
                    cognitoCompanyRequest = event.payload.cognitoCompanyRequest.copy(
                        members = listOf(
                            CognitoCompanyMemberRequest(
                                fullName = "MARIA DA SILVA",
                                nationalId = "747.390.010-20",
                                email = "<EMAIL>",
                                phone = "11-*********",
                                biologicalSex = "Masculino",
                                dateOfBirth = "2000-01-01",
                                mothersName = "Alice",
                                chosenAlicePlan = "Equilíbrio Enfermaria Mais",
                                copay = "SeM COPArticipaÇÃo",
                                streetAddressState = "SP",
                                dependencyType = "Dependente",
                                contractModel = "CLT",
                                cnpj = "08.337.157/0001-19",
                                accountableNationalId = "***********"
                            )
                        )
                    )
                )
                val testEvent = CognitoCompanyUpsertedEvent(
                    cognitoCompanyRequest = testPayload.cognitoCompanyRequest,
                    company = testPayload.company,
                    companyProductType = testPayload.companyProductType
                )
                val parentPerson = TestModelFactory.buildPerson(nationalId = "***********")
                val beneficiaryParent =
                    TestModelFactory.buildBeneficiary(personId = parentPerson.id, companyId = company.id)

                val membersNationalIds = testPayload.cognitoCompanyRequest.members?.map { it.nationalId } ?: emptyList()
                val holdersNationalIds =
                    testPayload.cognitoCompanyRequest.members?.mapNotNull { it.accountableNationalId } ?: emptyList()
                val nationalIds = (membersNationalIds + holdersNationalIds).distinct().map { it.onlyNumbers() }

                coEvery { personService.findByNationalIds(nationalIds) } returns listOf(parentPerson)
                coEvery { memberService.findActiveMembersByPersonIds(listOf(parentPerson.id)) } returns listOf(member)
                coEvery {
                    beneficiaryService.createBeneficiary(
                        any(),
                        any(),
                        any(),
                        any(),
                        any(),
                        any()
                    )
                } returns beneficiary
                coEvery { beneficiaryService.findByPersonId(parentPerson.id) } returns beneficiaryParent

                val result = consumer.handleCognitoCompanyUpserted(testEvent)
                assertThat(result).isSuccessWithData(true)

                verifyOnce {
                    BeneficiaryMetric.beneficiaryCreatedIncrement(
                        any(),
                        BeneficiaryMetric.BeneficiaryCreationFlow.COGNITO
                    )
                }
                coVerifyOnce { personService.findByNationalIds(nationalIds) }
            }
        }

    @Test
    fun `#handleCognitoCompanyUpserted should create beneficiaries with right information with copay by name`() =
        runBlocking {
            val members = listOf(
                CognitoCompanyMemberRequest(
                    fullName = "MARIA DA SILVA",
                    nationalId = "747.390.010-20",
                    email = "<EMAIL>",
                    phone = "11-*********",
                    biologicalSex = "Masculino",
                    dateOfBirth = "2000-01-01",
                    mothersName = "Alice",
                    chosenAlicePlan = "equilíbrio (copart.)",
                    streetAddressState = "SP",
                    dependencyType = "Titular"
                )
            )

            val cognitoCompanyRequest = CognitoCompanyRequest(
                id = "2",
                name = "Acme",
                legalName = "Acme Company",
                cnpj = "08.337.157/0001-19",
                email = "<EMAIL>",
                phoneNumber = "11-*********",
                rangeOfBeneficiaries = "2 a 5",
                contractType = "Livre adesão",
                contractStartedAt = "2023-10-01",
                beneficiariesCountAtDayZero = "10",
                microOrIndividualCompany = "MEI",
                billingAccountablePartyEmail = "<EMAIL>",
                billingAccountablePartyPhoneNumber = "11-*********",
                members = members
            )

            val productId = "5b5b93ac-03ba-4b2f-86ed-b22620922272".toUUID()

            val firstBeneficiaryTransport = buildBeneficiaryTransport(
                members.first(),
                productId
            )

            val event = CognitoCompanyUpsertedEvent(
                cognitoCompanyRequest = cognitoCompanyRequest,
                company = company,
                companyProductType = companyProductConfiguration.companyProductType
            )

            coEvery {
                beneficiaryService.createBeneficiary(
                    beneficiaryTransport = firstBeneficiaryTransport,
                    initialProductId = "5b5b93ac-03ba-4b2f-86ed-b22620922272".toUUID(),
                    flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW,
                    cassiMemberInfo = null,
                    createOptions = beneficiaryCreateOptions,
                    metadata = mapOf(
                        "canal" to "Small Biz",
                        "empresaVidas" to "2 a 5",
                        "nationalId" to firstBeneficiaryTransport.nationalId,
                    )
                )
            } returns beneficiary

            val membersNationalIds =
                cognitoCompanyRequest.members?.map { it.nationalId }?.map { it.onlyNumbers() } ?: emptyList()


            coEvery { personService.findByNationalIds(membersNationalIds) } returns emptyList()
            coEvery { memberService.findActiveMembersByPersonIds(listOf(person.id)) } returns listOf(member)

            withFeatureFlag(
                FeatureNamespace.BUSINESS,
                "commercial-plans-memberships-relationship",
                commercialPlansMembershipsRelationship
            ) {
                val result = consumer.handleCognitoCompanyUpserted(event)
                assertThat(result).isSuccessWithData(true)
            }

            coVerifyOnce {
                BeneficiaryMetric.beneficiaryCreatedIncrement(
                    any(),
                    BeneficiaryMetric.BeneficiaryCreationFlow.COGNITO
                )
            }
            coVerifyOnce { personService.findByNationalIds(membersNationalIds) }
        }

    @Test
    fun `#handleCognitoCompanyUpserted should not create beneficiaries when any person already has B2C membership`() =
        runBlocking {
            val b2cMember = TestModelFactory.buildMember(
                personId = person.id,
                status = MemberStatus.ACTIVE,
                productType = ProductType.B2C
            )
            coEvery { personService.findByNationalIds(any()) } returns listOf(person)
            coEvery { memberService.findActiveMembersByPersonIds(listOf(person.id)) } returns listOf(b2cMember)
            val result = consumer.handleCognitoCompanyUpserted(event)
            assertThat(result).isSuccessWithData(false)

            verifyNone { BeneficiaryMetric.beneficiaryCreatedIncrement(any(), any()) }
        }

    @Test
    fun `#handleCognitoCompanyUpserted should only create beneficiaries that does not has active membership`() =
        runBlocking {
            val membersNationalIds = cognitoCompanyRequest.members?.map { it.nationalId } ?: emptyList()
            val holdersNationalIds =
                cognitoCompanyRequest.members?.mapNotNull { it.accountableNationalId } ?: emptyList()
            val nationalIds = (membersNationalIds + holdersNationalIds).distinct().map { it.onlyNumbers() }
            coEvery { personService.findByNationalIds(nationalIds) } returns emptyList()
            coEvery { memberService.findActiveMembersByPersonIds(listOf(person.id)) } returns listOf(member)

            coEvery {
                beneficiaryService.createBeneficiary(
                    beneficiaryTransport = lastBeneficiaryTransport,
                    initialProductId = "5b5b93ac-03ba-4b2f-86ed-b22620922279".toUUID(),
                    flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW,
                    cassiMemberInfo = null,
                    createOptions = beneficiaryCreateOptions,
                    metadata = mapOf(
                        "canal" to "Small Biz",
                        "empresaVidas" to "2 a 5",
                        "nationalId" to lastBeneficiaryTransport.nationalId,
                    )
                )
            } returns MembershipAlreadyActiveMismatchException(beneficiary.personId)

            withFeatureFlag(
                FeatureNamespace.BUSINESS,
                "commercial-plans-memberships-relationship",
                commercialPlansMembershipsRelationship
            ) {
                val result = consumer.handleCognitoCompanyUpserted(event)
                assertThat(result).isSuccessWithData(true)
            }

            coVerifyOnce {
                BeneficiaryMetric.beneficiaryCreatedIncrement(
                    any(),
                    BeneficiaryMetric.BeneficiaryCreationFlow.COGNITO
                )
            }
            coVerifyOnce { personService.findByNationalIds(nationalIds) }
            coVerify(exactly = 2) { beneficiaryService.createBeneficiary(any(), any(), any(), any(), any(), any()) }
        }

    @Test
    fun `#handleCognitoCompanyUpserted should not create dependent beneficiary when given accountable national id is not found or same as yours`() =
        runBlocking {
            val membersWithInvalidOne = listOf(
                CognitoCompanyMemberRequest(
                    fullName = "MARIA DA SILVA",
                    nationalId = "747.390.010-20",
                    email = "<EMAIL>",
                    phone = "11-*********",
                    biologicalSex = "Masculino",
                    dateOfBirth = "2000-01-01",
                    mothersName = "Alice",
                    chosenAlicePlan = "Equilíbrio Enfermaria Mais",
                    copay = "SeM COPArticipaÇÃo",
                    streetAddressState = "SP",
                    dependencyType = "Titular",
                    contractModel = "CLT",
                    cnpj = "08.337.157/0001-19"
                ),
                CognitoCompanyMemberRequest(
                    fullName = "JOSE PINHEIRO",
                    nationalId = "524.809.180-25",
                    email = null,
                    accountableEmail = "<EMAIL>",
                    phone = "11-*********",
                    biologicalSex = "Masculino",
                    dateOfBirth = "2000-01-01",
                    mothersName = "Alice",
                    chosenMembershipMoreThanTwoLifes = "Alice Conforto 210",
                    streetAddressState = "SP",
                    dependencyType = "Dependente",
                    accountableNationalId = "524.809.180-25",
                    parentType = "filho"
                )
            )
            val event = CognitoCompanyUpsertedEvent(
                cognitoCompanyRequest = cognitoCompanyRequest.copy(members = membersWithInvalidOne),
                company = company,
                companyProductType = companyProductConfiguration.companyProductType
            )
            val membersNationalIds = members.map { it.nationalId }
            val holdersNationalIds = members.mapNotNull { it.accountableNationalId }
            val nationalIds = (membersNationalIds + holdersNationalIds).distinct().map { it.onlyNumbers() }
            coEvery { personService.findByNationalIds(nationalIds) } returns emptyList()
            coEvery { memberService.findActiveMembersByPersonIds(listOf(person.id)) } returns listOf(member)

            withFeatureFlag(
                FeatureNamespace.BUSINESS,
                "commercial-plans-memberships-relationship",
                commercialPlansMembershipsRelationship
            ) {
                val result = consumer.handleCognitoCompanyUpserted(event)
                assertThat(result).isSuccessWithData(true)
            }

            coVerifyOnce {
                BeneficiaryMetric.beneficiaryCreatedIncrement(
                    any(),
                    BeneficiaryMetric.BeneficiaryCreationFlow.COGNITO
                )
            }
            coVerifyOnce { personService.findByNationalIds(nationalIds) }
        }

    private fun buildBeneficiaryTransport(
        request: CognitoCompanyMemberRequest,
        initialProductId: UUID,
        parentBeneficiaryId: UUID? = null
    ) =
        request
            .toBeneficiaryAndPersonRequest(
                company = company,
                companyProductType = CompanyProductType.MICRO_2_5_OPTIONAL,
                broker = null,
                parentBeneficiaryId = parentBeneficiaryId
            )
            .copy(initialProductId = initialProductId)
            .toBeneficiaryTransport()

    @Test
    fun `#handleCognitoCompanyUpserted should find the beneficiary when duplicated error happens`() = runBlocking {
        val membersNationalIds = cognitoCompanyRequest.members?.map { it.nationalId } ?: emptyList()
        val holdersNationalIds =
            cognitoCompanyRequest.members?.mapNotNull { it.accountableNationalId } ?: emptyList()
        val nationalIds = (membersNationalIds + holdersNationalIds).distinct().map { it.onlyNumbers() }
        coEvery { personService.findByNationalIds(nationalIds) } returns emptyList()
        coEvery { memberService.findActiveMembersByPersonIds(listOf(person.id)) } returns listOf(member)
        coEvery {
            beneficiaryService.createBeneficiary(
                beneficiaryTransport = lastBeneficiaryTransport,
                initialProductId = "5b5b93ac-03ba-4b2f-86ed-b22620922279".toUUID(),
                flowType = BeneficiaryOnboardingFlowType.PARTIAL_RISK_FLOW,
                cassiMemberInfo = null,
                createOptions = beneficiaryCreateOptions,
                metadata = mapOf(
                    "canal" to "Small Biz",
                    "empresaVidas" to "2 a 5",
                    "nationalId" to lastBeneficiaryTransport.nationalId,
                )
            )
        } returns DuplicatedItemException("Duplicated item")

        coEvery { personService.findByNationalId(lastBeneficiaryTransport.nationalId.onlyNumbers()) } returns person
        coEvery { beneficiaryService.findByPersonId(person.id) } returns beneficiary.success()
        coEvery { companyService.findByCnpj(cognitoCompanyRequest.cnpj) } returns company.success()

        withFeatureFlag(
            FeatureNamespace.BUSINESS,
            "commercial-plans-memberships-relationship",
            commercialPlansMembershipsRelationship
        ) {
            val result = consumer.handleCognitoCompanyUpserted(event)
            assertThat(result).isSuccessWithData(true)
        }

        coVerifyOnce {
            BeneficiaryMetric.beneficiaryCreatedIncrement(
                any(),
                BeneficiaryMetric.BeneficiaryCreationFlow.COGNITO
            )
        }
        coVerifyOnce { personService.findByNationalIds(any()) }
        coVerifyOnce { companyService.findByCnpj(any()) }
    }

}
