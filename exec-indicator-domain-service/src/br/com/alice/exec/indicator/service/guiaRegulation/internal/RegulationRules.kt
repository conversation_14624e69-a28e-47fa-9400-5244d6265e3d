package br.com.alice.exec.indicator.service.guiaRegulation.internal

import br.com.alice.exec.indicator.models.ProcedureRegulationResult
import br.com.alice.exec.indicator.service.guiaRegulation.RegulationData

class RegulationRules() {

    suspend fun evaluate(regulationData: RegulationData): ProcedureRegulationResult {
        return TODO("Evaluate a procedure based on the regulation data")
    }

}
