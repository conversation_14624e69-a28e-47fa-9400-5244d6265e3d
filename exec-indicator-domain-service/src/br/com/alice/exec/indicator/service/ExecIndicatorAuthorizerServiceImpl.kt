package br.com.alice.exec.indicator.service

import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.mapFirst
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.not
import br.com.alice.data.layer.models.ExecIndicatorAuthorizer
import br.com.alice.data.layer.services.ExecIndicatorAuthorizerModelDataService
import br.com.alice.exec.indicator.client.ExecIndicatorAuthorizerService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import br.com.alice.exec.indicator.converters.modelConverters.toTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import java.util.UUID

class ExecIndicatorAuthorizerServiceImpl(
    private val execIndicatorAuthorizerDataService: ExecIndicatorAuthorizerModelDataService
) : ExecIndicatorAuthorizerService {
    override suspend fun add(model: ExecIndicatorAuthorizer): Result<ExecIndicatorAuthorizer, Throwable> {
        return execIndicatorAuthorizerDataService.add(model.toModel()).map { it.toTransport() }
    }

    override suspend fun get(id: UUID): Result<ExecIndicatorAuthorizer, Throwable> {
        return execIndicatorAuthorizerDataService.get(id).map { it.toTransport() }
    }

    override suspend fun update(model: ExecIndicatorAuthorizer): Result<ExecIndicatorAuthorizer, Throwable> {
        return execIndicatorAuthorizerDataService.update(model.toModel()).map { it.toTransport() }
    }

    override suspend fun countAll(): Result<Int, Throwable> =
        execIndicatorAuthorizerDataService.count { all() }

    override suspend fun getByRange(range: IntRange): Result<List<ExecIndicatorAuthorizer>, Throwable> =
        execIndicatorAuthorizerDataService.find {
            where {
                not(domain.eq(""))
            }
            orderBy { updatedAt }
                .sortOrder { desc }
                .offset { range.first }
                .limit { range.count() }
        }.map {
            it.toTransport()
        }

    override suspend fun getByDomain(domainRequest: String): Result<List<ExecIndicatorAuthorizer>, Throwable> =
        execIndicatorAuthorizerDataService.find { where { domain.eq(domainRequest) } }.map {
            it.toTransport()
        }

    override suspend fun getByProviderUnitId(providerUnitId: UUID): Result<ExecIndicatorAuthorizer, Throwable> =
        execIndicatorAuthorizerDataService.findOne { where { this.providerUnitId.eq(providerUnitId) } }
            .map { it.toTransport() }

    override suspend fun getByProviderUnitIds(providerUnitIds: List<UUID>, range: IntRange?): Result<List<ExecIndicatorAuthorizer>, Throwable> =
        execIndicatorAuthorizerDataService.find {
            where {
                providerUnitId.inList(providerUnitIds)
            }.let { it.withRange(range) }
        }.mapEach {
            it.toTransport()
        }

    override suspend fun countByProviderUnitIds(providerUnitIds: List<UUID>): Result<Int, Throwable> =
        execIndicatorAuthorizerDataService.count {
            where {
                providerUnitId.inList(providerUnitIds)
            }
        }

    private fun QueryBuilder<ExecIndicatorAuthorizerModelDataService.FieldOptions, ExecIndicatorAuthorizerModelDataService.OrderingOptions>.withRange(
        range: IntRange?
    ) =
        if (range != null) this.offset { range.first }.limit { range.count() }
        else this

    override suspend fun getByMvCdLocalPrestador(cdLocalPrestador: Int): Result<ExecIndicatorAuthorizer, Throwable> =
        execIndicatorAuthorizerDataService.find { where { mvCdLocalPrestador.eq(cdLocalPrestador) } }.mapFirst()
            .map { it.toTransport() }

    override suspend fun getByMvCdPrestador(cdPrestador: Int): Result<ExecIndicatorAuthorizer, Throwable> =
        execIndicatorAuthorizerDataService.find { where { mvCdPrestador.eq(cdPrestador) } }.mapFirst()
            .map { it.toTransport() }

    override suspend fun findByIds(ids: List<UUID>): Result<List<ExecIndicatorAuthorizer>, Throwable> =
        execIndicatorAuthorizerDataService.find { where { id.inList(ids) } }
            .map {
                it.toTransport()
            }

    override suspend fun delete(id: UUID): Result<Boolean, Throwable> =
        execIndicatorAuthorizerDataService.get(id)
            .flatMap {
                execIndicatorAuthorizerDataService.softDelete(it)
            }
}
