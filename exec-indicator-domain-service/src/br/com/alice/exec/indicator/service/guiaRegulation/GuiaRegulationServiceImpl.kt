package br.com.alice.exec.indicator.service.guiaRegulation

import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coResultOf
import br.com.alice.data.layer.models.HealthcareResource
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.ResourceGracePeriodType
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.ehr.client.MemberCptsService
import br.com.alice.ehr.model.GracePeriodType
import br.com.alice.ehr.model.MemberCpt
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.client.guiaRegulation.GuiaRegulationService
import br.com.alice.exec.indicator.models.GuiaRegulationResult
import br.com.alice.exec.indicator.models.ProcedureRegulationResult
import br.com.alice.exec.indicator.models.RegulationStatus
import br.com.alice.exec.indicator.service.guiaRegulation.internal.RegulationRules
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.util.UUID

class GuiaRegulationServiceImpl(
    private val totvsGuiaService: TotvsGuiaService,
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService,
    private val healthcareResourceService: HealthcareResourceService,
    private val memberCptsService: MemberCptsService,
    private val regulationRules: RegulationRules,
//    private val guiaRegulationResultService: GuiaRegulationResultService,
//    private val kafkaProducerService: KafkaProducerService
) : GuiaRegulationService {

    override suspend fun regulate(guiaId: UUID) = coResultOf<GuiaRegulationResult, Throwable> {
        coroutineScope {
            val guiaDataDeferred = async { fetchGuiaData(guiaId) }
            val proceduresDataDeferred = async { fetchProceduresData(guiaId) }

            val guiaData = guiaDataDeferred.await()
            val proceduresData = proceduresDataDeferred.await()

            val resourcesDataDeferred = async { fetchResourcesData(proceduresData) }
            val memberDataDeferred = async { fetchMemberData(guiaData.personId) }

            val resourcesData = resourcesDataDeferred.await()
            val memberData = memberDataDeferred.await()

            val resourceRegulationDataList = buildResourceRegulationDataList(proceduresData, resourcesData)
            val memberRegulationDataList = buildMemberRegulationDataList(memberData)

            val procedureEvaluations = evaluateProcedures(resourceRegulationDataList, memberRegulationDataList)
            val overallStatus = determineOverallStatus(procedureEvaluations)

            GuiaRegulationResult(
                guiaId = guiaId,
                status = overallStatus,
                procedures = procedureEvaluations
            )
        }
    }

    private suspend fun fetchGuiaData(guiaId: UUID): TotvsGuia =
        totvsGuiaService.get(guiaId).get()

    private suspend fun fetchProceduresData(guiaId: UUID): List<MvAuthorizedProcedure> =
        mvAuthorizedProcedureService.findByTotvsGuiaId(guiaId).get()

    private suspend fun fetchResourcesData(procedures: List<MvAuthorizedProcedure>): Map<String, HealthcareResource> {
        val procedureCodes = extractProcedureCodes(procedures)
        val resources = healthcareResourceService.findByCodes(procedureCodes, false).get()
        return resources.associateBy { it.code }
    }

    private fun extractProcedureCodes(procedures: List<MvAuthorizedProcedure>): List<String> =
        procedures.mapNotNull { it.procedureId }.distinct()

    private suspend fun fetchMemberData(personId: PersonId): MemberCpt =
        memberCptsService.buildPersonCptsByPersonId(personId).get()

    private fun buildResourceRegulationDataList(
        procedures: List<MvAuthorizedProcedure>,
        resourcesByCode: Map<String, HealthcareResource>
    ): List<ResourceRegulationData> =
        procedures.map { procedure ->
            buildResourceRegulationData(procedure, resourcesByCode)
        }

    private fun buildResourceRegulationData(
        procedure: MvAuthorizedProcedure,
        resourcesByCode: Map<String, HealthcareResource>
    ): ResourceRegulationData {
        val procedureCode = procedure.procedureId!!
        val resource = resourcesByCode[procedureCode]

        return ResourceRegulationData(
            code = procedureCode,
            tableType = resource?.tableType.orEmpty(),
            tussCode = resource?.tussCode.orEmpty(),
            quantity = procedure.extraGuiaInfo.quantity ?: DEFAULT_QUANTITY,
            isPac = resource?.pac == true,
            isRol = resource?.nrol == false,
            isDut = resource?.dut == true,
            resourceGracePeriodType = resource?.gracePeriodType
        )
    }

    private fun buildMemberRegulationDataList(memberCpt: MemberCpt): List<MemberRegulationData> =
        memberCpt.gracePeriod.map { gracePeriod ->
            MemberRegulationData(
                condition = gracePeriod.condition,
                validUntil = gracePeriod.validUntil,
                baseDate = gracePeriod.baseDate,
                type = gracePeriod.type,
                periodInDays = gracePeriod.periodInDays
            )
        }

    private suspend fun evaluateProcedures(
        resourceRegulationDataList: List<ResourceRegulationData>,
        memberRegulationDataList: List<MemberRegulationData>
    ): List<ProcedureRegulationResult> =
        resourceRegulationDataList.map { resourceData ->
            val regulationData = RegulationData(
                resourceRegulationData = resourceData,
                memberRegulationData = memberRegulationDataList
            )
            regulationRules.evaluate(regulationData)
        }

    private fun determineOverallStatus(evaluations: List<ProcedureRegulationResult>): RegulationStatus =
        when {
            evaluations.any { it.status == RegulationStatus.PENDING } -> RegulationStatus.PENDING
            evaluations.all { it.status == RegulationStatus.UNAUTHORIZED } -> RegulationStatus.UNAUTHORIZED
            evaluations.all { it.status == RegulationStatus.AUTHORIZED } -> RegulationStatus.AUTHORIZED
            else -> RegulationStatus.PARTIALLY_AUTHORIZED
        }

    companion object {
        private const val DEFAULT_QUANTITY = 1
    }
}

data class RegulationData(
    val resourceRegulationData: ResourceRegulationData,
    val memberRegulationData: List<MemberRegulationData>
)

data class ResourceRegulationData(
    val code: String,
    val tableType: String,
    val tussCode: String,
    val quantity: Int,
    val isPac: Boolean,
    val isRol: Boolean,
    val isDut: Boolean,
    val resourceGracePeriodType: ResourceGracePeriodType? = null
)

data class MemberRegulationData(
    val condition: String,
    val validUntil: String,
    val baseDate: LocalDate,
    val type: GracePeriodType,
    val periodInDays: Long
)

