# Guia Regulation Module

## Description
This module contains the logic for regulating medical requests (referred to as "Guias"). It validates requests based on business rules such as waiting periods (carência), coverage lists (ROL), and medical guidelines (DUT).

To evaluate and regulate medical guide requests by applying domain-specific rules, and produce a final status: `Authorized`, `Pending`, or `Denied`.

## Module Overview

## Usage
