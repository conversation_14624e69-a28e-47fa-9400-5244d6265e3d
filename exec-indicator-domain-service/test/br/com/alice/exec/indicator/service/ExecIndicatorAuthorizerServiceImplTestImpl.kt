package br.com.alice.exec.indicator.service

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.ExecIndicatorAuthorizerModelDataService
import br.com.alice.exec.indicator.converters.modelConverters.toModel
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import kotlin.test.AfterTest
import kotlin.test.Test


@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ExecIndicatorAuthorizerServiceImplTestImpl {
    private val data: ExecIndicatorAuthorizerModelDataService = mockk()
    private val service = ExecIndicatorAuthorizerServiceImpl(data)

    private val authorizer = TestModelFactory.buildExecIndicatorAuthorizer()

    @AfterTest
    fun setup() = clearAllMocks()

    @Test
    fun `#getByRange should retrieve a list of authorizers`() = runBlocking<Unit> {
        val range = IntRange(0, 19)

        coEvery {
            data.find(queryEq {
                orderBy { updatedAt }
                    .sortOrder { desc }
                    .offset { range.first }
                    .limit { range.count() }
            })
        } returns listOf(authorizer).toModel().success()

        val result = service.getByRange(range)

        assertThat(result).isSuccessWithData(listOf(authorizer))
    }

    @Test
    fun `#getByDomain should retrieve a list of authorizers`() = runBlocking<Unit> {
        val domainRequest = "alice.com.br"

        coEvery { data.find(queryEq { where { domain.eq(domainRequest) } }) } returns listOf(authorizer).toModel().success()

        val result = service.getByDomain(domainRequest)

        assertThat(result).isSuccessWithData(listOf(authorizer))
    }

    @Test
    fun `#add should add authorizer successfully`() = runBlocking<Unit> {
        val authorizerModel = authorizer.toModel()

        coEvery { data.add(authorizerModel) } returns authorizerModel.success()

        val result = service.add(authorizer)

        assertThat(result).isSuccessWithData(authorizer)
        coVerify { data.add(authorizerModel) }
    }

    @Test
    fun `#add should fail when data service fails`() = runBlocking<Unit> {
        val authorizerModel = authorizer.toModel()
        val exception = RuntimeException("Database error")

        coEvery { data.add(authorizerModel) } returns exception.failure()

        val result = service.add(authorizer)

        assertThat(result).isFailureOfType(RuntimeException::class)
        coVerify { data.add(authorizerModel) }
    }

    @Test
    fun `#get should retrieve authorizer by id successfully`() = runBlocking<Unit> {
        val authorizerId = authorizer.id
        val authorizerModel = authorizer.toModel()

        coEvery { data.get(authorizerId) } returns authorizerModel.success()

        val result = service.get(authorizerId)

        assertThat(result).isSuccessWithData(authorizer)
        coVerify { data.get(authorizerId) }
    }

    @Test
    fun `#get should fail when authorizer not found`() = runBlocking<Unit> {
        val authorizerId = authorizer.id
        val exception = NotFoundException("Authorizer not found")

        coEvery { data.get(authorizerId) } returns exception.failure()

        val result = service.get(authorizerId)

        assertThat(result).isFailureOfType(NotFoundException::class)
        coVerify { data.get(authorizerId) }
    }

    @Test
    fun `#update should update authorizer successfully`() = runBlocking<Unit> {
        val authorizerModel = authorizer.toModel()

        coEvery { data.update(authorizerModel) } returns authorizerModel.success()

        val result = service.update(authorizer)

        assertThat(result).isSuccessWithData(authorizer)
        coVerify { data.update(authorizerModel) }
    }

    @Test
    fun `#update should fail when data service fails`() = runBlocking<Unit> {
        val authorizerModel = authorizer.toModel()
        val exception = RuntimeException("Update failed")

        coEvery { data.update(authorizerModel) } returns exception.failure()

        val result = service.update(authorizer)

        assertThat(result).isFailureOfType(RuntimeException::class)
        coVerify { data.update(authorizerModel) }
    }


    @Test
    fun `#delete should soft delete authorizer successfully`() = runBlocking<Unit> {
        val authorizerId = authorizer.id
        val authorizerModel = authorizer.toModel()

        coEvery { data.get(authorizerId) } returns authorizerModel.success()
        coEvery { data.softDelete(authorizerModel) } returns true.success()

        val result = service.delete(authorizerId)

        assertThat(result).isSuccessWithData(true)
        coVerify { data.get(authorizerId) }
        coVerify { data.softDelete(authorizerModel) }
    }

    @Test
    fun `#delete should fail when authorizer is not found`() = runBlocking<Unit> {
        val authorizerId = authorizer.id
        val notFoundException = NotFoundException("Authorizer not found")

        coEvery { data.get(authorizerId) } returns notFoundException.failure()

        val result = service.delete(authorizerId)

        assertThat(result).isFailureOfType(NotFoundException::class)
        coVerify { data.get(authorizerId) }
        coVerify(exactly = 0) { data.softDelete(any()) }
    }

    @Test
    fun `#delete should fail when soft delete operation fails`() = runBlocking<Unit> {
        val authorizerId = authorizer.id
        val authorizerModel = authorizer.toModel()
        val deleteException = RuntimeException("Failed to delete")

        coEvery { data.get(authorizerId) } returns authorizerModel.success()
        coEvery { data.softDelete(authorizerModel) } returns deleteException.failure()

        val result = service.delete(authorizerId)

        assertThat(result).isFailureOfType(RuntimeException::class)
        coVerify { data.get(authorizerId) }
        coVerify { data.softDelete(authorizerModel) }
    }

    @Test
    fun `#getByProviderUnitId should retrieve authorizer by provider unit id successfully`() = runBlocking<Unit> {
        val providerUnitId = authorizer.providerUnitId
        val authorizerModel = authorizer.toModel()

        coEvery {
            data.findOne(queryEq { where { this.providerUnitId.eq(providerUnitId) } })
        } returns authorizerModel.success()

        val result = service.getByProviderUnitId(providerUnitId)

        assertThat(result).isSuccessWithData(authorizer)
        coVerify { data.findOne(any()) }
    }

    @Test
    fun `#getByProviderUnitId should fail when not found`() = runBlocking<Unit> {
        val providerUnitId = authorizer.providerUnitId
        val exception = NotFoundException("Authorizer not found")

        coEvery {
            data.findOne(queryEq { where { this.providerUnitId.eq(providerUnitId) } })
        } returns exception.failure()

        val result = service.getByProviderUnitId(providerUnitId)

        assertThat(result).isFailureOfType(NotFoundException::class)
    }

    @Test
    fun `#getByProviderUnitIds should retrieve authorizers by provider unit ids successfully`() = runBlocking<Unit> {
        val providerUnitIds = listOf(authorizer.providerUnitId)
        val authorizerModels = listOf(authorizer.toModel())

        coEvery {
            data.find(queryEq { where { providerUnitId.inList(providerUnitIds) } })
        } returns authorizerModels.success()

        val result = service.getByProviderUnitIds(providerUnitIds)

        assertThat(result).isSuccessWithData(listOf(authorizer))
        coVerify { data.find(any()) }
    }

}
