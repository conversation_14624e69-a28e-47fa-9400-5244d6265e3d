package br.com.alice.exec.indicator.service.guiaRegulation

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.ehr.client.MemberCptsService
import br.com.alice.ehr.model.CptGracePeriod
import br.com.alice.ehr.model.GracePeriodType
import br.com.alice.ehr.model.MemberCpt
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.models.GuiaRegulationResult
import br.com.alice.exec.indicator.models.ProcedureRegulationResult
import br.com.alice.exec.indicator.models.RegulationStatus
import br.com.alice.exec.indicator.service.guiaRegulation.internal.RegulationRules
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GuiaRegulationServiceImplTest {

    private val totvsGuiaService: TotvsGuiaService = mockk()
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService = mockk()
    private val healthcareResourceService: HealthcareResourceService = mockk()
    private val memberCptsService: MemberCptsService = mockk()
    private val regulationRules: RegulationRules = mockk()

    private val service = GuiaRegulationServiceImpl(
        totvsGuiaService = totvsGuiaService,
        mvAuthorizedProcedureService = mvAuthorizedProcedureService,
        healthcareResourceService = healthcareResourceService,
        memberCptsService = memberCptsService,
        regulationRules = regulationRules
    )

    private val guiaId = RangeUUID.generate()
    private val personId = PersonId()
    private val totvsGuia = TestModelFactory.buildTotvsGuia(
        id = guiaId,
        personId = personId
    )

    private val procedure1 = TestModelFactory.buildMvAuthorizedProcedure(
        procedureId = "12345",
        totvsGuiaId = guiaId,
        personId = personId
    )
    private val procedure2 = TestModelFactory.buildMvAuthorizedProcedure(
        procedureId = "67890",
        totvsGuiaId = guiaId,
        personId = personId
    )
    private val procedures = listOf(procedure1, procedure2)

    private val resource1 = TestModelFactory.buildHealthcareResource(
        code = "12345",
        tableType = "22",
        tussCode = "TUSS123",
        pac = true,
        nrol = false,
        dut = false
    )
    private val resource2 = TestModelFactory.buildHealthcareResource(
        code = "67890",
        tableType = "20",
        tussCode = "TUSS456",
        pac = false,
        nrol = true,
        dut = true
    )
    private val resources = listOf(resource1, resource2)

    private val gracePeriod = CptGracePeriod(
        condition = "Parto",
        validUntil = LocalDate.now().plusDays(300L).toString(),
        baseDate = LocalDate.now(),
        type = GracePeriodType.BIRTH,
        periodInDays = 300L
    )
    private val memberCpt = MemberCpt(
        gracePeriod = listOf(gracePeriod)
    )

    private val procedureRegulationResult1 = ProcedureRegulationResult(
        code = "12345",
        status = RegulationStatus.AUTHORIZED
    )
    private val procedureRegulationResult2 = ProcedureRegulationResult(
        code = "67890",
        status = RegulationStatus.PENDING
    )

    @AfterTest
    fun clear() = clearAllMocks()

    @Test
    fun `#regulate should return successful regulation result when all data is available`() = runBlocking {
        coEvery { totvsGuiaService.get(guiaId) } returns totvsGuia.success()
        coEvery { mvAuthorizedProcedureService.findByTotvsGuiaId(guiaId) } returns procedures.success()
        coEvery { healthcareResourceService.findByCodes(listOf("12345", "67890"), false) } returns resources.success()
        coEvery { memberCptsService.buildPersonCptsByPersonId(personId) } returns memberCpt.success()
        coEvery { regulationRules.evaluate(any()) } returnsMany listOf(
            procedureRegulationResult1,
            procedureRegulationResult2
        )

        val result = service.regulate(guiaId)

        assertThat(result).isSuccessWithData(
            GuiaRegulationResult(
                guiaId = guiaId,
                status = RegulationStatus.PENDING,
                procedures = listOf(procedureRegulationResult1, procedureRegulationResult2)
            )
        )

        coVerifyOnce { totvsGuiaService.get(guiaId) }
        coVerifyOnce { mvAuthorizedProcedureService.findByTotvsGuiaId(guiaId) }
        coVerifyOnce { healthcareResourceService.findByCodes(listOf("12345", "67890"), false) }
        coVerifyOnce { memberCptsService.buildPersonCptsByPersonId(personId) }
    }

    @Test
    fun `#regulate should return AUTHORIZED status when all procedures are authorized`() = runBlocking {
        val authorizedResult1 = procedureRegulationResult1.copy(status = RegulationStatus.AUTHORIZED)
        val authorizedResult2 = procedureRegulationResult2.copy(status = RegulationStatus.AUTHORIZED)

        coEvery { totvsGuiaService.get(guiaId) } returns totvsGuia.success()
        coEvery { mvAuthorizedProcedureService.findByTotvsGuiaId(guiaId) } returns procedures.success()
        coEvery { healthcareResourceService.findByCodes(listOf("12345", "67890"), false) } returns resources.success()
        coEvery { memberCptsService.buildPersonCptsByPersonId(personId) } returns memberCpt.success()
        coEvery { regulationRules.evaluate(any()) } returnsMany listOf(authorizedResult1, authorizedResult2)

        val result = service.regulate(guiaId)

        assertThat(result).isSuccessWithData(
            GuiaRegulationResult(
                guiaId = guiaId,
                status = RegulationStatus.AUTHORIZED,
                procedures = listOf(authorizedResult1, authorizedResult2)
            )
        )
    }

    @Test
    fun `#regulate should return UNAUTHORIZED status when all procedures are unauthorized`() = runBlocking {
        val unauthorizedResult1 = procedureRegulationResult1.copy(status = RegulationStatus.UNAUTHORIZED)
        val unauthorizedResult2 = procedureRegulationResult2.copy(status = RegulationStatus.UNAUTHORIZED)

        coEvery { totvsGuiaService.get(guiaId) } returns totvsGuia.success()
        coEvery { mvAuthorizedProcedureService.findByTotvsGuiaId(guiaId) } returns procedures.success()
        coEvery { healthcareResourceService.findByCodes(listOf("12345", "67890"), false) } returns resources.success()
        coEvery { memberCptsService.buildPersonCptsByPersonId(personId) } returns memberCpt.success()
        coEvery { regulationRules.evaluate(any()) } returnsMany listOf(unauthorizedResult1, unauthorizedResult2)

        val result = service.regulate(guiaId)

        assertThat(result).isSuccessWithData(
            GuiaRegulationResult(
                guiaId = guiaId,
                status = RegulationStatus.UNAUTHORIZED,
                procedures = listOf(unauthorizedResult1, unauthorizedResult2)
            )
        )
    }

    @Test
    fun `#regulate should return PARTIALLY_AUTHORIZED status when procedures have mixed statuses`() = runBlocking {
        val authorizedResult = procedureRegulationResult1.copy(status = RegulationStatus.AUTHORIZED)
        val unauthorizedResult = procedureRegulationResult2.copy(status = RegulationStatus.UNAUTHORIZED)

        coEvery { totvsGuiaService.get(guiaId) } returns totvsGuia.success()
        coEvery { mvAuthorizedProcedureService.findByTotvsGuiaId(guiaId) } returns procedures.success()
        coEvery { healthcareResourceService.findByCodes(listOf("12345", "67890"), false) } returns resources.success()
        coEvery { memberCptsService.buildPersonCptsByPersonId(personId) } returns memberCpt.success()
        coEvery { regulationRules.evaluate(any()) } returnsMany listOf(authorizedResult, unauthorizedResult)

        val result = service.regulate(guiaId)

        assertThat(result).isSuccessWithData(
            GuiaRegulationResult(
                guiaId = guiaId,
                status = RegulationStatus.PARTIALLY_AUTHORIZED,
                procedures = listOf(authorizedResult, unauthorizedResult)
            )
        )
    }

    @Test
    fun `#regulate should return PENDING status when any procedure is pending`() = runBlocking {
        val authorizedResult = procedureRegulationResult1.copy(status = RegulationStatus.AUTHORIZED)
        val pendingResult = procedureRegulationResult2.copy(status = RegulationStatus.PENDING)

        coEvery { totvsGuiaService.get(guiaId) } returns totvsGuia.success()
        coEvery { mvAuthorizedProcedureService.findByTotvsGuiaId(guiaId) } returns procedures.success()
        coEvery { healthcareResourceService.findByCodes(listOf("12345", "67890"), false) } returns resources.success()
        coEvery { memberCptsService.buildPersonCptsByPersonId(personId) } returns memberCpt.success()
        coEvery { regulationRules.evaluate(any()) } returnsMany listOf(authorizedResult, pendingResult)

        val result = service.regulate(guiaId)

        assertThat(result).isSuccessWithData(
            GuiaRegulationResult(
                guiaId = guiaId,
                status = RegulationStatus.PENDING,
                procedures = listOf(authorizedResult, pendingResult)
            )
        )
    }

    @Test
    fun `#regulate should fail when totvsGuiaService throws exception`() = runBlocking {
        val exception = RuntimeException("TotvsGuia service error")
        coEvery { totvsGuiaService.get(guiaId) } returns exception.failure()

        val result = service.regulate(guiaId)

        assertThat(result).isFailureOfType(RuntimeException::class)
        coVerifyOnce { totvsGuiaService.get(guiaId) }
        coVerifyNone { mvAuthorizedProcedureService.findByTotvsGuiaId(any()) }
        coVerifyNone { healthcareResourceService.findByCodes(any(), any()) }
        coVerifyNone { memberCptsService.buildPersonCptsByPersonId(any()) }
    }

    @Test
    fun `#regulate should fail when mvAuthorizedProcedureService throws exception`() = runBlocking {
        val exception = RuntimeException("MvAuthorizedProcedure service error")
        coEvery { totvsGuiaService.get(guiaId) } returns totvsGuia.success()
        coEvery { mvAuthorizedProcedureService.findByTotvsGuiaId(guiaId) } returns exception.failure()

        val result = service.regulate(guiaId)

        assertThat(result).isFailureOfType(RuntimeException::class)
        coVerifyOnce { totvsGuiaService.get(guiaId) }
        coVerifyOnce { mvAuthorizedProcedureService.findByTotvsGuiaId(guiaId) }
        coVerifyNone { healthcareResourceService.findByCodes(any(), any()) }
        coVerifyNone { memberCptsService.buildPersonCptsByPersonId(any()) }
    }

    @Test
    fun `#regulate should fail when healthcareResourceService throws exception`() = runBlocking {
        val exception = RuntimeException("HealthcareResource service error")
        coEvery { totvsGuiaService.get(guiaId) } returns totvsGuia.success()
        coEvery { mvAuthorizedProcedureService.findByTotvsGuiaId(guiaId) } returns procedures.success()
        coEvery { healthcareResourceService.findByCodes(listOf("12345", "67890"), false) } returns exception.failure()

        val result = service.regulate(guiaId)

        assertThat(result).isFailureOfType(RuntimeException::class)
        coVerifyOnce { totvsGuiaService.get(guiaId) }
        coVerifyOnce { mvAuthorizedProcedureService.findByTotvsGuiaId(guiaId) }
        coVerifyOnce { healthcareResourceService.findByCodes(listOf("12345", "67890"), false) }
        coVerifyNone { memberCptsService.buildPersonCptsByPersonId(any()) }
    }

    @Test
    fun `#regulate should fail when memberCptsService throws exception`() = runBlocking {
        val exception = RuntimeException("MemberCpts service error")
        coEvery { totvsGuiaService.get(guiaId) } returns totvsGuia.success()
        coEvery { mvAuthorizedProcedureService.findByTotvsGuiaId(guiaId) } returns procedures.success()
        coEvery { healthcareResourceService.findByCodes(listOf("12345", "67890"), false) } returns resources.success()
        coEvery { memberCptsService.buildPersonCptsByPersonId(personId) } returns exception.failure()

        val result = service.regulate(guiaId)

        assertThat(result).isFailureOfType(RuntimeException::class)
        coVerifyOnce { totvsGuiaService.get(guiaId) }
        coVerifyOnce { mvAuthorizedProcedureService.findByTotvsGuiaId(guiaId) }
        coVerifyOnce { healthcareResourceService.findByCodes(listOf("12345", "67890"), false) }
        coVerifyOnce { memberCptsService.buildPersonCptsByPersonId(personId) }
    }

}
