plugins {
    kotlin
    application
    id("com.github.johnrengelman.shadow")
    id("com.google.cloud.tools.jib")
    id("org.sonarqube")
}

group = "br.com.alice.exec-indicator-domain-service"
version = aliceExecIndicatorDomainServiceVersion

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    main {
        kotlin.sourceDirs = files("src")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:exec-indicator-domain-service")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

tasks {
    shadowJar {
        isZip64 = true
    }
}

dependencies {
    implementation(project(":common"))
    implementation(project(":common-kafka"))
    implementation(project(":common-service"))
    implementation(project(":communication"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:health-plan-domain-service-data-package"))
	implementation(project(":data-packages:product-domain-service-data-package"))
	implementation(project(":data-packages:staff-domain-service-data-package"))
	implementation(project(":data-packages:person-domain-service-data-package"))
	implementation(project(":data-packages:provider-domain-service-data-package"))
    implementation(project(":data-packages:exec-indicator-domain-service-data-package"))
    implementation(project(":data-packages:exec-indicator-domain-service-model-package"))
	implementation(project(":data-packages:eventinder-domain-service-data-package"))
    implementation(project(":data-packages:clinical-account-domain-service-data-package"))
    implementation(project(":data-packages:coverage-domain-service-data-package"))
    implementation(project(":data-packages:secondary-attention-domain-service-data-package"))
    implementation(project(":data-packages:ehr-domain-service-data-package"))
    implementation(project(":data-packages:db-integration-service-data-package"))
    implementation(project(":data-packages:nullvs-integration-service-data-package"))
    implementation(project(":data-packages:health-condition-domain-service-data-package"))
    implementation(project(":data-packages:appointment-domain-service-data-package"))
    implementation(project(":data-packages:file-vault-service-data-package"))
    implementation(project(":exec-indicator-domain-client"))
    implementation(project(":feature-config-domain-client"))
    implementation(project(":clinical-account-domain-client"))
    implementation(project(":secondary-attention-domain-client"))
    implementation(project(":membership-domain-client"))
    implementation(project(":staff-domain-client"))
    implementation(project(":provider-domain-client"))
    implementation(project(":person-domain-client"))
    implementation(project(":file-vault-client"))
    implementation(project(":amas-domain-client"))
    implementation(project(":product-domain-client"))
    implementation(project(":eventinder-domain-client"))
    implementation(project(":health-plan-domain-client"))
    implementation(project(":nullvs-integration-client"))
    implementation(project(":eita-nullvs-integration-client"))
    implementation(project(":health-condition-domain-client"))
    implementation(project(":appointment-domain-client"))
    implementation(project(":ehr-domain-client"))

    implementation(platform("software.amazon.awssdk:bom:$awsSdkVersion"))
    implementation("org.apache.commons:commons-csv:$commonsCsvVersion")
    implementation("software.amazon.awssdk:core:$awsSdkVersion")
    implementation("software.amazon.awssdk:auth:$awsSdkVersion")
    implementation("software.amazon.awssdk:ses")
    implementation("software.amazon.awssdk:pinpoint:$awsSdkVersion")
    ktor2Dependencies()

    testImplementation(project(":common-tests"))
    testImplementation(project(":data-layer-common-tests"))
    test2Dependencies()
    testImplementation("org.apache.commons:commons-csv:$commonsCsvVersion")
}
