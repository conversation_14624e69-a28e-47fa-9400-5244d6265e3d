package br.com.alice.channel.assistance.ioc

import br.com.alice.channel.assistance.clients.AmazonChimeClient
import br.com.alice.channel.assistance.clients.AmazonS3Client
import br.com.alice.channel.assistance.consumers.AppointmentConsumer
import br.com.alice.channel.assistance.consumers.AppointmentScheduleConsumer
import br.com.alice.channel.assistance.consumers.AssistanceScreeningChatCreatedConsumer
import br.com.alice.channel.assistance.consumers.AsyncAiChatProcessConsumer
import br.com.alice.channel.assistance.consumers.RecalculateVirtualClinicAttendanceTimeConsumer
import br.com.alice.channel.assistance.consumers.VirtualClinicMemberQueueExperienceConsumer
import br.com.alice.channel.assistance.consumers.VirtualClinicQueueConsumer
import br.com.alice.channel.assistance.controllers.RecurringController
import br.com.alice.channel.assistance.services.FollowUpTriageChannelServiceImpl
import br.com.alice.channel.assistance.services.VideoCallServiceImpl
import br.com.alice.channel.assistance.services.VideoCallTranscriptionServiceImpl
import br.com.alice.channel.assistance.services.VirtualClinicServiceImpl
import br.com.alice.channel.assistance.services.internal.AsyncAiTriageService
import br.com.alice.channel.assistance.services.internal.ChannelsInactivityService
import br.com.alice.channel.assistance.services.internal.GenerativeDalyaService
import br.com.alice.channel.assistance.services.internal.LongitudinalChannelsInactivityService
import br.com.alice.channel.assistance.services.internal.VirtualClinicDigitalCareQueueModalInfoService
import br.com.alice.channel.assistance.services.internal.VirtualClinicInactivityService
import br.com.alice.channel.assistance.services.internal.VirtualClinicMemberExperienceService
import br.com.alice.channel.assistance.services.internal.VirtualClinicMessagesService
import br.com.alice.channel.assistance.services.internal.VirtualClinicQueueService
import br.com.alice.channel.assistance.services.internal.VirtualClinicQueueTimeService
import br.com.alice.channel.assistance.services.internal.firestore.VirtualClinicQueueFirestoreService
import br.com.alice.channel.assistance.services.internal.processors.AnswerProcessor
import br.com.alice.channel.assistance.services.internal.processors.AsyncAiTriageProcessor
import br.com.alice.channel.client.FollowUpTriageChannelService
import br.com.alice.channel.client.VideoCallService
import br.com.alice.channel.client.VideoCallTranscriptionService
import br.com.alice.channel.client.VirtualClinicService
import br.com.alice.channel.core.services.internal.processors.PublicMessageProcessor
import org.koin.core.qualifier.named
import org.koin.dsl.module

val ChannelAssistanceModules = listOf(

    module(createdAtStart = true) {

        // Clients
        single { AmazonChimeClient() }
        single { AmazonS3Client() }

        // Exposed Services
        single<FollowUpTriageChannelService> { FollowUpTriageChannelServiceImpl(get(), get(), get(), get(), get()) }
        single<VideoCallService> { VideoCallServiceImpl(get(), get(), get(), get(), get(), get()) }
        single<VideoCallTranscriptionService> { VideoCallTranscriptionServiceImpl(get(), get(), get()) }
        single<VirtualClinicService> { VirtualClinicServiceImpl(get(), get()) }

        // Internal Services
        single { AsyncAiTriageService(get(), get(), get(), get(), get(), get()) }
        single { ChannelsInactivityService(get(), get(), get(), get(), get()) }
        single { VirtualClinicDigitalCareQueueModalInfoService(get(), get(), get()) }
        single { VirtualClinicInactivityService(get(), get(), get()) }
        single { VirtualClinicMemberExperienceService(get(), get(), get(), get(), get(), get(), get()) }
        single { VirtualClinicMessagesService(get(), get(), get(), get(), get(), get(), get()) }
        single { VirtualClinicQueueService(get(), get(), get(), get(), get(), get(), get(), get()) }
        single { GenerativeDalyaService(get(), get(), get(), get(), get(), get()) }
        single { VirtualClinicQueueTimeService(get()) }
        single { LongitudinalChannelsInactivityService(get(), get(), get(), get(), get()) }

        // Processors
        single { AsyncAiTriageProcessor(get()) }
        single<PublicMessageProcessor>(named("AsyncAiTriageProcessor")) { get() as AsyncAiTriageProcessor }
        single<PublicMessageProcessor>(named("AnswerProcessor")) { AnswerProcessor(get(), get(), get()) }

        // Firestore Services
        single { VirtualClinicQueueFirestoreService() }

        // Kafka Consumers
        single { AppointmentConsumer(get()) }
        single { AppointmentScheduleConsumer(get(), get(), get(), get()) }
        single { AssistanceScreeningChatCreatedConsumer(get()) }
        single { AsyncAiChatProcessConsumer(get()) }
        single { RecalculateVirtualClinicAttendanceTimeConsumer(get(), get()) }
        single {
            VirtualClinicMemberQueueExperienceConsumer(
                get(),
                get(),
                get(),
                get<VideoCallService>() as VideoCallServiceImpl,
                get()
            )
        }
        single { VirtualClinicQueueConsumer(get()) }

        // Controllers
        single { RecurringController(get(), get(), get(), get(), get(), get()) }
    }
)
