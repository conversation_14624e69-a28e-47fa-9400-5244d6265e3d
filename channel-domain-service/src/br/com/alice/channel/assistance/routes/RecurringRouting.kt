package br.com.alice.channel.assistance.routes

import br.com.alice.channel.assistance.controllers.RecurringController
import br.com.alice.common.coHandler
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.assistanceRecurringRoutes() {
    route("/recurring_subscribers") {
        val recurringController by inject<RecurringController>()

        post("/triage_ai_reprocess") {
            coHandler(recurringController::triageAiReprocess)
        }

        post("/missing_member_interaction") {
            co<PERSON>andler(recurringController::processMissingMemberInteraction)
        }

        post("/check_virtual_clinic_queue_waiting_time") {
            coHandler(recurringController::checkVirtualClinicQueueWaitingTime)
        }
        post("/longitudinal_channels_inactivity") {
            coHandler(recurringController::processLongitudinalChannelsInactivity)
        }
    }
}
