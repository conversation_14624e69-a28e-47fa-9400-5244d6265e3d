package br.com.alice.channel.assistance.controllers

import br.com.alice.channel.assistance.services.internal.AsyncAiTriageService
import br.com.alice.channel.assistance.services.internal.ChannelsInactivityService
import br.com.alice.channel.assistance.services.internal.LongitudinalChannelsInactivityService
import br.com.alice.channel.assistance.services.internal.VirtualClinicInactivityService
import br.com.alice.channel.assistance.services.internal.VirtualClinicQueueService
import br.com.alice.channel.assistance.services.internal.VirtualClinicQueueTimeService
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.controllers.ChannelController
import br.com.alice.common.Response
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope

class RecurringController(
    private val virtualClinicQueueService: VirtualClinicQueueService,
    private val channelsInactivityService: ChannelsInactivityService,
    private val virtualClinicInactivityService: VirtualClinicInactivityService,
    private val asyncAiTriageService: AsyncAiTriageService,
    private val virtualClinicQueueTimeService: VirtualClinicQueueTimeService,
    private val longitudinalChannelsInactivityService: LongitudinalChannelsInactivityService
) : ChannelController() {

    @OptIn(FirestoreContextUsage::class)
    suspend fun processMissingMemberInteraction() = withChannelEnvironmentFirestoreTransactional {
        coroutineScope {
            listOf(
                async { virtualClinicQueueService.processMissingMemberInteraction() },
                async { channelsInactivityService.process() },
                async { virtualClinicInactivityService.process() }
            ).awaitAll()
        }

        true.success()
    }

    @OptIn(FirestoreContextUsage::class)
    suspend fun triageAiReprocess(): Response {
        withChannelEnvironmentFirestoreTransactional {
            asyncAiTriageService.reprocessBlockedChats()
        }

        return Response.OK
    }

    @OptIn(FirestoreContextUsage::class)
    suspend fun processLongitudinalChannelsInactivity(): Response {
        withChannelEnvironmentFirestoreTransactional {
            longitudinalChannelsInactivityService.process()
            true.success()
        }

        return Response.OK
    }

    suspend fun checkVirtualClinicQueueWaitingTime() : Response {
        virtualClinicQueueTimeService.checkWaitingTime()

        return Response.OK
    }

}
