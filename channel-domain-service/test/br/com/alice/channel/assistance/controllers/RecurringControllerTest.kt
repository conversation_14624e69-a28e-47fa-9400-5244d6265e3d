package br.com.alice.channel.assistance.controllers

import br.com.alice.channel.assistance.services.internal.AsyncAiTriageService
import br.com.alice.channel.assistance.services.internal.ChannelsInactivityService
import br.com.alice.channel.assistance.services.internal.LongitudinalChannelsInactivityService
import br.com.alice.channel.assistance.services.internal.VirtualClinicInactivityService
import br.com.alice.channel.assistance.services.internal.VirtualClinicQueueService
import br.com.alice.channel.assistance.services.internal.VirtualClinicQueueTimeService
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.controllers.RoutesTestHelper
import br.com.alice.channel.models.VirtualClinicQueueDocument
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import io.mockk.spyk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class RecurringControllerTest : RoutesTestHelper() {

    private val virtualClinicQueueService: VirtualClinicQueueService = mockk()
    private val channelsInactivityService: ChannelsInactivityService = mockk()
    private val virtualClinicInactivityService: VirtualClinicInactivityService = mockk()
    private val asyncAiTriageService: AsyncAiTriageService = mockk()
    private val virtualClinicQueueTimeService: VirtualClinicQueueTimeService = mockk()
    private val longitudinalChannelsInactivityService: LongitudinalChannelsInactivityService = mockk()

    private val recurringController = spyk(
        RecurringController(
            virtualClinicQueueService,
            channelsInactivityService,
            virtualClinicInactivityService,
            asyncAiTriageService,
            virtualClinicQueueTimeService,
            longitudinalChannelsInactivityService
        )
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { recurringController }
    }

    @AfterTest
    fun confirmMocks() = confirmVerified(
        virtualClinicQueueService,
        channelsInactivityService,
        virtualClinicInactivityService,
        asyncAiTriageService,
        virtualClinicQueueTimeService,
        longitudinalChannelsInactivityService
    )

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#processMissingMemberInteraction returns ok and call service to process missing member interaction new route`() =
        runBlocking {
            coEvery { virtualClinicQueueService.processMissingMemberInteraction() } returns emptyList<Unit>().success()
            coEvery { channelsInactivityService.process() } returns Unit
            coEvery { virtualClinicInactivityService.process() } returns Unit

            mockFirestoreContextCommit {
                post("/recurring_subscribers/missing_member_interaction") {
                    assertThat(it).isOK()

                    coVerifyOnce { recurringController.withChannelEnvironmentFirestoreTransactional(any(), any()) }
                    coVerifyOnce { recurringController.processMissingMemberInteraction() }
                    coVerifyOnce { virtualClinicQueueService.processMissingMemberInteraction() }
                    coVerifyOnce { channelsInactivityService.process() }
                    coVerifyOnce { virtualClinicInactivityService.process() }
                }
            }
        }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#triageAiReprocess return success and run reprocess blocked chats (new flow)`() = runBlocking {
        coEvery { asyncAiTriageService.reprocessBlockedChats() } returns true.success()

        mockFirestoreContextCommit {
            post("/recurring_subscribers/triage_ai_reprocess") {
                assertThat(it).isOK()

                coVerifyOnce { recurringController.triageAiReprocess() }
                coVerifyOnce { recurringController.withChannelEnvironmentFirestoreTransactional(any(), any()) }
                coVerifyOnce { asyncAiTriageService.reprocessBlockedChats() }
            }
        }
    }

    @Test
    fun `#checkVirtualClinicQueueWaitingTime return success after check virtual clinic waiting time`() = runBlocking {
        coEvery {
            virtualClinicQueueTimeService.checkWaitingTime()
        } returns emptyList<Pair<VirtualClinicQueueDocument, Long>>().success()

        post("/recurring_subscribers/check_virtual_clinic_queue_waiting_time") {
            assertThat(it).isOK()

            coVerifyOnce { virtualClinicQueueTimeService.checkWaitingTime() }
        }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#processLongitudinalChannelsInactivity return success and run process longitudinal channels inactivity`() =
        runBlocking {
            coEvery { longitudinalChannelsInactivityService.process() } returns Unit

            mockFirestoreContextCommit {
                post("/recurring_subscribers/longitudinal_channels_inactivity") {
                    assertThat(it).isOK()

                    coVerifyOnce { recurringController.processLongitudinalChannelsInactivity() }
                    coVerifyOnce { longitudinalChannelsInactivityService.process() }
                }
            }
        }
}
