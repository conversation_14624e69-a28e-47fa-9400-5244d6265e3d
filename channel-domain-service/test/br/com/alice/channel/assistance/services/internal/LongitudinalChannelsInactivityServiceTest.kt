package br.com.alice.channel.assistance.services.internal

import br.com.alice.channel.assistance.services.internal.LongitudinalChannelsInactivityService.BaseInactivityConfigurations
import br.com.alice.channel.assistance.services.internal.LongitudinalChannelsInactivityService.Config
import br.com.alice.channel.core.FirestoreTransactionalTestHelper
import br.com.alice.channel.core.context.FirestoreContextUsage
import br.com.alice.channel.core.mockTimestamp
import br.com.alice.channel.core.services.internal.ChannelNotificationService
import br.com.alice.channel.core.services.internal.ChannelService
import br.com.alice.channel.core.services.internal.firestore.ChannelFirestoreService
import br.com.alice.channel.core.services.internal.firestore.MessageFirestoreService
import br.com.alice.channel.core.util.AI_STAFF_ID
import br.com.alice.channel.extensions.toTimestamp
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.ChannelStaffInfo
import br.com.alice.channel.models.MessageDocument
import br.com.alice.channel.models.MessageType
import br.com.alice.channel.notifier.ChannelArchivedByInactivityEventPayload
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ChannelArchivedReason
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.success
import com.google.cloud.Timestamp
import com.google.cloud.firestore.CollectionReference
import com.google.cloud.firestore.Query
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import java.time.LocalDateTime
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.AfterTest
import kotlin.test.Test

class LongitudinalChannelsInactivityServiceTest : FirestoreTransactionalTestHelper() {

    private val channelFirestoreService: ChannelFirestoreService = mockk()
    private val channelService: ChannelService = mockk()
    private val messageFirestoreService: MessageFirestoreService = mockk()
    private val personService: PersonService = mockk()
    private val channelNotificationService: ChannelNotificationService = mockk()

    private val inactivityService = spyk(
        LongitudinalChannelsInactivityService(
            channelFirestoreService,
            channelService,
            messageFirestoreService,
            personService,
            channelNotificationService
        )
    )

    private val personId = PersonId()
    private val baseDate = LocalDateTime.now()
    private val timestampNow = Timestamp.now()

    private val timestampMinus10 = baseDate.minusMinutes(10).toTimestamp()
    private val timestampMinus11 = baseDate.minusMinutes(11).toTimestamp()
    private val timestampMinus15 = baseDate.minusMinutes(15).toTimestamp()
    private val timestampMinus16 = baseDate.minusMinutes(16).toTimestamp()

    private val configs = BaseInactivityConfigurations(
        sendEducationalMessage = Config(baseDate = timestampMinus10, minutes = 10),
        archive = Config(baseDate = timestampMinus15, minutes = 15)
    )

    private val person = TestModelFactory.buildPerson(personId = personId)
    private val staff = ChannelStaffInfo.from(TestModelFactory.buildStaff(id = staffId.toUUID()))
    private val baseChannel = ChannelDocument(
        id = channelId,
        channelPersonId = "",
        personId = personId.toString(),
        timeLastMessage = baseDate.toTimestamp(),
        inactiveAt = null,
        staff = mutableMapOf(staffId to staff),
        category = ChannelCategory.ASSISTANCE,
        subCategory = ChannelSubCategory.LONGITUDINAL,
        kind = ChannelKind.CHAT
    )

    private val educationalMessageContent = "@nickname publicMessageContent"
    private val educationalMessageContentIdentified = "Zé publicMessageContent"
    private val educationalMessage = MessageDocument(
        userId = AI_STAFF_ID,
        content = educationalMessageContentIdentified,
        type = MessageType.TEXT,
        createdAt = timestampNow
    )

    private val channelToEducatoinalMessageId = "channelToEducatoinalMessageId"
    private val channelToArchiveId = "channelToArchiveId"
    private val channelToEducationalMessage = baseChannel.copy(
        id = channelToEducatoinalMessageId,
        timeLastMessage = timestampMinus11,
        inactiveAt = timestampMinus11
    )
    private val channelToArchive = baseChannel.copy(
        id = channelToArchiveId,
        timeLastMessage = timestampMinus16,
        inactiveAt = timestampMinus16,
        lastPreviewableMessage = MessageDocument(
            userId = AI_STAFF_ID,
            content = educationalMessageContentIdentified,
            type = MessageType.TEXT
        )
    )

    @AfterTest
    fun confirmMocks() = confirmVerified(
        channelFirestoreService,
        channelService,
        messageFirestoreService,
        personService,
        channelNotificationService,
        collectionReference,
        inactivityService
    )

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#process do not send messages and archive channel when flag is disabled`() = mocks(false) {
        coEvery {
            inactivityService.getInactiveLongitudinalChats(configs)
        } returns listOf(channelToEducationalMessage to person, channelToArchive to person).success()

        val result = inactivityService.process()
        assertThat(result).isEqualTo(Unit)

        coVerifyOnce { inactivityService.process() }
        coVerify(exactly = 5) { inactivityService.span(any(), any(), any()) }
        coVerifyOnce { inactivityService.getInactiveLongitudinalChats(any()) }
        coVerifyNone { messageFirestoreService.add(any(), any(), any()) }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#process just process channel to send educational message`() = mocks {
        coEvery {
            inactivityService.getInactiveLongitudinalChats(configs)
        } returns listOf(channelToEducationalMessage to person).success()
        coEvery {
            messageFirestoreService.add(
                channelToEducatoinalMessageId,
                educationalMessage
            )
        } returns messageId.success()

        val result = inactivityService.process()
        assertThat(result).isEqualTo(Unit)

        coVerifyOnce { inactivityService.process() }
        coVerify(exactly = 3) { inactivityService.span(any(), any(), any()) }
        coVerifyOnce { inactivityService.getInactiveLongitudinalChats(any()) }
        coVerifyOnce { messageFirestoreService.add(any(), any()) }
        coVerify { channelService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#process just process channel to send educational message but not send when message already sent`() = mocks {
        val channelToSendEducationalMessage = channelToEducationalMessage.copy(
            lastPreviewableMessage = MessageDocument(
                userId = AI_STAFF_ID,
                content = educationalMessageContentIdentified,
                type = MessageType.TEXT
            )
        )

        coEvery {
            inactivityService.getInactiveLongitudinalChats(configs)
        } returns listOf(channelToSendEducationalMessage to person).success()

        val result = inactivityService.process()
        assertThat(result).isEqualTo(Unit)

        coVerifyOnce { inactivityService.process() }
        coVerify(exactly = 2) { inactivityService.span(any(), any(), any()) }
        coVerifyOnce { inactivityService.getInactiveLongitudinalChats(any()) }
        coVerify { channelService wasNot called }
        coVerify { messageFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#process just process channel to archive`() = mocks {
        coEvery {
            inactivityService.getInactiveLongitudinalChats(configs)
        } returns listOf(channelToArchive to person).success()
        coEvery {
            channelService.archiveChannel(
                channelDocument = channelToArchive,
                requesterStaffId = "",
                archivedReason = ChannelArchivedReason.MEMBER_INACTIVE,
                force = true
            )
        } returns channelToArchive.success()
        coEvery {
            channelNotificationService.produceGenericEvent(match {
                it.payload == ChannelArchivedByInactivityEventPayload(channelToArchiveId, personId, channelToArchive)
            })
        } returns mockk()

        val result = inactivityService.process()
        assertThat(result).isEqualTo(Unit)

        coVerifyOnce { inactivityService.process() }
        coVerify(exactly = 3) { inactivityService.span(any(), any(), any()) }
        coVerifyOnce { inactivityService.getInactiveLongitudinalChats(any()) }
        coVerifyOnce { channelService.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
        coVerifyOnce { channelNotificationService.produceGenericEvent(any()) }
        coVerifyNone { channelService.updateFields(any(), any()) }
        coVerify { messageFirestoreService wasNot called }
    }

    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#process not archive chat when educational message was not sent and send the message`() = mocks {
        val channelToArchive = channelToArchive.copy(lastPreviewableMessage = null)

        coEvery {
            inactivityService.getInactiveLongitudinalChats(configs)
        } returns listOf(channelToArchive to person).success()
        coEvery {
            messageFirestoreService.add(
                channelToEducatoinalMessageId,
                educationalMessage
            )
        } returns messageId.success()

        val result = inactivityService.process()
        assertThat(result).isEqualTo(Unit)

        coVerifyOnce { inactivityService.process() }
        coVerify(exactly = 3) { inactivityService.span(any(), any(), any()) }
        coVerifyOnce { inactivityService.getInactiveLongitudinalChats(any()) }
        coVerifyOnce { messageFirestoreService.add(any(), any()) }
        coVerify { channelService wasNot called }
    }


    @OptIn(FirestoreContextUsage::class)
    @Test
    fun `#process process all channels`() = mocks {
        coEvery {
            inactivityService.getInactiveLongitudinalChats(configs)
        } returns listOf(channelToEducationalMessage to person, channelToArchive to person).success()
        coEvery {
            messageFirestoreService.add(channelToEducatoinalMessageId, educationalMessage)
        } returns messageId.success()
        coEvery {
            channelService.archiveChannel(
                channelDocument = channelToArchive,
                requesterStaffId = "",
                archivedReason = ChannelArchivedReason.MEMBER_INACTIVE,
                force = true
            )
        } returns channelToArchive.success()
        coEvery {
            channelNotificationService.produceGenericEvent(match {
                it.payload == ChannelArchivedByInactivityEventPayload(channelToArchiveId, personId, channelToArchive)
            })
        } returns mockk()

        val result = inactivityService.process()
        assertThat(result).isEqualTo(Unit)

        coVerifyOnce { inactivityService.process() }
        coVerify(exactly = 5) { inactivityService.span(any(), any(), any()) }
        coVerifyOnce { inactivityService.getInactiveLongitudinalChats(any()) }
        coVerifyOnce { messageFirestoreService.add(any(), any()) }
        coVerifyOnce { channelService.archiveChannel(any<ChannelDocument>(), any(), any(), any()) }
        coVerifyOnce { channelNotificationService.produceGenericEvent(any()) }
    }

    @Test
    fun `#getInactiveLongitudinalChats returns enriched channels`() = runBlocking {
        val channelWithStaff = channelToEducationalMessage.copy(staff = mutableMapOf())

        val functionSlot = slot<suspend (CollectionReference) -> Query>()

        every { collectionReference.whereEqualTo("status", "ACTIVE") } returns collectionReference
        every { collectionReference.whereEqualTo("kind", "CHAT") } returns collectionReference
        every { collectionReference.whereEqualTo("subCategory", "LONGITUDINAL") } returns collectionReference
        every { collectionReference.whereEqualTo("isWaiting", false) } returns collectionReference
        every { collectionReference.whereEqualTo("inactiveAt", null) } returns collectionReference
        every { collectionReference.whereLessThan("timeLastMessage", timestampMinus10) } returns collectionReference

        coEvery {
            channelFirestoreService.find(capture(functionSlot))
        } returns listOf(channelToEducationalMessage, channelWithStaff).success()
        coEvery {
            val personIdAsString = personId.toString()
            personService.findByIds(listOf(personIdAsString, personIdAsString))
        } returns listOf(person).success()

        val result = inactivityService.getInactiveLongitudinalChats(configs)
        assertThat(result).isSuccessWithData(
            listOf(
                channelToEducationalMessage to person,
                channelWithStaff to person,
            )
        )

        functionSlot.captured.invoke(collectionReference)

        coVerifyOnce { inactivityService.getInactiveLongitudinalChats(any()) }
        verify(exactly = 5) { collectionReference.whereEqualTo(any<String>(), any()) }
        verifyOnce { collectionReference.whereLessThan(any<String>(), any()) }
        coVerifyOnce { channelFirestoreService.find(any()) }
        coVerifyOnce { personService.findByIds(any()) }
    }

    private fun mocks(canProcess: Boolean = true, testFunction: suspend () -> Unit) = runBlocking {
        withFeatureFlags(
            FeatureNamespace.CHANNELS,
            mapOf(
                "can_process_longitudinal_inactive_chat" to canProcess,
                "educational_longitudinal_chat_message" to educationalMessageContent,
                "truncated_longitudinal_chat_config_time_in_minutes" to listOf(10, 15)
            )
        ) {
            mockTimestamp(timestampNow) {
                mockLocalDateTime(baseDate) {
                    testFunction()
                }
            }
        }
    }
}
