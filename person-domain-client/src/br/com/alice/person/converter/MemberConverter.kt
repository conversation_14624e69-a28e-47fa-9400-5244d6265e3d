package br.com.alice.person.converter

import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.Member

object MemberConverter {
    fun Member.toBeneficiary() = this.beneficiary?.let {
        Beneficiary(
            id = this.beneficiaryId
                ?: throw IllegalStateException(
                    "Member has Beneficiary but beneficiaryId is null. Member id: ${this.id}"
                ),
            parentBeneficiary = it.parentBeneficiary,
            personId = this.personId,
            memberId = this.id,
            companyId = this.companyId
                ?: throw IllegalStateException(
                    "Member has Beneficiary but companyId is null. Member id: ${this.id}"
                ),
            type = it.type,
            contractType = it.contractType,
            parentBeneficiaryRelationType = it.parentBeneficiaryRelationType,
            activatedAt = it.activatedAt,
            canceledAt = it.canceledAt,
            hiredAt = it.hiredAt,
            parentBeneficiaryRelatedAt = it.parentBeneficiaryRelatedAt,
            canceledReason = it.canceledReason,
            canceledDescription = it.canceledDescription,
            cnpj = it.cnpj,
            hasContributed = it.hasContributed,
            archived = this.archived,
            version = it.version,
            brand = this.brand,
            companySubContractId = this.companySubContractId
                ?: throw IllegalStateException(
                    "Member has Beneficiary but companySubContractId is null. Member id: ${this.id}"
                ),
            memberStatus = this.status,
            parentPerson = this.parentPerson,
            gracePeriodType = it.gracePeriodType,
            gracePeriodTypeReason = it.gracePeriodTypeReason,
            gracePeriodBaseDate = it.gracePeriodBaseDate,
        )
    }
}