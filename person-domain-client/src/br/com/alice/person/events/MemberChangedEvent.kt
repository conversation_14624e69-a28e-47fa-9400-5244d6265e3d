package br.com.alice.person.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.common.notification.NotificationEventAction
import br.com.alice.data.layer.models.Member
import br.com.alice.person.SERVICE_NAME

class MemberChangedEvent(
    val member: Member,
    eventAction: NotificationEventAction
) : NotificationEvent<MemberChangedEvent.Payload>(
    name = NAME,
    producer = SERVICE_NAME,
    payload = Payload(member),
    eventAction = eventAction
) {
    companion object {
        const val NAME = "MEMBER-CHANGED"
    }

    data class Payload(val member: Member)

    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as MemberChangedEvent

        return !(member != other.member || eventAction != other.eventAction)
    }

    override fun hashCode(): Int {
        var result = member.hashCode()
        result = 31 * result + eventAction.hashCode()
        return result
    }
}