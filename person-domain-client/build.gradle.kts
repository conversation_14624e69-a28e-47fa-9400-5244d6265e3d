plugins {
    kotlin
    `kotlin-kapt`
}

kapt {
    correctErrorTypes = false
    generateStubs = false
    includeCompileClasspath = false
    useBuildCache = true
}

group = "br.com.alice.person-domain-client"
version = alicePersonDomainClientVersion

sourceSets {
    main {
        kotlin.sourceDirs = files("src")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

val api by configurations

dependencies {
    implementation(project(":business-domain-client"))
    implementation(project(":common"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:product-domain-service-data-package"))
	implementation(project(":data-packages:membership-domain-service-data-package"))
    implementation(project(":data-packages:business-domain-service-data-package"))
	implementation(project(":data-packages:person-domain-service-data-package"))
    implementation(project(":product-domain-client"))

    kapt(project(":common"))
    ktor2Dependencies()


    testImplementation(project(":common-tests"))
    testImplementation(project(":data-layer-common-tests"))

    test2Dependencies()
}
