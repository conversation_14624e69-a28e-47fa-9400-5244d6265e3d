package br.com.alice.person.converter

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.BeneficiaryContractType
import br.com.alice.data.layer.models.Contract
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.MemberBeneficiary
import br.com.alice.data.layer.models.MemberProduct
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.ProductType
import br.com.alice.person.converter.MemberConverter.toBeneficiary
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class MemberConverterTest {

    private val memberId = RangeUUID.generate()
    private val personId = PersonId()
    private val beneficiaryId = RangeUUID.generate()
    private val companyId = RangeUUID.generate()
    private val companySubContractId = RangeUUID.generate()
    private val now = LocalDateTime.now()

    private val validMemberBeneficiary = MemberBeneficiary(
        type = BeneficiaryType.EMPLOYEE,
        contractType = BeneficiaryContractType.CLT,
        activatedAt = now,
        version = 1,
        gracePeriodBaseDate = LocalDate.now()
    )

    private val validMember = Member(
        id = memberId,
        personId = personId,
        contract = Contract("http://example.com/contract"),
        status = MemberStatus.ACTIVE,
        selectedProduct = MemberProduct(
            id = RangeUUID.generate(),
            type = ProductType.B2B,
            prices = emptyList(),
            priceListing = null,
            productPriceListingId = null
        ),
        brand = Brand.ALICE,
        beneficiaryId = beneficiaryId,
        companyId = companyId,
        companySubContractId = companySubContractId,
        archived = false,
        beneficiary = validMemberBeneficiary,
        createdAt = now,
        updatedAt = now
    )

    @Test
    fun `toBeneficiary should convert Member to Beneficiary correctly when all fields are present`() {
        val result = validMember.toBeneficiary()

        assertNotNull(result)
        assertEquals(beneficiaryId, result.id)
        assertEquals(validMemberBeneficiary.parentBeneficiary, result.parentBeneficiary)
        assertEquals(personId, result.personId)
        assertEquals(memberId, result.memberId)
        assertEquals(companyId, result.companyId)
        assertEquals(BeneficiaryType.EMPLOYEE, result.type)
        assertEquals(BeneficiaryContractType.CLT, result.contractType)
        assertEquals(validMemberBeneficiary.parentBeneficiaryRelationType, result.parentBeneficiaryRelationType)
        assertEquals(now, result.activatedAt)
        assertEquals(validMemberBeneficiary.canceledAt, result.canceledAt)
        assertEquals(validMemberBeneficiary.hiredAt, result.hiredAt)
        assertEquals(validMemberBeneficiary.parentBeneficiaryRelatedAt, result.parentBeneficiaryRelatedAt)
        assertEquals(validMemberBeneficiary.canceledReason, result.canceledReason)
        assertEquals(validMemberBeneficiary.canceledDescription, result.canceledDescription)
        assertEquals(validMemberBeneficiary.cnpj, result.cnpj)
        assertEquals(validMemberBeneficiary.hasContributed, result.hasContributed)
        assertEquals(false, result.archived)
        assertEquals(1, result.version)
        assertEquals(Brand.ALICE, result.brand)
        assertEquals(companySubContractId, result.companySubContractId)
        assertEquals(MemberStatus.ACTIVE, result.memberStatus)
        assertEquals(validMember.parentPerson, result.parentPerson)
        assertEquals(validMemberBeneficiary.gracePeriodType, result.gracePeriodType)
        assertEquals(validMemberBeneficiary.gracePeriodTypeReason, result.gracePeriodTypeReason)
        assertEquals(validMemberBeneficiary.gracePeriodBaseDate, result.gracePeriodBaseDate)
    }

    @Test
    fun `toBeneficiary should return null when beneficiary is null`() {
        val memberWithoutBeneficiary = validMember.copy(beneficiary = null)

        val result = memberWithoutBeneficiary.toBeneficiary()

        assertNull(result)
    }

    @Test
    fun `toBeneficiary should throw IllegalStateException when beneficiaryId is null`() {
        val memberWithNullBeneficiaryId = validMember.copy(beneficiaryId = null)

        val exception = assertFailsWith<IllegalStateException> {
            memberWithNullBeneficiaryId.toBeneficiary()
        }

        assertEquals(
            "Member has Beneficiary but beneficiaryId is null. Member id: $memberId",
            exception.message
        )
    }

    @Test
    fun `toBeneficiary should throw IllegalStateException when companyId is null`() {
        val memberWithNullCompanyId = validMember.copy(companyId = null)

        val exception = assertFailsWith<IllegalStateException> {
            memberWithNullCompanyId.toBeneficiary()
        }

        assertEquals(
            "Member has Beneficiary but companyId is null. Member id: $memberId",
            exception.message
        )
    }

    @Test
    fun `toBeneficiary should throw IllegalStateException when companySubContractId is null`() {
        val memberWithNullCompanySubContractId = validMember.copy(companySubContractId = null)

        val exception = assertFailsWith<IllegalStateException> {
            memberWithNullCompanySubContractId.toBeneficiary()
        }

        assertEquals(
            "Member has Beneficiary but companySubContractId is null. Member id: $memberId",
            exception.message
        )
    }
}
