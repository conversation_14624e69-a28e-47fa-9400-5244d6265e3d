package br.com.alice.provider.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.data.layer.helpers.TestModelFactory.buildStaff
import br.com.alice.data.layer.models.Provider
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.StructuredAddress
import br.com.alice.data.layer.models.StructuredAddressReferenceModel
import br.com.alice.provider.client.ProviderService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.event.BackfillAnesthetistCreatedEvent
import br.com.alice.staff.event.ProviderBackfillAddress
import br.com.alice.staff.event.ProviderBackfillPayload
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDateTime
import kotlin.test.Test


class BackfillProviderAssociationConsumerTest : ConsumerTest() {
    private val providerService: ProviderService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val consumer = BackfillProviderAssociationConsumer(providerUnitService, providerService)

    private val staff = buildStaff(
        role = Role.ANESTHETIST,
    )
    private val nowMock = LocalDateTime.now()

    private val providerData = ProviderBackfillPayload(
        name = "Clínica São Marcos",
        cnpj = "**************",
        bankCode = "341",
        accountNumber = "5423987-1",
        agencyNumber = "1234",
        address = ProviderBackfillAddress(
            street = "Av. Paulista",
            number = "1000",
            complement = "10º andar",
            neighborhood = "Bela Vista",
            state = "SP",
            city = "São Paulo",
            zipcode = "********",
            country = "Brasil",
            latitude = "-23.561414",
            longitude = "-46.********"
        )
    )

    private val staffProvider = Provider(
        id = "${staff.fullName}${staff.id}".toSafeUUID(),
        name = providerData.name,
        type = ProviderType.MEDICAL_COMPANY,
        imageUrl = staff.profileImageUrl,
        cnpj = providerData.cnpj,
        createdAt = nowMock,
        updatedAt = nowMock
    )
    val address = with(providerData.address!!) {
        StructuredAddress(
            id = staffProvider.id,
            street = street,
            number = number,
            complement = complement,
            neighborhood = neighborhood,
            city = city,
            state = state,
            zipcode = zipcode,
            latitude = latitude,
            longitude = longitude,
            referencedModelId = staffProvider.id,
            referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT,
            createdAt = nowMock,
            updatedAt = nowMock
        )
    }

    private val staffProviderUnit = ProviderUnit(
        id = staffProvider.id,
        type = ProviderUnit.Type.MEDICAL_COMPANY,
        showOnApp = false,
        name = providerData.name,
        providerId = staffProvider.id,
        clinicalStaffIds = listOf(staff.id),
        cnpj = providerData.cnpj,
        bankCode = providerData.bankCode,
        accountNumber = providerData.accountNumber,
        agencyNumber = providerData.agencyNumber,
        createdAt = nowMock,
        updatedAt = nowMock
    )

    @Test
    fun `should create provider and provider unit for health professional`() = mockLocalDateTime(nowMock) {
        val event = BackfillAnesthetistCreatedEvent(staff, providerData)

        coEvery {
            providerService.add(staffProvider)
        } returns staffProvider.success()

        coEvery {
            providerUnitService.addWithAddress(staffProviderUnit, address)
        } returns staffProviderUnit.success()
        coEvery {
            providerUnitService.getByCnpj(providerData.cnpj)
        } returns NotFoundException().failure()
        coEvery {
            providerService.getByCnpj(providerData.cnpj)
        } returns NotFoundException().failure()
        val result = consumer.associateProviderAndProviderUnitToHealthProfessional(event)
        assertThat(result).isSuccessWithData(staffProviderUnit)

        coVerifyOnce { providerService.add(any()) }
        coVerifyOnce { providerUnitService.addWithAddress(any(), any()) }
        coVerifyNone { providerService.get(any()) }
    }

    @Test
    fun `should handle duplicate provider creation for health professional`() = mockLocalDateTime(nowMock) {
        val event = BackfillAnesthetistCreatedEvent(staff, providerData)

        coEvery {
            providerService.add(staffProvider)
        } returns DuplicatedItemException().failure()

        coEvery {
            providerService.get(staffProvider.id)
        } returns staffProvider.success()
        coEvery {
            providerUnitService.getByCnpj(providerData.cnpj)
        } returns NotFoundException().failure()
        coEvery {
            providerService.getByCnpj(providerData.cnpj)
        } returns NotFoundException().failure()
        coEvery {
            providerUnitService.addWithAddress(staffProviderUnit, address)
        } returns staffProviderUnit.success()

        val result = consumer.associateProviderAndProviderUnitToHealthProfessional(event)
        assertThat(result).isSuccessWithData(staffProviderUnit)

        coVerifyOnce { providerService.add(any()) }
        coVerifyOnce { providerService.get(any()) }
        coVerifyOnce { providerUnitService.addWithAddress(any(), any()) }
    }

    @Test
    fun `should update existing provider unit with new staff`() = mockLocalDateTime(nowMock) {
        val existingProviderUnit = staffProviderUnit.copy(
            clinicalStaffIds = listOf(RangeUUID.generate())
        )

        val updatedProviderUnit = existingProviderUnit.copy(
            clinicalStaffIds = existingProviderUnit.clinicalStaffIds?.plus(staff.id)
        )

        val event = BackfillAnesthetistCreatedEvent(staff, providerData)

        coEvery {
            providerService.getByCnpj(providerData.cnpj)
        } returns staffProvider.success()

        coEvery {
            providerUnitService.getByCnpj(providerData.cnpj)
        } returns existingProviderUnit.success()

        coEvery {
            providerUnitService.update(updatedProviderUnit)
        } returns updatedProviderUnit.success()

        val result = consumer.associateProviderAndProviderUnitToHealthProfessional(event)
        assertThat(result).isSuccessWithData(updatedProviderUnit)

        coVerifyNone { providerService.add(any()) }
        coVerifyNone { providerService.get(any()) }
        coVerifyNone { providerUnitService.add(any()) }
        coVerifyOnce { providerUnitService.update(any()) }
    }
}
