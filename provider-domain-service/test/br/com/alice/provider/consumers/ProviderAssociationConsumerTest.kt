package br.com.alice.provider.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.data.layer.helpers.TestModelFactory.buildStaff
import br.com.alice.data.layer.models.PhoneNumber
import br.com.alice.data.layer.models.PhoneType
import br.com.alice.data.layer.models.Provider
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.StaffSignupRequestAddress
import br.com.alice.data.layer.models.StaffSignupRequestPhone
import br.com.alice.data.layer.models.StaffSignupRequestProvider
import br.com.alice.data.layer.models.StructuredAddress
import br.com.alice.data.layer.models.StructuredAddressReferenceModel
import br.com.alice.provider.client.ProviderService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.event.AnesthetistCreatedEvent
import br.com.alice.staff.event.StaffCreatedFromStaffSignupRequestEvent
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDateTime
import kotlin.test.Test

class ProviderAssociationConsumerTest : ConsumerTest() {
    private val providerService: ProviderService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val consumer = ProviderAssociationConsumer(providerUnitService, providerService)

    private val staff = buildStaff(
        role = Role.ANESTHETIST,
    )
    private val nowMock = LocalDateTime.now()

    private val anesthetistProvider = Provider(
        id = "${staff.fullName}${staff.id}".toSafeUUID(),
        name = staff.fullName,
        type = ProviderType.MEDICAL_COMPANY,
        imageUrl = staff.profileImageUrl,
        createdAt = nowMock,
        updatedAt = nowMock
    )

    private val anesthetistProviderUnit = ProviderUnit(
        id = anesthetistProvider.id,
        type = ProviderUnit.Type.MEDICAL_COMPANY,
        name = staff.fullName,
        providerId = anesthetistProvider.id,
        showOnApp = false,
        clinicalStaffIds = listOf(staff.id),
        createdAt = nowMock,
        updatedAt = nowMock
    )

    private val providerData = StaffSignupRequestProvider(
        name = "Clínica São Marcos",
        cnpj = "**************",
        cnes = "123456",
        bankCode = "341",
        accountNumber = "5423987-1",
        agencyNumber = "1234",
        phones = listOf(
            StaffSignupRequestPhone(type = "MOBILE", number = "***********"),
            StaffSignupRequestPhone(type = "WHATSAPP", number = "**********")
        ),
        address = StaffSignupRequestAddress(
            street = "Av. Paulista",
            number = "1000",
            complement = "10º andar",
            neighborhood = "Bela Vista",
            state = "SP",
            city = "São Paulo",
            zipcode = "********",
            country = "Brasil"
        )
    )

    private val healthProfessionalProvider = Provider(
        id = "${staff.fullName}${staff.id}".toSafeUUID(),
        name = providerData.name,
        type = ProviderType.CLINICAL,
        imageUrl = staff.profileImageUrl,
        cnpj = providerData.cnpj,
        createdAt = nowMock,
        updatedAt = nowMock
    )
    private val address = with(providerData.address) {
        StructuredAddress(
            id = healthProfessionalProvider.id,
            street = street,
            number = number,
            complement = complement,
            neighborhood = neighborhood,
            city = city,
            state = state,
            zipcode = zipcode,
            latitude = null, // next pull request we will add latitude and longitude
            longitude = null,
            referencedModelId = healthProfessionalProvider.id,
            referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT,
            createdAt = nowMock,
            updatedAt = nowMock
        )
    }
    private val healthProfessionalProviderUnit = ProviderUnit(
        id = healthProfessionalProvider.id,
        type = ProviderUnit.Type.CLINICAL,
        showOnApp = false,
        name = providerData.name,
        providerId = healthProfessionalProvider.id,
        clinicalStaffIds = listOf(staff.id),
        cnpj = providerData.cnpj,
        cnes = providerData.cnes,
        bankCode = providerData.bankCode,
        accountNumber = providerData.accountNumber,
        agencyNumber = providerData.agencyNumber,
        phones = listOf(
            PhoneNumber(
                phone = "***********",
                type = PhoneType.MOBILE
            ),
            PhoneNumber(
                phone = "**********",
                type = PhoneType.WHATSAPP
            )
        ),
        createdAt = nowMock,
        updatedAt = nowMock
    )

    @Test
    fun `should create provider and provider unit for anesthetist`() = mockLocalDateTime(nowMock) {
        val event = AnesthetistCreatedEvent(staff)

        coEvery {
            providerService.add(anesthetistProvider)
        } returns anesthetistProvider.success()

        coEvery {
            providerUnitService.add(anesthetistProviderUnit)
        } returns anesthetistProviderUnit.success()

        val result = consumer.associateProviderAddProviderUnitToAnesthetist(event)
        assertThat(result).isSuccessWithData(anesthetistProviderUnit)

        coVerifyOnce { providerService.add(any()) }
        coVerifyOnce { providerUnitService.add(any()) }
        coVerifyNone { providerService.get(any()) }
    }

    @Test
    fun `should handle duplicate provider creation for anesthetist`() = mockLocalDateTime(nowMock) {
        val event = AnesthetistCreatedEvent(staff)

        coEvery {
            providerService.add(anesthetistProvider)
        } returns DuplicatedItemException().failure()

        coEvery {
            providerService.get(anesthetistProvider.id)
        } returns anesthetistProvider.success()

        coEvery {
            providerUnitService.add(anesthetistProviderUnit)
        } returns anesthetistProviderUnit.success()

        val result = consumer.associateProviderAddProviderUnitToAnesthetist(event)
        assertThat(result).isSuccessWithData(anesthetistProviderUnit)

        coVerifyOnce { providerService.add(any()) }
        coVerifyOnce { providerService.get(any()) }
        coVerifyOnce { providerUnitService.add(any()) }
    }

    @Test
    fun `should create provider and provider unit for health professional`() = mockLocalDateTime(nowMock) {
        val event = StaffCreatedFromStaffSignupRequestEvent(staff, providerData)

        coEvery {
            providerService.add(healthProfessionalProvider)
        } returns healthProfessionalProvider.success()

        coEvery {
            providerUnitService.addWithAddress(healthProfessionalProviderUnit, address)
        } returns healthProfessionalProviderUnit.success()
        coEvery {
            providerUnitService.getByCnpj(providerData.cnpj)
        } returns NotFoundException().failure()
        coEvery {
            providerService.getByCnpj(providerData.cnpj)
        } returns NotFoundException().failure()
        val result = consumer.associateProviderAndProviderUnitToHealthProfessional(event)
        assertThat(result).isSuccessWithData(healthProfessionalProviderUnit)

        coVerifyOnce { providerService.add(any()) }
        coVerifyOnce { providerUnitService.addWithAddress(any(), any()) }
        coVerifyNone { providerService.get(any()) }
    }

    @Test
    fun `should handle duplicate provider creation for health professional`() = mockLocalDateTime(nowMock) {
        val event = StaffCreatedFromStaffSignupRequestEvent(staff, providerData)

        coEvery {
            providerService.add(healthProfessionalProvider)
        } returns DuplicatedItemException().failure()

        coEvery {
            providerService.get(healthProfessionalProvider.id)
        } returns healthProfessionalProvider.success()
        coEvery {
            providerUnitService.getByCnpj(providerData.cnpj)
        } returns NotFoundException().failure()
        coEvery {
            providerService.getByCnpj(providerData.cnpj)
        } returns NotFoundException().failure()
        coEvery {
            providerUnitService.addWithAddress(healthProfessionalProviderUnit, address)
        } returns healthProfessionalProviderUnit.success()

        val result = consumer.associateProviderAndProviderUnitToHealthProfessional(event)
        assertThat(result).isSuccessWithData(healthProfessionalProviderUnit)

        coVerifyOnce { providerService.add(any()) }
        coVerifyOnce { providerService.get(any()) }
        coVerifyOnce { providerUnitService.addWithAddress(any(), any()) }
    }

    @Test
    fun `should update existing provider unit with new staff`() = mockLocalDateTime(nowMock) {
        val existingProviderUnit = healthProfessionalProviderUnit.copy(
            clinicalStaffIds = listOf(RangeUUID.generate())
        )

        val updatedProviderUnit = existingProviderUnit.copy(
            clinicalStaffIds = existingProviderUnit.clinicalStaffIds?.plus(staff.id)
        )

        val event = StaffCreatedFromStaffSignupRequestEvent(staff, providerData)

        coEvery {
            providerService.getByCnpj(providerData.cnpj)
        } returns healthProfessionalProvider.success()

        coEvery {
            providerUnitService.getByCnpj(providerData.cnpj)
        } returns existingProviderUnit.success()

        coEvery {
            providerUnitService.update(updatedProviderUnit)
        } returns updatedProviderUnit.success()

        val result = consumer.associateProviderAndProviderUnitToHealthProfessional(event)
        assertThat(result).isSuccessWithData(updatedProviderUnit)

        coVerifyNone { providerService.add(any()) }
        coVerifyNone { providerService.get(any()) }
        coVerifyNone { providerUnitService.add(any()) }
        coVerifyOnce { providerUnitService.update(any()) }
    }

}
