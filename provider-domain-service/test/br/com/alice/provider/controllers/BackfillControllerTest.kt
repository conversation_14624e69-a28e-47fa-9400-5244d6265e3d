package br.com.alice.provider.controllers

import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.services.ProviderModelDataService
import br.com.alice.data.layer.services.ProviderUnitModelDataService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.provider.converters.modelConverters.toModel
import br.com.alice.provider.converters.modelConverters.toTransport
import br.com.alice.tiss.client.CnesService
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.spyk
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class BackfillControllerTest : ControllerTestHelper() {

    private val ProviderModelDataService: ProviderModelDataService = mockk()
    private val ProviderUnitModelDataService: ProviderUnitModelDataService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val cnesService: CnesService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val controller = spyk(
        BackfillController(
            ProviderModelDataService,
            ProviderUnitModelDataService,
            providerUnitService,
            cnesService,
            kafkaProducerService
        )
    )

    private val providerUnits = listOf(
        TestModelFactory.buildProviderUnit(cnpj = "12.345.678/0001-00"),
        TestModelFactory.buildProviderUnit(cnpj = "12.345.678/0001-00")
    )
    private val providerUnitsIds = providerUnits.map { it.id }

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { controller }
    }

    @AfterTest
    fun afterTest() {
        clearAllMocks()
    }

    @Test
    fun `#setProviderUnitsCnes - search and update a single provider unit`() {
        val providerUnitCnpj = "12345678000100"
        val cnes = "1234567"
        val providerUnit = TestModelFactory.buildProviderUnit(cnpj = providerUnitCnpj)
        val body = SetProviderUnitCnesRequest(nationalIds = listOf(providerUnitCnpj), 10, 10)

        coEvery { providerUnitService.getByCnpjs(listOf(providerUnitCnpj)) } returns listOf(providerUnit).success()
        coEvery { cnesService.getCnesByNationalId(providerUnitCnpj) } returns cnes.success()

        coEvery { ProviderUnitModelDataService.updateList(any()) } returns listOf(providerUnit.toModel()).success()

        authenticatedAs(token, staff) {
            post("/backfill/provider-unit/fill-cnes", body) { response ->
                assertThat(response).isSuccessfulJson()
            }
        }

        coVerifyOnce { cnesService.getCnesByNationalId(providerUnitCnpj) }
        coVerifyOnce { ProviderUnitModelDataService.updateList(listOf(providerUnit.toModel().copy(cnes = cnes))) }
    }

    @Test
    fun `#setProviderUnitsCnes - search and update all provider units without cnes`() {
        val providerUnitCnpj = "12345678000100"
        val cnes = "1234567"
        val providerUnit = TestModelFactory.buildProviderUnit(cnpj = providerUnitCnpj).toModel()
        val body = SetProviderUnitCnesRequest(nationalIds = emptyList(), 10, 10)

        coEvery { providerUnitService.getWithEmptyCnes(10, 10) } returns listOf(providerUnit.toTransport()).success()
        coEvery { cnesService.getCnesByNationalId(providerUnitCnpj) } returns cnes.success()

        coEvery { ProviderUnitModelDataService.updateList(any()) } returns listOf(providerUnit).success()

        authenticatedAs(token, staff) {
            post("/backfill/provider-unit/fill-cnes", body) { response ->
                assertThat(response).isSuccessfulJson()
            }
        }

        coVerifyOnce { cnesService.getCnesByNationalId(providerUnitCnpj) }
        coVerifyOnce { ProviderUnitModelDataService.updateList(listOf(providerUnit.copy(cnes = cnes))) }
    }

    @Test
    fun `#updateProviderUnits - search and update all provider units`() {
        val body = UpdateProviderUnitsRequest(
            providerUnitsIds = providerUnitsIds,
            contractOrigin = ProviderUnit.Origin.CASSI,
            status = null,
            brand = null
        )
        val expectedToUpdate = providerUnits.map { it.copy(contractOrigin = ProviderUnit.Origin.CASSI) }

        coEvery { providerUnitService.getByIds(match { it.containsAll(providerUnitsIds) }) } returns providerUnits
        coEvery { providerUnitService.updateList(expectedToUpdate) } returns expectedToUpdate

        authenticatedAs(token, staff) {
            post("/backfill/update_provider_units", body) { response ->
                assertThat(response).isOKWithData(expectedToUpdate.size)
            }
        }

        coVerifyOnce { providerUnitService.getByIds(any()) }
        coVerifyOnce { providerUnitService.updateList(any()) }
    }

    @Test
    fun `#updateProviderUnits - do not update when request is invalid`() {
        val body = UpdateProviderUnitsRequest(
            providerUnitsIds = providerUnitsIds,
            contractOrigin = null,
            status = null,
            brand = null
        )

        authenticatedAs(token, staff) {
            post("/backfill/update_provider_units", body) { response ->
                assertThat(response).isBadRequestWithErrorCode("invalid_request_parameters")
            }
        }

        coVerify { providerUnitService wasNot called }
    }

}
