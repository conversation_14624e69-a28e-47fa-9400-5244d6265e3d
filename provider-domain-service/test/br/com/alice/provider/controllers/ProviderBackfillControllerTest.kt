package br.com.alice.provider.controllers

import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.ProviderModelDataService
import br.com.alice.provider.converters.modelConverters.toModel
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test


class ProviderBackfillControllerTest : ControllerTestHelper() {
    private val providerService: ProviderModelDataService = mockk()
    val controller = ProviderBackfillController(
        providerService
    )

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { controller }
    }

    @Test
    fun `#updateProviderToSanitizedCnpj - should sanitize cnpj`() {
        val providerUnit = TestModelFactory.buildProvider(cnpj = "12.345.678/0001-00").toModel()
        val backfillRequest = BackfillRequest(
            limit = 10,
            offset = 0
        )
        coEvery {
            providerService.find(
                queryEq {
                    where { this.cnpj.isNotNull() }
                        .orderBy { this.createdAt }
                        .limit { backfillRequest.limit }
                        .offset { backfillRequest.offset }
                }
            )
        } returns listOf(providerUnit).success()
        coEvery { providerService.updateList(listOf(providerUnit)) } returns listOf(providerUnit).success()

        authenticatedAs(token, staff) {
            post("/backfill/provider/update-sanitized-cnpj", backfillRequest) { response ->
                assertThat(response).isSuccessfulJson()
            }
        }

        coVerifyOnce { providerService.find(any()) }
        coVerifyOnce { providerService.updateList(any()) }
    }

}
