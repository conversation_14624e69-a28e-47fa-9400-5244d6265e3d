package br.com.alice.provider.controllers

import br.com.alice.common.Brand
import br.com.alice.common.DefaultErrorResponse
import br.com.alice.common.Response
import br.com.alice.common.asyncLayer
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.Status
import br.com.alice.common.core.extensions.isCnpj
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.then
import br.com.alice.common.foldResponse
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.toResponse
import br.com.alice.common.useReadDatabase
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.PROVIDER_ENVIRONMENT_BACKFILL
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.services.ProviderModelDataService
import br.com.alice.data.layer.services.ProviderUnitModelDataService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.provider.converters.modelConverters.toModel
import br.com.alice.provider.converters.modelConverters.toTransport
import br.com.alice.provider.model.ProviderUnitUpdateGroupEvent
import br.com.alice.tiss.client.CnesService
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.Duration
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger
import kotlinx.coroutines.delay

class BackfillController(
    private val ProviderModelDataService: ProviderModelDataService,
    private val ProviderUnitModelDataService: ProviderUnitModelDataService,
    private val providerUnitService: ProviderUnitService,
    private val cnesService: CnesService,
    private val kafkaProducerService: KafkaProducerService,
) : Controller() {

    private suspend fun withBackfillEnvironment(func: suspend () -> Response) =
        asyncLayer {
            withRootServicePolicy(PROVIDER_ENVIRONMENT_BACKFILL) {
                withUnauthenticatedTokenWithKey(PROVIDER_ENVIRONMENT_BACKFILL) {
                    func.invoke()
                }
            }
        }

    suspend fun updateDaysForPaymentByType(request: SetDaysForPaymentRequest): Response {
        val clinicalProviders = ProviderModelDataService.find {
            where {
                this.type.eq(request.type)
            }
        }.get()

        logger.info(
            "Updating providers days for payment",
            "providers_id" to clinicalProviders.map { it.id },
            "providers_count" to clinicalProviders.size,
            "type" to request.type,
            "days_for_payment" to request.daysForPayment
        )

        val updatedClinicalProviders = clinicalProviders.map { it.copy(daysForPayment = request.daysForPayment) }

        return updatedClinicalProviders.chunked(size = 50).pmap {
            ProviderModelDataService.updateList(it).foldResponse()
        }.toResponse()
    }

    suspend fun setProviderUnitsCnes(request: SetProviderUnitCnesRequest) = withBackfillEnvironment {
        useReadDatabase {
            val providerUnits = if (request.nationalIds.isNotEmpty())
                providerUnitService.getByCnpjs(request.nationalIds).get()
            else
                providerUnitService.getWithEmptyCnes(offset = request.offset, limit = request.limit).get()
                    .filter { it.cnpj != null && it.cnpj!!.isCnpj() }.distinctBy { it.cnpj }

            logger.info("${providerUnits.size} provider units to set cnes")

            val providerUnitsWithCnes = providerUnits.mapNotNull { providerUnit ->
                val cnesConsulted = cnesService.getCnesByNationalId(providerUnit.cnpj!!).getOrElse {
                    logger.error("Error consulting cnes for provider unit", "cnpj" to providerUnit.cnpj, it)
                    null
                }

                val providerUnitWithCnesConsulted = if (cnesConsulted != null) {
                    logger.info(
                        "Cnes consulted for providerUnit",
                        "cnpj" to providerUnit.cnpj,
                        "cnes" to cnesConsulted
                    )
                    providerUnit.copy(cnes = cnesConsulted)
                } else null

                delay(Duration.ofSeconds(1).toMillis())

                providerUnitWithCnesConsulted
            }

            providerUnitsWithCnes.chunked(50).pmap {
                ProviderUnitModelDataService.updateList(it.map { it.toModel() }).then { updatedProviderUnits ->
                    logger.info("ProviderUnits updated", "providerUnits" to updatedProviderUnits)
                    updatedProviderUnits.success()
                }
            }.toResponse()
        }
    }

    suspend fun updateProviderUnits(request: UpdateProviderUnitsRequest) = withBackfillEnvironment {
        span("updateProviderUnits") { span ->
            val providerUnitsIds = request.providerUnitsIds

            span.setAttribute("provider_units_ids", providerUnitsIds.joinToString(","))

            if (invalidProviderUnitsRequest(request))
                return@span DefaultErrorResponse("invalid_request_parameters")

            val updated = AtomicInteger(0)
            providerUnitsIds.distinct().chunked(10).pmap { chunkedProviderUnits ->
                providerUnitService.getByIds(chunkedProviderUnits)
                    .map { providerUnits ->
                        val toUpdate = providerUnits.map { providerUnit -> updateProviderUnit(request, providerUnit) }
                        span.setAttribute("to_update", toUpdate.size.toString())
                        providerUnitService.updateList(toUpdate).then { updated.addAndGet(it.size) }
                    }
            }
            updated.get().toResponse()
        }
    }

    suspend fun populateGroups(request: PopulateGroupsRequest) = withBackfillEnvironment {
        ProviderUnitModelDataService.find {
            where {
                this.status.inList(listOf(Status.ACTIVE, Status.INACTIVE, Status.DELETED))
            }.orderBy { this.name }
                .offset { request.offset }
                .limit { request.limit }
        }.map { providerUnits ->
            providerUnits.pmap { kafkaProducerService.produce(ProviderUnitUpdateGroupEvent(it.toTransport())) }
        }

        return@withBackfillEnvironment Response
    }

    private fun invalidProviderUnitsRequest(request: UpdateProviderUnitsRequest) =
        request.contractOrigin == null
                && request.status == null
                && request.brand == null

    private fun updateProviderUnit(request: UpdateProviderUnitsRequest, providerUnit: ProviderUnit) =
        providerUnit.copy(
            contractOrigin = request.contractOrigin ?: providerUnit.contractOrigin,
            status = request.status ?: providerUnit.status,
            brand = request.brand ?: providerUnit.brand
        )

}

data class SetProviderUnitCnesRequest(
    val nationalIds: List<String>,
    val limit: Int,
    val offset: Int
)

data class SetDaysForPaymentRequest(
    val type: ProviderType,
    val daysForPayment: Int,
)

data class UpdateProviderUnitsRequest(
    val providerUnitsIds: List<UUID>,
    val contractOrigin: ProviderUnit.Origin? = null,
    val status: Status? = null,
    val brand: Brand? = null
)

data class PopulateGroupsRequest(
    val limit: Int,
    val offset: Int
)
