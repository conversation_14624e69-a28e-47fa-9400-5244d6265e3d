package br.com.alice.provider.controllers

import br.com.alice.common.Response
import br.com.alice.common.asyncLayer
import br.com.alice.common.controllers.Controller
import br.com.alice.common.foldResponse
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.PROVIDER_ENVIRONMENT_BACKFILL
import br.com.alice.data.layer.services.ProviderModelDataService
import com.github.kittinunf.result.map

class ProviderBackfillController(
    private val providerService: ProviderModelDataService,
) : Controller() {

    private suspend fun withBackfillEnvironment(func: suspend () -> Response) =
        asyncLayer {
            withRootServicePolicy(PROVIDER_ENVIRONMENT_BACKFILL) {
                withUnauthenticatedTokenWithKey(PROVIDER_ENVIRONMENT_BACKFILL) {
                    func.invoke()
                }
            }
        }

    suspend fun updateProviderToSanitizedCnpj(request: BackfillRequest): Response = withBackfillEnvironment {
        providerService.find {
            where { this.cnpj.isNotNull() }
                .orderBy { createdAt }
                .limit { request.limit }
                .offset { request.offset }
        }.map {
            it.chunked(50).map { providers ->
                providerService.updateList(providers)
            }
        }.foldResponse()
    }

}

data class BackfillRequest(
    val limit: Int,
    val offset: Int,
)
