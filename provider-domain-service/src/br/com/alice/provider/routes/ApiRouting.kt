package br.com.alice.provider.routes

import br.com.alice.common.asyncLayer
import br.com.alice.common.coHandler
import br.com.alice.provider.controllers.BackfillController
import br.com.alice.provider.controllers.ProviderBackfillController
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.apiRoutes() {
    val backfillController by inject<BackfillController>()
    put("days_for_payment_by_type") { asyncLayer { coHandler(backfillController::updateDaysForPaymentByType) } }
    post("/backfill/provider-unit/fill-cnes/") { coHandler(backfillController::setProviderUnitsCnes) }
    post("/backfill/update_provider_units") { coHandler(backfillController::updateProviderUnits) }
    post("/backfill/populate_provider_unit_groups") { co<PERSON>and<PERSON>(backfillController::populateGroups) }

    val providerBackfillController by inject<ProviderBackfillController>()
    route("/backfill") {
        post("/provider/update-sanitized-cnpj") { coHandler(providerBackfillController::updateProviderToSanitizedCnpj) }
    }
}
