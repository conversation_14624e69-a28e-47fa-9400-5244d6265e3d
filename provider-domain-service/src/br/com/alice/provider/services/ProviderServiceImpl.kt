package br.com.alice.provider.services

import br.com.alice.common.Brand
import br.com.alice.common.core.Status
import br.com.alice.common.core.Status.ACTIVE
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.useReadDatabase
import br.com.alice.data.layer.models.Provider
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.data.layer.services.ProviderModelDataService
import br.com.alice.data.layer.services.ProviderUnitGroupModelDataService
import br.com.alice.data.layer.services.ProviderUnitModelDataService
import br.com.alice.provider.client.OrderBy
import br.com.alice.provider.client.ProviderFilter
import br.com.alice.provider.client.ProviderService
import br.com.alice.provider.client.ProviderWithUnitsAndGroups
import br.com.alice.provider.converters.modelConverters.toModel
import br.com.alice.provider.converters.modelConverters.toTransport
import br.com.alice.provider.model.ProviderCreatedEvent
import br.com.alice.provider.model.ProviderUpdatedEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import java.util.UUID

class ProviderServiceImpl(
    private val data: ProviderModelDataService,
    private val providerUnitModelDataService: ProviderUnitModelDataService,
    private val providerUnitGroupModelDataService: ProviderUnitGroupModelDataService,
    private val kafkaProducerService: KafkaProducerService,
) : ProviderService {

    override suspend fun add(provider: Provider) =
        data.add(provider.toModel())
            .map { it.toTransport() }
            .then { kafkaProducerService.produce(ProviderCreatedEvent(it)) }

    override suspend fun update(provider: Provider) =
        data.update(provider.toModel())
            .map { it.toTransport() }
            .then { kafkaProducerService.produce(ProviderUpdatedEvent(it)) }

    override suspend fun get(id: UUID) = useReadDatabase {
        data.get(id).map { it.toTransport() }
    }

    override suspend fun getByFiltersWithRange(providerFilter: ProviderFilter, range: IntRange) =
        useReadDatabase {
            val (searchToken, status, ids) = providerFilter
            data.find {
                where {
                    this.status.inList(status.ifEmpty { listOf(ACTIVE) }).withSearchToken(searchToken).withIds(ids)
                }.offset { range.first }
                    .limit { range.count() }
            }.mapEach { it.toTransport() }
        }

    override suspend fun countByFilters(providerFilter: ProviderFilter): Result<Int, Throwable> {
        val (providerName, status, ids) = providerFilter
        return data.count {
            where {
                this.status.inList(status.ifEmpty { listOf(ACTIVE) }).withSearchToken(providerName).withIds(ids)
            }
        }
    }

    override suspend fun getByIds(list: List<UUID>) = useReadDatabase {
        data.find { where { id.inList(list).withStatus() } }.mapEach { it.toTransport() }
    }

    override suspend fun getByTypeAndFilterWithRange(
        type: ProviderType,
        searchToken: String?,
        range: IntRange
    ): Result<List<Provider>, Throwable> = useReadDatabase {
        data.find {
            where { this.type.eq(type).withSearchToken(searchToken) }
                .offset { range.first }
                .limit { range.count() }
        }.mapEach { it.toTransport() }
    }

    override suspend fun searchByIdsAndNameWithRange(
        ids: List<UUID>,
        range: IntRange,
        namePrefix: String?,
        orderBy: OrderBy
    ): Result<List<Provider>, Throwable> = useReadDatabase {
        data.find {
            val queryBuilder = buildBaseFilterQuery(ids, namePrefix)
                .offset { range.first }
                .limit { range.count() }

            if (orderBy == OrderBy.FLAGSHIP)
                queryBuilder.orderByList({ listOf(this.flagship, this.name) }, { listOf(desc, asc) })
            else
                queryBuilder.orderBy { name }
        }.mapEach { it.toTransport() }
    }

    override suspend fun getFlagships(): Result<List<Provider>, Throwable> =
        useReadDatabase {
            data.find { where { this.flagship.eq(true) } }.mapEach { it.toTransport() }
        }

    override suspend fun countByIdsAndNameWithRange(ids: List<UUID>, namePrefix: String?): Result<Int, Throwable> =
        useReadDatabase {
            data.count { buildBaseFilterQuery(ids, namePrefix) }
        }

    override suspend fun getWithProviderUnitsAndGroups(id: UUID): Result<ProviderWithUnitsAndGroups, Throwable> =
        useReadDatabase {
            val provider = data.get(id).get()
            val providerUnits =
                providerUnitModelDataService.find { where { this.providerId.eq(id).and(status.eq(ACTIVE)) } }.get()
            val groupIds = providerUnits.mapNotNull { it.providerUnitGroupId }

            return@useReadDatabase providerUnitGroupModelDataService
                .find { where { this.id.inList(groupIds) } }
                .map { ProviderWithUnitsAndGroups(
                    provider.toTransport(),
                    it.map { it.toTransport() },
                    providerUnits.map { it.toTransport() }
                )
                }
        }

    override suspend fun getByCnpj(cnpj: String): Result<Provider, Throwable> = useReadDatabase {
        data.findOne { where { this.cnpj.eq(cnpj.onlyNumbers()).withStatus() } }.map { it.toTransport() }
    }

    override suspend fun getByIdsAndBrands(
        providerIds: List<UUID>, brands: List<Brand>
    ) =
        data.find {
            where {
                this.id.inList(providerIds) and
                        this.brand.inList(brands).withStatus()
            }.orderBy { this.name }
        }.mapEach { it.toTransport() }

    private fun buildBaseFilterQuery(ids: List<UUID>, namePrefix: String?):
            QueryBuilder<ProviderModelDataService.FieldOptions, ProviderModelDataService.OrderingOptions> =
        QueryBuilder(
            ProviderModelDataService.FieldOptions(),
            ProviderModelDataService.OrderingOptions()
        ).where { withIdsPrefix(ids).withSearchToken(namePrefix).withStatus() }

    private fun withIdsPrefix(ids: List<UUID>): Predicate =
        Predicate.inList(ProviderModelDataService.FieldOptions().id, ids)

    private fun Predicate.withStatus(status: Status? = ACTIVE) =
        if (status != null)
            this.and(ProviderModelDataService.FieldOptions().status.eq(status))
        else this

    private fun Predicate.withIds(ids: List<UUID>) =
        if (ids.isNotEmpty())
            this.and(ProviderModelDataService.FieldOptions().id.inList(ids))
        else this

    private fun Predicate.withSearchToken(namePrefix: String?): Predicate =
        when {
            namePrefix != null -> this and Predicate.search(ProviderModelDataService.FieldOptions().searchTokens, namePrefix)
            else -> this
        }

}
