package br.com.alice.provider.consumers

import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.data.layer.models.Provider
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.StructuredAddress
import br.com.alice.data.layer.models.StructuredAddressReferenceModel
import br.com.alice.provider.client.ProviderService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.event.BackfillAnesthetistCreatedEvent
import br.com.alice.staff.event.ProviderBackfillPayload
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import java.util.UUID

class BackfillProviderAssociationConsumer(
    private val providerUnitService: ProviderUnitService,
    private val providerService: ProviderService
) : Consumer() {

    suspend fun associateProviderAndProviderUnitToHealthProfessional(event: BackfillAnesthetistCreatedEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {
            associateStaffWithProviderAndProviderUnit(
                staff = event.payload.staff,
                providerUnitType = ProviderUnit.Type.MEDICAL_COMPANY,
                providerType = ProviderType.MEDICAL_COMPANY,
                providerData = event.payload.provider
            )
        }

    private suspend fun associateStaffWithProviderAndProviderUnit(
        staff: Staff,
        providerUnitType: ProviderUnit.Type,
        providerType: ProviderType,
        providerData: ProviderBackfillPayload
    ): Result<Any, Throwable> {
        return getOrCreateProvider(providerData, staff, providerType).flatMap {
            getOrCreateUnit(it.id, providerUnitType, providerData, staff)
        }
    }

    private suspend fun getOrCreateProvider(
        providerData: ProviderBackfillPayload,
        staff: Staff,
        providerType: ProviderType
    ): Result<Provider, Throwable> {
        val providerId = "${staff.fullName}${staff.id}".toSafeUUID()

        return providerService.getByCnpj(providerData.cnpj.onlyNumbers())
            .coFoldNotFound {
                providerService.add(
                    Provider(
                        id = providerId,
                        name = providerData.name,
                        type = providerType,
                        imageUrl = staff.profileImageUrl,
                        cnpj = providerData.cnpj.onlyNumbers()
                    )
                )
            }.coFoldDuplicated {
                providerService.get(providerId)
            }
    }

    private suspend fun getOrCreateUnit(
        providerId: UUID,
        providerUnitType: ProviderUnit.Type,
        providerData: ProviderBackfillPayload,
        staff: Staff
    ) = providerUnitService.getByCnpj(providerData.cnpj).flatMap {
        providerUnitService.update(
            it.copy(
                clinicalStaffIds = it.clinicalStaffIds.orEmpty().plus(staff.id).distinct()
            )
        )
    }.coFoldNotFound {
        val unit = ProviderUnit(
            id = providerId,
            type = providerUnitType,
            showOnApp = false,
            name = providerData.name,
            providerId = providerId,
            clinicalStaffIds = listOf(staff.id),
            cnpj = providerData.cnpj,
            bankCode = providerData.bankCode,
            accountNumber = providerData.accountNumber,
            agencyNumber = providerData.agencyNumber,
        )
        val address = providerData.address?.let { address ->
            StructuredAddress(
                id = providerId,
                street = address.street,
                number = address.number,
                complement = address.complement,
                neighborhood = address.neighborhood,
                city = address.city,
                state = address.state,
                zipcode = address.zipcode,
                latitude = address.latitude,
                longitude = address.longitude,
                referencedModelId = providerId,
                referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT
            )
        }
        address?.let {
            providerUnitService.addWithAddress(unit, it)
        } ?: run {
            providerUnitService.add(unit)
        }
    }
}
