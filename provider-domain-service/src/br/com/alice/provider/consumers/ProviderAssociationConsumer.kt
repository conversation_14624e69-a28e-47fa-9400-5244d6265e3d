package br.com.alice.provider.consumers

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.extensions.coFoldDuplicated
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.data.layer.models.PhoneNumber
import br.com.alice.data.layer.models.PhoneType
import br.com.alice.data.layer.models.Provider
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.StaffSignupRequestProvider
import br.com.alice.data.layer.models.StructuredAddress
import br.com.alice.data.layer.models.StructuredAddressReferenceModel
import br.com.alice.provider.client.ProviderService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.event.AnesthetistCreatedEvent
import br.com.alice.staff.event.StaffCreatedFromStaffSignupRequestEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import java.util.UUID

class ProviderAssociationConsumer(
    private val providerUnitService: ProviderUnitService,
    private val providerService: ProviderService
) : Consumer() {

    suspend fun associateProviderAddProviderUnitToAnesthetist(event: AnesthetistCreatedEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {
            associateStaffWithProviderAndProviderUnit(
                staff = event.payload.staff,
                providerUnitType = ProviderUnit.Type.MEDICAL_COMPANY,
                providerType = ProviderType.MEDICAL_COMPANY,
            )
        }

    suspend fun associateProviderAndProviderUnitToHealthProfessional(event: StaffCreatedFromStaffSignupRequestEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {
            associateStaffWithProviderAndProviderUnit(
                staff = event.payload.staff,
                providerUnitType = ProviderUnit.Type.CLINICAL,
                providerType = ProviderType.CLINICAL,
                providerData = event.payload.provider
            )
        }

    private suspend fun associateStaffWithProviderAndProviderUnit(
        staff: Staff,
        providerUnitType: ProviderUnit.Type,
        providerType: ProviderType,
        providerData: StaffSignupRequestProvider? = null
    ): Result<Any, Throwable> {
        return getOrCreateProvider(providerData, staff, providerType).flatMap {
            getOrCreateUnit(it.id, providerUnitType, providerData, staff)
        }
    }

    private suspend fun getOrCreateProvider(
        providerData: StaffSignupRequestProvider?,
        staff: Staff,
        providerType: ProviderType
    ): Result<Provider, Throwable> {
        val providerId = "${staff.fullName}${staff.id}".toSafeUUID()

        return getProviderByCnpj(providerData?.cnpj)
            .coFoldNotFound {
                providerService.add(
                    Provider(
                        id = providerId,
                        name = providerData?.name ?: staff.fullName,
                        type = providerType,
                        imageUrl = staff.profileImageUrl,
                        cnpj = providerData?.cnpj?.onlyNumbers(),
                    )
                )
            }.coFoldDuplicated {
                providerService.get(providerId)
            }
    }

    private suspend fun getOrCreateUnit(
        providerId: UUID,
        providerUnitType: ProviderUnit.Type,
        providerData: StaffSignupRequestProvider?,
        staff: Staff
    ) = getAndUpdateUnitByCnpj(providerData, staff.id)
        .coFoldNotFound {
            val unit = ProviderUnit(
                id = providerId,
                type = providerUnitType,
                showOnApp = false,
                name = providerData?.name ?: staff.fullName,
                providerId = providerId,
                clinicalStaffIds = listOf(staff.id),
                cnpj = providerData?.cnpj,
                cnes = providerData?.cnes,
                bankCode = providerData?.bankCode,
                accountNumber = providerData?.accountNumber,
                agencyNumber = providerData?.agencyNumber,
                phones = providerData?.phones?.map { phone ->
                    PhoneNumber(
                        phone = phone.number,
                        type = PhoneType.valueOf(phone.type)
                    )
                } ?: emptyList()
            )
            val address = providerData?.address?.let { address ->
                StructuredAddress(
                    id = providerId,
                    street = address.street,
                    number = address.number,
                    complement = address.complement,
                    neighborhood = address.neighborhood,
                    city = address.city,
                    state = address.state,
                    zipcode = address.zipcode,
                    referencedModelId = providerId,
                    referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT
                )
            }
            address?.let {
                providerUnitService.addWithAddress(unit, it)
            } ?: run {
                providerUnitService.add(unit)
            }
        }

    private suspend fun getAndUpdateUnitByCnpj(
        providerData: StaffSignupRequestProvider?,
        staffId: UUID
    ): Result<ProviderUnit, Throwable> =
        providerData?.cnpj?.let { cnpj ->
            providerUnitService.getByCnpj(cnpj.onlyNumbers()).flatMap {
                providerUnitService.update(
                    it.copy(
                        clinicalStaffIds = it.clinicalStaffIds.orEmpty().plus(staffId).distinct()
                    )
                )
            }
        } ?: NotFoundException().failure()

    private suspend fun getProviderByCnpj(cnpj: String?): Result<Provider, Throwable> {
        return cnpj?.let { cnpj ->
            providerService.getByCnpj(cnpj.onlyNumbers())
        } ?: NotFoundException().failure()
    }
}
