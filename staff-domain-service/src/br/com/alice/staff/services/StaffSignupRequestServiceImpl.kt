package br.com.alice.staff.services

import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.QueryAllUsage
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.basePredicateForFilters
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.data.layer.models.StaffSignupRequest
import br.com.alice.data.layer.models.StaffSignupRequestAdditionalInfoModel
import br.com.alice.data.layer.models.StaffSignupRequestModel
import br.com.alice.data.layer.models.StaffSignupRequestStatusModel
import br.com.alice.data.layer.services.StaffSignupRequestModelDataService
import br.com.alice.staff.client.ApproveRequestCommand
import br.com.alice.staff.client.RejectRequestCommand
import br.com.alice.staff.client.StaffSignupRequestFilter
import br.com.alice.staff.client.StaffSignupRequestService
import br.com.alice.staff.converters.toModel
import br.com.alice.staff.converters.toTransport
import br.com.alice.staff.event.StaffSignupRequestApprovedEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class StaffSignupRequestServiceImpl(
    private val dataService: StaffSignupRequestModelDataService,
    private val kafkaProducerService: KafkaProducerService
) : StaffSignupRequestService {

    override suspend fun get(id: UUID): Result<StaffSignupRequest, Throwable> =
        dataService.get(id).map { it.toTransport() }

    @OptIn(QueryAllUsage::class)
    override suspend fun findBy(
        filters: StaffSignupRequestFilter,
        range: IntRange,
    ): Result<List<StaffSignupRequest>, Throwable> =
        dataService.find {
            filters.toPredicate(this.fieldOptions)?.let {
                where { it }
                    .orderBy { this.createdAt }
                    .sortOrder { desc }
                    .offset { range.first }
                    .limit { range.count() }
            } ?: all()
                .orderBy { this.createdAt }
                .sortOrder { desc }
                .offset { range.first }
                .limit { range.count() }
        }.mapEach { it.toTransport() }

    @OptIn(QueryAllUsage::class)
    override suspend fun countBy(
        filters: StaffSignupRequestFilter
    ): Result<Int, Throwable> =
        dataService.count {
            filters.toPredicate(this.fieldOptions)?.let { where { it } } ?: all()
        }

    override suspend fun create(model: StaffSignupRequest): Result<StaffSignupRequest, Throwable> =
        dataService.add(model.toModel()).map { it.toTransport() }

    override suspend fun update(model: StaffSignupRequest): Result<StaffSignupRequest, Throwable> =
        dataService.update(model.toModel()).map { it.toTransport() }

    override suspend fun reject(command: RejectRequestCommand): Result<StaffSignupRequest, Throwable> =
        dataService.get(command.id).flatMap { request ->
            validatePendingStatus(request).flatMap {
                val rejectedRequest = request.copy(
                    status = StaffSignupRequestStatusModel.REJECTED,
                    rejectionReason = command.rejectionReason,
                    reviewedBy = command.reviewerId,
                )

                dataService.update(rejectedRequest).map { it.toTransport() }
            }
        }

    override suspend fun approve(command: ApproveRequestCommand): Result<StaffSignupRequest, Throwable> =
        dataService.get(command.id).flatMap { request ->
            validatePendingStatus(request).flatMap {
                val approvedRequest = request.copy(
                    status = StaffSignupRequestStatusModel.APPROVED,
                    reviewedBy = command.reviewerId,
                    additionalInfo = StaffSignupRequestAdditionalInfoModel(
                        tier = command.tier,
                        theoristTier = command.theoristTier,
                        showOnApp = command.showOnApp,
                        specialtyId = command.specialtyId,
                        subSpecialtyIds = command.subSpecialtyIds,
                        imageUrl = command.imageUrl
                    )
                )

                dataService.update(approvedRequest).map { it.toTransport() }.then {
                    kafkaProducerService.produce(StaffSignupRequestApprovedEvent(it))
                }
            }
        }

    private fun validatePendingStatus(request: StaffSignupRequestModel): Result<StaffSignupRequestModel, Throwable> =
        if (request.status == StaffSignupRequestStatusModel.PENDING) {
            request.success()
        } else {
            IllegalStateException("Modification is only allowed for requests with status 'PENDING'.").failure()
        }


    @OptIn(WithFilterPredicateUsage::class)
    private fun StaffSignupRequestFilter.toPredicate(fieldOptions: StaffSignupRequestModelDataService.FieldOptions) =
        basePredicateForFilters()
            .withFilter(namePrefix) { fieldOptions.searchTokens.search(it) }
            .withFilter(status) { fieldOptions.status.eq(it.toString()) }
}
