package br.com.alice.staff.converters

import br.com.alice.common.Converter
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.Gender
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.models.State
import br.com.alice.data.layer.models.Council
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.HealthSpecialistScoreEnum
import br.com.alice.data.layer.models.StaffSignupRequestAdditionalInfo
import br.com.alice.data.layer.models.StaffSignupRequestHealthProfessional
import java.util.UUID

object StaffSignupRequestToHealthProfessionalConverter :
    Converter<StaffSignupRequestHealthProfessional, HealthProfessional>(
        StaffSignupRequestHealthProfessional::class, HealthProfessional::class
    ) {
    fun convert(
        source: StaffSignupRequestHealthProfessional,
        staffId: UUID,
        additionalInfo: StaffSignupRequestAdditionalInfo
    ): HealthProfessional =
        HealthProfessional(
            id = staffId,
            staffId = staffId,
            profileBio = source.profileBio,
            council = Council(
                type = CouncilType.valueOf(source.councilType),
                number = source.councilNumber,
                state = State.valueOf(source.councilState)
            ),
            specialtyId = additionalInfo.specialtyId,
            subSpecialtyIds = additionalInfo.subSpecialtyIds,
            quote = source.curiosity,
            education = source.education?.let { listOf(it) } ?: emptyList(),
            tier = SpecialistTier.valueOf(additionalInfo.tier),
            theoristTier = SpecialistTier.valueOf(additionalInfo.theoristTier),
            showOnApp = additionalInfo.showOnApp,
            email = source.email,
            name = "${source.firstName} ${source.lastName}",
            gender = Gender.valueOf(source.gender),
            nationalId = source.nationalId,
            role = Role.COMMUNITY,
            type = StaffType.COMMUNITY_SPECIALIST,
            imageUrl = additionalInfo.imageUrl,
            healthSpecialistScore = HealthSpecialistScoreEnum.DOMINATING
        )
}
